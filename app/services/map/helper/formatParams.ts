import { MAP_VERSION } from '../../../constants/map';
import {
  IMapMethods,
  IGeocoderQuery,
  IGetSuggestionQuery,
  IReverseGeocoderQuery,
  ISearchNearByQuery,
  IGetLocationByIpQuery,
} from 'definitions/map/index';
import { getMapKey, reverseLocationString } from './utils';

// 高德地图对应的参数处理
const AMapFormatParams: IMapMethods = {
  geocoder(query: IGeocoderQuery) {
    return {
      key: getMapKey(MAP_VERSION.AMAP, query.biz),
      ...query,
    };
  },
  reverseGeocoder(query: IReverseGeocoderQuery) {
    return {
      key: getMapKey(MAP_VERSION.AMAP, query.biz),
      ...query,
    };
  },
  searchNearBy(query: ISearchNearByQuery) {
    const offset = query.pageSize || 30;
    delete query.pageSize;
    return {
      key: getMapKey(MAP_VERSION.AMAP, query.biz),
      radius: 500,
      extensions: 'all',
      types: '购物服务|体育休闲服务|医疗保健服务|住宿服务|商务住宅|科教文化服务',
      page: 1,
      offset,
      ...query,
    };
  },
  getSuggestion(query: IGetSuggestionQuery) {
    return {
      key: getMapKey(MAP_VERSION.AMAP, query.biz),
      datatype: 'poi',
      citylimit: true,
      ...query,
    };
  },
  getLocationByIp(query: IGetLocationByIpQuery) {
    return {
      key: getMapKey(MAP_VERSION.AMAP, query.biz),
      ...query,
    };
  },
};

// 腾讯地图对应的参数处理
const QQMapFormatParams: IMapMethods = {
  geocoder(query: IGeocoderQuery) {
    return {
      key: getMapKey(MAP_VERSION.QQMAP, query.biz),
      ...query,
    };
  },
  reverseGeocoder(query: IReverseGeocoderQuery) {
    return {
      key: getMapKey(MAP_VERSION.QQMAP, query.biz),
      get_poi: 0,
      ...query,
      location: reverseLocationString(query.location),
    };
  },
  searchNearBy(query: ISearchNearByQuery) {
    const poiOptions =
      query.poiOptions ||
      `policy=2;radius=${query.radius || 500};page_size=${query.pageSize || 10};page_index=${query.page || 1};address_format=short`;
    return {
      key: getMapKey(MAP_VERSION.QQMAP, query.biz),
      get_poi: 1,
      poi_options: poiOptions,
      ...query,
      location: reverseLocationString(query.location),
    };
  },
  getSuggestion(query: IGetSuggestionQuery) {
    const region = query.city || '中国';
    const keyword = query.keywords;
    return {
      key: getMapKey(MAP_VERSION.QQMAP, query.biz),
      policy: 1,
      region,
      region_fix: 0,
      keyword,
      address_format: 'short',
      ...query,
    };
  },
  getLocationByIp(query: IGetLocationByIpQuery) {
    return {
      key: getMapKey(MAP_VERSION.QQMAP, query.biz),
      ...query,
    };
  },
};

const formatParams = {
  [MAP_VERSION.AMAP]: AMapFormatParams,
  [MAP_VERSION.QQMAP]: QQMapFormatParams,
};

export default formatParams;
