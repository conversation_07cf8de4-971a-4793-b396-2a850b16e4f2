import { checkPureWscSingleStore, checkRetailMinimalistShop, checkRetailSingleStore, checkRetailChainStore } from '@youzan/utils-shop';
import get from 'lodash/get';
import { isInGrayReleaseByKdtId } from './WhiteListUtils';

// 是否显示再来一单按钮
export async function getDirectBuyAgainBtnConfig(ctx: any) {
  // 是否满足切流
  const result = await isInGrayReleaseByKdtId(
    ctx,
    { namespace: 'wsc-h5-trade.gray-release', key: 'directBuyAgainConfig' },
    ctx.kdtId
  );
  if (!result) {
    return {
      show: false,
      abTraceId: null,
    };
  }
  // 店铺条件是否满足
  const shopMetaInfo = await ctx.callService(
    'iron-base/shop.ShopMetaReadService',
    'getShopMetaInfo',
    ctx.kdtId
  );
  const isPureWscSingleStore = checkPureWscSingleStore(shopMetaInfo);
  const isRetailMinimalistShop = checkRetailMinimalistShop(shopMetaInfo);
  const isRetailSingleStore = checkRetailSingleStore(shopMetaInfo);
  const isRetailChainStore = checkRetailChainStore(shopMetaInfo);
  // 不满足店铺类型(微商城单店、有赞连锁D版、零售单店、零售连锁)，不请求
  if (!(isPureWscSingleStore || isRetailMinimalistShop || isRetailSingleStore || isRetailChainStore)) {
    return {
      show: false,
      abTraceId: null,
    };
  }
  // 是否满足AB实验结果
  const { buyerId } = ctx;
  const abConfig = (await ctx.ABTestClient?.getTest('direct_buy_again', buyerId)) || {};
  // 实验是否有意义（下线是无意义的一种返回:false）
  const isValid = abConfig.isValid;
  const show = isValid ? get(abConfig, 'configurations.show', false) : true;
  const abTraceId = abConfig?.abTraceId || null;

  return {
    show,
    abTraceId,
    type: 'directBuyAgain',
  };
}