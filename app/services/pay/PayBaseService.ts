import BaseService from '../base/BaseService';

class PayBaseService extends BaseService {
  get PAY_GATEWAY() {
    return {
      // 小程序 - 支付方式列表
      WEAPP_PAY_CHANNELS: {
        service: 'youzan.pay.cashier.pre.pay.cashier',
        method: 'appletquery',
        version: '1.0.0'
      },
      // 支付方式列表
      PAY_CHANNELS: {
        service: 'youzan.pay.cashier.pre.pay.cashier',
        method: 'query',
        version: '1.0.1'
      },
      // 支付
      PAY: {
        service: 'youzan.pay.cashier.pre.pay.cashier.v3',
        method: 'prepay',
        version: '1.0.0'
      },
      PAY_101: {
        service: 'youzan.pay.cashier.pre.pay.cashier.v3',
        method: 'prepay',
        version: '1.0.1'
      },
      PAY_102: {
        service: 'youzan.pay.cashier.pre.pay.cashier.v3',
        method: 'prepay',
        version: '1.0.2'
      },
      PAY_105: {
        service: 'youzan.pay.cashier.pre.pay.cashier.v3',
        method: 'prepay',
        version: '1.0.5'
      },
      PAY_109: {
        service: 'youzan.pay.cashier.pre.pay.cashier.v3',
        method: 'prepay',
        version: '1.0.9'
      },
      QUERY_UMP: {
        service: 'youzan.pay.unified.cashier.api.v3.ump',
        method: 'query',
        version: '1.0.0'
      }
    };
  }
}

export = PayBaseService;
