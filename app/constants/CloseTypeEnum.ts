/** ************************************************************************************************
 * 订单关闭类型枚举值
 * 参考自: https://gitlab.qima-inc.com/trade-platform/trade-core/blob/40f056e63cbbbbaf6ef5cd92ac786c7bf66b519c/trade-core-api/src/main/java/com/youzan/trade/core/service/constant/CloseTypeEnum.java#L14
 ************************************************************************************************ */
enum CloseTypeEnum {
  NORMAL = 0,
  EXPIRED = 1,
  FULL_REFUND = 2,
  ORDER_CANCEL = 3,
  BUYER_CANCEL = 4,
  SELLER_CANCEL = 5,
  PART_REFUND = 6,
  CANNOT_COMM_BUYER = 10,
  BUYER_WRONG_BUY = 11,
  BUYER_NOT_WANT_BUY = 12,
  HAS_TANS_BY_BANK_OFFLINE = 13,
  HAS_ACCOMPLISH_BY_FACETOFACE = 14,
  HAS_ACCOMPLISH_BY_CASH_DELIVERY = 15,
  HAS_TRANS_BY_BANK_ONLINE = 16,
  CANNOT_ACCOMPLISH_OUT_OF_STOCK = 17,
  CUT_PAYMENT_FAIL = 18,
  ZERO_ORDER_CLOSE = 19,
  COMMUNITY_GROUP_BUY_NOT_PAY = 20,
  FEE_ORDER_LOTTERY_DRAW_FAIL = 21,
  REJECT_REFUND = 22,
  SAMPLE_ORDER_REJECT = 23,
  SPECIFICATION_STYLE_QUANTITY_IS_WRONG = 27,
  INABILITY_TO_PAY_PROPERLY = 28,
  INCORRECT_RECEIPT_ADDRESS_INFORMATION = 29,
  GOODS_OUT_OF_STOCK = 30,
  DONT_WANT_TO_BUY = 31,
  OTHER = 32,
  HOTEL_ORDER_PENALTY_REFUND = 33,
}

class CloseType {
  private static descriptions: { [key in CloseTypeEnum]: string } = {
    [CloseTypeEnum.NORMAL]: "未关闭",
    [CloseTypeEnum.EXPIRED]: "过期关闭",
    [CloseTypeEnum.FULL_REFUND]: "标记退款",
    [CloseTypeEnum.ORDER_CANCEL]: "订单取消",
    [CloseTypeEnum.BUYER_CANCEL]: "买家取消",
    [CloseTypeEnum.SELLER_CANCEL]: "卖家取消",
    [CloseTypeEnum.PART_REFUND]: "部分退款",
    [CloseTypeEnum.CANNOT_COMM_BUYER]: "无法联系上买家",
    [CloseTypeEnum.BUYER_WRONG_BUY]: "买家误拍或重拍了",
    [CloseTypeEnum.BUYER_NOT_WANT_BUY]: "买家无诚意完成交易",
    [CloseTypeEnum.HAS_TANS_BY_BANK_OFFLINE]: "已通过银行线下汇款",
    [CloseTypeEnum.HAS_ACCOMPLISH_BY_FACETOFACE]: "已通过同城见面交易",
    [CloseTypeEnum.HAS_ACCOMPLISH_BY_CASH_DELIVERY]: "已通过货到付款交易",
    [CloseTypeEnum.HAS_TRANS_BY_BANK_ONLINE]: "已通过网上银行直接汇款",
    [CloseTypeEnum.CANNOT_ACCOMPLISH_OUT_OF_STOCK]: "已经缺货无法交易",
    [CloseTypeEnum.CUT_PAYMENT_FAIL]: "扣款失败",
    [CloseTypeEnum.ZERO_ORDER_CLOSE]: "0元关单",
    [CloseTypeEnum.COMMUNITY_GROUP_BUY_NOT_PAY]: "社区团购活动结束未付款",
    [CloseTypeEnum.FEE_ORDER_LOTTERY_DRAW_FAIL]: "0元抽奖订单未中一等奖",
    [CloseTypeEnum.REJECT_REFUND]: "拒单退款",
    [CloseTypeEnum.SAMPLE_ORDER_REJECT]: "主播寄样申请被拒绝",
    [CloseTypeEnum.SPECIFICATION_STYLE_QUANTITY_IS_WRONG]: "规格/款式/数量拍错",
    [CloseTypeEnum.INABILITY_TO_PAY_PROPERLY]: "无法正常支付",
    [CloseTypeEnum.INCORRECT_RECEIPT_ADDRESS_INFORMATION]: "收货地址信息填写错误",
    [CloseTypeEnum.GOODS_OUT_OF_STOCK]: "商品缺货",
    [CloseTypeEnum.DONT_WANT_TO_BUY]: "我不想买了",
    [CloseTypeEnum.OTHER]: "其他",
    [CloseTypeEnum.HOTEL_ORDER_PENALTY_REFUND]: "酒店订单部分退（存在违约金）",
  };

  static findBuyerCloseReason(): CloseTypeEnum[] {
    return Object.values(CloseTypeEnum).filter(
      (value) => value >= 27 && value <= 32
    ) as CloseTypeEnum[];
  }

  static findByValue(value: number | null): CloseTypeEnum | null {
    if (value === null) {
      return null;
    }
    return Object.values(CloseTypeEnum).includes(value)
      ? value
      : null;
  }

  static findByName(name: string | null): CloseTypeEnum | null {
    if (!name) {
      return null;
    }
    return (CloseTypeEnum as any)[name] ?? null;
  }

  static getDesc(type: CloseTypeEnum): string {
    return this.descriptions[type];
  }
}

export {
  CloseTypeEnum,
  CloseType,
}