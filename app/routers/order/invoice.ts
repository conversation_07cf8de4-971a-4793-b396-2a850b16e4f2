export = [
  [
    'GET',
    '/wsctrade/order/invoice',
    'order.OrderInvoiceController',
    'getIndexHtml'
  ],
  [
    'GET',
    '/wsctrade/order/invoice/success',
    'order.OrderInvoiceController',
    // 'checkRedirectToShopDomain', // 自带kdtId
    'getInvoiceSuccessHtml'
  ],
  [
    'POST',
    '/wsctrade/order/invoice/getInvoiceDetail.json',
    'order.OrderInvoiceController',
    'postGetInvoiceDetail'
  ],
  [
    'POST',
    '/wsctrade/order/invoice/applyInvoice.json',
    'order.OrderInvoiceController',
    'postApplyInvoice'
  ],
  [
    'POST',
    '/wsctrade/order/invoice/queryTaxInfoListByCorpName.json',
    'order.OrderInvoiceController',
    'postQueryTaxInfoListByCorpName'
  ],
  [
    'POST',
    '/wsctrade/order/invoice/queryCompanyDetailTaxInfo.json',
    'order.OrderInvoiceController',
    'postQueryCompanyDetailTaxInfo'
  ],
  [
    'POST',
    '/wsctrade/order/invoice/getAuthorUrl.json',
    'order.OrderInvoiceController',
    'postGetAuthorUrl'
  ],
  [
    'POST',
    '/wsctrade/order/invoice/sendInvoiceEmail.json',
    'order.OrderInvoiceController',
    'postInvoiceEmail'
  ],
  [
    'GET',
    '/wsctrade/order/invoice-v2',
    'order.OrderInvoiceController',
    'getInvoiceCreateIndexHtml'
  ],
];
