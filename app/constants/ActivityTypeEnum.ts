/** ************************************************************************************************
 * 活动类型枚举值
 * 参考自: https://gitlab.qima-inc.com/trade-platform/trade-core/blob/8aaf563ebbf963e7fc473b52e30825c9a781433c/trade-core-api/src/main/java/com/youzan/trade/core/service/constant/ActivityTypeEnum.java
 ************************************************************************************************ */
enum ActivityTypeEnum {
  NO_ACTIVITY = 0,
  NONE = 1,
  GROUP_CASH_BACK = 2,
  AUCTION = 3,
  GROUP_BUY = 4,
  POINTS_EXCHANGE = 5,
  SEC_KILL = 6,
  PACKAGE_BUY = 7,
  PRESENT_LUCKY = 8,
  GOODS_SCAN = 9,
  CUSTOMER_DISCOUNT = 10,
  TIME_LIMIT_DISCOUNT = 11,
  CROWD_FUNDING = 12,
  PERIOD_BUY = 13,
  GIFT = 14,
  RANDOM_BUY = 15,
  QRCODE_BUY = 16,
  SHARE_CUT = 19,
  F_CODE = 20,
  HELP_CUT = 21,
  RECOMMEND_GIFT = 22,
  LUCKY_DRAW_GROUP = 23,
  PLUS_BUY = 24,
  GIFT_COMMUNITY = 63,
  MEET_REDUCE = 101,
  CASH_BACK = 102,
  SUPPLIER_MEET_REDUCE = 103,
  DEPOSIT_EXPANSION = 114,
  SECOND_HALF = 115,
  POINT_DEDUCTION = 256,
  JX_OLD_LEAD_NEW_GROUP_BUY = 62,
  HELP_DEPOSIT_EXPANSION = 116,
  LADDER_GROUPON = 26,
  CASHBACK_PRO = 203,
  INSOURCING_FISSION = 205,
  SET_POINTS_CARD_EXCHANGE = 258,
  TUITION_DEDUCTION = 259,
  MEET_SEND = 261,
  SCRM_LEVEL_REDUCE = 202,
  LUCKY_DRAW_CODE = 204,
  BLIND_BOX_GRANT = 401,
  BLIND_BOX_VERIFICATION = 402,
  UMP_SOLITAIRE = 508,
  STORE_TIME_LIMITED_DISCOUNT = 509,
  SEND_RED_PACKET = 510,
  RED_PACKET_REDUCE = 511,
  PLATFORM_SUBSIDY = 513,
  PH_SECKILL = 514,
  PH_RANDOM_REDUCTION = 517,
  TIME_LIMIT_SECKILL = 403,
  COMBINE_BUY = 208,
  GUANG_SECKILL = 30011,
  ENJOY_BUY = 32766,
  RESERVE = 14100,
  EXHIBITION = 14200,
  COUPON = 105,
}

class ActivityType {
  private static descriptions: { [key in ActivityTypeEnum]: string } = {
    [ActivityTypeEnum.NO_ACTIVITY]: "没有活动",
    [ActivityTypeEnum.NONE]: "没有活动",
    [ActivityTypeEnum.GROUP_CASH_BACK]: "团购返现",
    [ActivityTypeEnum.AUCTION]: "降价拍",
    [ActivityTypeEnum.GROUP_BUY]: "拼团",
    [ActivityTypeEnum.POINTS_EXCHANGE]: "积分兑换",
    [ActivityTypeEnum.SEC_KILL]: "秒杀",
    [ActivityTypeEnum.PACKAGE_BUY]: "优惠套餐",
    [ActivityTypeEnum.PRESENT_LUCKY]: "赠品",
    [ActivityTypeEnum.GOODS_SCAN]: "商品扫码",
    [ActivityTypeEnum.CUSTOMER_DISCOUNT]: "会员折扣",
    [ActivityTypeEnum.TIME_LIMIT_DISCOUNT]: "限时折扣",
    [ActivityTypeEnum.CROWD_FUNDING]: "众筹",
    [ActivityTypeEnum.PERIOD_BUY]: "周期购",
    [ActivityTypeEnum.GIFT]: "送礼",
    [ActivityTypeEnum.RANDOM_BUY]: "随机点餐",
    [ActivityTypeEnum.QRCODE_BUY]: "扫码优惠",
    [ActivityTypeEnum.SHARE_CUT]: "享立减",
    [ActivityTypeEnum.F_CODE]: "F码",
    [ActivityTypeEnum.HELP_CUT]: "助力砍价",
    [ActivityTypeEnum.RECOMMEND_GIFT]: "推荐有奖",
    [ActivityTypeEnum.LUCKY_DRAW_GROUP]: "抽奖拼团",
    [ActivityTypeEnum.PLUS_BUY]: "加价购",
    [ActivityTypeEnum.GIFT_COMMUNITY]: "送礼社区版",
    [ActivityTypeEnum.MEET_REDUCE]: "满减送",
    [ActivityTypeEnum.CASH_BACK]: "订单返现",
    [ActivityTypeEnum.SUPPLIER_MEET_REDUCE]: "供货商满包邮",
    [ActivityTypeEnum.DEPOSIT_EXPANSION]: "定金膨胀",
    [ActivityTypeEnum.SECOND_HALF]: "第二件半价",
    [ActivityTypeEnum.POINT_DEDUCTION]: "积分抵现",
    [ActivityTypeEnum.JX_OLD_LEAD_NEW_GROUP_BUY]: "精选老带新拼团",
    [ActivityTypeEnum.HELP_DEPOSIT_EXPANSION]: "助力定金膨胀",
    [ActivityTypeEnum.LADDER_GROUPON]: "阶梯拼团",
    [ActivityTypeEnum.CASHBACK_PRO]: "订单返现Pro",
    [ActivityTypeEnum.INSOURCING_FISSION]: "内购",
    [ActivityTypeEnum.SET_POINTS_CARD_EXCHANGE]: "集点卡兑换",
    [ActivityTypeEnum.TUITION_DEDUCTION]: "学费抵扣",
    [ActivityTypeEnum.MEET_SEND]: "实付满赠",
    [ActivityTypeEnum.SCRM_LEVEL_REDUCE]: "会员等级商品优惠",
    [ActivityTypeEnum.LUCKY_DRAW_CODE]: "0元抽奖码",
    [ActivityTypeEnum.BLIND_BOX_GRANT]: "盲盒获取",
    [ActivityTypeEnum.BLIND_BOX_VERIFICATION]: "盲盒核销",
    [ActivityTypeEnum.UMP_SOLITAIRE]: "社群接龙",
    [ActivityTypeEnum.STORE_TIME_LIMITED_DISCOUNT]: "限时到店优惠",
    [ActivityTypeEnum.SEND_RED_PACKET]: "发红包",
    [ActivityTypeEnum.RED_PACKET_REDUCE]: "红包优惠",
    [ActivityTypeEnum.PLATFORM_SUBSIDY]: "平台补贴",
    [ActivityTypeEnum.PH_SECKILL]: "普惠秒杀",
    [ActivityTypeEnum.PH_RANDOM_REDUCTION]: "普惠随机立减",
    [ActivityTypeEnum.TIME_LIMIT_SECKILL]: "限时秒杀",
    [ActivityTypeEnum.COMBINE_BUY]: "组合套餐",
    [ActivityTypeEnum.GUANG_SECKILL]: "爱逛买手店优惠",
    [ActivityTypeEnum.ENJOY_BUY]: "随心购",
    [ActivityTypeEnum.RESERVE]: "预约预定",
    [ActivityTypeEnum.EXHIBITION]: "活动展会",
    [ActivityTypeEnum.COUPON]: "优惠卡券",
  };

  /**
   * 根据枚举值获取描述
   * @param value 活动类型的枚举值
   * @return string 描述
   */
  static getDesc(value: ActivityTypeEnum): string {
    return this.descriptions[value];
  }

  /**
   * 根据枚举值获取枚举名称
   * @param value 活动类型的代码值
   * @return string | null 枚举名称 (如果找不到)
   */
  static getEnumName(value: number): string | null {
    const entry = Object.entries(ActivityTypeEnum).find(([_, val]) => val === value);
    return entry ? entry[0] : null;
  }
}

export {
  ActivityTypeEnum,
  ActivityType,
}