import mapKeysToCamelCase from '@youzan/utils/string/mapKeysToCamelCase';
import BaseService from '../base/BaseService';

class SelfFetchService extends BaseService {
  get SELF_FETCH_SERVICE() {
    return 'com.youzan.delivery.service.SelfFetchService';
  }

  // 获取店铺自提时间设置
  async getSelfFetchTime(params: any) {
    return this.invoke(this.SELF_FETCH_SERVICE, 'getSelfFetchTime', [mapKeysToCamelCase(params)]);
  }
}

export = SelfFetchService;
