import BaseService from '../base/BaseService';

interface IQueryCustomer {
  appId: string;
  accountType: number;
}

class ChannelCoreAccountService extends BaseService {
  SERVICE_NAME = 'com.youzan.channels.channel.core.api.service.ChannelCoreAccountService';

  /**
   * *  根据appId+accountType获取绑定的网店kdtId
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/832613
   *
   *  @param {Object} param -
   *  @param {string} param.appId - 三方渠道应用唯一标识appId
   *  @param {number} param.accountType - 三方渠道类型
   *  @param {number} param.mpId - 渠道在有赞唯一标识mpId
   *  @return {Promise}
   */
  async queryMpBindInfoBySelf(param: IQueryCustomer) {
    return this.invoke('queryMpBindInfoBySelf', [param]);
  }
}
export = ChannelCoreAccountService;
