import BaseController from '../base/BaseController';
import ValueCardRechargeOrderService from '../../services/pay/ValueCardRechargeOrderService';
import { Context } from 'astroboy';
import { IRechargeOrderPrepayRequest } from 'definitions/pay/IRechargeOrderPrepay';

class ValueCardRechargeOrderController extends BaseController {
  public async rechargeOrderPrepayJson(ctx: Context) {
    const postData = ctx.getPostData<IRechargeOrderPrepayRequest>();

    postData.acceptKdtId = ctx.kdtId;
    postData.buyerId = String(this.buyerId);
    postData.clientType = 1;
    postData.marketChannel = 1;
    postData.operatorMobile = this.buyer.buyerPhone;
    postData.operatorName = this.buyer.fansNickname;

    const valueCardRechargeOrderService = new ValueCardRechargeOrderService(
      ctx
    );

    const res = await valueCardRechargeOrderService.rechargeOrderPrepay(
      postData
    );

    ctx.json(0, 'ok', res);
  }
}

export = ValueCardRechargeOrderController;
