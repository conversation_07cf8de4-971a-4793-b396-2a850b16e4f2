const BaseService = require('../base/BaseService');

class SelfFetchService extends BaseService {
  get GPS_SERVICE() {
    return 'com.youzan.ic.delivery.service.GpsService';
  }

  async getReGpsInfo(params) {
    return this.invoke(this.GPS_SERVICE, 'getReGpsInfo', params);
  }

  /**
   * 
   * @param {*} params [city, address]
   * @returns 
   */
  async getGpsInfo(params) {
    return this.invoke(this.GPS_SERVICE, 'getGpsInfo', params);
  }
}

module.exports = SelfFetchService;
