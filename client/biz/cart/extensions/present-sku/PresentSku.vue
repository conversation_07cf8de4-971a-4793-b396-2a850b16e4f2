<template>
  <van-sku
    v-model="show"
    :sku="snakeCaseData"
    :quota="quota"
    :properties="snakeCaseData.item_sale_prop_list"
    :goods="goodsData"
    :goods-id="goodsData.goodsId"
    :initial-sku="initialSku"
    :hide-quota-text="true"
    :show-add-cart-btn="false"
    :disable-stepper-input="true"
    :close-on-click-overlay="true"
    :reset-selected-sku-on-hide="true"
    buy-text="完成"
    stepper-title="数量"
    :get-container="getContainer"
    @buy-clicked="handleBuyClicked"
  >
    <!-- 价格重写 -->
    <template slot="sku-header-price">
      <!-- 展示价 -->
      <p class="show-price">
        <span class="price-span-wrapper"> ￥<span class="big">0.00</span> </span>

        <span class="price-label"> 赠品 </span>
      </p>
    </template>
  </van-sku>
</template>

<script>
import { Sku, Toast } from 'vant';
import { api } from 'common/api';
import args from 'utils/browser/args';
import mapKeysToSnakeCase from '@youzan/utils/string/mapKeysToSnakeCase';

const { offlineId = 0 } = window._global || {};

export default {
  name: 'PresentSku',

  components: {
    [Sku.name]: Sku,
  },

  props: {
    selectableSkuList: Array,
  },

  data() {
    return {
      show: false,
      activityId: 0,
      quota: 1, // 赠品只能选1个
      goodsData: {}, // 商品信息
      skuData: {}, // sku信息
      initialSku: {}, // 当前选中的 sku 信息
    };
  },

  computed: {
    snakeCaseData() {
      return mapKeysToSnakeCase(this.skuData);
    },
    properties() {
      if (this.skuData.sku) {
        try {
          return JSON.parse(this.skuData.sku);
        } catch (e) {
          return [];
        }
      }
      return [];
    },
  },

  mounted() {
    this.ctx.event.listen('changePresentSku', ({ activityId, goods }) => {
      this.activityId = activityId;
      this.fetchSkuData(goods);
    });
  },

  methods: {
    getContainer() {
      return document.querySelector('body');
    },

    startLoading() {
      Toast.loading({
        mask: true,
        message: '加载中...',
        duration: 5000,
      });
    },

    stopLoading() {
      Toast.clear();
    },

    fetchSkuData(goodsData) {
      this.startLoading();
      this.goodsData = { picture: goodsData.attachmentUrl, ...goodsData };
      const { alias = '', goodsSkuInfoList = [], skuId } = goodsData || {};

      // 通过商品alias查询
      api
        .ajax({
          method: 'GET',
          url: args.add('/wscshop/sku/skudata.json', {
            alias,
            offlineId,
            v: 2,
          }),
        })
        .then(({ data }) => {
          this.stopLoading();
          if (!data) {
            return Toast('获取商品sku 失败，请重试');
          }

          // 拿独立的赠品库存覆盖商品库存
          if (goodsSkuInfoList.length) {
            data.list = data.list.map((item) => {
              const hasMatch = goodsSkuInfoList.some((sku) => {
                if (sku.skuId === item.id) {
                  item.stockNum = sku.stockNum;
                  return true;
                }
                return false;
              });

              // 没有相匹配的则表示没有库存
              if (!hasMatch) {
                item.stockNum = 0;
              }
              return item;
            });

            // 计算赠品的独立库存总和，覆盖商品库存
            data.stockNum = goodsSkuInfoList.reduce((total, item) => {
              return total + item.stockNum;
            }, 0);
          }

          data.messages = []; // 本期赠品不展示留言

          this.show = true;
          this.skuData = data;

          // 初始化选中的sku
          const selectedSku = goodsSkuInfoList?.find((item) => item.skuId === skuId) ?? {};
          this.initialSku = data.list?.find((item) => item.id === selectedSku.skuId) ?? {};
        })
        .catch(() => {
          this.stopLoading();
          Toast('获取商品sku 失败，请重试');
        });
    },
    handleBuyClicked(skuData) {
      this.stopLoading();
      this.show = false;
      this.ctx.event.emit('presentSkuChanged', {
        goodsData: this.goodsData,
        skuData: skuData.selectedSkuComb,
        activityId: this.activityId,
      });
    },
  },
};
</script>

<style scoped lang="scss">
// 覆盖 sku header 的样式
:global {
  .van-sku-header__goods-info {
    justify-content: flex-start;
  }
}

.show-price {
  color: #323233;

  .price-span-wrapper {
    font-size: 16px;

    .big {
      font-size: 22px;
      line-height: 24px;
      font-weight: 600;
    }
  }
}

.price-label {
  display: inline-block;
  background: #fbe8e6;
  border-radius: 8px;
  height: 16px;
  line-height: 16px;
  padding: 0 4px;
  font-size: 10px;
  margin-left: 4px;
  position: relative;
  top: -2px;
  color: #f0463f;
}
</style>
