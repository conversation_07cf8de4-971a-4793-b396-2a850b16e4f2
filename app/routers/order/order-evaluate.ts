export = [
  [
    'GET',
    '/wsctrade/order/evaluate/delete',
    'order.OrderEvaluateController',
    // 'checkRedirectToShopDomain', // 自带kdtId
    ['orderEvaluateAcl', 'getDeleteHtml']
  ],
  [
    'GET',
    '/wsctrade/order/evaluate/close',
    'order.OrderEvaluateController',
    // 'checkRedirectToShopDomain', // 自带kdtId
    ['orderEvaluateAcl', 'getCloseHtml']
  ],
  [
    'GET',
    '/wsctrade/order/evaluate/detail',
    'order.OrderEvaluateController',
    // 'checkRedirectToShopDomain', // 自带kdtId
    ['orderEvaluateAcl', 'getDetailHtml']
  ],
  [
    'GET',
    '/wsctrade/order/evaluate/create',
    'order.OrderEvaluateController',
    // 'checkRedirectToShopDomain', // 自带kdtId
    ['orderEvaluateAcl', 'createEvaluateHtml']
  ],
  [
    'GET',
    '/wsctrade/order/evaluate/center',
    'order.OrderEvaluateController',
    ['orderEvaluateAcl', 'getEvaluateCenterHtml']
  ],
  [
    'GET',
    '/wsctrade/order/evaluate/getEvaluateState.json',
    'order.OrderEvaluateController',
    'getEvaluateStateJson'
  ],
  [
    'POST',
    '/wsctrade/order/evaluate/create.json',
    'order.OrderEvaluateController',
    'createEvaluateJson'
  ],
  [
    'GET',
    '/wsctrade/order/evaluate/review',
    'order.OrderEvaluateController',
    ['orderEvaluateAcl', 'getEvaluationReviewHtml']
  ],
  [
    'POST',
    '/wsctrade/order/evaluate/create-evaluation.json',
    'order.OrderEvaluateController',
    'postCreateEvaluationJson'
  ],
  [
    'GET',
    '/wsctrade/order/evaluate/list.json',
    'order.OrderEvaluateController',
    'getEvaluationListJson'
  ],
  [
    'POST',
    '/wsctrade/order/evaluate/anonymous.json',
    'order.OrderEvaluateController',
    'postAnonymousEvaluationJson'
  ],
  [
    'POST',
    '/wsctrade/order/evaluate/cancel-anonymous.json',
    'order.OrderEvaluateController',
    'postCancelAnonymousEvaluationJson'
  ],
  [
    'POST',
    '/wsctrade/order/evaluate/delete-review.json',
    'order.OrderEvaluateController',
    'postDeleteReviewEvaluationJson'
  ],
  [
    'POST',
    '/wsctrade/order/evaluate/delete-item.json',
    'order.OrderEvaluateController',
    'postDeleteItemEvaluationJson'
  ],
  [
    'POST',
    '/wsctrade/order/evaluate/update-item-evaluation.json',
    'order.OrderEvaluateController',
    'postUpdateItemEvaluationJson'
  ],
  [
    'POST',
    '/wsctrade/order/evaluate/update-review-evaluation.json',
    'order.OrderEvaluateController',
    'postUpdateReviewEvaluationJson'
  ],
  [
    'GET',
    '/wsctrade/order/evaluate/goods.json',
    'order.OrderEvaluateController',
    'getEvaluateOrderGoods'
  ],
  [
    'GET',
    '/wsctrade/order/evaluate/alias.json',
    'order.OrderEvaluateController',
    'getItemEvaluation'
  ],
  [
    'GET',
    '/wsctrade/order/evaluate/maple-auth-token.json',
    'order.OrderEvaluateController',
    'getMapleAuthToken'
  ],
  [
    'POST',
    '/wsctrade/order/evaluate/get-items-evaluation-labels.json',
    'order.OrderEvaluateController',
    'getItemsEvaluationLabels'
  ],
  [
    'POST',
    '/wsctrade/order/evaluate/enrich-evaluation-content.json',
    'order.OrderEvaluateController',
    'enrichEvaluationContent'
  ],
];
