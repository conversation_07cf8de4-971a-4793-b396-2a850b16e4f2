import BaseService from '../../base/BaseService';

class WelikeMomentWhitelistService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.social.ebiz.content.api.welike.service.WelikeMomentWhitelistService';
  }

  /**
   *  店铺是否在灰度名单中
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1146306
   *
   *  @param {number} kdtId - 店铺ID
   *  @return {Promise}
   */
  async isInGrayReleaseByKdtId(kdtId: number) {
    return this.invoke('isInGrayReleaseByKdtId', [kdtId]);
  }
}

export default WelikeMomentWhitelistService;
