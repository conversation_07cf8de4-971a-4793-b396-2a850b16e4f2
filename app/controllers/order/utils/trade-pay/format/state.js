import formatDate from '@youzan/utils/date/formatDate';
import { grouponType, formatTime, isEmptyObject } from './utils/utils';

function initHandselState(prepare) {
  let isHandselFirstPhase = false;
  const trade = prepare?.tradeConfirmation || {};

  const { orderItems: activityTypeItems = [], phasePayment = {} } = trade;

  const isHandsel = activityTypeItems[0] && activityTypeItems[0].activityType === 116;

  if (isHandsel && phasePayment.currentPhase && phasePayment.currentPhase === 1) {
    isHandselFirstPhase = true;
  }

  return {
    isHandselFirstPhase,
  };
}

function initAddressState(prepare) {
  const trade = prepare.tradeConfirmation || {};
  const address = prepare.address || {};
  const display = prepare.displayConfig || {};
  const delivery = trade.delivery || {};

  const { showExpressTab } = display;
  const canSelfFetch = display.canSelfFetch || 'selfFetch' in delivery;
  let { activeTab } = address;

  // 只能自提的话就 改成1，只能配送就是0
  // 都可就保持不变
  if (showExpressTab && !canSelfFetch) {
    activeTab = 0;
  } else if (!showExpressTab && canSelfFetch) {
    activeTab = 1;
  }

  return {
    ...address,
    isSelfFetchDefault: display?.openSelfDefaultSwitch || false,
    activeTab,
  };
}

function initDeliveryState(prepare) {
  const display = prepare.displayConfig || {};
  const trade = prepare.tradeConfirmation || {};
  const delivery = trade.delivery || {};
  const postage = trade.postage || {};
  const localDelivery = prepare.shopDelivery || {};
  const deliveryTimeBucket = prepare.deliveryTimeBucket || {};

  let res = {
    ...delivery,
    text: delivery.text || postage.deliveryTimeDisplay || '',
  };

  if (!isEmptyObject(localDelivery)) {
    res = {
      ...res,
      ...localDelivery,
    };
  } // 计算送达时间的逻辑放到后端
  if (!isEmptyObject(deliveryTimeBucket)) {
    const { instantTimePoint, timeBuckets, timeSpan } = deliveryTimeBucket;
    let { textWithWeekday, text } = res;

    if (instantTimePoint && !display.showLocalDeliveryTime) {
      textWithWeekday = `尽快送达（预计 ${formatTime(new Date(instantTimePoint))}）`;
      text = isNaN(new Date(instantTimePoint).getTime?.())
        ? ''
        : formatDate(new Date(instantTimePoint));
    }

    res = {
      ...res,
      timeSpan,
      timeBucket: timeBuckets,
      deliveryTimeBucket,
      // 展示的送达时间文案（下单页送达时间项、时间选择弹窗）
      textWithWeekday,
      // 送达时间的唯一标识，用于时间选择弹窗（client/pages/order/buy/component/time-picker） 匹配 选中项
      text,
      instantTimePoint,
    };
  }

  return res;
}

function initSelfFetchState(prepare) {
  const trade = prepare?.tradeConfirmation || {};
  const display = prepare.displayConfig || {};
  const delivery = trade.delivery || {};
  const deliveryTimeBucket = prepare?.deliveryTimeBucket || {};

  const res = {
    time: display.selfFetchTime || '',
    isAllow: display.canSelfFetch || 'selfFetch' in delivery,
  };

  // 物流信息从这里取
  if (!isEmptyObject(deliveryTimeBucket)) {
    return {
      ...res,
      timeSpan: deliveryTimeBucket.timeSpan,
      timeBucket: deliveryTimeBucket.timeBuckets,
      deliveryTimeBucket,
    };
  }
  return res;
}

function initContactState(prepare) {
  const trade = prepare?.tradeConfirmation || {};
  const contact = prepare?.contact || {};

  const display = prepare.displayConfig || {};
  const items = trade.orderItems || [];
  const contactId = contact?.id || '';
  const currentContact = (contact?.list || []).find((item) => item.id === contactId) || {};

  // 联系人
  return {
    ...contact,
    required: !display.showAddressTab && items.some(({ virtualType }) => virtualType === 3),
    // 当前显示的联系人名称和联系方式，v2用
    userName: currentContact.userName || contact.userName,
    telephone: currentContact.telephone || contact.telephone,
  };
}

/** 初始化 Goods */
function initGoodsState(prepare) {
  const trade = prepare?.tradeConfirmation || {};
  const orderItems = trade.orderItems || [];
  return {
    list: orderItems,
    unavailable: trade.unavailableItems || [],
    prepareTime: trade.deliveryCheck?.prepareTime || 0,
  };
}

function initOrderState(prepare) {
  return {
    activityId: prepare?.tradeConfirmation?.orderItems?.[0].activityId,
    extensions: prepare?.extensions || {},
    agreeDeposit: false,
    // 是否隐藏优惠券组件
    couponDisplay: true,
    // 定金协议是否隐藏
    showDepositAgreementVal: true,
    orderNo: prepare.orderNo || '',
    orderNos: prepare.orderNos || [],
    ...(prepare?.tradeConfirmation?.orderConfig || {}),
  };
}

function initDisplayState(prepare) {
  const display = prepare.displayConfig || {};
  const trade = prepare?.tradeConfirmation || {};
  const adjustToChineseTimeZone = (new Date().getTimezoneOffset() + 8 * 60) * 60 * 1000;

  return {
    ...display,
    newRecommend: trade.newRecommend,
    serverTime: display.serverTime + adjustToChineseTimeZone,
    invoiceContent: +(display?.invoiceContent || 11), // 开票内容 10：开启商品明细开票；01：开启商品类目开票；11：两种方式均开启。

    payPrompt: display?.payPrompt || '',
    prompt: display?.prompt || '',
    selectedInstallmentPeriod: display?.selectedInstallmentPeriod || 0, // 分期期数

    installmentRate: display?.installmentRate || 0, // 分期费率

    showCouponBlock: true, // 是否展示优惠券模块
    showGoodsBlock: true, // 是否展示商品模块
    showBuyerMemoBlock: true, // 是否展示留言模块
  };
}

function initPayState(prepare) {
  const trade = prepare?.tradeConfirmation || {};
  const orderPayment = trade?.orderPayment;
  const prepayParams = prepare?.prePaymentPreparation || {};
  const assetPayInfos = trade?.assetPayInfos || [];
  const tradeTag = trade?.tag || {};

  if (tradeTag.continuousOrder) {
    // 会员等级连续订阅只能用银行卡支付
    // NOTE: 这个场景的下单不在这个页面，兼容支付页场景
    prepayParams.scene = 'MEM_SUB';
  } else {
    prepayParams.scene = 'VALUE_COMMON';
  }

  return {
    ...orderPayment,
    prepayParams,
    extraFees: orderPayment.extraFees || [],
    payParams: prepare?.paymentPreparation,
    phasePayment: trade?.phasePayment,
    multiPhase: trade?.multiPhasePayment,
    extPoint: prepayParams.extPointPayResultVO,
    assetPayInfos,
  };
}

function initShopState(prepare) {
  const { tradeConfirmation: trade = {} } = prepare;
  return {
    ...(trade.shop || {}),
    activityType: trade.activityType,
  };
}

function initUseBeforePayState(prepare) {
  const useBeforePay = prepare['@cashier/prior-use'] || {};
  return {
    show: useBeforePay?.show || false, // 先用后付是否露出
    enable: useBeforePay?.enable || true, // 先用后付是否可以点击
    confirm: useBeforePay?.confirm || '0', // 先用后付是否已经选中,
    protocol: useBeforePay?.protocol || false, // 先用后付用户协议是否展示
    range: useBeforePay?.range || [null, null], // 先用后付金额限制
    reason: useBeforePay?.reason || [null, null], // 先用后付金额限制时的文案
  };
}

// 周期购
function initPeriodBuy(prepare) {
  const trade = prepare?.tradeConfirmation || {};
  const items = trade?.orderItems || [];

  const item0 = items[0] || {};
  return {
    show: !!item0.issue,
    info: item0.issue || '',
    planTime: item0.planExpressTime || '',
    options: item0.deliverOptions || [],
    chosenOption: item0.deliverOption || '',
  };
}

function initCouponState(prepare) {
  const trade = prepare?.tradeConfirmation || {};
  const order = trade.orderConfig || {};
  const pay = trade.orderPayment || {};

  // 是否应用优惠券积分抵现流程新逻辑
  const newCouponProcess = prepare.orderCreation?.newCouponProcess || '';

  // 优惠券
  const coupon = {
    chosenId: 0,
    chosenIds: [],
    list: trade.coupons || [],
    disabledList: trade.unavailableCoupons || [],
  };

  // 如果是0元单或积分足额抵扣，优惠券与储值卡/礼品卡不设默认选中
  if (pay.realPay === 0) {
    if (newCouponProcess || order.newCouponProcess) {
      const chosenCoupon = coupon.list.find((item) => item.choose) || {};
      coupon.chosenId = chosenCoupon?.id || 0;
    } else {
      coupon.chosenId = 0;
    }

    return [coupon, true];
  }

  if (newCouponProcess || order.newCouponProcess) {
    // 如果是新流程 后端这边会告知最新的优惠券
    const chosenCoupon = coupon.list.find((item) => item.choose) || {};
    coupon.chosenId = chosenCoupon.id || 0;

    return [coupon, false];
  }

  // 初始化页面时，config.eventKey.couponChange 事件不存在，默认选中第一张
  const firstCoupon = coupon.list[0] || {};
  if (!(firstCoupon.value == null || firstCoupon.value === undefined)) {
    coupon.chosenId = firstCoupon.id || 0;
    // 优惠券叠加可能存在多张优惠券
    coupon.chosenIds = coupon.list.map((i) => i.id);
  }

  return [coupon, false];
}

function initValueCardState(prepare) {
  const trade = prepare?.tradeConfirmation || {};
  const pay = trade?.orderPayment || {};

  const res = {
    valueCard: {},
    list: trade?.payValueCards || [],
    disabled: trade?.unavailablePayValueCards || [],
    checked: (trade.payValueCards || [])
      .filter((item) => item.selected)
      .map((item) => item.summaryCardNo),
  };
  if (pay.realPay === 0) {
    res.valueCard.checked = [];
  }
  return res;
}

function initGroupon(prepare) {
  const trade = prepare?.tradeConfirmation || {};
  const { group = {}, activityType } = trade;
  let groupon = {};

  if (!isEmptyObject(group)) {
    if (group.receiveState === grouponType.forceReceive) {
      groupon.isChecked = true;
    }

    groupon = {
      ...groupon,
      isGroupon: true,
      isHeader: group.header,
      showAgencyReceive: group.displayAgencyReceive,
      receiveState: group.receiveState,
      headerName: group.headerUserName || '',
      activityType,
    };

    if (group.headerUserName) {
      groupon.headerAddress = {
        userName: group.headerUserName,
        tel: group.headerTel,
        province: group.headerProvince,
        city: group.headerCity,
        county: group.headerCounty,
        areaCode: group.headerAreaCode,
        addressDetail: group.headerAddressDetail,
        community: group.headerCommunity,
        idCardNumber: group.idCardNumber,
      };
    }
  }

  return groupon;
}

//
function initShopConfigState(prepare) {
  const { shopConfig = {} } = prepare;
  return {
    // 新的跑马灯开关，支持评价、好评等内容
    goodsTradeMarquee: null,
    abConfigInfo: {},
    ...shopConfig,
  };
}

// 积分兑换
function getPointDeductionState(prepare) {
  const trade = prepare?.tradeConfirmation || {};
  const { pointDeduction = {} } = trade;
  return {
    ...pointDeduction,
  };
}

export default function ({ prepare = {} } = {}) {
  const trade = prepare?.tradeConfirmation || {};
  const delivery = trade.delivery || {};

  const [coupon, isEmptyValueCard] = initCouponState(prepare);
  const display = initDisplayState(prepare);

  const state = {
    env: {
      isPayPage: true,
    },
    display,
    tradeTag: prepare?.tradeConfirmation?.tradeTag || {},
    user: {
      delivery,
    },
    pointsName: prepare.pointsConfig?.pointName || '积分',
    memberCard: trade.memberCard || {},
    // 物流
    postage: trade.postage || {},
    shop: initShopState(prepare),
    pay: initPayState(prepare),
    order: initOrderState(prepare),
    address: initAddressState(prepare),
    delivery: initDeliveryState(prepare),
    contact: initContactState(prepare),
    selfFetch: initSelfFetchState(prepare),
    goods: initGoodsState(prepare),
    useBeforePay: initUseBeforePayState(prepare),
    displayCard: trade?.displayCard || {},
    // 积分
    points: prepare.pointsConfig || {},
    // 优惠券
    coupon,
    // 储值卡的选中信息服务端决定
    valueCard: initValueCardState(prepare),
    periodBuy: initPeriodBuy(prepare),
    fissionActivity: trade.fissionActivity || {},
    groupon: initGroupon(prepare),

    // 获取店铺是否开启轮播的配置
    shopConfig: initShopConfigState(prepare),

    // 积分兑换
    pointDeduction: getPointDeductionState(prepare),

    // 下单挽留
    orderKeepApply: !!prepare.orderKeepApply,

    orderCreated: true,
    extra: trade.extra || {},
    extensions: prepare.extensions,
    newHotelExtensions: prepare.newHotelExtensions,
  };

  if (isEmptyValueCard) {
    state.valueCard.checked = [];
  }

  Object.assign(state, {
    ...initHandselState(prepare),
  });

  return state;
}
