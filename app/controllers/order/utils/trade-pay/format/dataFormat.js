import pick from '@youzan/utils/object/pick';
import getOrderStatusData from './modules/order-status';
import getPayCardCellData from './modules/pre-pay-card-cell';
import getAddressData from './modules/address';
import getPayData from './modules/pay';
import getGoodsData from './modules/goods';
import getGuarantee from './modules/guarantee';
import getPriceData from './modules/price';
import getDelivery from './modules/delivery';
import getOrderData from './modules/order';
import getUmpData from './modules/ump';
import getPeriod from './modules/period';
import getIdCard from './modules/id-card';
import getPrepare from './modules/prepare';
import getOrderExtra from './modules/order-extra';
import getRechargeInfoData from './modules/recharge-info';


/**
 * 为什么以这样的方式写 format 流程
 * 1. 待支付是简单的单向数据流, 以该方式模拟 store 可以最小成本复用 order-native 的逻辑
 * 2. 与接来下的中台化最佳实践靠拢, 逻辑收敛于 page-setup
 *
 * state, getters 仅供 page-setup 内部使用, 不对外直接暴露
 * 其他 ext 依赖需要提供精简后的数据
 */
export function getFormattedData({ prepare = {}, state = {}, getters = {} }) {
  return {
    footerShowStoreInfo: false,
    orderCreated: state.orderCreated,

    // 透传出精简后的prepare数据，供trade-buy-pay-view胶水层使用
    ...getPrepare({ prepare }),

    ...pick(state, ['shop', 'tradeTag', 'useBeforePay', 'display']),
    ...pick(getters, ['showPriorUseSummary']),
    ...getOrderStatusData({ prepare, state, getters }),
    ...getPayCardCellData({ prepare, state }),
    ...getAddressData({ prepare, state, getters }),
    ...getPayData({ prepare, state, getters }),
    ...getGoodsData({ prepare, state, getters }),
    ...getGuarantee({ prepare, state }),
    ...getPriceData({ prepare, state, getters }),
    ...getDelivery({ prepare, state, getters }),
    ...getOrderData({ prepare, state, getters }),
    ...getUmpData({ prepare, state, getters }),
    ...getPeriod({ prepare, state }),
    ...getIdCard({ prepare, state }),
    ...getOrderExtra({ prepare, state }),
    ...getRechargeInfoData({ prepare, state }),
  };
}
