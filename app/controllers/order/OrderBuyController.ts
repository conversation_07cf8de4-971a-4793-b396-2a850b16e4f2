/* eslint-disable camelcase,@typescript-eslint/ban-ts-comment */
import { PageException } from '@youzan/iron-base';
import { IGuideFromSlRequest } from 'definitions/api/guide/performance/GuideTradeSupportService/getGuideFromSl';
import { IYouzanFrameworkContext } from '@youzan/youzan-framework/definitions';
import BusinessServiceError from '@youzan/iron-base/app/services/base/BusinessServiceError';
import mapKeysToCamelCase from '@youzan/utils/string/mapKeysToCamelCase';
import args from '@youzan/utils/url/args';
// @ts-ignore
import crypto from '@youzan/crypto';
import { Context, SessionCachePlatform } from 'astroboy';
import { MultiStorePlugin, ShopCorePlugin } from '@youzan/plugin-h5-shop';
// @ts-ignore
import { getTradeApolloConfig } from '../../lib/Apollo';
import { NormalError } from 'definitions/common';
import { OrderCreationDTO } from 'definitions/order/buy/OrderCreationDTO';
import { OrderPaymentPreparationDTO } from 'definitions/order/buy/OrderPaymentPreparationDTO';
import {
  IGetParsePrepareOptionsParamsVO,
  IParsePrepareOptionsVO,
  PrepareOptions,
} from 'definitions/order/buy/PrepareOptions';
import { QueryExchangeGoodsParamsDTO } from 'definitions/order/buy/QueryExchangeGoodsParamsDTO';
import { SimpleOrderConfirmationVO } from 'definitions/order/buy/SimpleOrderConfirmationVO';
import { SimpleOrderPaymentPreparationVO } from 'definitions/order/buy/SimpleOrderPaymentPreparationVO';
import {
  IDisplayConfigVO,
  IOrderItemVO,
  IDisplayCardVO,
  SimpleOrderPreparationVO,
} from 'definitions/order/buy/SimpleOrderPreparationVO';
import { TradePayRequestDTO } from 'definitions/order/buy/TradePayRequestDTO';
import { WeappConfirmV2VO } from 'definitions/order/buy/WeappConfirmV2VO';
import { WeappPrepareV2VO } from 'definitions/order/buy/WeappPrepareV2VO';
import { PayChannelVO } from 'definitions/pay/PayChannelVO';
import escape from 'lodash/escape';
import get from 'lodash/get';
import omit from 'lodash/omit';
import flatten from 'lodash/flatten';
import { MassFlowLimitTypeMap, ServiceLevelEnum } from '../../constants/massFlowLimit';
import { SourceIdEnum } from '../../constants/SourceId.enum';
import { BizType } from '../../constants/bizType';
import { getTradePayClientData } from './utils/trade-pay/format';

// @ts-ignore
import findJsonValue from '../../lib/FindJsonValue';
import setJsonValue from '../../lib/SetJsonValue';
import OrderContext from '../../lib/OrderContext';
import { isInGrayReleaseByKdtId, isInGrayReleaseByKdtIdForGoodsApollo } from '../../lib/WhiteListUtils';
import DiscountService from '../../services/order/DiscountService';
import PayWaysService from '../../services/pay/PayWaysService';
import UserAccountService from '../../services/uic/UserAccountService';
import CustomerQueryService from '../../services/trade/CustomerQueryService';
import CompareVersion from '../../lib/CompareVersion';
import LivePlanManageService from '../../services/order/LivePlanManageService';
import BehaviorCaptchaService from '../../services/uic/BehaviorCaptchaService';
import MpVersionService from '../../services/channels/MpVersionService';
import ExtensionPointService from '../../services/cloud/ExtensionPointService';
import { IPayChannel, equal } from '@youzan/zan-pay-core';
import { IPayChannelMeta, IPayChannelMetaConfig } from 'definitions/order/buy/Cashier';
import { PayChannelMatcher } from '../../lib/ConfigMatcher';
import { IConfig, initOrderDataAsync } from '../../lib/PriorUse';
import { setQttWeappAppId } from '../../lib/Qtt';
import { WechatMiniProgramShoppingComponentService } from '../../services/wx-shop-component/WechatMiniProgramShoppingComponentService';
import { isCloudDesign } from '@youzan/plugin-h5-ecloud';
import { checkEduShop, checkPureWscSingleStore, checkRetailShop, createWscIsolateCheck, checkRetailSingleStore, checkRetailChainStore } from '@youzan/utils-shop';
import compareVersions from '@youzan/utils/versions/compare';
import { enterShopForH5, setApplicationScene } from '@youzan/plugin-h5-enter-shop';
import { SceneSource } from '@youzan/plugin-h5-enter-shop-core';
import { formatPackingFee, getDiscountPlatform } from '../../utils/index';
import { formatDisplayCard, formatPreSaleTips } from './utils/format';
import ShopBaseReadOuterService from '../../services/shop/ShopBaseReadOuterService';

import RetailTradeMiscCouponService from '../../services/api/retail/misc/CouponService';
import { initRanta } from '@youzan/plugin-h5-ranta-config';
import { getBrandShopStyleToState } from '@youzan/plugin-h5-global-theme';

import BaseController = require('../base/BaseController');
import GuideTradeSupportService = require('../../services/api/guide/performance/GuideTradeSupportService');
import BillService = require('../../services/trade/BillService');
import TradeService = require('../../services/trade/TradeService');
import TradeServiceProvider = require('../../services/trade/TradeServiceProvider');
import RoomVoucherPackageQueryService = require('../../services/order/RoomVoucherPackageQueryService');
import WscPayService = require('../../services/pay/WscPayService');
import AssetsService = require('../../services/trade/AssetsService');
import DeliveryService = require('../../services/trade/DeliveryService');
import OutAssetService = require('../../services/trade/OutAssetService');
// import CustomerQueryService = require('../../services/trade/CustomerQueryService');
import SelfFetchService = require('../../services/delivery/SelfFetchService');
import UserAddressService = require('../../services/uic/UserAddressService');
import UserContactService = require('../../services/uic/UserContactService');
import OrderShareService = require('../../services/order/OrderShareService');
import MarketRemoteService = require('../../services/yop/MarketRemoteService');
import SecuredTXQueryService = require('../../services/trade/SecuredTXQueryService');
import IdentityVerifyService = require('../../services/trade/IdentityVerifyService');
import ShopConfigReadService = require('../../services/shop/ShopConfigReadService');
import ShopAbilityInfoService = require('../../services/shop/ShopAbilityInfoService');
import AdvisoryService = require('../../services/order/AdvisoryService');
import OrderRebateService = require('../../services/order/OrderRebateService');
import OrderDrugService = require('../../services/order/OrderDrugService');
import BehalfOrderWapReadService = require('../../services/api/retail/sales/BehalfOrderWapReadService');
import HotelBookingService = require('../../services/order/HotelBookingService');
import TimeBucketService = require('../../services/delivery/TimeBucketService');
import ShopConfigurationService = require('../../services/shop/ShopConfigurationService');
import ShopConfigWriteService = require('../../services/shop/ShopConfigWriteService');
import HongShuMiniAppService = require('../../services/xhs/HongShuMiniAppService');

const formatMoneyBase = require('@youzan/utils/money/format');
const setupCdn = require('@youzan/iron-base/app/lib/set-up-cdn');

const formatMoney = (cent: number | string) => formatMoneyBase(cent, 1, !1);

const BEHAVIOR_ERROR_MSG = '行为组件token为非法值';

const checkHotelShop = (
  shopMetaInfo: {
    shopType?: number;
    shop_type?: number;
    shopTopic?: number;
    shop_topic?: number;
  } = {}
) => {
  try {
    return (
      (shopMetaInfo?.shopType === 0 || shopMetaInfo?.shop_type === 0) &&
      (shopMetaInfo?.shopTopic === 2 || shopMetaInfo?.shop_topic === 2)
    );
  } catch (e) {
    return false;
  }
};

interface ICouponOverConfig {
  /** 店铺类型 */
  shopType: number[];
  /** 白名单列表 */
  whitelist: number[];
  /** 黑名单列表 */
  blacklist: number[];
  /** 是否可叠加开关C端可用 */
  isOverlying: boolean;
  /** kdt切流百分比 */
  kdtPercent: number;
}

interface IDouyinCouponExchangeError {
  code: number;
  msg: string;
}

class OrderBuyController extends BaseController {
  async init() {
    // 初始化订单上下文
    OrderContext.init(this.ctx);

    await super.init({
      validKdtId: true,
      initMpAccount: false,
      initGlobalTheme: true,
      initShopSettings: true,
      initShopMetaInfo: true,
      initCopyrightFooter: !this.ctx.acceptJSON,
    });
  }

  async getPayChannels(isPriorUseAvailable: boolean, minAmount: number, maxAmount: number) {
    let channels: any[] = [];
    if (isPriorUseAvailable) {
      channels.push({
        payChannel: 'PRIOR_USE',
        minAmount,
        maxAmount,
      });
    }

    channels = this.getDecoratedPayChannels(channels);

    this.ctx.logger.info(`下单页、待支付页支付方式查询, kdtId=${this.ctx.kdtId}, buyerId=${this.ctx.buyerId}`, null, {
      channels,
      headers: this.ctx.headers,
      session: this.ctx.getLocalSession(),
      query: this.ctx.getQueryData(),
    });
    return channels;
  }

  /**
   * 根据 Apollo 配置加工支付方式信息
   * @param channels
   * @returns
   */
  private getDecoratedPayChannels(channels: any[]): IPayChannel[] {
    const PayChannelMeta: IPayChannelMetaConfig = getTradeApolloConfig('assets-cashier.routes', 'PayChannelMeta');
    const { base, customized } = PayChannelMeta;
    const matched =
      customized &&
      PayChannelMatcher.match<IPayChannelMeta[]>((this.ctx as unknown) as IYouzanFrameworkContext, customized);
    return channels.map((channel) => {
      const baseMeta: Record<string, any> = base?.find((it) => equal(it, channel)) || {};
      const customizedMeta = matched?.find((it: any) => equal(it, channel));
      if (baseMeta.payChannel === 'PRIOR_USE') {
        // 先用后付 原范围为1-600，具体范围根据后端传值确定
        const { min, max } = baseMeta?.price || {};
        if (channel.minAmount) {
          baseMeta.price.min = [channel?.minAmount, min?.[1]?.replace('0.01', formatMoney(channel.minAmount))];
        }
        if (channel.maxAmount) {
          baseMeta.price.max = [channel?.maxAmount, max?.[1]?.replace('600', formatMoney(channel.maxAmount))];
        }
      }
      return {
        ...channel,
        ...omit(baseMeta, 'ext', 'payChannel'),
        ...omit(customizedMeta, 'ext', 'payChannel'),
      };
    });
  }

  async orderBuyAcl() {
    await this.needPlatformAcl();
  }

  async getReceiverIdentityList(ctx: Context) {
    // const postData = ctx.getPostData();
    const rs = await new CustomerQueryService(ctx).getReceiverIdentityList({
      // ...postData,
      userId: this.buyerId,
      kdtId: ctx.kdtId,
    });
    return this.ctx.json(0, 'ok', rs);
  }

  // 获取地址列表中的默认项
  private getAddressDefaultItem(list: IPureObject[], defaultId?: string) {
    if (defaultId) {
      const match = list.filter((item) => String(item.id) === defaultId);
      if (match.length) {
        return match[0];
      }
    }

    const defaultItem = list.filter((item) => item.isDefault);
    if (defaultItem[0]) return defaultItem[0];
    return this.ctx.isTTApp ? {} : list[0] || {};
  }

  // 获取联系人列表中的默认项
  private getContactDefaultItem(list: IPureObject[], defaultId?: string) {
    if (defaultId) {
      const match = list.filter((item) => String(item.id) === defaultId);
      if (match.length) {
        return match[0];
      }
    }

    const defaultItem = list.filter((item) => item.isDefault);
    return defaultItem[0] || list[0] || {};
  }

  /**
   * 代客下单获取地址
   * @description 查询导购代客下单 导购设置的联系地址
   */
  private async getGuestAddress() {
    const { ctx } = this;
    const { bizNo } = ctx.query;
    const params = {
      behalfBizNo: bizNo,
      adminId: this.buyerId,
      kdtId: ctx.kdtId,
      retailSource: 'wsc-h5-trade',
    };
    try {
      const { address } = await new BehalfOrderWapReadService(ctx).getBehalfOrderCustomerAddress(params);
      if (address) {
        const {
          addressDetail,
          areaCode,
          city,
          county,
          isDefault = 0,
          recipients: name,
          province,
          source = 1,
          tel,
          type,
          recipients: userName,
        } = address;

        if (!addressDetail) throw new Error('缺失详细地址');

        return {
          addressDetail,
          areaCode,
          city,
          county,
          isDefault,
          name,
          province,
          source,
          tel,
          type,
          userName,
        };
      }
      return null;
    } catch (error) {
      this.ctx.logger.error(`代客下单 - 客户确认订单地址获取失败, params: ${JSON.stringify(params)}`, error);
      return null;
    }
  }

  async doHotelOrder(ctx: Context, extensions: Record<string, any>, orderItems: any[]) {
    const {
      IS_HOTEL_PACKAGE_ORDER: isHotelPackageOrder = '0',
      HOTEL_PRESALE_ORDER_NO,
      ITEM_ID,
      SELECT_DAYS = [],
      GOODS_ID,
      HOTEL_GOODS_ID,
    } = extensions;

    if (isHotelPackageOrder === '1' && orderItems[0]) {
      if (HOTEL_PRESALE_ORDER_NO) {
        // 预约单
        const params = {
          kdtId: +ctx.kdtId,
          itemId: +ITEM_ID,
        };
        const bookingParams = {
          itemId: +HOTEL_GOODS_ID,
          checkInTime: 0,
          checkOutTime: 0,
        };
        let selectDays;
        try {
          selectDays = JSON.parse(SELECT_DAYS);
        } catch (error) {
          selectDays = [];
        }
        const [start, end] = selectDays;
        if (start && end) {
          bookingParams.checkInTime = new Date(start).getTime();
          bookingParams.checkOutTime = new Date(end).getTime();
        }
        const [hotelPackageExtensions, hotelPriceDetail] = await Promise.all([
          new RoomVoucherPackageQueryService(ctx).getCclientDetail(params),
          new HotelBookingService(ctx).getRoomVoucherBookingBO({ ...params, ...bookingParams }),
        ]);
        const { roomTypeBOS, ...rest } = hotelPackageExtensions;
        ctx.setGlobal({
          hotelPackageExtensions: {
            ...rest,
            roomTypeBOS: roomTypeBOS.filter((item: any) => item.id === +GOODS_ID),
            selectDays: SELECT_DAYS,
          },
          hotelPriceDetail,
        });
      } else {
        // 预售单
        const params = {
          kdtId: +ctx.kdtId,
          itemId: orderItems[0].goodsId as number,
        };
        const hotelPackageExtensions = await new RoomVoucherPackageQueryService(ctx).getCclientDetail(params);

        hotelPackageExtensions &&
          ctx.setGlobal({
            hotelPackageExtensions: {
              ...hotelPackageExtensions,
              selectDays: [],
            },
          });
      }
    }
    return isHotelPackageOrder;
  }

  // H5 - 下单页
  async getIndexHtml(ctx: Context) {
    await this.teeRantaAcl();

    if (ctx.status === 302) {
      return;
    }

    this.checkFans('下单页acl登录后');

    this.setPageCloudBizIds('buy', 'bookKey', ctx.query.book_key);
    const tradeService = new TradeService(ctx);

    const mpData: IPureObject = ctx.getState('mpData' as never) || {};
    // @ts-ignore
    const directSeller = ctx.getLocalSession('direct_seller') || {};
    const { kdtId, userAgent, isYouzanmars } = ctx;
    // @ts-ignore
    const {
      is_fans: isFans,
      youzan_user_id: youzanUserId,
      user_privacy: userPrivacy = {},
    } = this.ctx.getLocalSession();

    // 商家级别
    const serviceLevel: ServiceLevelEnum = (ctx.serviceLevel as any) || ServiceLevelEnum.NORMAL;

    const addressList = await new UserAddressService(ctx).getAddressListFast({
      userId: this.buyerId,
    });

    await this.createUserContactInTTApp(ctx);

    const defaultAddress = this.getAddressDefaultItem(addressList, ctx.query.address_id || ctx.query.addressId);

    // 电商中台和用户组使用的姓名字段不一致，需要格式化
    if (defaultAddress.userName) {
      defaultAddress.recipients = defaultAddress.userName;
    }

    let guestAddress;
    // 代客下单地址注入
    if (ctx.query.type === 'behalf-order') {
      guestAddress = await this.getGuestAddress();
    }

    // 获取可叠加优惠券白名单 判断当前店铺是否在白名单中
    const isInCouponOverWhitelist = await this.judgeInCouponOverWhitelist(ctx);

    // 因为优惠券叠加的下单标记只支持中台化，非中台化并不支持，所以需要将判断是否中台化的条件前置 因为参数依赖到drugInfo, 所以还需要将请求是否为医药店铺的条件前置
    const drugShopInfo = await new ShopAbilityInfoService(ctx) // 调用店铺能力判断是否是医药店铺
      .queryShopAbilityInfo([kdtId, 'prescription_ability'])
      .catch(() => {
        return {};
      });

    const isUseRantaBuyPage = await this.getIsUseRantaBuyPage({ isDrugShop: drugShopInfo.valid });


    if (ctx.isXhsApp) {
      const hongShuMiniAppService = new HongShuMiniAppService(ctx);
      const accountParams = {
        kdtId,
        businessType: 1,
        accountType: 16,
      };

      let xhsAccountInfo = await hongShuMiniAppService.getAccountBasicInfo(accountParams);
      if (!xhsAccountInfo) {
        const shopMetaInfo = await this.getShopMetaInfo(ctx);
        const { rootKdtId } = shopMetaInfo;

        if (rootKdtId) {
          xhsAccountInfo = await hongShuMiniAppService.getAccountBasicInfo({
            ...accountParams,
            kdtId: rootKdtId,
          });
        }
      }

      const { tradeAbility } = xhsAccountInfo || {};
      this.ctx.isXhsLocalLife = tradeAbility === 2;
    }

    const isUsePointDeduction = !isYouzanmars;
    const prepareOrderParams = {
      ...this.buyer,
      kdtId,
      userAgent,
      address: guestAddress || (defaultAddress.id ? defaultAddress : null),
      shopName: mpData.shopName,
      platform: getDiscountPlatform(this.ctx, this.uaPlatform),
      clientIp: ctx.firstXff,
      bookKey: ctx.query.book_key || ctx.query.bookKey,
      // @ts-ignore
      salesman: directSeller.seller || '',
      ump: {
        // 新版是否根据后台配置默认使用积分抵现，h5默认都是true
        defaultPointDeductEffect: true,
        // 默认使用积分抵现，增加此参数便于平滑升级
        usePointDeduction: isUsePointDeduction,
        defaultDisableStoredDiscount: false, // 下单页默认不禁用折扣
        storedDiscountRechargeGuide: true,
      },
      kdtSessionId: ctx.sessionId,
      client: ctx.client,
      fans: isFans === 1, // 0：取消关注 1：已关注 2：其他
      // 是否是小程序，后端以此来判断是否要显示分期支付、银行卡支付下线的通知
      isWeapp: ctx.isWeapp,
      // 是否使用最优优惠计算，H5始终为true
      extensions: {
        IS_OPTIMAL_SOLUTION: 'true',
        NEW_MEMBER_FLOW: 'true', // 是否使用办会员新流程
        IS_OVERLYING_COUPON: isInCouponOverWhitelist && isUseRantaBuyPage ? 'true' : 'false', // 是否支持券叠加， H5 始终为 true
        ...this.getPrioruseWeixinSence(),
        ...this.getXhsLocalLifeExt(), // 小红书担保交易-本地生活参数
      },
    };
    try {
      // @ts-ignore 店铺元数据在初始化时已经查询并缓存
      if (checkRetailShop(this.ctx.getState('shopMetaInfo'))) {
        // 指定当前为为支持特殊时段费的版本，是否真实计算取决于商家配置
        Object.assign(prepareOrderParams.extensions, this.getRetailSpecialPeriodCostExts(true));
      }
    } catch (error) {
      this.ctx.logger.warn('于检测是否是零售店铺时，店铺元数据获取失败', error as Error);
    }
    const riskWarnParams = {
      group: 'wsc-h5-trade',
      tag: 'RISK_SHOP',
      sourceType: 'ORDER_CREATE',
      contentType: 'KDT_ID',
      identityType: 'SELLER',
      value: kdtId,
    };

    const abConfig = (await this.ctx.ABTestClient?.getTest('trade_buy', String(this.buyerId))) || {};

    const abConfigExpressWay =
      (await this.ctx.ABTestClient?.getTest('order_page_self_fetch_default', String(this.buyerId))) || {};
    this.ctx.logger.info(`下单页、自提H5abtest, kdtId=${abConfigExpressWay}, buyerId=${this.ctx.buyerId}`, null, {
      abConfigExpressWay,
    });
    const isNewProcess = await new TradeService(ctx).getDeliveryApolloConfig(ctx.kdtId).catch(() => {});

    const buyerMsgTips = await new ShopConfigReadService(ctx).queryShopConfigsBuyerMsg(ctx.kdtId).catch(() => 0);
    ctx.setGlobal({
      mpShopLogo: mpData.logo,
      // 覆盖掉几个不需要的参数
      mp_data: undefined,
      platformInfo: undefined,
      platform_info: undefined,
      shop_settings: undefined,
      isNewProcess,
      // 收银台组件埋点metadata
      '@cashier/metadata': {
        shop_name: mpData && mpData.shopName,
        shop_type: mpData && mpData.shopType,
      },
      // 收银台 AB 切流使用
      youzan_user_id: youzanUserId,
      abTestInfo: abConfig?.isValid ? abConfig : {},
      platform: this.ctx.platform,
      buyerMsgTips,
      isXhsLocalLife: this.ctx.isXhsLocalLife,
    });

    let response;
    try {
      // 判断是否应用优惠券积分抵现流程新逻辑
      const isNewCouponProcess = await isInGrayReleaseByKdtId(
        ctx,
        { namespace: 'wsc-h5-trade.application', key: 'newCouponProcess' },
        ctx.kdtId
      );
      prepareOrderParams.newCouponProcess = isNewCouponProcess;
      if (ctx.isAlipayApp) {
        prepareOrderParams.orderMark = 'alipay_mini_program';
      }
      if (ctx.isQQApp) {
        prepareOrderParams.orderMark = 'qq_mini_program';
      }

      if (ctx.isXhsApp) {
        prepareOrderParams.orderMark = 'xiaohongshu_mini_program';
      }

      if (ctx.isKsApp) {
        prepareOrderParams.orderMark = 'kuaishou_xcx';
      }

      const bizEnv = get(ctx, 'query.bizEnv');

      // 云闪付APP
      if (bizEnv === 'UnionpayApp') {
        prepareOrderParams.orderMark = 'unionpay_app';
      }

      // 建行生活APP
      if (bizEnv === 'CCBLifeApp') {
        prepareOrderParams.orderMark = 'ccblife_app';
      }

      // 赞拼拼小程序订单
      if (bizEnv === 'fx-zpp') {
        prepareOrderParams.extensions.ATTR_IS_FX_ZPP_ORDER = '1';
        prepareOrderParams.extensions.ATTR_FX_ZPP_NOTE_ID = ctx.query.noteId || '0';
      }

      // 点单宝h5场景(24hshelf)，目前用于抖音小程序（内嵌webview）
      // 抖音且零售点单宝
      if (ctx.query.retailOrderScene === '24hshelf') {
        prepareOrderParams.orderMark = 'fulfill_tool';
        prepareOrderParams.platform = 'dy_mini_program';
      }

      // 私域直播订单
      if (ctx.query.pdlive) {
        prepareOrderParams.platform = 'pl_live';
      }

      if (ctx.query.prepayCardType === 'delivery_card' && ctx.query.prepayCardNo) {
        prepareOrderParams.usePayAsset = {
          kdtId,
          deliveryCardNos: [ctx.query.prepayCardNo],
        };
      }

      response = await Promise.all([
        tradeService.prepareFast(prepareOrderParams),
        new AdvisoryService(ctx).queryIsRiskWarnShopFast(riskWarnParams).catch(() => {}),
        new ShopAbilityInfoService(ctx) // 调用店铺能力判断是否接入行为组件
          .queryShopAbilityInfo([kdtId, 'risk_prevention_of_marketing_ability'])
          .catch(() => {
            return {};
          }),
        isInGrayReleaseByKdtId(
          ctx,
          { namespace: 'wsc-h5-trade.gray-release', key: 'useOrderUserAuthorize' }, // 授权组件下单页接入
          ctx.kdtId
        ),
        isInGrayReleaseByKdtIdForGoodsApollo(
          ctx,
          { namespace: 'wsc-h5-goods.goods-release', key: 'goods-img-cover' },
          ctx.kdtId
        ),
        this.getConfirmAddressValidate(ctx),
        this.getCurrentShopInfo(ctx),
      ]);
    } catch (error) {
      const content = error.errorContent;
      const errorMsg = error.message || '';

      // 框架层限流错误码
      if (errorMsg.indexOf('Over traffic limit') !== -1) {
        this.jumpWaitingPage();
        return;
      }

      if (content) {
        const { code } = content;
        // 下单页限流，跳转到限流等待页面
        if (code === 429 || code === ********* || code === *********) {
          this.jumpWaitingPage();
          return;
        }

        // 未关注公众号不可购买
        if (code === *********) {
          await this.initMpAccount();
          const mpAccount: any = ctx.getState('mpAccount' as never);
          return ctx.render('order/follow-shop-qrcode.html', {
            mpWeixin: mpAccount.weixinAccount || '',
          });
        }

        // 积分兑换超出最大兑换数量
        if (code === *********) {
          return ctx.redirect(args.add('/wscump/pointstore/tradedetails', {
            bookKey: ctx.query.book_key || ctx.query.bookKey,
            code: *********,
            errMsg: content.msg,
          }));
        }

        throw new PageException(content.code, content.msg);
      } else {
        throw error;
      }
    }
    this.setExtensionParams(ctx, 'buy', { bookKey: ctx.query.book_key });

    let [prepare = ''] = response;
    const [_, riskWarnShop = '', enableUseOrderBehaviorInfo, enableUseUserAuthorize, isGoodsImgCover, confirmAddressValidate, currentShopInfo] = response;
    // 补齐prepare.tradeConfirmation?.shop.address
    if (currentShopInfo && currentShopInfo.address) {
      const shop = findJsonValue(prepare, 'tradeConfirmation.shop', true);
      if (shop) {
        prepare = setJsonValue(prepare, 'tradeConfirmation.shop', {
          ...shop,
          address: currentShopInfo.address,
        });
      }
    }
    // jira: https://jira.qima-inc.com/browse/ONLINE-773734；
    // ctx.isThirdApp()替换成ctx.isAppSdk；
    // 暂时只替换这个场景，后续有反馈再替换其他的；
    const isThirdApp = ctx.isAppSdk;
    // 页面停留超时
    const redirectConfig = findJsonValue(prepare, 'redirectConfig') || '';
    const redirectTimeout = findJsonValue(redirectConfig, 'timeout');
    const redirectOrderCreated = findJsonValue(redirectConfig, 'orderCreated');
    // 页面停留超时
    if (redirectTimeout) {
      // 充值优惠场景下，如果是从支付宝、银行卡等三方页面跳转回来时，当页面超时，直接到结果页，展示相关进度
      const { checkRecharge: rechargeNo, is_free_order } = this.ctx.query || {};
      if (rechargeNo && is_free_order) {
        // 跳转到充值优惠结果页
        return ctx.redirect(
          `https://h5.youzan.com/wscassets/card/free-order-result?kdt_id=${ctx.kdtId}&recharge_no=${rechargeNo}`
        );
      }

      throw new PageException(101240006, '页面停留太久，请返回重新购买');
    }

    // 订单已创建
    if (redirectOrderCreated) {
      const orderNos = findJsonValue(prepare, 'orderNos', true);
      return ctx.redirect(`/pay/wsctrade_pay?order_no=${orderNos}&kdt_id=${kdtId}`);
    }
    const { valid: isDrugShop } = drugShopInfo || {};
    this.setEnv({
      isThirdApp,
      isDrugShop, // 是否是医药店铺
      isFxZpp: get(ctx, 'query.bizEnv') === 'fx-zpp', // 赞拼拼订单
    });

    const isPartialFastAdOrder = findJsonValue(prepare, 'orderCreation.config.isPartialFastAdOrder') || false;
    const tradeConfirmation = findJsonValue(prepare, 'tradeConfirmation') || '';

    const tradeTag = findJsonValue(tradeConfirmation, 'tradeTag');
    const orderItems: IOrderItemVO[] = findJsonValue(tradeConfirmation, 'orderItems', true) || [];
    const shopSetting: IPureObject = this.ctx.getState('shopSettings' as never) || {};
    this.setDesignAndFooterFast([tradeTag, orderItems], prepare, true);

    const parsePrepareOptions: IParsePrepareOptionsVO = {
      isUsePointDeduction,
      copyrightPicUrl: shopSetting.isLogoCustomized ? shopSetting.customizedLogo : '',
      serverTime: Date.now(),
      ...(await this.getParsePrepareOptions({
        isThirdApp,
        orderItems,
        isPartialFastAdOrder,
      })),
    };

    // 是否接入行为组件
    const { valid: enableUseOrderBehaviorParam = false } = enableUseOrderBehaviorInfo;

    // 是否支持前置收银台功能
    const usePreCashierPay = await this.checkUsePreCashierPay(ctx, prepare, {
      bookKey: ctx.query.book_key,
    });
    prepare = setJsonValue(prepare, 'usePreCashierPay', usePreCashierPay);
    // 确认订单页商品图片填充
    prepare = setJsonValue(prepare, 'isGoodsImgCover', isGoodsImgCover);

    // 地图 key 接入 apollo
    const addressMapKeyConfig = this.getAddressMapKeyApollo();

    // 权益卡/付费等级字段处理
    const displayCard = findJsonValue(tradeConfirmation, 'displayCard', true);
    if (displayCard) {
      prepare = setJsonValue(prepare, 'tradeConfirmation.displayCard', formatDisplayCard(displayCard));
    }

    // 预售商品自提/配送选择时间提示
    const preSaleTips = formatPreSaleTips(orderItems, findJsonValue(prepare, 'deliveryTimeBucket', true));
    if (preSaleTips) {
      prepare = setJsonValue(prepare, 'displayConfig.selfFetchTips', preSaleTips.selfFetchTips);
      prepare = setJsonValue(prepare, 'displayConfig.deliveryTips', preSaleTips.deliveryTips);
    }

    try {
      // 活动预定添加信息
      const reservationMsg = JSON.parse(
        findJsonValue(prepare, 'orderCreation.extensions.EXHIBITIONRESERVE_ENROLLMENT')
      );
      ctx.logger.info(`reservationMsg: ${JSON.stringify(reservationMsg)}`);
      if (reservationMsg) {
        const { applyShopName = '', selectDate = '', selectTime = '' } = reservationMsg;
        const { applyInfoList } = reservationMsg.data;
        const date = new Date(selectDate);
        const dayOfWeek = date.toLocaleDateString('zh-CN', { weekday: 'long' });
        let week = '';
        if (dayOfWeek.includes('星期')) {
          week = ` 周${dayOfWeek.replace('星期', '')}`;
        }
        const userApplyInfoList = [
          { label: '门店', value: applyShopName },
          { label: '日期', value: selectDate + week },
          { label: '时间', value: selectTime },
          { label: '姓名', value: this.buyer.fansNickname || '' },
          ...applyInfoList,
        ];
        reservationMsg.data.userApplyInfoList = userApplyInfoList;
        prepare = setJsonValue(
          prepare,
          'orderCreation.extensions.EXHIBITIONRESERVE_ENROLLMENT',
          JSON.stringify(reservationMsg)
        );
        ctx.logger.info(
          `reservationMsg result: ${JSON.stringify({
            userApplyInfoList,
            // @ts-ignore
            data: reservationMsg?.data,
          })}`
        );
      }
    } catch (error) {
      // @ts-ignore
      ctx.logger.info(`reservationMsg error: ${error?.message}`);
    }

    const useYouzanMapApi = await isInGrayReleaseByKdtId(
      ctx,
      { namespace: 'wsc-h5-trade.gray-release', key: 'useYouzanMapApi' },
      kdtId
    );

    ctx.setGlobal({
      prepare,
      riskWarnShop,
      parsePrepareOptions,
      enableUseOrderBehaviorParam,
      enableUseUserAuthorize,
      isInCouponOverWhitelist,
      addressMapKeyConfig,
      useYouzanMapApi,
      confirmAddressValidate,
    });

    // 如果是新的酒店套餐需要走套餐详情接口
    let getExtensions = findJsonValue(prepare, 'orderCreation.extensions');
    try {
      getExtensions = JSON.parse(getExtensions);
    } catch (error) {
      getExtensions = {};
    }

    const isHotelPackageOrder = await this.doHotelOrder(ctx, getExtensions, orderItems);
    // 新酒店商品获取额外信息
    const hasHotelGoods = findJsonValue(tradeTag, 'hasHotelGoods') || false;

    if (isHotelPackageOrder !== '1' && hasHotelGoods && orderItems[0]) {
      const params = {
        kdtId,
        itemId: orderItems[0].goodsId,
      };
      const newHotelExtensions = await tradeService.getHotelBooking(params);

      if (newHotelExtensions) {
        ctx.setGlobal({
          newHotelExtensions,
        });
      }
    }
    // 协议强制不显示
    userPrivacy.agreement_signed = true;
    // 设置 global 参数
    ctx.setGlobal({
      address: {
        id: defaultAddress.id || '',
        list: addressList,
        abConfigExpressWay: abConfigExpressWay.isValid ? abConfigExpressWay : {},
      },
      massFlowLimitType: MassFlowLimitTypeMap[serviceLevel],
      userPrivacy,
    });

    const displayConfig = findJsonValue(prepare, 'displayConfig');
    const canSelfFetch = findJsonValue(displayConfig, 'canSelfFetch');
    const contactRequired = orderItems.some(({ virtualType }) => virtualType === 3);

    // 盲盒支持自提
    const blindBoxDistTypes = findJsonValue(prepare, 'orderCreation.config.blindBoxDistTypes', true) || [];
    // // 快递-1，自提-2
    const blindBoxCanSelfFetch = blindBoxDistTypes?.includes(2);

    const reservationInfo = findJsonValue(tradeConfirmation, 'extra.EXHIBITIONRESERVE_ENROLLMENT');

    if (canSelfFetch || contactRequired || blindBoxCanSelfFetch) {
      await new UserContactService(ctx).getContactListFast({ userId: this.buyerId }).then((contactList) => {
        let contact = contactList;
        if (reservationInfo) {
          contact = this.resolveReservationContact(reservationInfo, contactList);
        }

        ctx.setGlobal('contact', contact);
      });
    }

    const name = findJsonValue(prepare, 'pointsConfig.pointName') || '积分';
    ctx.setGlobal('pointsName', name);

    // 日志埋点模型
    this.setSpm(isPartialFastAdOrder ? 'multiTrade' : 'trade', kdtId);

    // 读取有赞云沙箱白名单
    try {
      const sandboxShopList = getTradeApolloConfig('wsc-h5-trade.youzanyun', 'sandboxShopList');

      const shopList = sandboxShopList.split(',');
      const useCloudGuard = shopList.includes(String(ctx.kdtId));

      ctx.setState('useCloudGuard', useCloudGuard);
      ctx.setGlobal('useCloudGuard', useCloudGuard);
    } catch (err) {
      // eslint-disable-next-line no-console
      console.log(err);
    }

    const overseaRegulationWhiteList = getTradeApolloConfig('wsc-h5-trade.application', 'overseaRegulationWhiteList');

    const whitelist = overseaRegulationWhiteList?.split(',');

    ctx.setGlobal({
      overseaRegulationWhiteList: whitelist,
    });

    // 先用后付场景数据初始化
    const is_prior = findJsonValue(prepare, 'orderCreation.extensions.IS_SELECTED_PRIOR_USE_PAY_WAY') || '-1';
    const selectedPayChannel = String(is_prior) === '1' ? 'PRIOR_USE' : 'OTHERS';
    await this.initPriorUseOrderBuyData(ctx, {
      env: 'buy',
      prepare,
      selectedPayChannel,
    });

    const asyncOrderUnLimitCode = getTradeApolloConfig('wsc-h5-trade.application', 'asyncOrderUnLimitCode');
    const monitorLoggerFilterCode = getTradeApolloConfig('wsc-h5-trade.application', 'monitorLoggerFilterCode');
    ctx.setGlobal({
      asyncOrderUnLimitCode,
      monitorLoggerFilterCode,
    });
    if (isPartialFastAdOrder) {
      await ctx.render('order/fast-buy.html', {
        title: '确认订单',
      });
    } else {
      await this.renderBuyTemplate(isUseRantaBuyPage);
    }
  }

  resolveReservationContact(reservationInfo: any, contactList: string) {
    try {
      const list = JSON.parse(contactList);
      const firstContact = list[0];

      if (!/^1[3456789]\d{9}$/.test(firstContact.telephone)) {
        const { applyInfoList } = JSON.parse(reservationInfo);
        // 手机号 attributeId === 3
        const phoneValue = applyInfoList.find((info: any) => +info.attributeId === 3);
        const { value } = phoneValue;

        firstContact.telephone = value;
        list.splice(0, 1, firstContact);

        return JSON.stringify(list);
      }
    } catch (error) {}

    return contactList;
  }

  subPageRedirect(ctx: Context) {
    const { kdtId } = ctx;
    const { query } = ctx;

    const newQuery = {
      ...query,
      book_key: query.book_key,
      kdt_id: kdtId, // ctx会自动优先取query中的kdt_id
    };

    const filterList = ['dbid', 'contactStr'];

    const queryArr = Object.keys(newQuery)
      .filter((key) => {
        // 过滤掉一些参数
        return filterList.indexOf(key) === -1;
      })
      .map((key) => `${key}=${newQuery[key]}`);

    const queryStr = queryArr.join('&');

    return ctx.redirect(`/pay/wsctrade_buy?${queryStr}`);
  }

  async isUseRantaBuyPage({ isDrugShop = false } = {}) {
    const { ctx } = this;
    const { force_shunt = '' } = ctx.query;

    switch (force_shunt) {
      case 'v1':
        return false;
      case 'v2':
        return true;
      default:
        break;
    }
    const isBanDrugShop: string = getTradeApolloConfig(
      'wsc-h5-trade.gray-release',
      'ranta_web_buy_shunt_ban_drug_shop'
    );
    if (isDrugShop && isBanDrugShop === 'true') {
      return false;
    }

    try {
      const isUseRantaPage = await isInGrayReleaseByKdtId(
        ctx,
        { namespace: 'wsc-h5-trade.gray-release', key: 'ranta_web_buy_shunt' },
        ctx.kdtId
      );
      let isCloudCustomized = false;

      if (isUseRantaPage) {
        isCloudCustomized = await isCloudDesign(ctx as any, { kdtId: ctx.kdtId, pageName: 'buy' });
      }
      ctx.setState('isCloudCustomized', isCloudCustomized);

      const useWebRantaBuyWithCloudDesign = await isInGrayReleaseByKdtId(
        ctx,
        { namespace: 'wsc-h5-trade.gray-release', key: 'useWebRantaBuyWithCloudDesign' },
        ctx.kdtId
      );

      return (
        (isCloudCustomized && useWebRantaBuyWithCloudDesign && isUseRantaPage) || (!isCloudCustomized && isUseRantaPage)
      );
    } catch (err) {
      // skip
    }

    return false;
  }

  // 渲染标准下单页
  async renderBuyTemplate(isUseRantPage: boolean) {
    const { ctx } = this;

    if (isUseRantPage) {
      await initRanta(ctx as any, {
        framework: 'tee',
        appName: 'wsc-tee-h5',
        bizName: '@wsc-tee-h5-trade/order',
        ecloud: {
          setExtensionParams: [
            {
              kdtId: +ctx.kdtId,
              pageName: 'buy',
              pageNameV2: 'trade-buy',
              conditionContext: this.getConditionContext(ctx, { bookKey: ctx.query.book_key }),
            },
          ],
        },
      });
      let url = ctx.href;

      // 赞拼拼订单
      if (get(ctx, 'query.bizEnv') === 'fx-zpp' && /^http?:\/\//i.test(url)) {
        url = url.replace('http', 'https');
      }

      await new ShopCorePlugin(ctx as any).initRantaShopInfo();
      await new MultiStorePlugin(ctx as any).initRantaMultiStore(url);
      await getBrandShopStyleToState(ctx as any, { useAppStyleIcon: true });
      await ctx.render(
        'order/tee-buy.html',
        {
          title: '确认订单',
        },
        {
          appName: 'wsc-tee-h5', // 新增
          skipLoadOpsWebConfig: true, // 中台化在插件中会加载配置并挂载全局
        }
      );
    } else {
      ctx.logger.info(`orderBuyInNativeVersion: ${ctx.kdtId}`);
      await ctx.render('order/buy.html', {
        title: '确认订单',
      });
    }
  }

  // 获取下单页是否中台化定制
  async getIsUseRantaBuyPage({ isDrugShop = false } = {}) {
    // @ts-ignore 店铺元数据在初始化时已经查询并缓存
    const shopMetaInfo = this.ctx.getState('shopMetaInfo');
    if (checkHotelShop(shopMetaInfo)) return false; // 酒店商品强制走原生h5下单页
    const [isUseRantPage] = await Promise.all([
      this.isUseRantaBuyPage({ isDrugShop }),
      this.initZanPay(), // cashier dragonfly hack
    ]);
    return isUseRantPage;
  }

  // 获取支付页是否是中台化定制
  async getIsUseRantaPayPage() {
    const [isUseRantPage] = await Promise.all([
      this.isUseRantaPayPage(),
      this.initZanPay(), // cashier dragonfly hack
    ]);
    return isUseRantPage;
  }

  // H5 - 支付页
  async getPayHtml(ctx: Context) {
    await this.teeRantaAcl();

    if (ctx.status === 302) {
      return;
    }

    const { kdtId, userAgent, isYouzanmars } = ctx;
    const query = ctx.getQueryData<IPureObject>();
    const { orderNo = '', forbidWxpay = 0 } = mapKeysToCamelCase(query);
    const isIVR = query.from === 'ivr'; // IVR订单
    const orderNos = Array.isArray(orderNo) ? orderNo : [orderNo];

    const preparePaymentParams: OrderPaymentPreparationDTO = {
      kdtId,
      orderNos,
      userAgent,
      buyerId: this.buyerId,
      forbidWxpay,
      orderMark: this.isGuang() ? 'weapp_guang' : '',
      source: isYouzanmars ? 'youzanmars' : '',
    };

    // 小红书的订单标识
    if (ctx.isXhsApp) {
      preparePaymentParams.orderMark = 'xiaohongshu_mini_program';
    }

    // 商家级别
    const serviceLevel: ServiceLevelEnum = (ctx.serviceLevel as any) || ServiceLevelEnum.NORMAL;

    // @ts-ignore
    const { youzan_user_id: youzanUserId } = this.ctx.getLocalSession();

    if (isIVR) {
      preparePaymentParams.receiverTel = this.buyer.buyerPhone;
      preparePaymentParams.buyerId = 0;
    }

    this.setPageCloudBizIds('orderShowPay', 'orderNo', orderNos.join('|'));

    let tradeOrderPrefix = '';
    try {
      // @ts-ignore 店铺元数据在初始化时已经查询并缓存
      const shopMetaInfo = this.ctx.getState('shopMetaInfo');
      if (checkEduShop(shopMetaInfo)) {
        tradeOrderPrefix = 'edu';
      } else if (checkRetailShop(shopMetaInfo)) {
        tradeOrderPrefix = 'retail';
      }
    } catch (error) {
      this.ctx.logger.warn('收银台业务标识前缀识别失败', error);
    }
    const countdownAbTestConfig = ctx.ABTestClient?.getTest('pay_count_down_prominent', String(this.buyerId)) || {};

    ctx.setGlobal({
      // 覆盖掉几个不需要的参数
      mp_data: undefined,
      platformInfo: undefined,
      platform_info: undefined,
      shop_settings: undefined,

      // 收银台 AB 切流使用
      youzan_user_id: youzanUserId,
      /** 收银台业务标识前缀 */
      tradeOrderPrefix,
      countdownAbTestConfig,
    });

    // 获取下单参数
    let response;
    try {
      response = await Promise.all([
        new TradeService(ctx).preparePaymentV2(preparePaymentParams),
        ctx.isThirdApp(),
        new ShopAbilityInfoService(ctx) // 调用店铺能力判断是否是医药店铺
          .queryShopAbilityInfo([kdtId, 'prescription_ability'])
          .catch(() => {
            return {};
          }),
      ]);
    } catch (error) {
      const content = error.errorContent || {};
      const errorMsg = error.message || '';
      const { code, msg } = content;

      // 框架层限流错误码
      if (errorMsg.indexOf('Over traffic limit') !== -1) {
        this.jumpWaitingPage();
        return;
      }

      // 防止绕过前置助力定金膨胀活动页直接进入下单页
      if (+code === 101305055) {
        return ctx.redirect(
          OrderContext.buildUrl(`/wscump/handsel-expand?order_no=${orderNo}&kdt_id=${kdtId}`, 'h5', kdtId)
        );
      }

      if (+code === 101305032) {
        return ctx.render('order/discount-forbid.html', {
          msg,
        });
      }

      // 待支付页需要过滤一些场景的错误code，绕开错误兜底页面，使用异常弹窗展示
      const erroCodeList = getTradeApolloConfig(
        'wsc-h5-trade.application',
        'pay-page-error-code-for-dialog'
      );
      if (erroCodeList?.includes(+code)) {
        ctx.setGlobal({
          isShowErrorDialog: true,
          errorDialogMsg: msg,
        });
        await this.renderPayTemplate();
        return;
      }

      if (code && msg) {
        throw new PageException(content.code, content.msg);
      } else {
        throw error;
      }
    }

    // 请求待支付页是否定制 to-pay
    // isToPay 代表单独定制了待支付不定制下单的情况
    const isToPay = await isCloudDesign(ctx as any, { kdtId: ctx.kdtId, pageName: 'to-pay' });
    if (isToPay) {
      // to-pay
      this.setExtensionParams(ctx, 'to-pay', { orderNo: orderNos.join('|') });
    } else {
      // buy
      this.setExtensionParams(ctx, 'buy', { orderNo: orderNos.join('|') });
    }

    const [prepare, isThirdApp, drugShopInfo] = response;

    const redirect = prepare.redirectConfig || {};

    // android抖音侧跳【/order/payresult】再跳【/order/detail】会出现sessionId失效跳到404
    if (
      redirect.timeout ||
      redirect.orderCanceled ||
      (isIVR && redirect.orderPaid) ||
      (ctx.isTTApp && redirect.orderPaid)
    ) {
      ctx.redirect(
        OrderContext.buildUrl(
          `/wsctrade/order/detail?order_no=${orderNo}&kdt_id=${kdtId}${isIVR ? '&from=ivr' : ''}`,
          'h5',
          kdtId
        )
      );
    } else if (redirect.orderPaid) {
      ctx.redirect(
        OrderContext.buildUrl(`/wsctrade/order/payresult?request_no=${orderNo}&kdt_id=${kdtId}`, 'h5', kdtId)
      );
    } else if (redirect.peerpay && redirect.peerpayUrl) {
      ctx.redirect(redirect.peerpayUrl);
    }

    // 这里直接增加医药订单的接口数据请求
    // 前置请求 优化交互
    const rxNo = get(prepare, 'tradeConfirmation.prescriptionFeatureVO.prescriptionNo');
    if (rxNo) {
      const params = {
        rxNo,
        adminId: this.buyerId,
        kdtId: ctx.kdtId,
        retailSource: 'wsc-h5-trade',
      };
      const info = await new OrderDrugService(ctx).get(params);
      const { patientInfoDTO, medicalInformationDTO } = info;
      const { name, phoneNum } = patientInfoDTO!;
      const { diagnosedDiseaseDTOS } = medicalInformationDTO!;
      const result = {
        patientInfo: {
          name,
          phoneNum,
        },
        medicalInformation: diagnosedDiseaseDTOS,
      };
      ctx.setGlobal({
        orderCreatedMsg: result,
      });
    }

    const buyerInfo = get(prepare, 'tradeConfirmation.buyerInfo', {});
    const isIVROwner = buyerInfo.buyerId === this.buyerId && isIVR;
    const { valid } = drugShopInfo || {};

    const isFxZpp = get(prepare, 'extensions.ATTR_IS_FX_ZPP_ORDER', false);

    if (isFxZpp) {
      try {
        await setQttWeappAppId(ctx);
      } catch (e) {}
    }

    this.setEnv({
      isThirdApp,
      isPayPage: true,
      isDrugShop: valid, // 是否是医药店铺
      isFxZpp: !!isFxZpp, // 是否赞拼拼订单
    });

    this.setDesignAndFooter(prepare);

    const isPartialFastAdOrder =
      get(prepare, 'tradeConfirmation.orderConfig.shopSettingOrderMode', '') === 'IS_PARTIAL_FAST_AD';
    const isFastAdOrder = get(prepare, 'displayConfig.isFastAdOrder', false);

    await this.setPrepare(prepare, {
      isPartialFastAdOrder,
      isThirdApp,
    });

    // 新酒店商品获取额外信息
    const hasHotelGoods = get(prepare, 'tradeConfirmation.tradeTag', false);
    const orderItems = get(prepare, 'tradeConfirmation.orderItems', []);

    if (hasHotelGoods && orderItems[0]) {
      const params = {
        kdtId,
        itemId: orderItems[0].goodsId,
      };
      const newHotelExtensions = await new TradeService(ctx).getHotelBooking(params);

      if (newHotelExtensions) {
        ctx.setGlobal({
          newHotelExtensions,
        });
      }
    }

    // 是否支持前置收银台功能
    const usePreCashierPay = await this.checkUsePreCashierPay(ctx, prepare, { orderNo });
    (prepare as WeappPrepareV2VO).usePreCashierPay = usePreCashierPay;

    ctx.setGlobal({
      isIVROwner,
      massFlowLimitType: MassFlowLimitTypeMap[serviceLevel],
    });

    // 日志埋点模型
    this.setSpm(isPartialFastAdOrder || isFastAdOrder ? 'fastPaySuspend' : 'paySuspend', kdtId);

    // 先用后付场景数据初始化
    const is_prior = get(prepare, 'extensions.IS_SELECTED_PRIOR_USE_PAY_WAY', '-1');
    const selectedPayChannel = String(is_prior) === '1' ? 'PRIOR_USE' : 'OTHERS';
    await this.initPriorUseOrderBuyData(ctx, {
      env: 'buy',
      prepare,
      selectedPayChannel,
    });

    // 服务端数据处理
    const clientPrepare = {
      newHotelExtensions: ctx.getGlobal('newHotelExtensions' as never),
      '@cashier/prior-use': ctx.getGlobal('@cashier/prior-use' as never),
      orderNo,
      ...prepare
    }
    const { clientState, clientCtxData } = getTradePayClientData(clientPrepare);
    ctx.setGlobal({
      clientCtxData,
      clientState,
    });

    if (isPartialFastAdOrder || isFastAdOrder) {
      await ctx.render('order/pay.html', {
        title: '待付款的订单',
      });
    } else {
      await this.renderPayTemplate();
    }
  }

  async isUseRantaPayPage() {
    const { ctx } = this;
    const { force_shunt = '' } = ctx.query;

    switch (force_shunt) {
      case 'v1':
        return false;
      case 'v2':
        return true;
      default:
        break;
    }

    try {
      // 仅定制了待支付或者定制了下单(包含待支付)
      const isCloudCustomized =
        (await isCloudDesign(ctx as any, { kdtId: ctx.kdtId, pageName: 'to-pay' })) ||
        (await isCloudDesign(ctx as any, { kdtId: ctx.kdtId, pageName: 'buy' }));

      const useWebRantaPayWithCloudDesign = isInGrayReleaseByKdtId(
        ctx,
        {
          namespace: 'wsc-h5-trade.gray-release',
          key: 'useWebRantaPayWithCloudDesign',
        },
        ctx.kdtId
      );

      ctx.setState({ isCloudCustomized });

      return !isCloudCustomized || (isCloudCustomized && useWebRantaPayWithCloudDesign);
    } catch (err) {
      // skip
    }

    return false;
  }

  async renderPayTemplate() {
    const { ctx } = this;

    const [isUseRantPage] = await Promise.all([
      this.isUseRantaPayPage(),
      this.initZanPay(), // cashier dragonfly hack
    ]);

    const title = '待付款的订单';
    if (isUseRantPage) {
      const isToPay = await isCloudDesign(ctx as any, { kdtId: ctx.kdtId, pageName: 'to-pay' });
      const pageName = isToPay ? 'to-pay' : 'buy';
      await initRanta(ctx as any, {
        framework: 'tee',
        appName: 'wsc-tee-h5',
        bizName: '@wsc-tee-h5-trade/trade-pay',
        ecloud: {
          setExtensionParams: [
            {
              kdtId: +ctx.kdtId,
              pageName,
              pageNameV2: 'trade-pay',
              conditionContext: this.getConditionContext(ctx),
            },
          ],
        },
      });
      await ctx.render(
        'order/tee-pay.html',
        { title },
        {
          appName: 'wsc-tee-h5',
          skipLoadOpsWebConfig: true, // 中台化在插件中会加载配置并挂载全局
        }
      );
    } else {
      await ctx.render('order/buy.html', {
        title,
      });
    }
  }

  /**
   * 小程序接口
   * 同城预约的Apollo开关配置
   * @param ctx
   */
  async checkIsNewProcess(ctx: Context) {
    const isNewProcess = await new TradeService(ctx).getDeliveryApolloConfig(ctx.kdtId).catch(() => {});
    return ctx.json(0, 'ok', isNewProcess);
  }

  /**
   * 小程序支付页接口
   * @deprecated 只有2.9以前版本小程序在调用
   */
  async postShowPayJson(ctx: Context) {
    const { kdtId, userAgent } = ctx;
    const { order_no = '' } = ctx.getPostData();
    const preparePaymentParams = {
      kdtId,
      userAgent,
      orderNo: order_no,
      buyerId: this.buyerId,
      forbidWxpay: 0,
    };

    // 获取下单参数
    const data = await new TradeService(ctx).preparePayment(preparePaymentParams);

    this.formatShopItem(data);

    const countdownAbTestConfig = ctx.ABTestClient?.getTest('pay_count_down_prominent', String(this.buyerId)) || {};
    data.countdownAbTestConfig = countdownAbTestConfig;

    ctx.r(0, 'ok', data);
  }

  // 小程序支付页接口v2
  async postPrepareJson(ctx: Context) {
    const { kdtId } = ctx;
    const tradeService = new TradeService(ctx);
    const orderNo = ctx.getPostData().order_no || '';
    const orderNos = Array.isArray(orderNo) ? orderNo : [orderNo];
    const { orderMark, useOrderKeep } = ctx.getPostData();
    const forbidWxpay = ctx.getPostData().forbid_wxpay || 0;
    const preparePaymentParams = {
      kdtId,
      orderNos,
      orderMark,
      buyerId: this.buyerId,
      forbidWxpay,
    };
    let response: any;
    try {
      // 获取下单参数
      response = await Promise.all([
        tradeService.preparePaymentV2(preparePaymentParams),
        this.getShopMetaInfo(ctx).catch(() => ({})),
      ]);
    } catch (error) {
      const content = error.errorContent;
      const errorMsg = error.message || '';

      const { isLimitMsg, transformLimitMsg } = OrderBuyController;

      if (isLimitMsg(errorMsg)) {
        // tether 层限流，模拟 429
        throw new BusinessServiceError(429, transformLimitMsg(errorMsg));
      } else if (content) {
        throw new BusinessServiceError(content.code, transformLimitMsg(content.msg));
      }
      throw error;
    }
    const [prepare, shopMetaInfo] = response;

    const showTradeCarouselTest = await new ShopConfigReadService(ctx).queryShopConfigs(ctx.kdtId).catch(() => 0);
    const abConfig = (await this.ctx.ABTestClient?.getTest('entertaining_diversions', String(this.buyerId))) || {};
    const abConfigInfo = abConfig.isValid ? abConfig : {};
    const hasEntry = abConfig.isValid ? get(abConfig, 'configurations.hasEntry', false) : false;
    let showTradeCarouselTestFlag = false;

    try {
      // 店铺
      showTradeCarouselTestFlag = !!+showTradeCarouselTest && hasEntry;
    } catch (error) {
      showTradeCarouselTestFlag = false;
    }

    (prepare as WeappPrepareV2VO).shopConfig = {
      goodsTradeMarquee: {
        show: showTradeCarouselTestFlag, // 下单页是否展示跑马灯
      },
      abConfigInfo,
    };

    (prepare as WeappPrepareV2VO).orderKeepApply = !!useOrderKeep;
    // TODO: 清理 wsc/wsc-tee-h5 中对应依赖
    (prepare as WeappPrepareV2VO).showWxSubscribe = true;

    // 处理打包费
    formatPackingFee(prepare.tradeConfirmation?.orderPayment, 'extraFees', this.ctx.mpVersion, shopMetaInfo);

    const isPartialFastAdOrder =
      get(prepare, 'tradeConfirmation.orderConfig.shopSettingOrderMode', '') === 'IS_PARTIAL_FAST_AD';
    const isFastAdOrder = get(prepare, 'displayConfig.isFastAdOrder', false);

    if (ctx.isWeappNative && (isPartialFastAdOrder || isFastAdOrder)) {
      throw new PageException(10000, '暂不支持在小程序中查看本订单');
    }

    // 先用后付场景数据初始化
    const is_prior = get(prepare, 'extensions.IS_SELECTED_PRIOR_USE_PAY_WAY', '-1');
    const selectedPayChannel = String(is_prior) === '1' ? 'PRIOR_USE' : 'OTHERS';
    await this.initPriorUseOrderBuyData(
      ctx,
      {
        env: 'buy',
        prepare,
        selectedPayChannel,
      },
      (data) => {
        (prepare as any)['@cashier/prior-use'] = data;
      }
    );

    // 是否支持前置收银台功能
    const usePreCashierPay = await this.checkUsePreCashierPay(ctx, prepare, { orderNo });
    (prepare as WeappPrepareV2VO).usePreCashierPay = usePreCashierPay;

    // 服务端数据处理
    prepare.orderNo = prepare.orderNo || orderNo;
    const { clientState, clientCtxData } = getTradePayClientData(prepare);
    prepare.clientCtxData = clientCtxData;
    prepare.clientState = clientState;

    return ctx.json(0, 'ok', prepare);
  }

  // 获取微信场景值
  private getPrioruseWeixinSence() {
    // 传递场景值
    const params: Record<string, any> = {};
    if (this.ctx.isWeixin) {
      if (this.ctx.isWeapp) {
        params.PAY_CREDIT_PAY_DEVICE_TYPE = 'WX_MINI_APP';
      } else {
        params.PAY_CREDIT_PAY_DEVICE_TYPE = 'WX_H5';
      }
    }

    return params;
  }

  // 获取小红书担保交易-本地生活扩展参数
  private getXhsLocalLifeExt() {
    let res = {};
    try {
      if (this.ctx.isXhsApp && (this.ctx.isXhsLocalLife || this.ctx.query.isXhsLocalLife === 'true')) {
        res = {
          BIZ_ORDER_ATTRIBUTE: JSON.stringify({
            MULTI_PLAT_OUT_CHANNEL: 'XHS_MINIAPP_LOCAL_LIFE',
          }),
        };
      }
    } catch (err) {
      console.log('err: ', err);
    }

    return res;
  }

  // 新的获取先用后付的字段信息
  private async getPriorUsePrice(prepare: any) {
    // 前端判断weixin环境
    if (!this.ctx.isWeixin) return { support: false };
    /** 小程序里要用 get ，不然拿不到值 */
    let priorUseExtra = null;
    if (this.ctx.isWeapp) {
      priorUseExtra = get(prepare, 'tradeConfirmation.extra.PRIOR_USE_SUPPORT');
    } else {
      priorUseExtra = findJsonValue(prepare, 'tradeConfirmation.extra.PRIOR_USE_SUPPORT');
    }
    try {
      priorUseExtra = JSON.parse(priorUseExtra);
    } catch (e) {}
    // support, minAmount, maxAmount;
    return priorUseExtra;
  }

  /**
   * 检查先用后付可用性
   *
   * 下单页支持先用后付 ||
   * 商详页支持先用后付 ||
  //  * 店铺配置开启 &&
  //  * 实物商品 &&
  //  * 无活动或白名单之内的活动 &&
  //  * 有赞担保 &&
  //  * 基础消费保障
   * @param prepare
   */
  private async checkPriorUseAvailable(prepare: any): Promise<boolean> {
    // 增加先用后付降级能力
    const { kdtId } = this.ctx;
    let blackList = [];
    let isForbiddenAll = false;
    try {
      const forbiddenConfig = JSON.parse(
        getTradeApolloConfig('assets-cashier.public', 'priorUseForbiddenConfig') ?? '{}'
      );
      blackList = forbiddenConfig?.blackList || [];
      isForbiddenAll = forbiddenConfig?.isForbiddenAll || false;
    } catch (e) {}
    const isForbidden = blackList.includes(Number(kdtId)) || isForbiddenAll;
    if (isForbidden) {
      return false;
    }
    let isPriorUse = '-1';
    /** 小程序里要用 get ，不然拿不到值 */
    const priorUseExtra = await this.getPriorUsePrice(prepare);
    // 能从新的字段里面获取就判断新的接口，不然就从之前的字段里面拿取信息，新字段{support:boolean,minAmount:number,maxAmount:number}
    if (priorUseExtra) {
      isPriorUse = priorUseExtra.support ? '1' : '-1';
    } else {
      // 待支付/下单页面返回 1:支持先用后付并选中 0:支持先用后付 -1:不支持先用后付
      /** 待支付页面先用后付，能获取到此字段表示是待支付页面 */
      const isSelectedPriorUsePayWay = get(prepare, 'extensions.IS_SELECTED_PRIOR_USE_PAY_WAY', '-1');
      const query = this.ctx.getQueryData();
      const { headers } = this.ctx;
      if (isSelectedPriorUsePayWay === '0' || isSelectedPriorUsePayWay === '1') {
        this.ctx.logger.info('待支付页支持先用后付', null, {
          prepare,
          query,
          headers,
        });
        return true;
      }
      // 下单页面先用后付
      if (this.ctx.isWeapp) {
        isPriorUse = get(prepare, 'orderCreation.extensions.IS_SELECTED_PRIOR_USE_PAY_WAY', '-1');
      } else {
        isPriorUse = findJsonValue(prepare, 'orderCreation.extensions.IS_SELECTED_PRIOR_USE_PAY_WAY') || '-1';
      }
    }
    const query = this.ctx.getQueryData();
    const { headers } = this.ctx;
    this.ctx.logger.info('下单页面支持先用后付', null, {
      prepare,
      query,
      headers,
      isPriorUse,
      priorUseExtra,
    });
    return isPriorUse === '0' || isPriorUse === '1';
  }

  private async initPriorUseOrderBuyData(ctx: Context, options: Record<string, any>, onSuccess?: IConfig['onSuccess']) {
    return initOrderDataAsync({
      type: 'buy',
      context: options,
      transform: async (context: any) => {
        const { prepare } = options;
        const isPriorUseAvailable = await this.checkPriorUseAvailable(prepare);
        const { minAmount = 0, maxAmount = 0 } = (await this.getPriorUsePrice(prepare)) || {};
        const payChannels = await this.getPayChannels(isPriorUseAvailable, minAmount, maxAmount);
        return { ...context, payChannels, isPriorUse: isPriorUseAvailable };
      },
      onSuccess: onSuccess ?? ((data) => ctx.setGlobal('@cashier/prior-use', data)),
      onError: (error, context) =>
        ctx.logger.error(`[prior-use::order/${options.env}]先用后付数据初始化失败：${String(error)}`, error, {
          ...context,
          error,
          buyerId: ctx.buyerId,
          kdtId: ctx.kdtId,
          isWeapp: this.ctx.isWeapp,
        }),
    });
  }

  // 用以区分支付宝交易组件场景，营销后端会隐藏所有支付宝交易组件场景无法参与的营销活动
  getAliPayTradeModule(isAlipayTradeModule?: boolean | string) {
    let res = {};
    if (isAlipayTradeModule === true || isAlipayTradeModule === 'true') {
      res = {
        BIZ_ORDER_ATTRIBUTE: JSON.stringify({
          MULTI_PLAT_OUT_CHANNEL: 'ALIPAY_TRADE_MODULE',
        }),
      };
    }

    return res;
  }

  // 零售特殊时段费 extensions 配置
  getRetailSpecialPeriodCostExts(enable: boolean | string) {
    if (enable === true || enable === 'true') {
      return { ATTR_SUPPORT_TIMESPAN_DELIVERY_FEE: '1' };
    }
  }

  // 营销最优价 extensions 配置
  getUmpOptimalExts(useOptimalCalculate: boolean | string) {
    if (useOptimalCalculate === false || useOptimalCalculate === 'false') {
      return { USE_OPTIMAL_CALCULATE: '0' };
    }
  }

  async createUserContactInTTApp(ctx: Context) {
    // 抖音环境下，如果没有联系人则新建一个
    if (this.ctx.isTTApp) {
      const contactListStr = await new UserContactService(ctx).getContactListFast({
        userId: this.buyerId,
      });
      const contactListObj = JSON.parse(contactListStr);

      if (contactListObj?.length === 0) {
        const mobileData = await new OrderShareService(ctx).getMobileInfo({
          userId: this.buyerId,
          appName: 'wsc-h5-trade',
        });
        const { mobile } = mobileData || {};
        if (mobile) {
          // 没有联系人则使用授权手机号新建一个
          await new UserContactService(ctx).addContact({
            isDefault: 0,
            telephone: mobile,
            userId: this.buyerId,
            userName: '抖音用户',
          });
        }
      }
    }
  }

  // 函数抽离,OrderBuySubController公用了此方法
  async getPrepareByBookKeyEvent(ctx: Context, getQueryData: Record<string, any>) {
    const { kdtId, userAgent, isYouzanmars } = ctx;
    const queryData = mapKeysToCamelCase(getQueryData);
    // useNewCoupon 用于 判断小程序 client 层是否支持新流程
    const {
      bookKey,
      addressId,
      addressExtra,
      useNewCoupon,
      useOrderKeep,
      isSupportDefaultSelfFetch,
      isSupportSpecialPeriodCost,
      retailOrderScene,
      isOptimalSolution,
      isOverlyingCoupon,
      disableStoredDiscount,
      platform,
      noteId,
      partyId,
      originNoteId,
      originPartyId,
      defaultPointDeductEffect,
      isDyCouponOrder,
      isNewMemberFlow, // 是否使用办会员新流程
      useOptimalCalculate, // 是否使用最优价
      isAlipayTradeModule, // 是否支付宝交易组件场景
      noUseDefaultAddress, // 不使用默认地址
      pdlive, // 私域直播订单标
    } = queryData;
    // 群团团订单不走营销逻辑
    const isQtt = queryData.isQtt === 'true';
    let { usePointDeduction = !isYouzanmars } = queryData;

    if (typeof usePointDeduction === 'string') {
      if (usePointDeduction === 'false') {
        usePointDeduction = false;
      }
    }
    const directSeller = ctx.getLocalSession('direct_seller' as any) || {};
    const mpData: IPureObject = ctx.getState('mpData' as never) || {};
    const { user_privacy: userPrivacy = {} } = this.ctx.getLocalSession();

    let addressList: any[] = [];

    if (this.buyerId === 0) {
      const sessionInfo = ctx.getLocalSession();
      ctx.logger.warn(
        `buyerId为0:getPrepareByBookKeyJson
    ${(ctx.traceCtx as any).rootId}
    sessionId: ${ctx.sessionId}
    session内容: ${sessionInfo ? JSON.stringify(ctx.getLocalSession()) : ''}
  `
      );
    } else {
      addressList = await new UserAddressService(ctx).getAddressList({
        userId: this.buyerId,
      });
    }

    let defaultAddress = this.getAddressDefaultItem(addressList, addressId);
   
    if (noUseDefaultAddress && !addressId) {
      defaultAddress = {};
    } 
    // 如果地址id存在且地址存在且地址id存在且地址额外信息存在，则将地址额外信息赋值给地址
    if (addressId && defaultAddress && defaultAddress.id && addressExtra) {
      defaultAddress.extra = addressExtra;
    }

    // 电商中台和用户组使用的姓名字段不一致，需要格式化
    if (defaultAddress.userName) {
      defaultAddress.recipients = defaultAddress.userName;
    }

    this.setPageCloudBizIds('buy', 'bookKey', bookKey || '');

    let defaultDisableStoredDiscount = false;
    let storedDiscountRechargeGuide = true;

    let storedDiscountWeappVersion = '2.50.3';
    try {
      storedDiscountWeappVersion = getTradeApolloConfig('wsc-h5-trade.application', 'storedDiscountWeappVersion') || '';
    } catch (err) {
      console.log(err);
    }

    // 低版本小程序下单页禁用储值专享折扣
    try {
      if (this.ctx.xExtraData.version && CompareVersion.isLt(this.ctx.xExtraData.version, storedDiscountWeappVersion)) {
        defaultDisableStoredDiscount = true;
        storedDiscountRechargeGuide = false;
      }
    } catch (err) {
      // do noting
    }

    // 如果query里禁用，则优先级最高，禁用储值专享折扣
    if (disableStoredDiscount) {
      defaultDisableStoredDiscount = true;
      storedDiscountRechargeGuide = false;
    }
    // 判断优惠券叠加当前店铺是否在白名单中
    const isInCouponOverWhitelist = await this.judgeInCouponOverWhitelist(ctx);

    const prepareOrderParams = {
      ...this.buyer,
      kdtId,
      userAgent,
      bookKey,
      address: defaultAddress.id ? defaultAddress : null,
      shopName: mpData.shopName,
      platform: platform || getDiscountPlatform(this.ctx, this.uaPlatform),
      clientIp: ctx.firstXff,
      salesman: directSeller.seller || '',
      ump: {
        // 是否根据后台配置默认使用积分抵现，小程序版本升上来默认都是true
        defaultPointDeductEffect: Boolean(defaultPointDeductEffect),
        usePointDeduction: Boolean(usePointDeduction),
        defaultDisableStoredDiscount,
        storedDiscountRechargeGuide,
      },
      kdtSessionId: ctx.sessionId,
      client: ctx.client,
      // 是否是小程序，后端以此来判断是否要显示分期支付、银行卡支付下线的通知
      isWeapp: ctx.isWeapp,
      isSupportDefaultSelfFetch: !!isSupportDefaultSelfFetch,
      extensions: {
        IS_OPTIMAL_SOLUTION: isOptimalSolution || 'false',
        NEW_MEMBER_FLOW: isNewMemberFlow || 'false',
        IS_OVERLYING_COUPON: isOverlyingCoupon && isInCouponOverWhitelist ? 'true' : 'false', // 只有query里传了true才会做二次切流判断 不传或者为false则默认为‘false’ 后端接收的是字符串false
        ...this.getUmpOptimalExts(useOptimalCalculate),
        ...this.getRetailSpecialPeriodCostExts(isSupportSpecialPeriodCost),
        ...this.getPrioruseWeixinSence(),
        ...this.getAliPayTradeModule(isAlipayTradeModule),
      },
    };
    const riskWarnParams = {
      group: 'wsc',
      tag: 'RISK_SHOP',
      sourceType: 'ORDER_CREATE',
      contentType: 'KDT_ID',
      identityType: 'SELLER',
      value: ctx.query.kdt_id,
    };

    let response;
    try {
      // 判断是否应用优惠券积分抵现流程新逻辑
      const isNewCouponProcess = await isInGrayReleaseByKdtId(
        ctx,
        { namespace: 'wsc-h5-trade.application', key: 'newCouponProcess' },
        ctx.kdtId
      );

      prepareOrderParams.newCouponProcess = isNewCouponProcess && !!useNewCoupon;

      await this.createUserContactInTTApp(ctx);

      if (ctx.isTTApp && retailOrderScene === '24hshelf') {
        prepareOrderParams.orderMark = 'fulfill_tool';
        prepareOrderParams.platform = 'dy_mini_program';
      }

      if (ctx.isTTApp && retailOrderScene === 'ttDiandan') {
        prepareOrderParams.platform = 'dy_mini_program';
      }

      if (ctx.isTTApp && isDyCouponOrder) {
        prepareOrderParams.orderMark = 'dy_coupon';
        prepareOrderParams.platform = 'dy_mini_program';
      }

      if (pdlive) {
        prepareOrderParams.platform = 'pl_live';
      }

      if (ctx.query.prepayCardType === 'delivery_card' && ctx.query.prepayCardNo) {
        prepareOrderParams.usePayAsset = {
          kdtId,
          deliveryCardNos: [ctx.query.prepayCardNo],
        };
      }

      // 群团团的prepareV2启用新的接口
      let prepareV2Req = null;
      if (isQtt) {
        prepareOrderParams.noteId = noteId;
        prepareOrderParams.partyId = partyId;
        prepareOrderParams.originNoteId = originNoteId;
        prepareOrderParams.originPartyId = originPartyId;
        if (queryData.liveId) {
          prepareOrderParams.liveId = queryData.liveId;
        }
        if (queryData.liveRewardId) {
          prepareOrderParams.liveRewardId = queryData.liveRewardId;
        }
        prepareV2Req = new TradeServiceProvider(ctx).prepareV2(prepareOrderParams);
      } else {
        prepareV2Req = new TradeService(ctx).prepareV2(prepareOrderParams);
      }

      response = await Promise.all([
        prepareV2Req,
        new AdvisoryService(ctx).queryIsRiskWarnShop(riskWarnParams).catch(() => {}),
        isInGrayReleaseByKdtId(ctx, { namespace: 'wsc-h5-trade.application', key: 'useWeappBuyPageV2' }, ctx.kdtId),
        new ShopConfigReadService(ctx).queryShopConfigs(ctx.kdtId).catch(() => 0),
        new ShopAbilityInfoService(ctx) // 调用店铺能力判断是否接入行为组件
          .queryShopAbilityInfo([kdtId, 'risk_prevention_of_marketing_ability'])
          .catch(() => {
            return {};
          }),
        isInGrayReleaseByKdtId(
          ctx,
          { namespace: 'wsc-h5-trade.gray-release', key: 'useOrderUserAuthorize' }, // 行为组件下单页接入
          ctx.kdtId
        ),
        isInGrayReleaseByKdtIdForGoodsApollo(
          ctx,
          { namespace: 'wsc-h5-goods.goods-release', key: 'goods-img-cover' },
          ctx.kdtId
        ),
        this.getConfirmAddressValidate(ctx),
        this.getCurrentShopInfo(ctx),
      ]);
    } catch (error) {
      const content = error.errorContent;
      const errorMsg = error.message || '';
      const { isLimitMsg, transformLimitMsg } = OrderBuyController;

      // tether 层限流，模拟 429
      if (isLimitMsg(errorMsg)) {
        throw new BusinessServiceError(429, transformLimitMsg(errorMsg));
      } else if (content) {
        throw new BusinessServiceError(content.code, transformLimitMsg(content.msg));
      } else {
        throw error;
      }
    }

    const [
      prepare,
      riskWarnShop = {},
      useWeappBuyPageV2,
      showTradeCarouselTest,
      enableUseOrderBehaviorInfo,
      enableUseUserAuthorize,
      isGoodsImgCover,
      confirmAddressValidate,
      currentShopInfo,
    ] = response;
    // 补齐prepare.tradeConfirmation?.shop.address
    if (prepare && prepare.tradeConfirmation?.shop && currentShopInfo && currentShopInfo.address) {
      prepare.tradeConfirmation.shop.address = currentShopInfo.address;
    }

    // 页面停留超时
    const redirect = prepare.redirectConfig || {};

    if (redirect.timeout) {
      throw new PageException(101240006, '页面停留太久，请返回重新购买');
    }

    const canSelfFetch = get(prepare, 'displayConfig.canSelfFetch', false);
    const contactRequired = get(prepare, 'tradeConfirmation.orderItems', []).some(
      ({ virtualType }: IOrderItemVO) => virtualType === 3
    );
    // 是否接入行为组件
    const { valid: enableUseOrderBehaviorParam } = enableUseOrderBehaviorInfo;
    // 积分名称自定义
    const pointsName = get(prepare, 'pointsConfig.pointName', '积分');
    (prepare as WeappPrepareV2VO).pointsName = pointsName;
    (prepare as WeappConfirmV2VO).version = useWeappBuyPageV2 ? 2 : 1;
    (prepare as WeappPrepareV2VO).orderKeepApply = !!useOrderKeep;
    // TODO: 清理 wsc/wsc-tee-h5 中对应依赖
    (prepare as WeappPrepareV2VO).showWxSubscribe = true;
    const promises = [
      this.setPrepare(
        prepare,
        {
          riskWarnShop,
        },
        false
      ),
    ];

    if (canSelfFetch || contactRequired) {
      promises.push(
        new UserContactService(ctx).getContactList({ userId: this.buyerId }).then((contactList) => {
          (prepare as WeappPrepareV2VO).contact = {
            list: contactList,
            id: this.getContactDefaultItem(contactList).id,
          };
        })
      );
    }

    await Promise.all(promises);
    const abConfigExpressWay =
      (await this.ctx.ABTestClient?.getTest('order_page_self_fetch_default', String(this.buyerId))) || {};

    const buyerMsgTips = await new ShopConfigReadService(ctx).queryShopConfigsBuyerMsg(ctx.kdtId).catch(() => 0);

    this.ctx.logger.info(`下单页、自提小程序abtest, kdtId=${abConfigExpressWay}, buyerId=${this.ctx.buyerId}`, null, {
      abConfigExpressWay,
    });
    (prepare as WeappPrepareV2VO).address = {
      list: addressList,
      id: defaultAddress.id,
      abConfigExpressWay: abConfigExpressWay.isValid ? abConfigExpressWay : {},
    };
    // 协议强制不显示
    userPrivacy.agreement_signed = true;
    (prepare as WeappPrepareV2VO).userPrivacy = userPrivacy;

    // 读取用户协议和隐私政策
    let agreement = {};
    try {
      const agreementStr = getTradeApolloConfig('wsc-h5-trade.application', 'agreement') || '{}';
      agreement = JSON.parse(agreementStr);
    } catch (err) {
      console.log(err);
    }
    (prepare as WeappPrepareV2VO).agreement = agreement;
    // 是否开启轮播图
    const abConfig = (await this.ctx.ABTestClient?.getTest('entertaining_diversions', String(this.buyerId))) || {};
    const abConfigInfo = abConfig.isValid ? abConfig : {};
    const hasEntry = abConfig.isValid ? get(abConfig, 'configurations.hasEntry', false) : false;
    let showTradeCarouselTestFlag = false;
    try {
      showTradeCarouselTestFlag = !!+showTradeCarouselTest && hasEntry;
    } catch (error) {
      showTradeCarouselTestFlag = false;
    }

    (prepare as WeappPrepareV2VO).shopConfig = {
      goodsTradeMarquee: {
        show: showTradeCarouselTestFlag,
      },
      abConfigInfo,
      buyerMsgTips: buyerMsgTips || {},
    };

    const overseaRegulationWhiteList = getTradeApolloConfig('wsc-h5-trade.application', 'overseaRegulationWhiteList');
    const whitelist = overseaRegulationWhiteList?.split(',');
    if (whitelist) {
      (prepare as WeappPrepareV2VO).ignoreIdBinding = whitelist.indexOf(String(kdtId)) !== -1;
    }

    // 标记切流expressTypeChoice需要传给后端,行为组件切流
    const { orderCreation, displayConfig } = prepare as SimpleOrderPreparationVO;
    if (orderCreation) {
      // 小程序2.83.4版本及以后对此字段依赖已下线，这里强制为true服务于此前一个月的留存版本
      orderCreation.enableExpressTypeChoiceParam = true;
    }
    if (displayConfig) {
      displayConfig.enableUseOrderBehaviorParam = enableUseOrderBehaviorParam;
      displayConfig.enableUseUserAuthorize = enableUseUserAuthorize;
    }
    // 先用后付场景数据初始化
    const is_prior = get(prepare, 'orderCreation.extensions.IS_SELECTED_PRIOR_USE_PAY_WAY', '-1');
    const selectedPayChannel = String(is_prior) === '1' ? 'PRIOR_USE' : 'OTHERS';
    await this.initPriorUseOrderBuyData(
      ctx,
      {
        env: 'buy',
        prepare,
        selectedPayChannel,
      },
      (data) => {
        (prepare as any)['@cashier/prior-use'] = data;
      }
    );

    // 优惠券叠加白名单
    (prepare as WeappPrepareV2VO).isInCouponOverWhitelist = isInCouponOverWhitelist;

    // 是否支持前置收银台功能
    const usePreCashierPay =
      queryData.isQtt === 'true'
        ? {
          value: true,
          conditions: {},
        }
        : await this.checkUsePreCashierPay(ctx, prepare, { bookKey });
    (prepare as WeappPrepareV2VO).usePreCashierPay = usePreCashierPay;

    // 小程序场景，且带了微信小程序场景值，则实时更新book-key缓存中的商品的scene值
    const scene = ctx.getQuery('scene');
    if (ctx.isWeappNative && scene && orderCreation?.source?.itemSources) {
      const itemSources = orderCreation.source.itemSources || [];
      itemSources.forEach((item) => {
        try {
          const bizTracePointExt = JSON.parse(item.bizTracePointExt || '{}');
          bizTracePointExt.scene = scene;
          item.bizTracePointExt = JSON.stringify(bizTracePointExt);
        } catch (e) {}
      });
      orderCreation.source.itemSources = itemSources;
    }
    const asyncOrderUnLimitCode = getTradeApolloConfig('wsc-h5-trade.application', 'asyncOrderUnLimitCode');
    const monitorLoggerFilterCode = getTradeApolloConfig('wsc-h5-trade.application', 'monitorLoggerFilterCode');
    prepare.asyncOrderUnLimitCode = asyncOrderUnLimitCode;
    prepare.monitorLoggerFilterCode = monitorLoggerFilterCode;

    // 地图 key 接入 apollo
    const addressMapKeyConfig = this.getAddressMapKeyApollo();
    prepare.addressMapKeyConfig = addressMapKeyConfig;
    // 确认订单页商品图片是否填充
    prepare.isGoodsImgCover = isGoodsImgCover;
    // 确认地址校验开关
    prepare.confirmAddressValidate = confirmAddressValidate;
    // 由于sku下单调用的prepare接口，也需要拿到env，因此在prepare接口返回env前端做处理
    prepare.env = {
      isThirdApp: ctx.isAppSdk,
      platform: ctx.platform,
      isWeixin: ctx.isWeixin,
      isSwanApp: ctx.isSwanApp,
      isWeapp: ctx.isWeapp,
      isTTApp: ctx.isTTApp,
      isAlipayApp: ctx.isAlipayApp, // 阿里系支付宝小程序环境内
      isQQApp: ctx.isQQApp,
      isKsApp: ctx.isKsApp,
      isXhsApp: ctx.isXhsApp,
    };
    return prepare;
  }

  /**
   * 小程序下单初始化
   */
  async getPrepareByBookKeyJson(ctx: Context) {
    const prepare = await this.getPrepareByBookKeyEvent(ctx, ctx.getQueryData());

    ctx.json(0, '', prepare);
  }

  // 获取mapKey 的 apollo 配置，默认为{}
  getAddressMapKeyApollo() {
    const mapKeyJson = getTradeApolloConfig('wsc-h5-trade.application', 'address-map-key');
    if (!mapKeyJson) {
      return {};
    }
    try {
      return JSON.parse(mapKeyJson);
    } catch (err) {
      return {};
    }
  }

  async crossStoreAutoEnterShop(ctx: Context) {
    const { targetKdtId } = mapKeysToCamelCase(ctx.getQueryData());

    ctx.query.sub_kdt_id = targetKdtId;

    // sceneId: wsc-h5-shop.enter-shop.extend_scene_id: "9008": "自提点列表页切换"
    await setApplicationScene(ctx as any, {
      sceneId: 9008,
      sceneSource: SceneSource.CUSTOM,
    });

    const result = await enterShopForH5(ctx as any);

    ctx.json(0, '', result);
  }

  async crossStoreReCacheOrderCreation(ctx: Context) {
    const { targetKdtId, bookKey, changeItems, buyerMsg, extensions = {}, delivery } = mapKeysToCamelCase(
      ctx.getPostData()
    );
    const { buyerId = 0 } = this.buyer;

    const params: Record<string, string | number> = {
      buyerId,
      targetKdtId,
      bookKey,
      changeItems,
      buyerMsg,
      extensions,
    };

    // 点单宝跨店自提参数调整：https://qima.feishu.cn/docx/DBtidzDOtoBr3ExLd0ScPnJKnzq
    if (delivery) {
      params.delivery = delivery;
    }

    const result = await new TradeService(ctx).crossStoreReCacheOrderCreation(params);

    return ctx.json(0, 'ok', result);
  }

  /**
   * 获取下单挽留信息
   */
  async postFetchOrderKeepJson(ctx: Context) {
    const { kdtId } = ctx;
    const tradeService = new TradeService(ctx);
    const postData = ctx.getPostData();
    postData.kdtId = kdtId;
    postData.buyerId = this.buyerId;
    let result;
    try {
      // 下单挽留参数
      result = await tradeService.letBuyerStay(postData);
    } catch (error) {
      const content = error.errorContent;
      const errorMsg = error.message || '';

      const { isLimitMsg, transformLimitMsg } = OrderBuyController;

      if (isLimitMsg(errorMsg)) {
        // tether 层限流，模拟 429
        throw new BusinessServiceError(429, transformLimitMsg(errorMsg));
      } else if (content) {
        throw new BusinessServiceError(content.code, transformLimitMsg(content.msg));
      }
      throw error;
    }
    return ctx.json(0, 'ok', result);
  }

  /**
   * 格式化店铺数据
   * @deprecated
   */
  formatShopItem(data: IPureObject) {
    const shopSetting: IPureObject = this.ctx.getState('shopSettings' as never) || {};
    data.shopItem = data.shopItem || {};
    data.shopItem.theme = this.ctx.getState('globalTheme' as never);
    data.shopItem.teamCertificateGf = data.teamCertificateGf;
    data.shopItem.youzanSecured = data.youzanSecured;

    if (shopSetting.isLogoCustomized) {
      data.shopItem.copyrightPicUrl = shopSetting.customizedLogo;
    }
  }

  /**
   * 设置下单页design列表 并 处理底部footer
   */
  private setDesignAndFooter(prepare: SimpleOrderPreparationVO | SimpleOrderPaymentPreparationVO, isBuy = false) {
    const yzGuarantee = prepare.displayConfig && prepare.displayConfig.yzGuarantee;
    const item0 = get(prepare, 'tradeConfirmation.orderItems[0]');
    const isPeriodBuy: boolean = item0 && (item0.issue || item0.planExpressTime);

    // 下单页+海淘商品 设置海淘footer
    const hasOverseaGoods = get(prepare, 'tradeConfirmation.tradeTag.hasOverseaGoods', false);
    if (isBuy && hasOverseaGoods) {
      this.ctx.setState('showHaitaoFooter', true);
    }

    // design列表中去除有赞担保，使用公共footer，周期购不显示有赞担保
    this.setYouzanSecured(yzGuarantee && !isPeriodBuy);
  }

  /**
   * 设置下单页design列表 并 处理底部footer
   * @private
   * @param {String} prepare
   * @param {boolean} [isBuy=false]
   * @memberof OrderBuyController
   */
  private setDesignAndFooterFast([tradeTag, orderItems]: [string, IOrderItemVO[]], prepare = '', isBuy = false) {
    const yzGuarantee = findJsonValue(prepare, 'displayConfig.yzGuarantee');
    const item0 = orderItems[0];
    const isPeriodBuy: boolean = item0 && !!(item0.issue || item0.planExpressTime);

    // 下单页+海淘商品 设置海淘footer
    const hasOverseaGoods = findJsonValue(tradeTag, 'hasOverseaGoods') || false;
    if (isBuy && hasOverseaGoods) {
      this.ctx.setState('showHaitaoFooter', true);
    }

    // design列表中去除有赞担保，使用公共footer，周期购不显示有赞担保
    this.setYouzanSecured(yzGuarantee && !isPeriodBuy);
  }

  /**
   * 更新下单页订单数据
   * @description v1 版本，返回下划线结构
   * @deprecated 只有2.9以下的小程序在使用
   */
  async postShowJson(ctx: Context) {
    const postData = ctx.getPostData();
    postData.buyer = this.buyer;

    const data = await new TradeService(ctx).confirm(postData);

    ctx.r(0, 'ok', data);
  }

  // 更新下单页订单数据
  // v2 版本，返回驼峰结构
  async postConfirmJson(ctx: Context) {
    const postData: any = ctx.getPostData<OrderCreationDTO>();
    postData.buyer = this.buyer;
    const { kdt_id: kdtId } = ctx.request.query;

    this.setPageCloudBizIds('buy', 'bookKey', postData.bookKey || '');

    let data = ({} as any) as WeappConfirmV2VO;
    const options: PrepareOptions = {};
    // 首次请求返回 收货地址列表 和 联系人列表 目前小程序用
    // @deprecated 仅限旧版小程序使用，不要在里面写逻辑了 2019-09-09

    // 传递场景值
    postData.extensions = {
      ...(postData.extensions || {}),
      ...this.getPrioruseWeixinSence(),
    };

    const [enableUseOrderBehaviorInfo, enableUseUserAuthorize, isGoodsImgCover, confirmAddressValidate] = await Promise.all([
      new ShopAbilityInfoService(ctx) // 调用店铺能力判断是否接入行为组件
        .queryShopAbilityInfo([kdtId, 'risk_prevention_of_marketing_ability'])
        .catch(() => {
          return {};
        }),
      isInGrayReleaseByKdtId(
        ctx,
        { namespace: 'wsc-h5-trade.gray-release', key: 'useOrderUserAuthorize' }, // 授权组件下单页接入
        ctx.kdtId
      ),
      isInGrayReleaseByKdtIdForGoodsApollo(
        ctx,
        { namespace: 'wsc-h5-goods.goods-release', key: 'goods-img-cover' },
        ctx.kdtId
      ),
      this.getConfirmAddressValidate(ctx),
    ]);
    // 是否接入行为组件
    const { valid: enableUseOrderBehaviorParam } = enableUseOrderBehaviorInfo;

    if (postData.first) {
      const riskWarnParams = {
        group: 'wsc',
        tag: 'RISK_SHOP',
        sourceType: 'ORDER_CREATE',
        contentType: 'KDT_ID',
        identityType: 'SELLER',
        value: ctx.query.kdt_id,
      };

      const [addressList, pointsName, riskWarnShop = {}] = await Promise.all([
        new UserAddressService(ctx).getAddressList({ userId: this.buyerId }),
        new ShopConfigReadService(ctx).queryShopPointsName(kdtId).catch(() => {}),
        new AdvisoryService(ctx).queryIsRiskWarnShop(riskWarnParams).catch(() => {}),
      ]);
      const defaultAddress = this.getAddressDefaultItem(addressList);

      postData.delivery = postData.delivery || {};
      postData.source = postData.source || {};
      // 是否是小程序，后端以此来判断是否要显示分期支付、银行卡支付下线的通知
      postData.source.isWeapp = ctx.isWeapp;
      const { expressType } = postData.delivery;
      if (!expressType) {
        postData.delivery.expressType = 'express';
      }

      if (postData.delivery.expressType === 'express' && defaultAddress.id) {
        postData.delivery.address = defaultAddress;
      }
      let contactList;

      [data, contactList] = await Promise.all([
        // 群团团的confirmV2启用新的接口
        postData.isQtt ? new TradeServiceProvider(ctx).confirmV2(postData) : new TradeService(ctx).confirmV2(postData),
        new UserContactService(ctx).getContactList({ userId: this.buyerId }),
      ]);
      data.address = {
        list: addressList,
        id: this.getAddressDefaultItem(addressList).id || '',
      };
      data.contact = {
        id: this.getContactDefaultItem(contactList).id || '',
        list: contactList,
      };

      // 积分名称自定义
      data.pointsName = pointsName;
      options.riskWarnShop = riskWarnShop;
    } else {
      postData.source = postData.source || {};
      // 是否是小程序，后端以此来判断是否要显示分期支付、银行卡支付下线的通知
      postData.source.isWeapp = ctx.isWeapp;

      // 群团团的confirmV2启用新的接口
      const confirmReq = postData.isQtt
        ? new TradeServiceProvider(ctx).confirmV2(postData)
        : new TradeService(ctx).confirmV2(postData);
      data = await confirmReq;
    }

    options.isPartialFastAdOrder = get(postData, 'config.isPartialFastAdOrder', false);
    // 同prepare返回
    if (data.displayConfig) {
      data.displayConfig.enableUseOrderBehaviorParam = enableUseOrderBehaviorParam;
      data.displayConfig.enableUseUserAuthorize = enableUseUserAuthorize;
    }
    await this.setPrepare(data, options, false);

    const asyncOrderUnLimitCode = getTradeApolloConfig('wsc-h5-trade.application', 'asyncOrderUnLimitCode');
    data.asyncOrderUnLimitCode = asyncOrderUnLimitCode;
    data.isGoodsImgCover = isGoodsImgCover;
    data.confirmAddressValidate = confirmAddressValidate;

    ctx.json(0, 'ok', data);
  }

  // 更新下单页订单数据
  // v2 版本，返回驼峰字符串结构
  async postConfirmJsonFast(ctx: Context) {
    const postData = ctx.getPostData<OrderCreationDTO>();
    postData.buyer = this.buyer;

    this.setPageCloudBizIds('buy', 'bookKey', postData.bookKey || '');
    // 传递场景值
    postData.extensions = {
      ...(postData.extensions || {}),
      ...this.getPrioruseWeixinSence(),
    };
    const [confirmData, isThirdApp] = await Promise.all([
      new TradeService(ctx).confirmV2Fast(postData),
      ctx.isThirdApp(),
    ]);

    const {
      proxy_weixin_openid: wxSubOpenId = '', // 大账号 openId
      verify_weixin_openid: wxSelfOpenId = '', // 自有粉丝 openId
    } = ctx.getLocalSession();
    /**
     * copyright中的店铺logo
     * @deprecated 理论上下单页copyright被公共footer接管后无需设置了
     */
    const shopSetting: IPureObject = this.ctx.getState('shopSettings' as never) || {};
    const isPartialFastAdOrder = get(postData, 'config.isPartialFastAdOrder', false);
    const orderItems: IOrderItemVO[] = findJsonValue(confirmData, 'tradeConfirmation.orderItems', true) || [];

    const parsePrepareOptions: IParsePrepareOptionsVO = {
      copyrightPicUrl: shopSetting.isLogoCustomized ? shopSetting.customizedLogo : '',
      serverTime: Date.now(),
      isPartialFastAdOrder,
      ...(await this.getParsePrepareOptions({
        isThirdApp,
        orderItems,
        isPartialFastAdOrder,
      })),
    };

    parsePrepareOptions.wechatPayParams = {
      wxSubOpenId,
      wxSelfOpenId,
    };
    ctx.json(0, 'ok', {
      confirmData,
      parsePrepareOptions,
    });
  }

  /**
   * 提交订单
   * @description v1 版本，返回下划线结构
   * @deprecated 只有2.9以下的小程序在使用
   */
  async postBillJson(ctx: Context) {
    const postData = ctx.getPostData();
    postData.buyer = this.buyer;

    // TODO 短信通知兼容
    if (postData.config) {
      postData.config.receiveMsg = true;
    }

    // 修复通过mock请求下单接口篡改数据
    if (postData.source) {
      postData.source.clientIp = ctx.firstXff;
      postData.source.userAgent = ctx.userAgent;
    }

    // 攻击者调用下单接口截获
    if (ctx.kdtId === 0) {
      let enableBillKdtIdInvalidPrevent = false;
      try {
        // apollo控制是否启用防御
        enableBillKdtIdInvalidPrevent =
          getTradeApolloConfig('wsc-h5-trade.application', 'enableBillKdtIdInvalidPrevent') === 'true';
      } catch (err) {
        // console.log(err);
      }

      if (enableBillKdtIdInvalidPrevent) {
        ctx.logger.info(`ctxKdtId0 rootId-[${ctx.traceCtx && (ctx.traceCtx as any).parentId}]`, null, postData);
        // 为了迷惑攻击者，这里返回正常
        ctx.json(9999, 'are you ok');
        return;
      }
    }

    if (ctx.kdtId !== postData.seller?.kdtId + '') {
      ctx.logger.info(`ctxKdtId与paramsKdtId不一致:${ctx.kdtId} ${postData.seller?.kdtId}`);
    }
    // 事实上只有老版本小程序才会调这个接口 调用账号接口查询该店铺正在使用的小程序版本，如果发现该店铺存在更高版本的小程序 却还走的这个接口，说明是刷单的，进行拦截
    const mpVerionPostData = {
      kdtId: +ctx.kdtId,
      accountType: 2,
      businessType: 1,
    };
    // 行为组件下单页接入
    const enableUseOrderBehaviorInfo = await new ShopAbilityInfoService(ctx) // 调用店铺能力判断是否接入行为组件
      .queryShopAbilityInfo([
        postData.seller?.kdtId ? postData.seller?.kdtId + '' : ctx.kdtId,
        'risk_prevention_of_marketing_ability',
      ])
      .catch(() => {
        return {};
      });
    // 是否接入行为组件
    const { valid: enableUseOrderBehaviorParam = false } = enableUseOrderBehaviorInfo;
    try {
      if (enableUseOrderBehaviorParam) {
        const mpVerionData = await new MpVersionService(ctx).getMpVersion(mpVerionPostData);
        // 发布时间不存在或者超出2019-06-01 这个时间点，认为即为不正常的刷单请求
        if (!mpVerionData?.releaseTime || +mpVerionData.releaseTime > *************) {
          return ctx.json(10000, '验证失败');
        }
      }
    } catch (error) {}

    postData.extensions = this.handleRechargeOrderPayParams(postData);
    const data = await new TradeService(ctx).create(postData);
    ctx.r(0, 'ok', data);
  }

  // TODO: 故障引起的地址经纬度异常，经过数据修复后，存在部分经纬度缺失的地址，当前在此拦截这些地址，将来会删除此逻辑
  async checkAddressIsDeletion(ctx: Context, postData: OrderCreationDTO): Promise<void> {
    const { address = {}, expressTypeChoice = -1 } = postData?.delivery || {};
    if (+expressTypeChoice === 2 /* 同城配送 */ && address.type === 2 /* 地图选点 */ && !(address.lat && address.lon)) {
      ctx.logger.warn(`地址信息已失效，下单拦截拦截: ${ctx.kdtId} ${this.buyerId} ${JSON.stringify(address)}`);
      throw new Error('请重新对当前地址进行地图定位选点');
    }
  }

  // 异步下单之前，先生成asyncBooKKey
  async postCreateAsyncBookKey(ctx: Context) {
    const postData: OrderCreationDTO = ctx.getPostData();
    postData.buyer = this.buyer;
    const itemIds: number[] = [];
    postData.items &&
      postData.items.forEach((item) => {
        itemIds.push(item.goodsId);
      });
    this.setPageCloudBizIds('buy', 'goodsId', itemIds.join('|'));
    try {
      await this.checkAddressIsDeletion(ctx, postData);
    } catch (error) {
      return ctx.json(100001111, error.message);
    }
    // controller方法里调用风控的校验接口进行校验，若返回不通过则阻断下单，成功则发起下单接口请求，风控接口异常则默认通过
    try {
      await this.settleSecondCheckBehaviorToken(ctx, postData);
    } catch (error) {
      const content = error.errorContent || {};
      const message = error.message || '';
      // 非法token 验证失败
      if (content.code === 135900028 || message === BEHAVIOR_ERROR_MSG) {
        ctx.logger.warn('下单页【行为组件】验证失败', error);
        return ctx.json(10000, '验证失败');
      }
    }

    let createOrderResponse;
    try {
      postData.extensions = this.handleRechargeOrderPayParams(postData);
      createOrderResponse = await new TradeService(ctx).createAsync(postData);

      const { bookKey } = createOrderResponse;
      return ctx.json(0, 'ok', { asyncOrderBookKey: bookKey });
    } catch (error) {
      const content = error.errorContent;
      const { transformLimitMsg } = OrderBuyController;

      if (content) {
        throw new BusinessServiceError(content.code, transformLimitMsg(content.msg));
      } else {
        throw error;
      }
    }
  }

  // 提交订单
  // v2 版本，返回驼峰结构，小程序在用，H5 已替换成 postBillV2JsonFast
  async postBillV2Json(ctx: Context) {
    this.checkFans('创建订单', true);

    const postData = ctx.getPostData<OrderCreationDTO>();
    postData.buyer = this.buyer;
    const itemIds: number[] = [];
    postData.items &&
      postData.items.forEach((item) => {
        itemIds.push(item.goodsId);
      });
    // 修复通过mock请求下单接口篡改数据
    if (postData.source) {
      postData.source.clientIp = ctx.firstXff;
      postData.source.userAgent = ctx.userAgent;
    }

    if (ctx.kdtId !== postData.seller?.kdtId + '') {
      ctx.logger.info(`ctxKdtId与paramsKdtId不一致:${ctx.kdtId} ${postData.seller?.kdtId}`);

      // @ts-ignore 店铺元数据在初始化时已经查询并缓存
      const shopMetaInfo = await this.getShopMetaInfo(ctx);
      const { rootKdtId } = shopMetaInfo;

      // 先判断提交的参数是否是总部 kdtId
      if (rootKdtId === postData.seller?.kdtId) {
        ctx.logger.info(`替换 seller 的 kdtId: ${ctx.kdtId} ${rootKdtId} ${postData.seller?.kdtId}`);
        // 2022-08-29 若不一致，以 ctx.kdtId 为准
        postData.seller = {
          ...postData.seller,
          kdtId: ctx.kdtId,
        };
      } else {
        const isCheckQueryKdtId = await this.isInCPGray('check_trade_query_kdt_id').catch(() => false);
        if (isCheckQueryKdtId) {
          ctx.logger.info(`触发kdtId不一致校验: ctx.kdtId: ${ctx.kdtId} postData.kdtId${postData.seller?.kdtId}`);
          ctx.json(9999, 'are you ok');
          return;
        }
      }
    }

    // 攻击者调用下单接口截获
    if (ctx.kdtId === 0 || (postData.seller?.kdtId + '' === '46411350' && postData.seller?.kdtId + '' !== ctx.kdtId)) {
      let enableBillKdtIdInvalidPrevent = false;
      try {
        // apollo控制是否启用防御
        enableBillKdtIdInvalidPrevent =
          getTradeApolloConfig('wsc-h5-trade.application', 'enableBillKdtIdInvalidPrevent') === 'true';
      } catch (err) {
        // console.log(err);
      }

      if (enableBillKdtIdInvalidPrevent) {
        ctx.logger.info(`ctxKdtId0 rootId-[${ctx.traceCtx && (ctx.traceCtx as any).parentId}]`, null, postData);
        // 为了迷惑攻击者，这里返回正常
        ctx.json(9999, 'are you ok');
        return;
      }
    }

    this.setPageCloudBizIds('buy', 'goodsId', itemIds.join('|'));

    // TODO 短信通知兼容
    // 由于目前后端的短信逻辑依赖前端传值，先保留这个值
    if (postData.config) {
      postData.config.receiveMsg = true;
    }

    // 爱逛 - 判断店铺是否加入担保交易
    const orderMark = get(postData, 'source.orderMark', '');
    if (orderMark === 'weapp_guang') {
      const securedTransactions = await new SecuredTXQueryService(ctx).querySecuredTransactionsStatus(ctx.kdtId);
      if (!securedTransactions) {
        ctx.logger.info('爱逛商家未加入担保交易', null, postData);
        throw new BusinessServiceError(108001001, '商家未加入担保交易，存在风险，当前无法下单');
      }
    }
    // controller方法里调用风控的校验接口进行校验，若返回不通过则阻断下单，成功则发起下单接口请求，风控接口异常则默认通过
    try {
      await this.settleSecondCheckBehaviorToken(ctx, postData);
    } catch (error) {
      const content = error.errorContent || {};
      const message = error.message || '';
      // 非法token 验证失败
      if (content.code === 135900028 || message === BEHAVIOR_ERROR_MSG) {
        ctx.logger.warn('下单页【行为组件】验证失败', error);
        return ctx.json(10000, '验证失败');
      }
    }
    try {
      // NOTE: 需要先单独调用创建订单，因为后续uic接口没有接入限流服务。避免大流量时交易触发限流，uic未限流挂了
      postData.extensions = this.handleRechargeOrderPayParams(postData);
      const createOrderResponse = await new TradeService(ctx).create(postData);
      const requests = [];

      /**
       * 设置默认收货地址，不阻塞主流程
       * @deprecated h5地址编辑页由用户手动指定默认状态，只有小程序还需要自动设置默认项
       * TODO: 下单页重构小程序部分上线后，这里会修改为只有v1版小程序才设置默认项
       */
      const addressId = get(postData, 'delivery.address.id');
      if (addressId && ctx.isWeappNative && postData.version !== 2) {
        requests.push(
          new UserAddressService(ctx)
            .setDefaultAddress({
              userId: this.buyerId,
              addressId,
            })
            .catch(() => {})
        );
      }

      /**
       * 设置默认联系人，不阻塞主流程
       * @deprecated h5联系人编辑页由用户手动指定默认状态，只有小程序还需要自动设置默认项
       * TODO: 下单页重构小程序部分上线后，这里会修改为只有v1版小程序才设置默认项
       */
      const contactId = get(postData, 'delivery.contacts.id') || get(postData, 'delivery.selfFetch.appointmentId');
      if (contactId && ctx.isWeappNative && postData.version !== 2) {
        requests.push(
          new UserContactService(ctx)
            .setDefaultContact({
              userId: this.buyerId,
              contactId,
            })
            .catch(() => {})
        );
      }
      // 未签署用户协议时，需要自动签署掉
      const { user_privacy = {} } = this.ctx.getLocalSession() || {};
      if (!get(user_privacy, 'agreement_signed', false)) {
        requests.push(
          new UserAccountService(ctx)
            .userPrivacyAuth({
              authTypeList: [3],
              signTriggerLink: 'weapp_order',
              sessionId: ctx.sessionId,
              userId: this.buyerId,
              scene: {
                userAgent: ctx.userAgent,
                ipAddress: ctx.firstXff,
              },
            })
            .catch(() => {})
        );
      }

      await Promise.all(requests);

      ctx.json(0, 'ok', createOrderResponse);
    } catch (error) {
      throw OrderBuyController.formatBillError(<NormalError>error);
    }
  }

  //
  /**
   * 提交订单
   * v2 版本，返回驼峰字符串结构,多端以及H5在用
   *
   * 这个版本是目前wsc-tee-h5项目中在使用的
   * @param ctx
   */
  async postBillV2JsonFast(ctx: Context) {
    this.checkFans('创建订单', true);

    const postData = ctx.getPostData<OrderCreationDTO>();
    postData.buyer = this.buyer;
    const itemIds: number[] = [];
    postData.items &&
      postData.items.forEach((item) => {
        itemIds.push(item.goodsId);
      });

    // 修复通过mock请求下单接口篡改数据
    if (postData.source) {
      postData.source.clientIp = ctx.firstXff;
      postData.source.userAgent = ctx.userAgent;
    }

    try {
      await this.checkAddressIsDeletion(ctx, postData);
    } catch (error) {
      return ctx.json(100001111, error.message);
    }

    if (ctx.kdtId !== postData.seller?.kdtId + '') {
      ctx.logger.info(`ctxKdtId与paramsKdtId不一致:${ctx.kdtId} ${postData.seller?.kdtId}`);

      // @ts-ignore 店铺元数据在初始化时已经查询并缓存
      const shopMetaInfo = await this.getShopMetaInfo(ctx);
      const { rootKdtId } = shopMetaInfo;

      // 先判断提交的参数是否是总部 kdtId
      if (rootKdtId === postData.seller?.kdtId) {
        ctx.logger.info(`替换 seller 的 kdtId: ${ctx.kdtId} ${rootKdtId} ${postData.seller?.kdtId}`);
        // 2022-08-29 若不一致，以 ctx.kdtId 为准
        postData.seller = {
          ...postData.seller,
          kdtId: ctx.kdtId,
        };
      } else {
        const [isCheckQueryKdtId, isCheckQueryKdtIdBySeller] = await Promise.all([
          this.isInCPGray('check_trade_query_kdt_id').catch(() => false),
          this.isInCPGray('check_trade_query_kdt_id', postData.seller?.kdtId).catch(() => false),
        ]);
        if (isCheckQueryKdtId || isCheckQueryKdtIdBySeller) {
          ctx.logger.info(`【触发kdtId不一致校验】: ctx.kdtId: ${ctx.kdtId} postData.kdtId: ${postData.seller?.kdtId}`);
          ctx.json(9999, 'are you ok');
          return;
        }
      }
    }
    // apollo控制下单拦截逻辑
    const isInPreventWhileList = await isInGrayReleaseByKdtId(
      ctx,
      // 加价购的商品是否参与商品兑换优惠券计算
      { namespace: 'wsc-h5-trade.application', key: 'enableBillKdtIdInvalidPreventValid' },
      String(postData.seller?.kdtId)
    );

    try {
      // 攻击者调用下单接口截获
      // 群团团的买家身份kdtId === 0
      const isBuyerId0 = !this.buyer || !this.buyer.buyerId;
      const isKtdId0 = ctx.kdtId === 0;
      const isPrevent = isInPreventWhileList && postData.seller?.kdtId + '' !== ctx.kdtId;
      if (!postData.isQtt && (isBuyerId0 || isKtdId0 || isPrevent)) {
        let enableBillKdtIdInvalidPrevent = false;
        // apollo控制是否启用防御
        enableBillKdtIdInvalidPrevent =
          getTradeApolloConfig('wsc-h5-trade.application', 'enableBillKdtIdInvalidPrevent') === 'true';
        if (enableBillKdtIdInvalidPrevent) {
          ctx.logger.info(`ctxKdtId0 rootId-[${ctx.traceCtx && (ctx.traceCtx as any).parentId}]`, null, postData);
          ctx.json(10000, '暂时无法下单，请联系商家处理');
          return;
        }
      }
    } catch (err) {
      // console.log(err);
    }

    this.setPageCloudBizIds('buy', 'goodsId', itemIds.join('|'));

    // TODO 短信通知兼容
    // 由于目前后端的短信逻辑依赖前端传值，先保留这个值
    if (postData.config) {
      postData.config.receiveMsg = true;
    }

    // 爱逛 - 判断店铺是否加入担保交易
    const orderMark = get(postData, 'source.orderMark', '');
    if (orderMark === 'weapp_guang') {
      const securedTransactions = await new SecuredTXQueryService(ctx).querySecuredTransactionsStatus(ctx.kdtId);
      if (!securedTransactions) {
        ctx.logger.info('爱逛商家未加入担保交易', null, postData);
        throw new BusinessServiceError(108001001, '商家未加入担保交易，存在风险，当前无法下单');
      }
    }
    // controller方法里调用风控的校验接口进行校验，若返回不通过则阻断下单，成功则发起下单接口请求，风控接口异常则默认通过
    try {
      await this.settleSecondCheckBehaviorToken(ctx, postData);
    } catch (error) {
      const content = error.errorContent || {};
      const message = error.message || '';
      // 非法token 验证失败
      if (content.code === 135900028 || message === BEHAVIOR_ERROR_MSG) {
        ctx.logger.warn('下单页【行为组件】验证失败', error);
        return ctx.json(10000, '验证失败');
      }
    }

    try {
      if (!this.buyer || !this.buyer.buyerId) {
        ctx.logger.info(`ctxBuyerId0 rootId-[${ctx.traceCtx && (ctx.traceCtx as any).parentId}]`, null, postData.buyer);
      }
    } catch (err) {}

    /** ********************************************************************************************
     * 这个是为了做京东的定制需求特意提供的一个字段，京东需要脱敏手机号 15212341234 -> 152****1234
     * 但是默认正常情况下后端只会存储数字部分的数据，也就是说如果不设置这个 untouchedPhone 字段，那么
     * 后端存储的就是 1521234 (忽略了中间4个*)
     * untouchedPhone = 1 表示原样存储
     * untouchedPhone = any 如果不是1那么就按照默认处理，也就是忽略星号
     * 如果不传 untouchedPhone 字段，那么也是默认处理
     ******************************************************************************************** */
    const keepFieldsRaw = get(postData, 'cloudOrderExt.createOrderSettings.keepFieldsRaw', []);
    if (keepFieldsRaw.includes('receiverTel') && postData?.delivery?.address) {
      // 因为三方强制改写了地址信息，已经不是原本的地址数据了，因此不能再保留原本的地址id
      delete postData.delivery.address.id;
      delete postData.delivery.address.addressId;
      postData.delivery.address.untouchedPhone = 1;
    }

    try {
      postData.extensions = this.handleRechargeOrderPayParams(postData);
      // NOTE: 需要先单独调用创建订单，因为后续uic接口没有接入限流服务。避免大流量时交易触发限流，uic未限流挂了
      const createOrderResponse = postData.isQtt
        ? await new TradeServiceProvider(ctx).create(postData)
        : await new TradeService(ctx).createFast(postData);

      const requests = [];

      // 未签署用户协议时，需要自动签署掉
      const { user_privacy = {} } = this.ctx.getLocalSession() || {};
      if (!get(user_privacy, 'agreement_signed', false)) {
        requests.push(
          new UserAccountService(ctx)
            .userPrivacyAuth({
              authTypeList: [3],
              signTriggerLink: ctx.href,
              sessionId: ctx.sessionId,
              userId: this.buyerId,
              scene: {
                userAgent: ctx.userAgent,
                ipAddress: ctx.firstXff,
              },
            })
            .catch(() => {})
        );
      }

      await Promise.all(requests);

      ctx.fastJson(0, 'ok', createOrderResponse);
    } catch (error) {
      throw OrderBuyController.formatBillError(<NormalError>error);
    }
  }

  // 轮询异步下单的订单结果
  async postAsyncOrderResult(ctx: Context) {
    const postData = ctx.getPostData();
    const { asyncOrderBookKey: bookKey } = postData;
    try {
      const result = await new TradeService(ctx).queryAsyncOrderResult(bookKey);
      // 异步下单轮询接口不返回 orderNo 字段 而是 parentOrderNo
      const { parentOrderNo = '' } = result;
      result.orderNo = parentOrderNo;
      ctx.json(0, 'ok', result);
    } catch (error) {
      const content = error.errorContent;
      if (content) {
        throw new BusinessServiceError(content.code, content.msg);
      } else {
        throw error;
      }
    }
  }

  async postPrePayJson(ctx: Context) {
    const params = ctx.getPostData<TradePayRequestDTO>();

    Object.assign(params, {
      kdtId: ctx.kdtId,
      buyerId: this.buyerId,
      userAgent: ctx.userAgent,
      receiverTel: this.buyer.buyerPhone,
    });

    try {
      const data = await new TradeService(ctx).prepay(params);
      ctx.json(0, '', data);
    } catch (error) {
      const content = error.errorContent;
      const errorMsg = error.message || '';
      const { isLimitMsg, transformLimitMsg } = OrderBuyController;

      // tether 层限流，模拟 429
      if (isLimitMsg(errorMsg)) {
        throw new BusinessServiceError(429, transformLimitMsg(errorMsg));
      } else if (content) {
        throw new BusinessServiceError(content.code, transformLimitMsg(content.msg));
      } else {
        throw error;
      }
    }
  }

  /**
   * 获取自提点列表
   * @description v1 版本，返回下划线结构
   * @deprecated v2.10 - v2.18小程序在使用
   */
  async postSelfFetchListJson(ctx: Context) {
    const postData = ctx.getPostData();

    // 兼容一下旧版小程序传 'undefined' 的问题
    // 后续可以去掉
    if (postData && postData.cityCode === 'undefined') {
      delete postData.cityCode;
    }

    const data = await new DeliveryService(ctx).getSelfFetchList(postData);

    ctx.r(0, '', data);
  }

  // 获取自提点列表
  // v2 版本，返回驼峰结构
  async postSelfFetchListV2Json(ctx: Context) {
    const postData = ctx.getPostData();
    const { buyerId = 0 } = this.buyer;
    const { page, concurrenceFetchCount } = postData;
    postData.buyerId = buyerId;
    // 并发请求，e.g. page: 2, concurrenceFetchCount: 4, 请求 2-5 页
    if (concurrenceFetchCount) {
      const data = await Promise.all(
        Array.from({ length: concurrenceFetchCount }).map((_, index) => {
          postData.page = page + index;
          return new DeliveryService(ctx).getSelfFetchList(postData);
        })
      );

      ctx.json(
        0,
        '',
        data.reduce((acc, cur) => {
          const { total, list } = acc;
          return {
            total,
            list: list.concat(cur.list),
          };
        })
      );
    } else {
      const data = await new DeliveryService(ctx).getSelfFetchList(postData);
      ctx.json(0, '', data);
    }
  }

  // 获取自提点列表，带最近使用网点
  // v3 版本，增加最近使用网点参数
  async postSelfFetchListV3Json(ctx: Context) {
    const postData = ctx.getPostData();
    const { buyerId = 0 } = this.buyer;
    postData.buyerId = buyerId;
    const data = await new DeliveryService(ctx).listSelfFetchWithRecentlyUsed(postData);

    ctx.json(0, '', data);
  }

  // 获取默认自提点
  async getDefaultSelfFetchJson(ctx: Context) {
    const postData = ctx.getPostData();

    const defaultSelfFetch = await new DeliveryService(ctx).getDefaultSelfFetch({
      ...postData,
      buyerId: this.buyerId,
    });

    /**
     * 如果 defaultSelfFetch 是空对象，data 为 null，否则为 defaultSelfFetch
     * https://jira.qima-inc.com/browse/ONLINE-806129
     * ONLINE-806129 这个JIRA的原因是后端返回的是空对象（用户没有默认地址时）,前端只判断了返回值是否是对象，并没有
     * 校验是否是真正的地址，因此可能出现在client端校验失败的情况，为了避免这种情况，所以这里进行了自提点校验
     */
    const data = Object.keys(defaultSelfFetch ?? {}).length ? defaultSelfFetch : null;

    ctx.json(0, '', data);
  }

  // 获取自提点单个地址
  // v1 版本，返回下划线结构
  async postSelfFetchJson(ctx: Context) {
    const postData = ctx.getPostData();
    const data = await new DeliveryService(ctx).getSelfFetch(postData);

    ctx.r(0, '', data);
  }

  // 获取自提点单个地址
  // v2 版本，返回驼峰结构
  async postSelfFetchV2Json(ctx: Context) {
    const postData = ctx.getPostData();
    const data = await new DeliveryService(ctx).getSelfFetch(postData);

    ctx.json(0, '', data);
  }

  // 获取店铺自提设置
  // v1 版本，返回下划线结构
  async postSelfFetchTimeJson(ctx: Context) {
    const postData = ctx.getPostData();
    const data = await new SelfFetchService(ctx).getSelfFetchTime(postData);

    ctx.r(0, '', data);
  }

  // 获取店铺自提设置
  // v2 版本，返回驼峰结构
  async postSelfFetchTimeV2Json(ctx: Context) {
    const postData = ctx.getPostData();
    const data = await new SelfFetchService(ctx).getSelfFetchTime(postData);

    ctx.json(0, '', data);
  }

  // 获取附近可送门店
  async postMatchOfflineJson(ctx: Context) {
    const postData = ctx.getPostData();
    const data = await new TradeService(ctx).matchOffline(postData);

    ctx.r(0, '', data);
  }

  // 获取外部券/积分信息
  async getAssetForOrderJson(ctx: Context) {
    const queryData = ctx.request.query || {};

    queryData.kdtId = ctx.kdtId;
    queryData.buyerId = this.buyerId;

    const data = await new OutAssetService(ctx).getAssetForOrder(queryData);

    ctx.r(0, '', data);
  }

  async tryExchangeDouyinCoupon(ctx: Context) {
    const postData = ctx.getPostData();
    const { kdtId } = ctx;

    const [shopMetaInfo, hasPlatformCouponAbility] = await Promise.all([
      this.getShopMetaInfo(ctx),
      new ShopAbilityInfoService(ctx)
        .queryShopAbilityInfo([ctx.kdtId, 'platform_coupon_verification_ability'])
        .catch(() => ({})),
    ]);
    const isRetailShop = checkRetailShop(shopMetaInfo);

    // 零售店铺 且有 卡券管理核销能力
    if (isRetailShop && hasPlatformCouponAbility) {
      try {
        const result = await new RetailTradeMiscCouponService(ctx).exchangeByThirdCodeValue({
          kdtId: +kdtId,
          userId: this.buyerId,
          couponCode: postData.userCouponCode,
          platform: 2, // 抖音券为 2
        });
        return result;
      } catch (e) {
        const { code, msg } = e as IDouyinCouponExchangeError;
        // 抖音券需要处理的几个 code 由后端提供
        const douyinCouponExchangeErrorTypes = [233200004, 234031032, 234031033, 234031034];
        // 处理指定的错误码
        if (douyinCouponExchangeErrorTypes.includes(code)) {
          return ctx.json(code, msg);
        }
        return false;
      }
    } else {
      return false;
    }
  }

  // 兑换优惠券
  async postExchangeCoupon(ctx: Context) {
    const postData = ctx.getPostData();
    postData.buyer = this.buyer;

    const douyinCouponResult = await this.tryExchangeDouyinCoupon(ctx);

    // 抖音兑换流程直接中断，不需要走后续有赞兑换流程
    if (typeof douyinCouponResult !== 'boolean') return;

    // 兑换成功，直接返回
    if (douyinCouponResult) {
      ctx.success('ok');
      return;
    }

    const data = await new AssetsService(ctx).exchangeCoupon(postData);
    ctx.json(0, '', data);
  }

  // 获取返储值金说明内容
  async postRebateMessage(ctx: Context) {
    const postData = ctx.getPostData();
    postData.kdtId = ctx.kdtId;
    if (ctx.isWeapp) {
      postData.platform = 2;
    } else {
      postData.platform = 1;
    }
    const result = await new OrderRebateService(ctx).queryShopUserCenterName(postData);
    ctx.json(0, '', result);
  }

  async postUseAssetJson(ctx: Context) {
    const postData = ctx.getPostData();
    if (postData.coupon) {
      postData.coupon.userId = this.buyerId;
      postData.coupon.kdtId = ctx.kdtId;
    }
    if (postData.point) {
      postData.point.userId = this.buyerId;
      postData.point.kdtId = ctx.kdtId;
    }

    const data = await new OutAssetService(ctx).useAsset(postData);

    ctx.json(0, '', data);
  }

  /**
   * 小程序下单
   * @deprecated 只有2.9以前版本小程序在调用
   */
  async postWeappBillJson(ctx: Context) {
    const postData = ctx.getPostData();

    // 调用订购中心，判断应用是否过期，只有小程序需要
    // 有赞公共版小程序不需要校验 weapp_youzan
    const billSource = postData.billSource || {};
    const orderMark = billSource.orderMark || '';
    if (orderMark !== 'weapp_youzan') {
      const appData = await new MarketRemoteService(ctx).findApplicationStatus(ctx.kdtId);

      if (!appData || !appData.isValid) {
        return ctx.json(10000, '店铺暂停营业，无法下单');
      }
    }

    try {
      const data = await new BillService(ctx).createWeappOrder(postData);
      ctx.r(0, '', data);
    } catch (e) {
      const { errorContent } = e;
      if (!errorContent) {
        // 未知异常
        // eslint-disable-next-line no-console
        console.error('createOrder unexpected error: ', e);
        throw e;
      } else {
        // 异常太多，可控异常不需处理
        // eslint-disable-next-line no-console
        console.error(e.message, e);
        ctx.json(errorContent.code, errorContent.msg);
      }
    }
  }

  async postPayChannelsJson(ctx: Context) {
    const postData = ctx.getPostData();
    const buyerId = ctx.getLocalSession('buyer_id') || ctx.getLocalSession('youzan_user_id');

    const cashierList = await new WscPayService(ctx).getWeappPayChannels(postData.partner_id, {
      cashierSign: postData.cashier_sign,
      cashierSalt: postData.cashier_salt,
      prepayId: postData.prepay_id,
      buyerId: buyerId + '', // 必须是字符串
      environment: 'WX_APPLET', // 小程序环境
    });

    const payChannels = (cashierList && cashierList.pay_channels) || [];
    // TODO 临时处理 信用卡支付改为银行卡支付 后续收银台增加支付方式 20180327
    payChannels.forEach((channel: PayChannelVO) => {
      if (channel.pay_channel === 'CREDIT_CARD') {
        channel.pay_channel_name = '银行卡支付';
      }
    });
    ctx.r(0, '', cashierList);
  }

  async postPreorderPayJson(ctx: Context) {
    const postData = ctx.getPostData();
    const buyerId = `${this.buyerId}`;
    const buyer = ctx.getLocalSession('buyer') || {};

    const payData = {
      cashierSign: postData.cashier_sign,
      cashierSalt: postData.cashier_salt,
      prepayId: postData.prepay_id,
      payTool: postData.pay_tool,
      buyerId,
      wxSubOpenId: postData.wx_sub_open_id,
      payToken: postData.pay_token,
      acceptPrice: postData.accept_price || 0,
      newPrice: postData.new_price || -1,
      valueCardNo: postData.value_card_no,
      bizExt: postData.out_biz_ctx,
      environment: 'WX_APPLET', // 小程序环境
    };

    // 货到付款
    if (postData.pay_tool === 'CASH_ON_DELIVERY') {
      // 货到付款直接请求交易接口，这里不处理
      ctx.json(10000, '暂不支持该支付方式');
      return;
    }

    if (postData.pay_tool === 'ECARD') {
      Object.assign(payData, { account: buyer.mobile });
    } else if (postData.pay_tool === 'CREDIT_CARD') {
      // 信用卡处理
      Object.assign(payData, { pay_router: 'credit_mock' });
    }

    const payResult = await new WscPayService(ctx).pay(postData.partner_id, payData);
    ctx.r(0, '', payResult);
  }

  // 身份证实名校验
  async postVerifyIdcardJson(ctx: Context) {
    const params = ctx.getPostData();
    const { fansNickname = '匿名' } = this.buyer || {};
    Object.assign(params, {
      buyerId: this.buyerId,
      // NOTE: fansNickname可能会是空字符串
      buyerName: fansNickname || '匿名',
      kdtId: ctx.kdtId,
    });
    const { iv, key, idCardName, idCardNumber } = params;
    if (iv && key) {
      Object.assign(params, {
        idCardName: crypto.aes.decrypt({ encrypted: idCardName, key, iv }),
        idCardNumber: crypto.aes.decrypt({ encrypted: idCardNumber, key, iv }),
      });
    }
    const identityVerifyService = new IdentityVerifyService(ctx);
    const data = await identityVerifyService.verify(params);
    ctx.json(0, '', data);
  }

  /**
   * 获取加价购数据
   */
  public async postPlusBuyJson(ctx: Context) {
    const query = ctx.getPostData<QueryExchangeGoodsParamsDTO>();
    // NOTE: 来自小程序的请求有时参数不全，可能是vanx在commit后马上dispatch没取到数据，需要做个兼容
    if (!query.activityId || !query.kdtId) {
      throw new BusinessServiceError(9999, '网络错误，请稍后再试');
    }

    query.buyerId = this.buyerId;
    const data = await new TradeService(ctx).queryPlusBuyExchangeInfo(query);
    ctx.json(0, '', {
      ...data,
      enableGoodsExchangeCalc: true,
    });
  }

  public async sceneCheck(ctx: Context) {
    const scene = ctx.getQuery('scene');
    const request = {
      scene: +scene,
      kdtId: ctx.kdtId,
    };
    ctx.logger.info('[微信自定义版购物组件] scene/check 调用开始 buyerId: ' + ctx.buyerId, null, {
      request,
    });
    const response = await new WechatMiniProgramShoppingComponentService(ctx).needSyncWechat(request);
    ctx.logger.info('[微信自定义版购物组件] scene/check 调用成功 buyerId: ' + ctx.buyerId, null, {
      request,
      response,
    });
    ctx.json(0, '', response);
  }

  public async sceneCheckCompatible(ctx: Context) {
    const scene = ctx.getQuery('scene');
    const request = {
      scene: +scene,
      kdtId: ctx.kdtId,
    };
    ctx.logger.info('[微信自定义版购物组件] scene/check 调用开始 buyerId: ' + ctx.buyerId, null, {
      request,
    });
    const response = await new WechatMiniProgramShoppingComponentService(ctx).needSyncWechatNew(request);
    setupCdn(ctx, 15);
    ctx.logger.info('[微信自定义版购物组件] scene/check 调用成功 buyerId: ' + ctx.buyerId, null, {
      request,
      response,
    });
    ctx.json(0, '', response);
  }

  public async getWxOrderInfo(ctx: Context) {
    const orderNo = ctx.getQuery('orderNo');
    const request = {
      orderNo,
      buyerId: ctx.buyerId,
    };
    ctx.logger.info('[微信自定义版购物组件] orderInfo查询 调用开始 buyerId: ' + ctx.buyerId, null, {
      request,
    });
    const response = await new WechatMiniProgramShoppingComponentService(ctx).getOrderDetail(request);
    ctx.logger.info('[微信自定义版购物组件] orderInfo查询 调用成功 buyerId: ' + ctx.buyerId, null, {
      request,
      response,
    });
    ctx.json(0, '', response);
  }

  private isGuang() {
    const tpps = this.ctx.cookies.get('tpps') || '';
    const REG = /^pf\./;
    const platformSource = REG.test(tpps) ? tpps.replace(REG, '') : '';
    return platformSource === 'GUANG';
  }

  /**
   * 处理prepare数据
   */
  private async setPrepare(
    prepare: SimpleOrderConfirmationVO | SimpleOrderPaymentPreparationVO,
    options: PrepareOptions,
    setGlobal = true
  ) {
    prepare.tradeConfirmation = prepare.tradeConfirmation || {};
    prepare.displayConfig = prepare.displayConfig || ({} as IDisplayConfigVO);
    const { tradeConfirmation, displayConfig } = prepare;
    tradeConfirmation.orderPayment = tradeConfirmation.orderPayment || {};
    const { orderItems = [], orderPayment = {} } = tradeConfirmation;

    // 权益卡/付费等级字段处理
    if (tradeConfirmation?.displayCard) {
      tradeConfirmation.displayCard = formatDisplayCard(tradeConfirmation.displayCard);
    }

    // 预售商品自提/配送选择时间提示
    const preSaleTips = formatPreSaleTips(tradeConfirmation.orderItems, prepare.deliveryTimeBucket);
    if (preSaleTips) {
      displayConfig.selfFetchTips = preSaleTips.selfFetchTips;
      displayConfig.deliveryTips = preSaleTips.deliveryTips;
    }

    const { ctx } = this;
    const promiseQueue = [];

    // 影响正常商品title，jira： https://jira.qima-inc.com/browse/ONLINE-509190
    // xss防御
    // orderItems.forEach((item) => {
    //   item.title = escape(item.title);
    // });

    if (options.isUsePointDeduction) {
      displayConfig.isUsePointDeduction = options.isUsePointDeduction;
    }

    if (options.riskWarnShop) {
      const { riskWarnShop } = options;
      displayConfig.riskWarnShopPrompt = riskWarnShop.data && riskWarnShop.data.match ? riskWarnShop.data.note : '';
    }

    /**
     * copyright中的店铺logo
     * @deprecated 理论上下单页copyright被公共footer接管后无需设置了
     */
    const shopSetting: IPureObject = this.ctx.getState('shopSettings' as never) || {};
    displayConfig.copyrightPicUrl = shopSetting.isLogoCustomized ? shopSetting.customizedLogo : '';

    // NOTE: 分布极速下单禁用优惠券、储值卡、礼品卡、积分抵扣等营销活动
    if (options.isPartialFastAdOrder) {
      tradeConfirmation.payGiftCards = [];
      tradeConfirmation.payValueCards = [];
      tradeConfirmation.unavailablePayGiftCards = [];
      tradeConfirmation.unavailablePayValueCards = [];
      displayConfig.isUsePointDeduction = false;
      tradeConfirmation.pointDeduction = {};
      tradeConfirmation.coupons = [];

      promiseQueue.push(
        this.getWechatPayDiscount()
          .then((wechatPayDiscount) => {
            orderPayment.wechatPayDiscount = wechatPayDiscount;
          })
          .catch(() => {})
      );

      // 非虚拟商品 且 商家配置可用货到付款时 加入货到付款支付
      if (!orderItems.some((item) => item.virtual)) {
        promiseQueue.push(
          this.canCashOnDelivery()
            .then((cashOnDelivery) => {
              displayConfig.cashOnDelivery = cashOnDelivery;
            })
            .catch(() => {})
        );
      }
    }

    // 三方 App 专有配置
    if (options.isThirdApp) {
      promiseQueue.push(
        this.callService('iron-base/yop.AppConfigRemoteService', 'getAppConfigInfo', ctx.platform).then(
          (appConfigInfo) => {
            displayConfig.hideShopLink = (appConfigInfo || {}).hideShopLink;
          }
        )
      );
    }

    await Promise.all(promiseQueue);

    const {
      proxy_weixin_openid: wxSubOpenId = '', // 大账号 openId
      verify_weixin_openid: wxSelfOpenId = '', // 自有粉丝 openId
    } = ctx.getLocalSession();

    orderPayment.wechatPayParams = {
      wxSubOpenId,
      wxSelfOpenId,
    };

    if (setGlobal) {
      ctx.setGlobal({ prepare });
    }
  }

  /**
   * 设置环境信息
   */
  private setEnv(options: IPureObject) {
    const { ctx } = this;
    ctx.setGlobal('env', {
      ...options,
      platform: ctx.platform,
      isWeixin: ctx.isWeixin,
      isSwanApp: ctx.isSwanApp,
      isWeapp: ctx.isWeapp,
      isTTApp: ctx.isTTApp,
      isAlipayApp: ctx.isAlipayApp, // 阿里系支付宝小程序环境内
      isQQApp: ctx.isQQApp,
      isKsApp: ctx.isKsApp,
      theme: ctx.getState('globalTheme' as never),
      isXhsApp: ctx.isXhsApp,
    });
  }

  /**
   * 查询是否能货到付款
   */
  private async canCashOnDelivery() {
    const { ctx } = this;
    const payWaysService = new PayWaysService(ctx);

    const { userNo } = await payWaysService.queryUserNoBySourceId({
      sourceId: SourceIdEnum.KDTID,
      userId: ctx.kdtId,
    });
    const payTools = await payWaysService.getAvailablePayToolListFromCore({
      partnerId: ctx.getConfig('PARTNER_ID'),
      userNo,
    });

    if (payTools.some((item) => item.payTool === 'CASH_ON_DELIVERY')) {
      return true;
    }

    return false;
  }

  /**
   * 设置微信支付时的折扣
   */
  private async getWechatPayDiscount() {
    const { ctx } = this;
    let discount = 100;
    try {
      discount = await new DiscountService(ctx).getDiscount({
        kdtId: ctx.kdtId,
      });
    } catch (err) {
      // empty
    }

    return discount;
  }

  // 跳转到限流页面
  private jumpWaitingPage() {
    const callbackURL = `https://cashier.youzan.com${this.ctx.request.url}`;
    const waitingPage = 'https://h5.youzan.com/v3/shop/mass-flow-waiting';
    const redirectCount = args.get('waitRedirectCount', callbackURL) || 0;
    const redirectURL = args.add(waitingPage, {
      callbackURL,
      redirectCount,
    });

    return this.ctx.redirect(redirectURL);
  }

  private checkFans(prefix: string, forceLog = false) {
    try {
      const { ctx } = this;
      if (ctx.platform === 'mobile') {
        return;
      }

      const session = ctx.getLocalSession();
      const { rootId } = (ctx.traceCtx as IPureObject) || {};
      const { platform = ({} as any) as SessionCachePlatform, fans_type, fans_id } = session;

      if (forceLog) {
        // NOTE: 创建订单有时session内容有些异常，上报一下session内容留作证据
        ctx.logger.info(`
          ${prefix}
          ${rootId}
          sessionId: ${ctx.sessionId}
          session内容: ${JSON.stringify(session)}
        `);
      }

      if (!fans_id || !fans_type || !platform.platform_type || !platform.platform_fans_id) {
        ctx.logger.warn(`
          ${prefix}粉丝信息缺失
          ${rootId}
          sessionId: ${ctx.sessionId}
          session内容: ${JSON.stringify(session)}
        `);
      }
    } catch (err) {
      // empty
    }
  }

  // 是否为限流错误
  static isLimitMsg(msg?: string): boolean {
    if (msg) {
      return msg.indexOf('Over traffic limit') !== -1;
    }

    return false;
  }

  // 将限流错误转换为用户友好的文案
  static transformLimitMsg(msg: string): string {
    if (OrderBuyController.isLimitMsg(msg)) {
      return '店铺太火爆啦，请稍后重试';
    }

    return msg;
  }

  static formatBillError(e: NormalError): NormalError {
    const content = e.errorContent;
    const errorMsg = e.message || '';
    let code = (content && content.code) || e.code;
    let msg = (content && content.msg) || e.msg;

    const { isLimitMsg, transformLimitMsg } = OrderBuyController;

    // 重构：遵循原语义，先取e.message 如果isLimitMsg，那么转化e.message文案；否则判断处理content.msg，这时code保留
    const isMessageInLimit = isLimitMsg(errorMsg);
    // tether 层限流，模拟 429；处理文案
    if (isMessageInLimit || isLimitMsg(msg)) {
      code = isMessageInLimit ? 429 : code;
      msg = isMessageInLimit ? transformLimitMsg(errorMsg) : transformLimitMsg(msg);

      e.code = code;
      e.errorContent = { code, msg };
    }
    return e;
  }

  private async getParsePrepareOptions({
    isPartialFastAdOrder,
    orderItems,
    isThirdApp,
  }: IGetParsePrepareOptionsParamsVO) {
    const promiseQueue = [];
    const extendOptions = ({} as any) as IParsePrepareOptionsVO;
    if (isPartialFastAdOrder) {
      promiseQueue.push(
        this.getWechatPayDiscount()
          .then((wechatPayDiscount) => {
            extendOptions.wechatPayDiscount = wechatPayDiscount;
          })
          .catch(() => {})
      );

      // 非虚拟商品 且 商家配置可用货到付款时 加入货到付款支付
      if (!orderItems.some((item) => item.virtual)) {
        promiseQueue.push(
          this.canCashOnDelivery()
            .then((cashOnDelivery) => {
              extendOptions.cashOnDelivery = cashOnDelivery;
            })
            .catch(() => {})
        );
      }
    }

    // 三方 App 专有配置
    if (isThirdApp) {
      promiseQueue.push(
        this.callService('iron-base/yop.AppConfigRemoteService', 'getAppConfigInfo', this.ctx.platform).then(
          (appConfigInfo) => {
            extendOptions.hideShopLink = (appConfigInfo || {}).hideShopLink;
          }
        )
      );
    }

    await Promise.all(promiseQueue);

    const {
      proxy_weixin_openid: wxSubOpenId = '', // 大账号 openId
      verify_weixin_openid: wxSelfOpenId = '', // 自有粉丝 openId
    } = this.ctx.getLocalSession();

    extendOptions.wechatPayParams = {
      wxSubOpenId,
      wxSelfOpenId,
    };

    return extendOptions;
  }

  // 获取直播活动内容
  async getLiveActivity(ctx: Context) {
    const postData = ctx.getPostData();
    postData.shopId = ctx.kdtId;
    const result = await new LivePlanManageService(ctx).getActivityForOrderMark(postData);
    ctx.json(0, '', result);
  }

  // 判断和以及进行行为组件token校验
  async settleSecondCheckBehaviorToken(ctx: Context, postData: any) {
    // 行为组件下单页接入
    const enableUseOrderBehaviorInfo = await new ShopAbilityInfoService(ctx) // 调用店铺能力判断是否接入行为组件，RT为8ms
      .queryShopAbilityInfo([
        postData.seller?.kdtId ? postData.seller?.kdtId + '' : ctx.kdtId,
        'risk_prevention_of_marketing_ability',
      ])
      .catch(() => {
        return {};
      });
    // 是否接入行为组件
    const { valid: enableUseOrderBehaviorParam = false } = enableUseOrderBehaviorInfo;
    // 增加sessionId传参
    const behaviorTokenParams = {
      ...postData.behaviorOrderInfo,
      sessionId: ctx.sessionId,
    };
    // 先判断白名单
    if (enableUseOrderBehaviorParam) {
      // 仅当小程序原生情况下才进行版本校验
      if (ctx.isWeappNative) {
        await this.weappVersionCheck(ctx, behaviorTokenParams, postData);
      } else {
        // 当接口返回data:false的时候 也认为不通过
        const behaviorData = await new BehaviorCaptchaService(ctx).secondCheckBehaviorToken(behaviorTokenParams);
        if (!behaviorData) {
          throw new Error(BEHAVIOR_ERROR_MSG);
        }
      }
    }
  }

  // 版本校验
  async weappVersionCheck(ctx: Context, behaviorTokenParams: any, postData: any) {
    const versionParam = await getTradeApolloConfig(
      'wsc-h5-trade.gray-release',
      'orderBehaviorMpVersion' // 接入行为组件的小程序版本
    );
    const WSC_VERSION = JSON.parse(versionParam).wsc;
    const RETAIL_VERSION = JSON.parse(versionParam).retail;
    const mpVerionPostData = {
      kdtId: postData.seller?.kdtId ? postData.seller?.kdtId : +ctx.kdtId,
      accountType: 2,
      businessType: 1,
    };
    const mpVerionData = await new MpVersionService(ctx).getMpVersion(mpVerionPostData);
    // 经过和账号侧沟通，如果mpVerionData返回undefined=没有小程序版本 说明可能是mock的，也可能是商家存在解绑后又绑定的情况  所以不做拦截
    if (!mpVerionData?.releaseVersion) {
      this.ctx.logger.warn('【下单页行为组件】该商家不存在小程序版本');
      return;
    }
    // 账号接口返回的shopType不准，所以需要再调店铺那边的接口查
    const shopMetaInfo = await this.callService('iron-base/shop.ShopMetaReadService', 'getShopMetaInfo', ctx.kdtId);
    // 分零售小程序和微信小程序
    if (
      shopMetaInfo &&
      ((shopMetaInfo.shopType === 0 && CompareVersion.isGte(mpVerionData.releaseVersion, WSC_VERSION)) ||
        (shopMetaInfo.shopType === 7 && CompareVersion.isGte(mpVerionData.releaseVersion, RETAIL_VERSION)))
    ) {
      // 当接口返回data:false的时候 也认为不通过
      const behaviorData = await new BehaviorCaptchaService(ctx).secondCheckBehaviorToken(behaviorTokenParams);
      if (!behaviorData) {
        throw new Error(BEHAVIOR_ERROR_MSG);
      }
    }
    //  其他渠道以及版本均不校验
  }

  // 修改订单缓存信息
  async modifyCacheOrderJson(ctx: Context) {
    const postData = ctx.getPostData();
    const res = await new TradeService(ctx).modifyCacheOrder(postData);
    ctx.json(0, '', res);
  }

  // 修改订单内的商品信息
  async queryMultiOrderPreparationV2(ctx: Context) {
    const bookKey = ctx.getQuery('bookKey');
    const res = await new TradeService(ctx).queryMultiOrderPreparationV2(bookKey);
    ctx.json(0, '', res);
  }

  private getCouponOverConfig(ctx: Context) {
    return ctx.apolloClient.getConfig({
      // @ts-ignore appId是要传的
      appId: 'wsc-pc-ump',
      namespace: 'wsc-pc-ump.whiteList',
      key: 'couponOverConfig',
    });
  }

  /**
   * 获取店铺信息
   * @param ctx Context
   * @returns
   */
  private async getShopMetaInfo(ctx: Context) {
    const { kdtId } = ctx;
    // 先从state中获取店铺信息 小程序场景下可能直接调接口 state还未初始化 在state中没有的情况下 调用服务获取店铺信息
    const shopMetaInfo =
      ctx.getState('shopMetaInfo' as never) ||
      this.callService('iron-base/shop.ShopMetaReadService', 'getShopMetaInfo', kdtId);
    return shopMetaInfo || {};
  }

  /**
   * 根据sl标返回导购信息
   * @param ctx Context
   * @returns
   */
  async getEarliestGuideOpenId(ctx: Context) {
    const { sl } = ctx.getQueryData();
    const { kdtId } = ctx;
    const params: IGuideFromSlRequest = {
      sl,
      kdtId,
    };

    const result = await new GuideTradeSupportService(ctx).getGuideFromSl(params);

    ctx.json(0, 'ok', result);
  }

  private async judgeInCouponOverWhitelist(ctx: Context) {
    const { kdtId } = ctx;
    // 优惠券分流名单
    const [couponOverConfig, shopMetaInfo] = await Promise.all([
      this.getCouponOverConfig(ctx),
      this.getShopMetaInfo(ctx),
    ]);
    const { shopType, rootKdtId = kdtId } = shopMetaInfo;
    const { blacklist, whitelist, shopType: shopTypeArr, kdtPercent } = couponOverConfig;
    // 可见行优先级： (支持的店铺类型 并且 不在黑名单中) && (在白名单中 > kdtId百分比 )
    const ceil = 1000;
    const promotion = kdtPercent > ceil ? ceil : Math.max(kdtPercent, 0);
    return (
      shopTypeArr.includes(shopType) &&
      !blacklist.includes(+rootKdtId) &&
      (whitelist.includes(+rootKdtId) || ceil - (+rootKdtId % ceil) <= promotion)
    );
  }

  // 判断是否支持前置收银台功能
  private async checkUsePreCashierPay(
    ctx: Context,
    prepare: SimpleOrderPreparationVO | string,
    trace: { orderNo?: string; bookKey?: string }
  ) {
    const { kdtId } = ctx;
    const traceStr =
      `[前置收银台功能开关]  kdtId=${kdtId}, buyerId=${this.buyerId}, ` + trace.bookKey
        ? `bookKey=${trace.bookKey}`
        : `orderNo=${trace.orderNo}`;

    // 判断后端开关是否允许展示前置收银台
    const getBackendShow = async () => {
      let isBackendShow = false;
      try {
        let isShowPayWay = 'false';
        if (typeof prepare === 'string') {
          isShowPayWay = findJsonValue(prepare, 'tradeConfirmation.extra.IS_SHOW_PAY_WAY') || 'false';
        } else {
          const { extensions, tradeConfirmation } = (prepare as any) || {};
          isShowPayWay = extensions?.IS_SHOW_PAY_WAY || tradeConfirmation?.extra?.IS_SHOW_PAY_WAY || 'false';
        }

        isBackendShow = JSON.parse(isShowPayWay) || false;
      } catch (error) {}

      return isBackendShow;
    };

    // 判断切流白名单
    const getWhiteListShow = async () => {
      const platform = ctx.isWeappNative ? 'weapp' : ctx.platform;
      const mapPlatformToKey: Record<string, string> = {
        // mobile: 'usePreCashierPay_h5', // 浏览器
        weixin: 'usePreCashierPay_h5', // 微信h5
        weapp: 'usePreCashierPay_weapp', // 微信小程序
      };
      const key = mapPlatformToKey[platform];
      const isInWhiteList = key
        ? await isInGrayReleaseByKdtId(ctx, { namespace: 'wsc-h5-trade.gray-release', key }, kdtId)
        : false;

      return { isInWhiteList, platform };
    };

    // 判断店铺类型
    const getShopType = async () => {
      const shopMetaInfo = await this.getShopMetaInfo(ctx);
      const { isPureWscSingleStore } = createWscIsolateCheck(shopMetaInfo);
      const isRetailStore = checkRetailShop(shopMetaInfo);

      return [isPureWscSingleStore, isRetailStore];
    };

    // 判断小程序版本是否支持
    const getIsWeappVersionSupport = async () => {
      const weappVersion = ctx.mpVersion;
      const minWeappVersion =
        getTradeApolloConfig('wsc-h5-trade.gray-release', 'usePreCashierPay_weappVersion') || '999.999.999'; // 无法从apollo获取版本号时，取最大版本号以屏蔽前置收银台

      let isWeappVersionSupport = false;
      try {
        // 非标准版本号会报错，用try包一下
        isWeappVersionSupport = compareVersions(weappVersion, minWeappVersion) >= 0;
      } catch (error) {}
      return { isWeappVersionSupport, minWeappVersion, weappVersion };
    };

    // 检查后端是否有下单流程相关的扩展点
    const getBackendCustomized = async () => {
      let isBackendCustomized = true;
      let extensionIdList: number[] = [];
      let tradeExtensionIdList: number[] = [4, 746, 747, 5, 748, 749];

      try {
        extensionIdList = await new ExtensionPointService(ctx).getExtensionIdList(kdtId);

        const list = getTradeApolloConfig('wsc-h5-trade.application', 'tradeExtensionIdList');
        tradeExtensionIdList = JSON.parse(list) || [];

        isBackendCustomized = extensionIdList.some((id) => tradeExtensionIdList.includes(id));
      } catch (error) {
        this.ctx.logger.warn(`${traceStr}, 查询后端扩展点列表失败: ${JSON.stringify(error)}`);
      }

      return { isBackendCustomized, tradeExtensionIdList, extensionIdList };
    };

    // 获取abTest结果
    // 计划2022.5上旬下线，产品 @虹静，交易开发 @风来
    const getAbTestConfig = async () => {
      const abTestConfig = ctx.ABTestClient?.getTest('trade-buy-pre-cashier', String(this.buyerId));

      return abTestConfig;
    };

    // 检查前置条件是否满足，例如：后端开关、切流名单、店铺类型
    const checkPreCondition = async () => {
      const [isBackendShow, { isInWhiteList, platform }, [isPureWscSingleStore, isRetailStore]] = await Promise.all([
        getBackendShow(),
        getWhiteListShow(),
        getShopType(),
      ]);

      // 只要定制了待支付或下单其中一个页面，就禁用前置收银台
      const isCloudCustomized =
        (await isCloudDesign(ctx as any, { kdtId: ctx.kdtId, pageName: 'to-pay' })) ||
        (await isCloudDesign(ctx as any, { kdtId: ctx.kdtId, pageName: 'buy' }));

      const result: WeappPrepareV2VO['usePreCashierPay'] = {
        value:
          isBackendShow && // 后端开关
          isInWhiteList && // 切流白名单
          (isPureWscSingleStore || isRetailStore) && // 店铺类型
          !isCloudCustomized && // 不是定制商家
          ctx.hostApp === 'weixin', // 必须在微信环境中
        conditions: {
          platform,
          isBackendShow,
          isInWhiteList,
          isPureWscSingleStore,
          isRetailStore,
          hostApp: ctx.hostApp,
        },
      };

      // 如果是微信小程序环境，则需要判断小程序版本是否支持
      if (ctx.isWeappNative) {
        const { isWeappVersionSupport, minWeappVersion, weappVersion } = await getIsWeappVersionSupport();
        result.value = result.value && isWeappVersionSupport;
        result.conditions = {
          ...result.conditions,
          isWeappVersionSupport,
          minWeappVersion,
          weappVersion,
        };
      }

      return result;
    };

    // 检查后置条件是否满足，例如：是否定制、网店充值推荐开关
    const checkPostCondition = async () => {
      const [
        { isBackendCustomized, tradeExtensionIdList, extensionIdList },
        isUseRantaBuyPage,
        isUseRantaPayPage,
      ] = await Promise.all([
        getBackendCustomized(),
        this.isUseRantaBuyPage(), // 检查h5是否有定制
        this.isUseRantaPayPage(), // 检查h5是否有定制
      ]);

      const result: WeappPrepareV2VO['usePreCashierPay'] = {
        value:
          !isBackendCustomized && // 后端是否有下单流程相关的扩展点
          isUseRantaBuyPage && // h5下单页是否中台化及是否定制
          isUseRantaPayPage, // h5待支付页是否中台化及是否定制
        conditions: {
          isBackendCustomized,
          isBuyPageCustomized: !isUseRantaBuyPage,
          isPayPageCustomized: !isUseRantaPayPage,
          shopExtensionIdList: extensionIdList,
          tradeExtensionIdList,
        },
      };

      return result;
    };

    // 检查前置条件
    const preCondition = await checkPreCondition();
    const result: WeappPrepareV2VO['usePreCashierPay'] = {
      value: preCondition.value, // 是否启用前置收银台
      conditions: preCondition.conditions, // 判断条件，辅助排查无法启用前置收银台的原因
    };

    // 前置条件满足，检查后置条件
    if (preCondition.value) {
      const postCondition = await checkPostCondition();
      result.value = result.value && postCondition.value;
      result.conditions = { ...result.conditions, ...postCondition.conditions };
    }

    // 最终条件满足，获取abTest配置
    if (result.value) {
      const abTestConfig = await getAbTestConfig();
      result.abTestConfig = abTestConfig;
    }

    // 天网上报
    this.ctx.logger.info(`${traceStr}, result=${JSON.stringify(result)}`);

    return result;
  }

  async getPrefetchIndexHtml() {
    await this.renderBuyTemplate(true);
  }

  // 查询时间分片限单状态
  async queryOrderLimit(ctx: Context) {
    const postData = ctx.getPostData();
    const { bizType, offlineId, dispatchWarehouseId, timeBucketList } = postData;
    let result = [];
    const params: {
      buyerId: number;
      bizType: BizType;
      dispatchWarehouseId: number;
      timeBucketList: Array<{
        index: number;
        startTime: string;
        endTime: string;
      }>;
      offlineId?: number;
    } = {
      buyerId: this.buyerId,
      bizType,
      dispatchWarehouseId,
      timeBucketList,
    };
    offlineId && (params.offlineId = offlineId);
    try {
      result = await new TimeBucketService(ctx).queryOrderLimit(params);
    } catch (error) {
      // 天网上报
      this.ctx.logger.warn(`[时段限单查询] params: ${postData}, error: ${error}`);
      ctx.json(9999, 'queryOrderLimit fail');
    }
    ctx.json(0, '', result);
  }

  // 获取下单默认的物流地址信息
  async getDefaultDeliveryData(ctx: Context) {
    const { kdtId } = ctx;
    const { buyerId } = this;
    const [ defaultPostageType, addressList ] = await Promise.all([
      // 0:快递 1:到店自提 2:同城配送 ,请求报错则返回0默认为配送方式
      new DeliveryService(ctx).getDefaultLogisticType({ kdtId, buyerId }).catch((err) => {
        console.log('getDefaultLogisticType err: ', err);
        return 0;
      }),
      new UserAddressService(ctx).getAddressListFast({ userId: this.buyerId }),
    ])
    const defaultAddress = this.getAddressDefaultItem(addressList, ctx.query.address_id);

    ctx.json(0, '', {
      expressType: defaultPostageType,
      defaultAddress,
    });
  }

  /**
   * 获取确认收货地址校验开关
   * @param ctx 上下文对象
   * @returns 
   */
  async getConfirmAddressValidate(ctx: Context) {
    try {
      const shopMetaInfo = await this.getShopMetaInfo(ctx);
      const isPureWscSingleStore = checkPureWscSingleStore(shopMetaInfo);
      const isRetailSingleStore = checkRetailSingleStore(shopMetaInfo);
      const isRetailChainStore = checkRetailChainStore(shopMetaInfo);
      // 零售单店、微商城、零售连锁支持确认收货地址校验
      if (!(isPureWscSingleStore || isRetailSingleStore || isRetailChainStore)) {
        return false;
      }

      const shopConfiguration = new ShopConfigurationService(ctx);
      const confirmAddressConfig = await shopConfiguration.queryShopConfig('before_create_order_address_confirm', ctx.kdtId);
      // 开关未开
      if (String(confirmAddressConfig) === '1') {
        return true;
      }
      return false;
    } catch (error) {
      return false;
    }
  }

  // 设置确认收货地址校验隐藏
  async setConfirmAddressHidden(ctx: Context) {
    const {value = '1'} = ctx.query;
    const res = await new ShopConfigWriteService(ctx).setShopConfig({
      kdtId: ctx.kdtId,
      key: 'before_create_order_address_confirm_hidden',
      value,
      operator: {
        fromApp: 'wsc-h5-trade',
      },
    });
    ctx.json(0, '', res);
  }

  async getCurrentShopInfo(ctx: Context) {
    const {kdtId} = ctx;
    const shopBaseInfo = await new ShopBaseReadOuterService(ctx).queryShopBaseInfo(kdtId);
    return shopBaseInfo;
  }

  handleRechargeOrderPayParams(postData: OrderCreationDTO): OrderCreationDTO['extensions'] {
    try {
      // 如果没有 extensions 或 RECHARGE_ORDER_PAY 字段,直接返回原 extensions
      if (!postData.extensions?.RECHARGE_ORDER_PAY) {
        return postData.extensions;
      }

      const rechargeOrderPayParams = JSON.parse(postData.extensions.RECHARGE_ORDER_PAY);
      
      // 如果解析结果不是对象,返回原 extensions
      if (!rechargeOrderPayParams || typeof rechargeOrderPayParams !== 'object') {
        return postData.extensions;
      }

      return {
        ...postData.extensions,
        RECHARGE_ORDER_PAY: JSON.stringify({
          ...rechargeOrderPayParams,
          // 用 node 的 buyerId 替换,不从query上拿
          buyerId: this.buyerId,
        })
      };
    } catch (error) {
      this.ctx.logger.info(`解析${this.ctx.kdtId}, buyerId=${this.buyerId}的充值订单支付参数失败`, null, {
        error,
        extensions: postData.extensions,
      });
      return postData.extensions;
    }
  }
}

export = OrderBuyController;
