<template>
  <div>
    <div v-if="addOnCopywriting" class="coupon-addon" @click="onPopupOpen">
      <div
        class="coupon-addon__title"
        style="color: var(--ump-tag-text, #323233); background: var(--ump-tag-bg, #f2f2ff)"
      >
        优惠券
      </div>
      <div class="coupon-addon__text">
        {{ addOnCopywriting }}
      </div>
      <div v-if="couponAddOnData.actionTagStr" class="coupon-addon__action" style="color: var(--ump-icon, #323233)">
        {{ couponAddOnData.actionTagStr }}
        <van-icon name="arrow" />
      </div>
    </div>
    <coupon-addon-popup
      :show-popup="showPopup"
      :coupon-list="coupons"
      @close="onPopupClose"
      @sendCoupon="handleSendCoupon"
      @onClickItem="onClickItem"
    />
  </div>
</template>
<script>
import { Icon, Toast } from 'vant';
import userInfoAuthorize from '@youzan/user-info-authorize';
import { sendCoupon, updateSelectGoods } from '@biz/cart/api';
import { getCouponAddOnInfo } from './api';
import CouponAddonPopup from './components/CouponAddonPopup';
import { IS_LOGIN } from '@biz/cart/constant';
import format from '@youzan/utils/money/format';
import args from '@youzan/utils/url/args';
import LOGS from './log';

// 按照老数组的顺序，把新优惠券顺序排一下
function reorderCouponByOrigin(newCoupons = [], oldCoupons = []) {
  let couponReordered = [];
  // 不修改入参内容
  const newCouponCopy = [...newCoupons];
  let goodsCountDecreased = false;
  oldCoupons.forEach((oldCoup, oldCoupIndex) => {
    const { activityId: oldCoupActivityId } = oldCoup;

    // 看看优惠券顺序有没有变化
    if (newCoupons[oldCoupIndex]?.activityId && oldCoupActivityId !== newCoupons[oldCoupIndex]?.activityId) {
      console.log('优惠券顺序发生变化!');
    }

    const newCoupIndex = newCouponCopy.findIndex((item) => item.activityId === oldCoupActivityId && item.ableUse);

    if (newCoupIndex > -1) {
      const [newCoup] = newCouponCopy.splice(newCoupIndex, 1);
      if (newCoup.allAbleCartGoods?.length < oldCoup.allAbleCartGoods?.length) {
        goodsCountDecreased = true;
      }
      couponReordered.push(newCoup);
    }
  });
  // 如果新数组长度更长
  if (newCouponCopy.length) {
    couponReordered = [...couponReordered, ...newCouponCopy];
  }

  if (goodsCountDecreased) {
    Toast('因活动规则配置，部分已选中商品无法使用该优惠券');
    LOGS.logGoodsDecreasedToastExpo();
  }

  return couponReordered;
}
// 后端接口在每次返回之间有可能调换优惠券顺序，前端本地缓存一下优惠券顺序
let couponOriginOrder = null;

export default {
  components: {
    [Icon.name]: Icon,
    'coupon-addon-popup': CouponAddonPopup,
  },
  data() {
    return {
      showPopup: false,
      kdtId: 0,
      couponAddOnData: {},
      canUseCouponAddOn: false,
      originData: {},
      coupons: [],
    };
  },
  computed: {
    addOnCopywriting() {
      return this.couponAddOnData?.addOnCopywriting || '';
    },
    couponNotFullyFetched() {
      return this.couponAddOnData?.addOnVoucherDetailDTOList?.some((e) => !e.couponId);
    },
  },

  watch: {
    // 每次请求到数据的时候都有可能发起ab
    originData(originData = {}) {
      if (originData.isInExperiment && !this.abTestIsLogged) {
        const {
          abConfig: { abTraceId },
        } = originData;
        this.abTestIsLogged = true;
        const goodsId = this.shopCart?.items?.map((i) => i.goodsId);
        LOGS.logABTest(abTraceId, goodsId);
      }
      // 如果某次凑单条文案为空了，则下次出现时还需要上报AB和曝光
      // 这个逻辑太恶心了，后面不好维护，以后最好不要出现这种逻辑@虹静
      if (!originData?.couponAddOnData?.addOnCopywriting) {
        // this.abTestIsLogged = false;
        this.gatherBarExposed = false;
      }
    },
    couponAddOnData(couponAddOnData) {
      if (!this.gatherBarExposed && couponAddOnData?.addOnCopywriting) {
        LOGS.logGatherBarExpo();
        this.gatherBarExposed = true;
      }
      if (couponAddOnData?.addOnVoucherDetailDTOList?.length > 0) {
        let coupons = couponAddOnData?.addOnVoucherDetailDTOList || [];
        coupons = coupons.map(
          ({
            valueCopywriting,
            unitCopywriting,
            title,
            thresholdCopywriting,
            validTimeCopywriting,
            allAbleCartGoods,
            ...rest
          }) => ({
            value: valueCopywriting,
            unit: unitCopywriting,
            name: title,
            threshold: thresholdCopywriting,
            validTime: validTimeCopywriting,
            allAbleCartGoods: allAbleCartGoods.map((good) => ({
              ...good,
              price: format(good.price),
            })),
            ...rest,
          })
        );
        if (this.disableCouponOrderCache) {
          this.coupons = coupons;
        }

        if (couponOriginOrder && this.showPopup) {
          coupons = reorderCouponByOrigin(coupons, couponOriginOrder);
        }

        couponOriginOrder = coupons;

        this.coupons = coupons;
      } else {
        // 无有效数据时
        /* 凑单面板开启时 */
        if (this.showPopup) {
          this.showPopup = false;
          Toast('暂无可用优惠券');
        }
        // 在面板关闭后清除数据
        setTimeout(() => {
          this.coupons = [];
        }, 1000);
      }
    },
  },

  created() {
    this.ctx.event.listen('updatingCart', () => {
      this.getCouponAddon(this.kdtId);
    });
    this.disableCouponOrderCache = args.get('disableCouponOrderCache');
  },

  mounted() {
    // dev发现如果未登录情况下 kdtId set事件无法触发watch
    const kdtId = window._global?.kdtId || 0;
    if (kdtId) {
      this.kdtId = kdtId;
      /** page-block 触发首次 updatingCart 事件时，
       * 还没有初始化coupon-addon组件，无法监听到事件
       * 首次需要手动触发
       *  */
      this.getCouponAddon(this.kdtId);
    }

    this.unwatchShopCart = this.ctx.watch('shopCart', (val) => {
      this.shopCart = val;
    });
  },
  destroyed() {
    this.unwatch && this.unwatch();
    this.unwatchShopCart && this.unwatchShopCart();
    couponOriginOrder = null;
  },
  methods: {
    getCouponAddon(kdtId) {
      getCouponAddOnInfo({
        kdtId,
      }).then((originData = {}) => {
        const { couponAddOnData = {}, canUseCouponAddOn } = originData;
        this.canUseCouponAddOn = canUseCouponAddOn;
        this.ctx.data.canUseCouponAddOn = canUseCouponAddOn;
        this.couponAddOnData = couponAddOnData;
        this.originData = originData;
      });
    },
    onClickItem(item) {
      const { selectState } = item;
      updateSelectGoods({
        type: selectState ? 'remove' : 'add',
        goods: item,
      }).then(() => {
        this.ctx.event.emit('updateCartGoodsList');
      });
    },

    onPopupClose() {
      this.showPopup = false;
      // 清楚本地缓存
      couponOriginOrder = null;
      if (this.couponNotFullyFetched) {
        Toast('你有可用的优惠券还未领取');
      }
    },
    onPopupOpen() {
      if (!this.couponAddOnData.actionTagStr) return;
      LOGS.logGatherBarClick(this.couponAddOnData?.actionTagStr);
      LOGS.batchLogGoodsExpo(this.couponAddOnData?.addOnVoucherDetailDTOList);
      this.showPopup = true;
    },
    handleSendCoupon(coupon) {
      if (!IS_LOGIN) {
        return userInfoAuthorize
          .open({
            authTypeList: ['mobile'], // 只手机号授权
          })
          .then(() => {
            window.location.reload();
          });
      }
      sendCoupon({ activityId: coupon.activityId })
        .then((res) => {
          if (res.code === 0) {
            const couponId = res?.data?.voucherIdentity?.couponId;
            LOGS.logCouponGetClick({
              activityId: coupon.activityId,
              couponId,
            });
            Toast('领取成功');
            this.getCouponAddon(this.kdtId);
          } else {
            Toast(res.msg || '领取失败');
          }
        })
        .catch((err) => {
          Toast(err.msg || '领取失败');
        });
    },
  },
};
</script>
<style lang="scss">
.coupon-addon {
  z-index: 99;
  border-top: 1px solid var(--ump-border, #c9c9ff);
  width: 100%;
  height: 36px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  &::before {
    content: '';
    height: 1px;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    transform: scaleY(0.5);
  }
  &__title {
    border-radius: 2px;
    font-family: PingFangSC-Regular;
    font-size: 12px;
    // color: #ee0a24;
    line-height: 16px;
    padding: 0 4px;
  }

  & > &__action {
    font-family: PingFangSC-Regular;
    font-size: 12px;
    display: flex;
    align-items: baseline;
  }

  & > &__text {
    flex-grow: 1;
    font-family: PingFangSC-Regular;
    font-size: 12px;
    color: #323233;
    line-height: 20px;
    padding: 0 4px;
  }
}
</style>
