/**
 * 海报服务接口
 */

export = [
  // 订单分享海报cdn地址生成
  [
    'GET',
    ['/wsctrade/poster/order-share.json'],
    'poster.PosterOrderShareController',
    ['initState', 'checkCacheInfo', 'makeQrCode', 'getPosterJson']
  ],
  [
    'GET',
    ['/wsctrade/poster/test.json'],
    'poster.PosterPaidController',
    ['initSelfFetchState', 'getPosterView']
  ],
  // 支付成功页面自提海报
  [
    'POST',
    ['/wsctrade/poster/self-fetch.json'],
    'poster.PosterPaidController',
    ['initSelfFetchState', 'getPosterJson']
  ],
  // 支付成功页面电子卡券海报
  [
    'POST',
    ['/wsctrade/poster/card-voucher.json'],
    'poster.PosterPaidController',
    ['initCardVoucherState', 'getPosterJson']
  ],
  // 支付成功页面订阅物流海报
  [
    'POST',
    ['/wsctrade/poster/subscription.json'],
    'poster.PosterPaidController',
    ['initSubscription', 'getPosterJson']
  ],
  // 生成小程序二维码
  [
    'POST',
    ['/wsctrade/poster/generate-wxcode.json'],
    'poster.PosterPaidController',
    'generateWxCode'
  ],
];
