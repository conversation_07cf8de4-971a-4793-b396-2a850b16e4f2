import { IAddressInfoDto } from 'definitions/order/detail/AddressInfo';
import { IOrderInfoDto } from 'definitions/order/detail/OrderInfo';
import { get } from 'lodash';

function getDeliveryTimeShortDisplay(addressInfo: IAddressInfoDto) {
  const { deliveryStartTime, deliveryEndTime } = addressInfo || {};

  // 容错处理：检查时间戳是否有效
  if (!deliveryStartTime || !deliveryEndTime ||
      !Number.isInteger(deliveryStartTime) || !Number.isInteger(deliveryEndTime) ||
      deliveryStartTime <= 0 || deliveryEndTime <= 0) {
    return '';
  }

  const startDate = new Date(deliveryStartTime);
  const endDate = new Date(deliveryEndTime);
  const now = new Date();

  // 容错处理：检查日期是否有效
  if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
    return '';
  }

  // 格式化时间为 HH:MM
  const formatTime = (date: Date): string => {
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  };

  const startTimeStr = formatTime(startDate);
  const endTimeStr = formatTime(endDate);
  const timeRange = `${startTimeStr} - ${endTimeStr}`;

  // 获取今天的日期（去除时间部分）
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const deliveryDate = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());

  // 计算日期差（天数）
  const dayDiff = Math.floor((deliveryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

  // 1. 今天
  if (dayDiff === 0) {
    return `今天 ${timeRange}`;
  }

  // 2. 明天
  if (dayDiff === 1) {
    return `明天 ${timeRange}`;
  }

  // 3. 后天
  if (dayDiff === 2) {
    return `后天 ${timeRange}`;
  }

  // 获取本周的开始和结束日期（周一为一周的开始）
  const getCurrentWeekRange = (date: Date) => {
    const currentDay = date.getDay(); // 0=周日, 1=周一, ..., 6=周六
    const mondayOffset = currentDay === 0 ? -6 : 1 - currentDay; // 计算到周一的偏移

    const weekStart = new Date(date.getFullYear(), date.getMonth(), date.getDate() + mondayOffset);
    const weekEnd = new Date(weekStart.getTime() + 6 * 24 * 60 * 60 * 1000);

    return { weekStart, weekEnd };
  };

  const { weekStart: thisWeekStart, weekEnd: thisWeekEnd } = getCurrentWeekRange(today);
  const { weekStart: nextWeekStart, weekEnd: nextWeekEnd } = getCurrentWeekRange(
    new Date(thisWeekStart.getTime() + 7 * 24 * 60 * 60 * 1000)
  );

  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];

  // 4. 本周内（大于后天且在本周日之前）
  if (dayDiff > 2 && deliveryDate >= thisWeekStart && deliveryDate <= thisWeekEnd) {
    const weekday = weekdays[startDate.getDay()];
    return `${weekday} ${timeRange}`;
  }

  // 5. 下周（下周一到下周日）
  if (deliveryDate >= nextWeekStart && deliveryDate <= nextWeekEnd) {
    const weekday = weekdays[startDate.getDay()];
    return `下周${weekday.substring(1)} ${timeRange}`; // 去掉"周"字，变成"下周一"、"下周二"等
  }

  // 6. 下周之后但今年内
  if (startDate.getFullYear() === now.getFullYear()) {
    const month = startDate.getMonth() + 1; // getMonth() 返回 0-11
    const day = startDate.getDate();
    return `${month}月${day}日 ${timeRange}`;
  }

  // 7. 下一年及以后
  const year = startDate.getFullYear();
  const month = startDate.getMonth() + 1;
  const day = startDate.getDate();
  return `${year}年${month}月${day}日 ${timeRange}`;
}

/**
 * 同城订单处理器
 */
class CityOrderFormatter {
  public static format(
    addressInfoDto: IAddressInfoDto,
    orderInfoDto: IOrderInfoDto,
    orderBizExtra: any,
    addressInfoDo: any,
    mainOrderInfo: any
  ): any {
    // 同城配送范围
    if (addressInfoDo.areaList && addressInfoDo.areaList.length > 0) {
      const areaList0 = addressInfoDo.areaList[0].isAccording && addressInfoDo.areaList[0];
      addressInfoDto.localDeliveryScope = areaList0 || {};

      // 显示查看同城配送范围
      orderInfoDto.showCheckDeliveryScope = !!(mainOrderInfo.expressType === 2 && areaList0);
    }

    // 定时达 - 送达时间
    const deliveryTimeDisplay = mainOrderInfo.deliveryTimeDisplay || '';
    const deliveryTimeShortDisplay = getDeliveryTimeShortDisplay(addressInfoDto);
    // 是否显示送达时间
    orderInfoDto.showDeliveryTime = !!deliveryTimeDisplay;
    addressInfoDto.deliveryTimeDisplay = deliveryTimeDisplay;
    addressInfoDto.deliveryTimeShortDisplay = deliveryTimeShortDisplay;
  }
}

export default CityOrderFormatter;
