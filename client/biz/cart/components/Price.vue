<template>
  <span>
    <span class="price">
      <span class="price--unit">¥</span>
      <span class="price--integer num-font">{{ formatPrice[0] }}</span>
      <span v-if="+formatPrice[1]" class="price--decimal num-font">.{{ formatPrice[1] }}</span>
    </span>
    <!-- 有预估到手价，优先展示预估到手价 -->
    <span v-if="estimatedPrice || estimatedPrice === 0" class="price--estimated num-font">每件预计到手¥{{ formatEstimatedPrice }}</span>
    <span v-else-if="formatOriginPrice" class="price--origin num-font">¥{{ formatOriginPrice }}</span>
  </span>
</template>
<script>
import format from '@youzan/utils/money/format';

// 格式化价格，原价和预估价
const formatPrice = (price) => {
  const formatPrice = format(price);
  const formatPriceArr = formatPrice.split('.');
  // 小数去 0
  const decimalReverse = +(formatPriceArr[1].split('').reverse().join(''));
  formatPriceArr[1] = decimalReverse.toString().split('').reverse().join('');
  if (!+formatPriceArr[1]) {
    formatPriceArr.splice(1, 1);
  }
  return formatPriceArr.join('.');
}

export default {
  props: {
    price: {
      type: [String, Number],
      required: true,
      default: 0,
    },
    originPrice: {
      type: [String, Number],
      default: 0,
    },
    estimatedPrice: {
      type: [String, Number],
    }
  },
  computed: {
    formatOriginPrice() {
      if (this.originPrice <= this.price) {
        return '';
      }
      return formatPrice(this.originPrice);
    },

    formatPrice() {
      let newPrice = this.price.toString();
      if (newPrice.indexOf(',') === -1) {
        newPrice = format(newPrice);
      }
      const newPriceArr = newPrice.split('.');
      // 小数去 0
      const decimalReverse = +(newPriceArr[1].split('').reverse().join(''));
      newPriceArr[1] = decimalReverse.toString().split('').reverse().join('');

      return newPriceArr;
    },
    formatEstimatedPrice() {
      if (typeof this.estimatedPrice !== 'number') {
        return undefined;
      }
      return formatPrice(this.estimatedPrice);
    }
  },
};
</script>
<style scoped lang="scss">
.price {
  &--unit {
    font-size: 12px;
    margin-right: 2px;
  }

  &--integer {
    font-size: 16px;
    font-weight: 600;
  }

  &--decimal {
    font-size: 12px;
    font-weight: 600;
  }

  &--origin {
    font-size: 12px;
    color: #969799;
    margin-left: 5px;
    text-decoration: line-through;
    white-space: nowrap;
  }
  &--estimated {
    max-width: 80px;
    margin-top: 8px;
    display: block;
    font-size: 12px;
    white-space: nowrap;
    color: var(--ump-icon);
  }
}
</style>
