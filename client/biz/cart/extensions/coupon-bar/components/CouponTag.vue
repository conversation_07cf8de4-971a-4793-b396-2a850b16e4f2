<template>
  <span class="coupon-tag">
    <slot />
  </span>
</template>

<script>
export default {
  name: 'CouponTag',
};
</script>

<style lang="scss" scoped>
$coupon-color: #ee0a24;
$coupon-bg-color: rgba(238, 10, 36, 0.1);

@mixin concave-border {
  position: absolute;
  z-index: 1;
  width: 4px;
  height: 8px;
  background: #fff;
  top: 6px;
  content: '';
}

.coupon-tag {
  position: relative;
  display: inline-flex;
  background: $coupon-bg-color;
  background: var(--ump-tag-bg, #f2f2ff);
  box-sizing: border-box;
  border-radius: 2px;
  font-size: 12px;
  line-height: 16px;
  color: var(--ump-tag-text, #323233);
  border: 1px solid var(--ump-border, #c9c9ff);
  padding: 1px 6px;
  cursor: pointer;
  &:after {
    @include concave-border;
    left: -2px;
    border-right: 1px solid var(--ump-border, #c9c9ff);
    border-color: var(--ump-border, #c9c9ff);
    border-radius: 0 4px 4px 0;
  }
  &:before {
    @include concave-border;
    right: -2px;
    border-left: 1px solid var(--ump-border, #c9c9ff);
    border-color: var(--ump-border, #c9c9ff);
    border-radius: 4px 0 0 4px;
  }
}
</style>
