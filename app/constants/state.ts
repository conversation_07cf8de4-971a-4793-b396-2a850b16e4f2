export const enum ORDER_STATE {
  // -------------------------------
  // 订单状态码
  // -------------------------------
  STATE_BOOK = 1,
  STATE_ADDRESS = 2,
  STATE_SIGNED = 8,
  STATE_TOPAY = 10, // old:3 待支付
  STATE_CONFIRM = 30, // old:50 待确认,待接单,待成团,已支付
  STATE_PAID = 50, // old:5 已成团 待发货
  STATE_SENDED = 60, // old:6 已发货
  STATE_CLOSED = 99,
  STATE_SUCCESS = 100
}

export const enum ORDER_TYPE {
  // -------------------------------
  // 订单类型
  // -------------------------------
  TYPE_GENERNAL = 0,
  TYPE_GIFT = 1, // 我要送人
  TYPE_PEERPAY = 2,
  TYPE_FENXIAO = 3,
  TYPE_SEND = 4, // 小牛 赠品
  TYPE_WISH = 5,
  TYPE_QRCODE = 6,
  TYPE_QRCODE_3RD = 61,

  TYPE_QRCODE_FENXIAO_SELLER = 7,
  TYPE_TEAM_TRUENAME = 8,
  TYPE_PINJIAN = 9,
  TYPE_GROUP = 10, // 拼团订单
  TYPE_REBATE = 15,
  TYPE_HOTEL = 35, // 酒店
  TYPE_TAKEAWAY = 40, // 外卖
  TYPE_CATERING_ORCODE = 46, // 餐饮买单
  TYPE_CATERING_OFFLINE = 41, // 堂食点餐
  TYPE_ENTERPRISE_PURCHASE = 51,
  TYPE_FENXIAO_DEOPSIT = 52,

  TYPE_BEAUTY_RESERVE = 71, // 美业预约单
  TYPE_BEAUTY_BILL = 72, // 美业服务单

  TYPE_KNOWLEDGE = 75, // 知识付费
  TYPE_GIFT_CARD = 81, // 礼品卡

  TYPE_PF = 100 // 100〜200留个批发，其它人别占用
}
