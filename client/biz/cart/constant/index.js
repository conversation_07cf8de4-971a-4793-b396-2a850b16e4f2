import args from 'utils/browser/args';
import buildUrl from '@youzan/utils/url/buildUrl';
import UA from '@youzan/utils/browser/ua_browser';

const global = window._global || {};
const kdtId = global.kdtId;
// 需要的各种常量
const APP_UA_LIST = ['youzanmars'];

const IS_APP_CART = APP_UA_LIST.indexOf(global.platform) > -1;

const IS_MAIJIABAN = APP_UA_LIST.indexOf(global.platform) === 0;

// 农行
const IS_BANK_ABC = global.platform === 'bankabc';

const URL_SOURCE = args.get('source');

const URL_CART_TYPE = args.get('cart_type');

const HAS_EMPTY_LINK = kdtId > 0 && !IS_MAIJIABAN && URL_SOURCE !== 'yzapp';

const EMPTY_CART_LINK = HAS_EMPTY_LINK
  ? buildUrl(`${global.url.wap}/showcase/homepage?kdt_id=${kdtId}`, '', kdtId)
  : '';

const GOODS_LINK = buildUrl(`${global.url.wap}/showcase/goods`, '', kdtId);

// 非商家 非微小店 需要ajax-login
const NEED_LOGIN = !global.isThirdApp && global.platform !== 'youzanwxd';

const IS_LOGIN = global.buyerId > 0;

// 跨店购物车
const IS_CROSS_SHOP_CART = URL_CART_TYPE === 'all' || global.platform === 'youzanmars' || URL_SOURCE === 'yzapp';

// 周期购相关的常量
const FREQUENCY_MAP = ['每日', '每周', '每月'];
const MONTHLY_MAP = [];

const DELIVER_MAP = [
  ['每天送达', '工作日每天送达', '周末每天送达', '隔天送达'],
  ['', '每周一送达', '每周二送达', '每周三送达', '每周四送达', '每周五送达', '每周六送达', '每周日送达'],
  MONTHLY_MAP,
];

// 是否使用店铺自定义logo
const IS_CUSTOMER_LOGO = !IS_CROSS_SHOP_CART && global.shop_settings && global.shop_settings.is_logo_customized;

const URL_FROM = args.get('from');

const SHOP_LOGO = global.shopLogo;

const HIDE_STROLL = global.openAppConfig && global.openAppConfig.hideStroll;

const SEPARATE_BUY_TYPE = {
  MULTISHOP: 'multishop',
  COURSE_MIX_TYPE: 'courseMixType',
  MIX_TYPE: 'mixType',
  LOGISTICS: 'logistics',
};

// 活动对应number
const ACTIVITY_TYPE_ALIAS_NUMBER = {
  PACKAGE_BUY: 104,
  MEET_REDUCE: 101,
  MEET_SEND: 261,
  PLUS_BUY: 24,
  SECOND_HALF: 115,
  MULTI_COURSE_APPLY: 10101,
};
// 活动
const ACTIVITY_TYPE_ALIAS_MAP = {
  [ACTIVITY_TYPE_ALIAS_NUMBER.PACKAGE_BUY]: 'packageBuy',
  [ACTIVITY_TYPE_ALIAS_NUMBER.MEET_REDUCE]: 'meetReduce',
  [ACTIVITY_TYPE_ALIAS_NUMBER.MEET_SEND]: 'meetSend', // 实付满赠
  [ACTIVITY_TYPE_ALIAS_NUMBER.PLUS_BUY]: 'plusBuy',
  [ACTIVITY_TYPE_ALIAS_NUMBER.SECOND_HALF]: 'secondHalfDiscount', // 第二支半价
  [ACTIVITY_TYPE_ALIAS_NUMBER.MULTI_COURSE_APPLY]: 'multiCourseApply', // 教育活动-多科连报
};

let IS_IPHONEX = false;
if (UA.isIOS() && (screen.height === 812 || screen.height === 896)) {
  IS_IPHONEX = true;
}

// 是否教育店铺
const IS_EDU_STORE = global.shopMetaInfo?.shopTopic === 1;

const IS_IOS_WEAPP = global.miniprogram?.isWeapp && global.platformInfo?.mobile_system === 'ios';

// 医药相关
const DRUG_QUANLITY_LIMIT = 5;

// 小程序传入的标记
const IS_OPEN_NEW = args.get('__openNew__');

// 买家Id
const BUYER_ID = global.buyerId;

// 请求node url
const QUERY_GOODS_URL = '/wsctrade/cartGoodstList.json';
const UPDATE_GOODS_NUM_URL = '/wsctrade/cart/updateCartGoodsNum.json';
const UPDATE_GOODS_SKU_URL = '/wsctrade/cart/reselect-goods.json';
const DELETE_GOODS_SINGLE_URL = '/wsctrade/cart/deleteGoods.json';
const DELETE_GOODS_BATCH_URL = '/wsctrade/cart/deleteBatchList.json';
const SELECT_GOODS_URL = '/wsctrade/cart/selectGoods.json';
const UNSELECT_GOODS_URL = '/wsctrade/cart/unselectGoods.json';
const BATCH_SELECT_GOODS_URL = '/wsctrade/cart/batchSelectGoods.json';
const BATCH_UNSELECT_GOODS_URL = '/wsctrade/cart/batchUnselectGoods.json';
const SELECT_ALL_GOODS_URL = '/wsctrade/cart/selectAllGoods.json';
const UNSELECT_ALL_GOODS_URL = '/wsctrade/cart/unselectAllGoods.json';
const MULTI_RECOMMEND_GOODS_URL = '/wsctrade/cart/getMultiRecommendGoods.json';
const QUERY_AVL_COUPON_URL = '/wscump/coupon/cart/get-avl-coupon.json';
const SEND_COUPON_URL = '/wscump/coupon/cart/send-coupon.json';
const GET_VOUCHER_URL = '/wsctrade/cart/getVoucher.json';
const GET_SHOP_CONFIG_URL = '/wscshop/shop/config.json';
const GET_WHOLESALE_ISWHOLESALE = '/wsctrade/wholesale/isWholesaler.json';

export {
  IS_IPHONEX,
  IS_APP_CART,
  IS_MAIJIABAN,
  IS_OPEN_NEW,
  EMPTY_CART_LINK,
  NEED_LOGIN,
  IS_LOGIN,
  IS_CROSS_SHOP_CART,
  IS_BANK_ABC,
  IS_EDU_STORE,
  IS_IOS_WEAPP,
  GOODS_LINK,
  FREQUENCY_MAP,
  DELIVER_MAP,
  URL_FROM,
  SHOP_LOGO,
  QUERY_GOODS_URL,
  UPDATE_GOODS_NUM_URL,
  UPDATE_GOODS_SKU_URL,
  DELETE_GOODS_SINGLE_URL,
  DELETE_GOODS_BATCH_URL,
  SELECT_GOODS_URL,
  UNSELECT_GOODS_URL,
  BATCH_SELECT_GOODS_URL,
  BATCH_UNSELECT_GOODS_URL,
  SELECT_ALL_GOODS_URL,
  UNSELECT_ALL_GOODS_URL,
  IS_CUSTOMER_LOGO,
  HIDE_STROLL,
  SEPARATE_BUY_TYPE,
  HAS_EMPTY_LINK,
  ACTIVITY_TYPE_ALIAS_MAP,
  ACTIVITY_TYPE_ALIAS_NUMBER,
  MULTI_RECOMMEND_GOODS_URL,
  QUERY_AVL_COUPON_URL,
  SEND_COUPON_URL,
  DRUG_QUANLITY_LIMIT,
  GET_VOUCHER_URL,
  GET_SHOP_CONFIG_URL,
  BUYER_ID,
  GET_WHOLESALE_ISWHOLESALE,
};
