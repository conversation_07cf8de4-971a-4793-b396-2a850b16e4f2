const BaseService = require('../base/BaseService');

class BatchRefundService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.ebiz.mall.trade.buyer.api.service.BatchRefundService';
  }

  async getSelectGoodsList(params) {
    return this.invoke(this.SERVICE_NAME, 'getBatchOrderItemInfo', [params]);
  }

  async getApplyGoodsList(params) {
    return this.invoke(this.SERVICE_NAME, 'batchBuyerRefundInit', [params]);
  }

  async getRefundReason(params) {
    return this.invoke(this.SERVICE_NAME, 'batchRefundReason', params);
  }

  async applyBatchRefund(params) {
    return this.invoke(this.SERVICE_NAME, 'batchBuyerRefundApply', [params]);
  }

  async getApplyResult(params) {
    return this.invoke(this.SERVICE_NAME, 'checkOperatorResult', [params]);
  }
}

module.exports = BatchRefundService;
