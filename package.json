{"name": "wsc-h5-trade", "version": "0.0.1", "description": "Iron 拆分业务：交易、下单、订单", "license": "MIT", "scripts": {"start": "yarn start:qa", "start:map": "LOCAL_MAP=1 yarn start:qa", "start:qa": "ast dev --mock --inspect --env=qa --ts --tsconfig tsconfig.json", "start:pre": "NODE_TETHER=tether-proxy.youzan.com ast dev --mock --inspect --env=pre --ts --tsconfig tsconfig.json", "bootstrap": "yarn && cd ./client && yarn", "dev": "cd ./client && yarn dev", "lint": "cd ./client && yarn lint", "lint:node": "eslint . --fix", "format:node": "prettier --write .", "format-check:node": "prettier --check .", "cspell:node": "cspell . --no-progress", "jscpd:node": "jscpd --silent", "postinstall": "ast build", "test": "NODE_ENV=qa astroboy-mock test", "test:silent": "NODE_ENV=qa astroboy-mock test", "test:watch": "NODE_ENV=qa astroboy-mock test --watch", "test:report": "NODE_ENV=qa astroboy-mock test --json --outputFile=test/report.json", "test:clear": "astroboy-mock test --clearCache", "test:coverage": "open test/coverage/index.html", "prepare": "node_modules/.bin/husky-run install"}, "repository": {"type": "git", "url": "***********************:wsc-node/wsc-h5-trade.git"}, "dependencies": {"@types/chance": "^1.1.3", "@types/jest": "^26.0.24", "@types/koa": "2.0.48", "@types/koa-router": "7.0.40", "@types/lodash": "4.14.122", "@types/node": "11.9.5", "@youzan-types/iron-base": "0.1.21", "@youzan/apollox-dynamic-components": "0.0.4", "@youzan/astroboy-mock": "^2.0.2", "@youzan/cdn-fallback": "0.0.8", "@youzan/config-matcher": "1.2.4", "@youzan/crypto": "^1.1.1", "@youzan/femp-runtime-node": "^2.2.2", "@youzan/guide-b-utils": "2.0.10", "@youzan/h5-cpns-injector-plugin": "1.1.0", "@youzan/im-base-pure": "0.2.3", "@youzan/iron-base": "5.3.23", "@youzan/matrix-cli": "2.0.2", "@youzan/plugin-h5-abtest": "2.0.1", "@youzan/plugin-h5-auto-salesman": "1.0.0", "@youzan/plugin-h5-ecloud": "1.3.0", "@youzan/plugin-h5-enter-shop": "3.0.4", "@youzan/plugin-h5-global-theme": "1.2.10", "@youzan/plugin-h5-hummer": "^1.3.0", "@youzan/plugin-h5-inject-console": "0.0.2", "@youzan/plugin-h5-language": "1.0.2", "@youzan/plugin-h5-ranta-config": "1.6.2", "@youzan/plugin-h5-redirect": "2.1.0", "@youzan/plugin-h5-shop": "^1.4.1", "@youzan/plugin-h5-yun-shop-config": "1.2.0", "@youzan/retail-h5-monitor-logger": "1.0.3", "@youzan/scrm-common": "1.1.0", "@youzan/utils": "3.2.5", "@youzan/utils-shop": "1.2.0", "@youzan/wholesale-plugin": "1.0.0", "@youzan/yz-aes": "1.0.0", "@youzan/zan-node-tracker": "2.0.12", "@youzan/zan-pay-core": "1.1.2", "amd-loader": "0.0.8", "astroboy-cli": "0.2.0", "chance": "^1.1.8", "query-string": "6.3.0", "tslib": "1.9.3", "zod": "^1.11.17"}, "devDependencies": {"@youzan/apollox": "2.2.4", "@youzan/eslint-config-koko": "3.0.1", "cosmiconfig": "5.0.7", "cspell": "^6.31.2", "eslint": "^6.0.0", "eslint-plugin-prettier": "^4.2.1", "husky": "^4.3.0", "jscpd": "^3.4.5", "lint-staged": "^10.5.3", "prettier": "^2.0.0", "typescript": "^3.8.3"}, "eslintConfig": {"extends": ["@youzan/koko/node"]}, "astroboyMock": {"framework": "@youzan/iron-base"}, "husky": {"hooks": {"pre-commit": "koko pre-commit", "commit-msg": "koko commit-msg"}}}