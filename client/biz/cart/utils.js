import LS from 'utils/browser/local-storage';
import get from 'utils/get';
import { IS_IOS_WEAPP } from '@biz/cart/constant';
import mapKeysToCamelCase from '@youzan/utils/string/mapKeysToCamelCase';
import { action as ZanJSBridgeAction } from '@youzan/zan-jsbridge';

// 传入单个商品判断是否是ios小程序教育线上商品
export function isEduIosOnlineGoods(goods) {
  return IS_IOS_WEAPP && goods.goodsType === 31 && goods.bizExtension?.cartBizMark?.isOnlineCourse === '1';
}

/**
 * 判断当前商品是否可选
 *
 * @export
 * @param {*} goods
 * @param {boolean} [isEditMode=false]  是否是编辑模式
 * @returns
 */
export function isGoodsCheckboxEnable(goods, isEditMode = false) {
  // ios小程序教育线上课不可选
  if (isEduIosOnlineGoods(goods)) return false;
  
  let startSaleNum = goods.startSaleNum || 0; // 起售
  const revive = goods.revive || false; // 待重选商品
  // 换购商品不限制起售
  if (goods.activityType === 24) startSaleNum = 0;

  const isMeetStartSaleNum = goods.num >= startSaleNum;

  const nowTime = new Date().getTime() / 1000;
  const isNotStartSold = goods.startSoldTime && goods.startSoldTime > nowTime;

  return (isMeetStartSaleNum && !revive && !isNotStartSold) || isEditMode;
}

// 取得商品在当前店铺商品列表的索引
export function whereCurrentGoodsInList(list, goods) {
  let currentIndex = (list || []).findIndex((item) => {
    if (goods.cartId && item.cartId) {
      return goods.cartId === item.cartId;
    } else {
      // 兼容有赞云
      // 兼容没有activity_id  的情况
      item.activityId = item.activityId || 0;
      return (
        item.goodsId === goods.goodsId &&
        item.skuId === goods.skuId &&
        item.canyinId === goods.canyinId &&
        item.activityId === goods.activityId
      );
    }
  });

  return currentIndex;
}

// 取得商品在当前店铺中的 索引
export function whereCurrentGoodsInShop(shop = {}, goods) {
  let goodsGroupListIndex = -1;
  let goodsIndex = -1;

  goodsGroupListIndex = (shop.goodsGroupList || []).findIndex((goodsGroup) => {
    goodsIndex = (goodsGroup.goodsList || []).findIndex((item) =>
      isSameGoods(item, goods)
    );
    return goodsIndex !== -1;
  });

  return {
    goodsGroupListIndex,
    goodsIndex,
  };
}

// 判断是否是同个商品
function isSameGoods(oldGoods, newGoods) {
  // 新版商品有cart_id，有赞云可能没传 cart_id
  if (newGoods.cart_id && oldGoods.cart_id) {
    return newGoods.cart_id === oldGoods.cart_id;
  }

  // 兼容没有activity_id  的情况
  if (!oldGoods.activity_id) oldGoods.activity_id = 0;
  if (!newGoods.activity_id) newGoods.activity_id = 0;
  return (
    oldGoods.goods_id === newGoods.goods_id &&
    oldGoods.sku_id === newGoods.sku_id &&
    oldGoods.canyin_id === newGoods.canyin_id &&
    oldGoods.activity_id === newGoods.activity_id
  );
}

// TODO 改成 cartId索引
// 从缓存中移除一项
export function removeItemFromStorgeList(goods) {
  const chosenGoods = JSON.parse(LS.get('current-chosen') || '[]');
  const alias = `${goods.kdt_id}-${goods.goods_id}-${goods.sku_id}`;
  const index = chosenGoods.indexOf(alias);
  if (index !== -1) {
    chosenGoods.splice(index, 1);
  }
  LS.set('current-chosen', JSON.stringify(chosenGoods));
}

// 获取商品属性
export function getGoodsPropertiesStr(properties = []) {
  const propertiesArr = [];

  mapKeysToCamelCase(properties).forEach((currentProperty) => {
    const propValueList = get(currentProperty, 'propValueList', []);
    propValueList.forEach((currentValue) => {
      propertiesArr.push(currentValue.propValueName);
    });
  });

  return propertiesArr.join('，');
}

// 获取属性值 id 数组
export function getGoodsPropertiesIds(properties = []) {
  const propertiesIds = [];

  mapKeysToCamelCase(properties).forEach((currentProperty) => {
    const propValueList = get(currentProperty, 'propValueList', []);
    propValueList.forEach((currentValue) => {
      propertiesIds.push(currentValue.propValueId);
    });
  });

  return propertiesIds;
}

/**
 * 把sku 组件获取的数据格式化成下面格式
[
  {
    "prop_id": 131127,
    "prop_name": "杯型",
    "prop_value_list": [
      {
          "prop_value_id": 571382,
          "prop_value_name": "大杯"
      }
    ]
  }
]
 */
export function parseGoodsProperties(properties = []) {
  return mapKeysToCamelCase(properties).map((property) => {
    return {
      propId: property.kId,
      propName: property.k,
      propValueList: (property.v || []).map((value) => ({
        propValueId: value.id,
        propValueName: value.name,
      })),
    };
  });
}

/*
  normalize sku tree

  [
    {
      count: 2,
      k: "品种", // 规格名称 skuKeyName
      k_id: "1200", // skuKeyId
      k_s: "s1" // skuKeyStr
      v: [ // skuValues
        { // skuValue
          id: "1201", // skuValueId
          name: "萌" // 具体的规格值 skuValueName
        }, {
          id: "973",
          name: "帅"
        }
      ]
    },
    ...
  ]
                |
                v
  {
    s1: [{
      id: "1201",
      name: "萌"
    }, {
      id: "973",
      name: "帅"
    }],
    ...
  }
 */
export const normalizeSkuTree = (skuTree) => {
  const normalizedTree = {};
  skuTree.forEach((treeItem) => {
    normalizedTree[treeItem.k_s] = treeItem.v;
  });
  return normalizedTree;
};

// 获取已选择的sku名称
export const getSelectedSkuFromSkuData = (skuTree, selectedSku) => {
  const normalizedTree = normalizeSkuTree(skuTree);
  return Object.keys(selectedSku)
    .filter((key) => /^s\d$/.test(key))
    .reduce((selectedValues, skuKeyStr) => {
      const skuValues = normalizedTree[skuKeyStr] || [];
      const skuValueId = selectedSku[skuKeyStr];

      if (+skuValueId) {
        const skuValue = skuValues.filter(
          (value) => +value.id === +skuValueId
        )[0];
        skuValue && selectedValues.push(skuValue);
      }
      return selectedValues;
    }, []);
};

// 将选中sku格式化为 [{ k_s: xx, v_id: xx }]
export const getSkuData = (comb) => {
  return Object.keys(comb)
    .filter((key) => /^s\d$/.test(key))
    .reduce((selectedValues, skuKeyStr) => {
      if (+comb[skuKeyStr]) {
        selectedValues.push({
          kS: skuKeyStr,
          vId: comb[skuKeyStr],
        });
      }
      return selectedValues;
    }, []);
};

/**
 * 平滑滚动到某元素
 * @param {element} element 到滚动到的元素
 * @param {Object} Options 参数
 * time: 滚动时间（s）
 */
export const animateScroll = (element, { time = 0.3 }) => {
  if (!element) return;

  // ======= 获取元素距离页面顶部距离
  let elementOffsetTop = element.offsetTop;
  let elementParent = element.offsetParent;

  while (elementParent !== null) {
    elementOffsetTop += elementParent.offsetTop;
    elementParent = elementParent.offsetParent;
  }
  // ======= end

  let currentTop = document.documentElement.scrollTop;
  // 可滚动总高度
  const totalScrollHeight =
    document.documentElement.scrollHeight -
    document.documentElement.clientHeight;

  const speed = (elementOffsetTop - currentTop) / (time * 60); // 按照每秒60帧计算

  if (!speed) return;

  let requestId;

  function step(timestamp) {
    if (
      Math.abs(elementOffsetTop - currentTop) >= Math.abs(speed) &&
      currentTop <= totalScrollHeight &&
      currentTop >= 0
    ) {
      currentTop += speed;
      window.scrollTo(0, currentTop);
      requestId = window.requestAnimationFrame(step);
    } else if (
      Math.abs(elementOffsetTop - currentTop) < Math.abs(speed) &&
      Math.abs(elementOffsetTop - currentTop) > 0
    ) {
      // 剩余滚动距离
      currentTop = elementOffsetTop;
      window.scrollTo(0, currentTop);
      requestId = window.requestAnimationFrame(step);
    } else {
      window.cancelAnimationFrame(requestId);
    }
  }
  window.requestAnimationFrame(step);
};

/**
 * 购物车点击结算给app发送回调
 * @param {Array} allShopInfo 购物车中的商品数据
 * @param {Object} currentChosenGoods 当前购物车中选择的商品
 */
export const appSkdSettleEvent = (currentChosenGoods, allShopInfo) => {
  try {
    const appSdkData = allShopInfo
      .reduce((list, shop) => {
        return list.concat(shop.goodsList || []);
      }, [])
      .map(item => ({
          item_id: item.goodsId,
          sku_id: item.skuId,
          alias: item.alias,
          title: item.title,
          num: item.num,
          pay_price: item.payPrice,
          selected: item.selectState === 1 ? true : false
        })
      );
    ZanJSBridgeAction.doAction({
      action: 'addUp',
      goodsList: JSON.stringify(appSdkData)
    });
  } catch (e) {
    // do nothing
  }
}
