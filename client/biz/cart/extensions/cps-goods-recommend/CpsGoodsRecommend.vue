<template>
  <AsyncCpsGoodsRecommend :cps-config-key="configKey" />
</template>

<script>
import Vue from 'vue';
import * as ZNB from '@youzan/znb';
import ajax from '@youzan/zan-h5-ajax';
import VueComponentsInjector from '@youzan/vue-components-injector';
import { Icon } from 'vant';

// 一个 injector 一般对应一个业务线，创建该业务线下的多个异步组件
const injector = new VueComponentsInjector({
  uniqueId: 'statcenter',
  loggerMeta: {
    appName: 'wsc-h5-statcenter',
    logIndex: 'jserror_log',
  },
  externals: {
    content: { Vue, ZNB, ajax, vant: { Icon } },
  },
});

const AsyncCpsGoodsRecommend = injector.genAsyncComponent({
  link: '/wscstatcenter/common/dynamic-components/getResource.json',
  componentName: 'cps-goods-recommend',
  ajaxOptions: {
    data: {
      name: 'cps-goods-recommend',
    },
  },
});

export default {
  name: 'cps-goods-recommend',

  components: {
    AsyncCpsGoodsRecommend,
  },

  props: {
    ctx: Object,
    configKey: {
      type: String,
      default: '',
    },
  },
};
</script>
