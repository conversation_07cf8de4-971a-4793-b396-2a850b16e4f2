import _ from 'lodash';
import queryString from 'query-string';
import buildUrlWithCtx from '@youzan/utils/url/buildUrlWithCtx';
import mapKeysToSnakeCase from '@youzan/utils/string/mapKeysToSnakeCase';
import BaseService from '../base/BaseService';
import CompareVersion from '../../lib/CompareVersion';
import {
  IOrderPayInfo,
  IOrderListReqUnion,
  IOrderListResp,
  IOrderListItem,
  IOrderListPermission,
  IItemProperty,
  IItemSku,
} from 'definitions/order/OrderList';
import { CrmOfflineType } from '../../constants/crmOffline';
import { RETAIL_FREE_BUY_ORDER_CAN_CANCEL } from '../../constants/shopConfigs';
import formatMoney from '@youzan/utils/money/format';
import OrderProcessFormatter from './format/list/OrderProcessFormatter';
import { IExtOpt, IOrderProcessItem } from 'definitions/order/list/OrderListProcess';

function goodsPropertiesStr(item: { properties: IItemProperty[] }) {
  const properties = item.properties || [];
  const propertiesArr: unknown[] = [];

  properties.forEach((currentProperty) => {
    const propValueList = currentProperty.propValueList || [];
    propValueList.forEach((currentValue) => {
      propertiesArr.push(currentValue.propValueName);
    });
  });

  return propertiesArr.join('；');
}

function goodsSku(item: any) {
  const sku: IItemSku[] = item.sku || [];
  const skuArr: string[] = [];

  sku.forEach((skuItem) => {
    skuArr.push(skuItem.v || skuItem.value || '');
  });

  return [skuArr.join('；'), goodsPropertiesStr(item)].filter((val) => !!val).join('；');
}

class OrderListService extends BaseService {
  get ORDER_SERVICE() {
    return 'com.youzan.ebiz.mall.trade.buyer.api.service.OrderService';
  }

  get SELF_FETCH_SERVICE() {
    return 'com.youzan.trade.api.order.service.SelfFetchService';
  }

  get ORDER_QUERY_SERVICE() {
    return 'com.youzan.ebiz.mall.trade.buyer.api.service.OrderQueryService';
  }

  // 处方相关
  get PRESCRIPTION_MANAGEMENT_SERVICE(): string {
    return 'com.youzan.retail.trade.misc.api.service.medicine.PrescriptionManagementApi';
  }

  private _buildUrl(url: any, name: any, newKdtId: any) {
    const { kdtId } = this.ctx;
    const buildUrl = buildUrlWithCtx(this.ctx);
    return buildUrl(url, name, newKdtId || kdtId);
  }

  public async getSelffetchOrder(params: any) {
    const result = await this.invoke(this.ORDER_SERVICE, 'getSelfFetchOrders', [params]);
    return result;
  }

  public async verifySelffetch(params: any) {
    const result = await this.invoke(this.SELF_FETCH_SERVICE, 'verifyTrade', [params]);
    return result;
  }

  public async getSelffetchDetailByOrderNo(
    orderNo: string,
    kdtId: string | number,
    kosTokenVerified = false,
    selfFetchId?: number
  ) {
    const {
      fans_id: fansId = 0,
      fans_type: fansType = 0,
      buyer = {} as any,
      youzan_user_id: youzanUserId = 0,
    } = this.ctx.getLocalSession();
    const { buyer_id: buyerId } = buyer;

    const params = {
      orderNo,
      kdtId,
      buyerId: kosTokenVerified ? 0 : buyerId || youzanUserId,
      customerId: kosTokenVerified ? 0 : fansId,
      customerType: kosTokenVerified ? 0 : fansType,
    };

    /**
     * tether调用
     */
    let order = await this.invoke(
      'com.youzan.ebiz.trade.api.manage.detail.DetailService',
      'getOrderInfo',
      [params],
      {
        allowBigNumberInJSON: true,
      }
    );
    // console.log(order);

    if (!order) {
      return {};
    }
    order = {
      itemInfo: this._getItemInfo(order, selfFetchId),
      selfFetchVoucher: this._getSelfFetchVoucher(order),
      selfFetchInfo: this._getSelfFetchInfo(order),
      orderNo: order.orderAddressInfo.orderNo,
    };
    order = mapKeysToSnakeCase(order);

    return order;
  }

  _getItemInfo(order: any, currentSelfFetchId?: number) {
    return order.itemInfo.map((one: any) => {
      let feedback = '';
      if (one.sku && typeof one.sku === 'string') {
        one.sku = JSON.parse(one.sku);
      }

      if (one.feedback === 250) {
        feedback = '退款成功';
      }

      if (one.feedback === 201) {
        feedback = '退款中';
      }

      let hideConfirmBtn = false;
      try {
        const extra = JSON.parse(one.extra);
        /**
         * IS_SERIAL_ITEM = 1 代表唯一码的商品
         * IS_WEIGHING_SENDERS = 1 并且实际支付金额大于0 代表补差价的商品
         * 两者都会使得自提详情页的确认提货按钮不可用
         */
        const { IS_SERIAL_ITEM = 0, IS_WEIGHING_SENDERS = 0 } = extra;
        // 跨店自主核销自提点不同时隐藏确认收货
        const hideWithDifferentSite =
          currentSelfFetchId &&
          !isNaN(currentSelfFetchId) &&
          order.orderAddressInfo.selfFetchId !== +currentSelfFetchId;
        hideConfirmBtn = !!(
          IS_SERIAL_ITEM ||
          (IS_WEIGHING_SENDERS && one.realPay > 0) ||
          hideWithDifferentSite
        );
      } catch (err) {}

      return {
        itemId: one.itemId,
        num: one.num,
        sku: one.sku,
        goodsName: one.goodsName,
        feedback,
        hideConfirmBtn,
      };
    });
  }

  _getSelfFetchVoucher(order: any) {
    const orderAddressInfo = order.orderAddressInfo || {};
    const { barCodeBase64 = '', qrCodeBase64 = '', selfFetchNo = '' } =
      orderAddressInfo.selfFetchResult || {};
    return {
      verify_barcode_src: barCodeBase64,
      verify_code_src: qrCodeBase64,
      verify_code: selfFetchNo,
    };
  }

  _getSelfFetchInfo(order: any) {
    const { orderAddressInfo } = order;
    let selfFetchInfo;
    if (
      orderAddressInfo &&
      orderAddressInfo.selfFetchInfo &&
      typeof orderAddressInfo.selfFetchInfo === 'string'
    ) {
      try {
        selfFetchInfo = JSON.parse(orderAddressInfo.selfFetchInfo);
      } catch (error) {
        selfFetchInfo = {};
      }
    }
    return {
      ...selfFetchInfo,
      selfFetchState: _.get(orderAddressInfo, 'selfFetchResult.selfFetchState', 0),
      updateTime: _.get(orderAddressInfo, 'selfFetchResult.updateTime', 0),
    };
  }

  // 订单列表接口
  public async getListJson(params: IOrderListReqUnion): Promise<IOrderListResp> {
    let method = 'search';
    if (params.caller === 'IVR') {
      // VIR订单搜索
      method = 'searchIVR';
    }
    const isDrugList = params?.isDrugList;

    let result;
    if (isDrugList === 'true') {
      // 需求清单请求接口
      result = await this.invoke(this.PRESCRIPTION_MANAGEMENT_SERVICE, 'orderSearch', [params]);
    } else {
      // 订单列表请求接口
      result = await this.invoke(this.ORDER_QUERY_SERVICE, method, [params]);
    }

    const shopConfig = await this.ctx
      .getShopConfigs([RETAIL_FREE_BUY_ORDER_CAN_CANCEL])
      .catch((err: any) => {
        console.log(err);
        return {};
      });

    if (isDrugList === 'true') {
      return {
        hasNext: result.paginator.totalNum > result.paginator.page * result.paginator.pageSize,
        list: this._formatListData(result.items || [], shopConfig),
        page: result.paginator.page,
        pageId: result.paginator.pageId || 0,
        pageSize: result.paginator.pageSize,
      };
    }
    return {
      hasNext: result.hasNext,
      list: this._formatListData(result.list || [], shopConfig),
      page: result.page,
      pageId: result.pageId,
      pageSize: result.pageSize,
    };
  }

  private _formatListData(list: any, shopConfig: any): IOrderListItem[] {
    return list.map((record: any) => {
      const orderHeader = record.orderHeader || {};
      const orderList = record.orderList || [];
      const firstOrder = orderList[0] || {};
      const orderNo = orderHeader.bizNo || (firstOrder.order && firstOrder.order.orderNo);
      // 订单列表目前仍存在商品列表空的情况，这个时候orderHeader.kdtId为空 @2018.12.26
      const kdtId = orderHeader.kdtId || (firstOrder.shop && firstOrder.shop.kdtId);
      // 支付有礼具体内容和复购券内容
      const marketingCluster = firstOrder.marketingCluster || {};
      const paidPromotionInfo = marketingCluster.paidPromotionInfo || {};
      const paidPromotionType = Object.keys(paidPromotionInfo)[0] || '';
      const paidPromotionValue = paidPromotionInfo[paidPromotionType] || {};
      const repurchaseCoupon = marketingCluster.repurchaseCoupon || null;
      const { presaleVoucherStatus } = firstOrder;

      // 三方线下门店订单，外部订单编号
      const outBizNo = firstOrder?.order.outBizNo;
      // crm线上/线下订单标签
      const { crmOfflineType = CrmOfflineType.NONE } = firstOrder.orderExtra || {};

      const orderStatus = (firstOrder.order && firstOrder.order.status) || '';

      const retItem: any = {
        id: orderHeader.bizNo,
        kdtId,
        orderNo,
        outBizNo,
        shopName: orderHeader.shopName,
        brandCertType: orderHeader.brandCertType,
        principalCertType: orderHeader.principalCertType,
        status: orderStatus,
        statusCode: (firstOrder.order && firstOrder.order.statusCode) || '',
        orderStateStr: (firstOrder.order && firstOrder.order.statusStr) || '',
        orderPermission: this._formatPermission(firstOrder, shopConfig),
        orderItems: this._formatOrderItem(orderList, orderHeader),
        medicalRx: firstOrder.medicalRx || {},
        paidPromotion: {
          paidPromotionType,
          paidPromotionValue,
        },
        repurchaseCoupon,
        presaleVoucherStatus,
        crmOfflineType,
      };
      // 如果有fulfillmentDetail，则进行加工处理返回到C端
      if (firstOrder.fulfillmentDetail) {
        retItem.itemDetail = this._formatItemDetail(firstOrder.fulfillmentDetail, {
          kdtId,
          orderNo,
          orderStatus,
        });
        retItem.wayTag = firstOrder.fulfillmentDetail.wayTag;

        retItem.phaseDesc = firstOrder.fulfillmentDetail.phaseDesc;
      }
      return retItem;
    });
  }

  // 零售订单详情信息格式化
  private _formatItemDetail(itemDetail: any, extOpt: IExtOpt) {
    if (!itemDetail) {
      return null;
    }

    // 如果是待支付订单，返回倒计时，C端拿到倒计时字段则展示倒计时
    const countDownTime = itemDetail.expiredTime - new Date().getTime();
    const cutTime = extOpt.orderStatus === 10 && countDownTime > 0 ? countDownTime : 0;

    // 处理订单流程的配置信息，C端根据配置展示，依赖C端的组件支持
    const orderProcessPrimary = OrderProcessFormatter.formatPrimary(itemDetail, extOpt);
    const orderProcessAddress = OrderProcessFormatter.formatAddress(itemDetail);
    const orderProcesses = [orderProcessPrimary, orderProcessAddress].filter(Boolean);

    return {
      ...itemDetail,
      orderProcesses,
      cutTime,
    };
  }

  private _formatPermission(orderItem: any, shopConfig: any): IOrderListPermission {
    const permission = orderItem.orderPermission || {};
    const order = orderItem.order || {};
    const orderExtra = orderItem.orderExtra || {};
    const firstGoods = (orderItem.items || [])[0] || {};
    // eslint-disable-next-line eqeqeq
    const isDepositExpand = firstGoods.activityType == '116'; // 助力定金膨胀

    permission.isShowTotalPrice = true;
    permission.isShowTopay = order.statusCode === 'topay';
    permission.isShowCancelOrder = order.statusCode === 'topay';
    // 门店线下订单不显示付款按钮
    permission.isShowTopay && (permission.isShowTopay = !orderExtra.isOfflineOrder);

    // 扫码点单的订单是否能取消
    const isRetailFreeBuy = order.orderMark === 'retail_free_buy';
    if (isRetailFreeBuy) {
      const orderCanCancel = shopConfig?.[RETAIL_FREE_BUY_ORDER_CAN_CANCEL] === '1';
      permission.isShowCancelOrder = orderCanCancel;
    }

    // 收银台的订单类型 || 代付订单
    if (orderExtra.isCashierOrder || orderExtra.isPeerpay) {
      permission.isShowTopay = false;
      permission.isShowCancelOrder = false;
    }
    /**
     * 单人代付未付款以及多人代付未付款可取消
     * buyWay 等于7为代付订单
     * payStrategy 等于1表示单人代付，等于2表示多人代付
     * status <= 10 表示未付款
     */
    if (+order.buyWay === 7 && !permission.isShowCancelOrder) {
      permission.isShowCancelOrder =
        (order.payStrategy === 1 || order.payStrategy === 2) && order.status <= 10;
    }

    // 定金预售
    if (orderExtra.isPresaleOrder && +order.closeState <= 0) {
      permission.isShowTopay = false;
      permission.isShowCancelOrder = false;

      // 待付定金：取消 + 支付定金
      if (orderExtra.isDownPayment) {
        permission.isShowCancelOrder = true;
        permission.isShowTopayPresaleDownpayment = true;
      } else if (orderExtra.isFinalPayment && orderExtra.isFinalpaymentTopay) {
        // 待付尾款 && 已开始：支付尾款
        permission.isShowTopayPresaleFinalpayment = true;

        if (isDepositExpand) {
          permission.isShowTotalPrice = false;
        }
      } else if (orderExtra.isFinalPayment) {
        // 待付尾款 && 未开始：支付尾款
        if (isDepositExpand) {
          permission.isShowInviteHelp = true;
          permission.isShowTotalPrice = false;
        } else {
          permission.isShowBeforePresaleFinalpayment = true;
        }
      }
    }
    // 抖音融合订单不展示取消订单按钮
    if (orderExtra.isExternalOrder && order.platform === 'dy_mini_program') {
      permission.isShowCancelOrder = false;
    }

    return permission;
  }

  private _formatOrderItem(orderItemList: any, orderHeader: any) {
    return orderItemList.map((orderItem: any) => {
      const orderExtra = orderItem.orderExtra || {};
      const innerOrder = orderItem.order || {};
      const firstGoods = (orderItem.items || [])[0] || {};
      // eslint-disable-next-line eqeqeq
      const isDepositExpand = firstGoods.activityType == '116'; // 助力定金膨胀

      if (orderItem.orderPermission && orderItem.orderPermission.allowViewLogistics) {
        const params = {
          order_no: innerOrder.orderNo,
          kdt_id: orderHeader.kdtId,
        };
        const { items } = orderItem;
        const periodBuyParams = {
          ...params,
          item_id: items && items[0] && items[0].itemId,
        };
        if (orderExtra.isPeriod) {
          orderItem.order.expressUrl = this._buildUrl(
            `https://h5.youzan.com/wsctrade/express/periodbuy?${queryString.stringify(
              periodBuyParams
            )}`,
            '',
            orderHeader.kdtId
          );
        } else {
          orderItem.order.expressUrl = this._buildUrl(
            `https://h5.youzan.com/wsctrade/express/detail?${queryString.stringify(params)}`,
            '',
            orderHeader.kdtId
          );
        }
      }

      // 代付
      if (innerOrder.buyWay === 7) {
        innerOrder.peerpayUrl = innerOrder.peerpayUrl || innerOrder.order.detailUrl;
      }

      // 自提订单
      orderExtra.isSelfFetch = innerOrder.expressType === 1;

      // 付费会员等级
      if (firstGoods.payGradeCard) {
        const url = `https://cashier.youzan.com/pay/wscuser_paylevel?alias=${firstGoods.alias}&kdt_id=${orderHeader.kdtId}`;
        innerOrder.detailUrl = url;
        innerOrder.confirmUrl = url;
      }

      // 待付款
      if (
        innerOrder.statusCode === 'topay' &&
        orderItemList.length > 0 &&
        !orderExtra.isCashierOrder
      ) {
        const orderNosStr = orderItemList
          .map((_orderItem: any) => {
            return `order_no=${(_orderItem.order && _orderItem.order.orderNo) || ''}`;
          })
          .join('&');
        const url = `https://cashier.youzan.com/pay/wsctrade_pay?${orderNosStr}&kdt_id=${orderHeader.kdtId}`;
        innerOrder.detailUrl = url;
        innerOrder.confirmUrl = url;
      }

      // 助力定金膨胀
      if (isDepositExpand && orderExtra.isFinalPayment) {
        const url = `https://h5.youzan.com/wscump/handsel-expand?order_no=${innerOrder.orderNo}&kdt_id=${orderHeader.kdtId}`;
        innerOrder.detailUrl = url;
        innerOrder.confirmUrl = url;
      }

      // 社区团购订单
      if (orderExtra.isMallGroupBuy) {
        const url = `https://cashier.youzan.com/pay/wsctrade_tradeDetail?orderNo=${innerOrder.orderNo}&kdtId=${orderHeader.kdtId}`;
        innerOrder.detailUrl = url;
        innerOrder.confirmUrl = url;
      }

      const payInfo: IOrderPayInfo = {
        amountDesc: '',
        payAmount: 0,
        last: '',
      };

      // 合计(兼容旧版小程序)
      let totalPrice = 0;
      try {
        if (this.ctx.isWeapp) {
          if (CompareVersion.isLte(this.ctx.xExtraData.version, '2.28.4')) {
            totalPrice = innerOrder.totalPrice || 0;
          } else {
            totalPrice = innerOrder.deductedRealPay || 0;
          }
        } else {
          totalPrice = innerOrder.deductedRealPay || 0;
        }
      } catch (err) {
        // fix lint
      }

      if (orderExtra.isPresaleOrder) {
        // 定金预售的金额：分为单位
        if (orderExtra.isDownPayment) {
          payInfo.amountDesc = '应付定金';
          payInfo.payAmount = (innerOrder.preSale && innerOrder.preSale.downPaymentPrice) || 0;
        } else if (orderExtra.isFinalPayment) {
          payInfo.amountDesc = '应付尾款';
          payInfo.payAmount = (innerOrder.preSale && innerOrder.preSale.finalPaymentPrice) || 0;
        } else {
          payInfo.amountDesc = '实付款';
          payInfo.payAmount = totalPrice || 0;
        }
      } else if (innerOrder.statusCode === 'topay') {
        payInfo.amountDesc = '需付款';
        payInfo.payAmount = totalPrice || 0;
      } else {
        payInfo.amountDesc = '实付款';
        payInfo.payAmount = totalPrice || 0;
      }
      if (innerOrder.closeState === 1) {
        payInfo.amountDesc = '应付款';
      }
      if (innerOrder.buyWay === 49) {
        // 先用后付
        if (orderExtra?.isPriorUseCompleted) {
          // 已经完成
          payInfo.amountDesc = '先用后付，实付款';
        } else {
          payInfo.amountDesc = '应付款';
          payInfo.last = '确认收货后自动扣款';
        }
      }

      const isHotel = +innerOrder.orderType === 35;
      if (isHotel && orderItem.hotel) {
        let { checkInTime, checkOutTime } = orderItem.hotel;
        if (checkInTime) {
          const checkInTimeArr = checkInTime.split('-');
          if (checkInTimeArr.length === 3) {
            checkInTime = `${checkInTimeArr[1]}月${checkInTimeArr[2]}日`;
          }
        }
        if (checkOutTime) {
          const checkOutTimeArr = checkOutTime.split('-');
          if (checkOutTimeArr.length === 3) {
            checkOutTime = `${checkOutTimeArr[1]}月${checkOutTimeArr[2]}日`;
          }
        }
        Object.assign(orderItem.hotel, { checkInTime, checkOutTime });

        // 酒店商品价格特殊处理
        const firstItem = orderItem.items && orderItem.items.length > 0 ? orderItem.items[0] : null;
        if (firstItem) {
          let totalPrice = 0;
          let totalPointsPrice = 0;
          orderItem.items.forEach((goodsItem: { price: number; pointsPrice: number }) => {
            totalPrice += goodsItem.price;
            /**
             * 当时 陈帅 写的逻辑是
             * totalPointsPrice += (goodsItem.pointsPrice || 0); + (goodsItem.pointsPrice || 0);
             * 算了两遍这个积分，我只是碰到了一个jira，然后大客那边的人又说不需要算两遍，所以这里改掉了
             * 何时认为这个代码是稳定的：2022-7-26 如果这期间没有jira，那这里的注释就可以删了
             * https://jira.qima-inc.com/browse/ONLINE-454763
             */
            totalPointsPrice += goodsItem.pointsPrice || 0;
          });
          firstItem.price = totalPrice;
          firstItem.pointsPrice = totalPointsPrice;
        }
      }

      // 格式化商品数据 - 降低体积
      orderItem.items.forEach((item: { price: number; priceStr: string; skuStr: string }) => {
        item.priceStr = formatMoney(item.price, true, false);
        item.skuStr = goodsSku(item);
      });

      // 酒店订单标记
      orderExtra.isHotel = isHotel;

      // 快手小程序强制不展示放心购和退货包运费
      if (this.ctx?.isKsApp) {
        orderItem.financeServiceVO = null;
        orderExtra.isYZGuarantee = false;
        orderExtra.isHasFreightInsurance = false;
        orderExtra.isFreightInsuranceFree = 0;
      }

      return {
        ...innerOrder,
        orderExtra,
        payInfo,
        hotel: orderItem.hotel,
        items: orderItem.items,
        orderDesc: orderItem.orderDesc || {},
        financeServiceVO: orderItem.financeServiceVO,
      };
    });
  }
}

export default OrderListService;
