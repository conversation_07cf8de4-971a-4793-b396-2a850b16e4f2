const formatDate = require('@youzan/utils/date/formatDate');
const BaseService = require('../base/BaseService');

class MyProfitService extends BaseService {
  get WITHDRAW_SERVICE() {
    return 'com.youzan.ebiz.salesman.api.withdraw.WithdrawService';
  }

  get ORDER_SERVICE() {
    return 'com.youzan.ebiz.mall.rigel.api.groupbuy.service.order.OrderService';
  }

  get HEADER_SERVICE() {
    return 'com.youzan.ebiz.mall.rigel.api.groupbuy.service.header.GroupHeaderService';
  }

  async headerInfo(params) {
    const dubboData = await this.invoke(this.HEADER_SERVICE, 'getById', [
      params.kdtId,
      params.headerBuyerId,
    ]);

    if (dubboData && dubboData.joinTime) {
      dubboData.joinTime = formatDate(dubboData.joinTime, 'YYYY-MM-DD');
    }

    return dubboData;
  }

  async headerSettle(params) {
    const dubboData = await this.invoke(this.ORDER_SERVICE, 'queryOrderScore', [
      params.kdtId,
      params.headerBuyerId,
    ]);

    if (dubboData && dubboData.joinTime) {
      dubboData.joinTime = formatDate(dubboData.joinTime, 'YYYY-MM-DD');
    }

    return dubboData;
  }

  async getProfitList(params) {
    const dubboData = await this.invoke(this.ORDER_SERVICE, 'queryOrders', [
      params,
    ]);
    dubboData.list = dubboData.list.map((item) => {
      item.bookTime = formatDate(item.bookTime, 'YYYY-MM-DD HH:mm:ss');
      return item;
    });
    return dubboData;
  }

  async getWxBindWallet(params) {
    const dubboData = await this.invoke(
      this.WITHDRAW_SERVICE,
      'getBindWallet',
      [params.kdtId, params.buyerId]
    );
    return dubboData;
  }

  async bindWeChatWallet(params) {
    const dubboData = await this.invoke(
      this.WITHDRAW_SERVICE,
      'bindWeChatWallet',
      [params.buyerId, params.nickName, params.kdtId]
    );
    return dubboData;
  }

  async getRecordList(params) {
    const dubboData = await this.invoke(this.WITHDRAW_SERVICE, 'getList', [
      params.buyerId,
      params.page,
      params.pageSize,
    ]);
    dubboData.items = dubboData.items.map((item) => {
      item.time = formatDate(item.applyTime * 1000, 'YYYY-MM-DD HH:mm:ss');
      return item;
    });
    return dubboData;
  }

  async getWithdrawDetial(params) {
    const dubboData = await this.invoke(this.WITHDRAW_SERVICE, 'get', [
      params.waterNo,
      params.buyerId,
      params.dataType,
    ]);
    dubboData.time = formatDate(
      dubboData.applyTime * 1000,
      'YYYY-MM-DD HH:mm:ss'
    );
    return dubboData;
  }

  async getLeaderForward(params) {
    const dubboData = await this.invoke(this.WITHDRAW_SERVICE, 'getAssets', [
      params.buyerId,
    ]);
    return dubboData;
  }

  async putForward(params) {
    const dubboData = await this.invoke(this.WITHDRAW_SERVICE, 'apply', [
      params.buyerId,
      params.money,
      params.acctType,
      params.bindCardId,
    ]);
    return dubboData;
  }

  async querySalemanUpgradeResult(params) {
    const options = {
      service: 'youzan.pay.user.api.userinfo.saleman.upgrade.result',
      method: 'query',
      version: '1.0.0',
      partnerId: '',
    };
    const dubboData = await this.invokePay(options, params);
    return dubboData;
  }
}

module.exports = MyProfitService;
