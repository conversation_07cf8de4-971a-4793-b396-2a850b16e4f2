import UA from '@youzan/iron-base/app/lib/UA';
import mapKeysToCamelCase from '@youzan/utils/string/mapKeysToCamelCase';
import args from '@youzan/utils/url/args';
import PayBaseController from './PayBaseController';
import WscPayService from '../../services/pay/WscPayService';
import TradeService from '../../services/trade/TradeService';
import TokenService from '../../services/uic/UicTokenService';
import CompareVersion from '../../lib/CompareVersion';
import { IPayChannelsReq, IPayReq, IPayRsp } from 'definitions/pay/WscPay';

const enum CHANNEL_TOKEN_ACCOUNT_TYPE {
  qqApplet = 11,
}

const enum CHANNEL_TOKEN_BUSINESS_TYPE {
  mall = 1,
}

/**
 * 微商城支付相关接口
 */
class WscPayController extends PayBaseController {
  /**
   * @description 用来跳转中转页的第三方APP列表
   * @readonly
   * @memberof WscPayController
   */
  get thirdAppInterceptList() {
    return ['momo', 'huya', 'kuaishoufast'];
  }

  /**
   * 获取支付列表
   */
  public async postPayChannelsJson() {
    const { ctx } = this;
    const postData = mapKeysToCamelCase(ctx.getPostData() || {});
    const excludePayTools = this.getFilteredPayChannels();

    const { cashierSign, cashierSalt, prepayId, cashierBizExt = {} } = postData;
    if (!(cashierSign && cashierSalt && prepayId)) {
      ctx.r(99, '预下单失败，请稍后重试', {});
      return;
    }

    const payParams: IPayChannelsReq = {
      cashierSign,
      cashierSalt,
      prepayId,
      buyerId: this.buyer.buyerId, // 必须是字符串
      environment: this.getEnv(), // 小程序环境
      scene: postData.scene,
      payReqFrom: this.sourceName,
    };

    if (excludePayTools) {
      payParams.excludePayTools = excludePayTools;
    }

    const payClient = postData.payClient || {};
    Object.assign(payParams, {
      cashierBizExt: JSON.stringify(
        Object.assign(cashierBizExt, this.supportPayWayExt(payClient))
      ),
    });

    const payChannelsResp = await new WscPayService(ctx).getPayChannels(
      postData.partnerId,
      payParams
    );
    const payChannelsWithColor = this.getPayChannelsWithColor(
      payChannelsResp.pay_channels
    );
    payChannelsResp.pay_channels = payChannelsWithColor;
    ctx.r(0, '处理成功', payChannelsResp);
  }

  /**
   * 微商城支付接口
   */
  public async postPayJson() {
    const { ctx } = this;
    const postData = mapKeysToCamelCase(ctx.getPostData() || {});
    const userAgent = this.ctx.userAgent;

    const {
      proxy_weixin_openid = '', // 大账号 openId
      verify_weixin_openid = '', // 自有粉丝 openId
      platform = { platform_user_id: '' }
    } = ctx.getLocalSession();

    const { cashierSign, cashierSalt, prepayId } = postData;
    if (!(cashierSign && cashierSalt && prepayId)) {
      ctx.r(99, '预下单失败，请稍后重试', {});
      return;
    }

    // qq 小程序中扩展字段，qq 支付需要
    const qqBizExt: { QQ_APP_ID?: string; QQ_ACCESS_TOKEN?: string } = {};

    let cashierBizExt = postData.cashierBizExt || '{}';

    if (this.ctx.isQQApp) {
      const { appId = 0, accessToken = '' } = await this.getChannelToken(Number(ctx.kdtId));
      qqBizExt.QQ_APP_ID = appId;
      qqBizExt.QQ_ACCESS_TOKEN = accessToken;

      try {
        cashierBizExt = JSON.parse(postData.cashierBizExt || '{}');
        cashierBizExt = Object.assign(cashierBizExt, qqBizExt);
        cashierBizExt = JSON.stringify(cashierBizExt);
      } catch (e) {
        //  cashierBizExt 解析失败不阻塞下单流程
        ctx.logger.warn(`cashierBizExt 解析失败：${postData.cashierBizExt}`);
      }
    }

    // eslint-disable-next-line prefer-destructuring
    let payTool: string = postData.payTool;
    // eslint-disable-next-line no-void
    let secondPayTool: string | undefined = void 0;

    if (payTool && payTool.includes("@")) {
      const results = payTool.split("@");
      payTool = results[0];
      secondPayTool = results[1];
    }
    
    const payData: IPayReq = {
      cashierSign,
      cashierSalt,
      prepayId,
      cashierBizExt,
      payTool,
      secondPayTool,
      aliBuyerId: platform.platform_user_id,
      wxSubOpenId: proxy_weixin_openid,
      wxSelfOpenId: verify_weixin_openid,
      wxYzOpenId: proxy_weixin_openid,
      payToken: postData.payToken,
      acceptPrice: postData.acceptPrice || 0,
      newPrice: postData.newPrice || -1,
      valueCardNo: postData.valueCardNo,
      bizExt: postData.outBizCtx,
      buyerId: this.buyer.buyerId,
      userIp: postData.userIp || this.getRemoteIp(),
      environment: this.getEnv(),
      payReqFrom: this.sourceName,
    };

    switch (payTool) {
      case this.PAY_CHANNEL.WX_H5: {
        // 陌陌 IOS 环境下，手动改写 wx_h5 支付回调，解决回跳回 APP 和 返回循环的问题
        let { platform } = this.ctx;
        // 快手极速版不在 platform 中，会识别为快手
        platform = /ksnebula/i.test(userAgent) ? 'kuaishoufast' : platform;
        const thirdAppInterceptList = this.thirdAppInterceptList;
        const beforePayResultUrl =
          'https://cashier.youzan.com/assets/before_pay_result';
        const partnerReturnUrl = args.add(beforePayResultUrl, {
          from: platform,
          result: postData.partnerReturnUrl,
        });
        if (thirdAppInterceptList.includes(platform) && UA.isIos(userAgent)) {
          payData.partnerReturnUrl = partnerReturnUrl;
        }
        break;
      }
      case this.PAY_CHANNEL.ECARD:
        payData.account = this.buyer.buyerPhone;
        break;
      case this.PAY_CHANNEL.CREDIT_CARD:
      case this.PAY_CHANNEL.BANK_CARD:
        payData.payRouter = 'credit_mock';
        break;
      case this.PAY_CHANNEL.INSTALMENT:
        payData.payRouter = 'instalment_mock';
        break;
      default:
        break;
    }

    let payResult = null;
    let useHongbao = false;

    try {
      const bizInfo: any = JSON.parse(postData.cashierBizExt || '{}');
      useHongbao = bizInfo['RED_PACKET'] // eslint-disable-line
        && bizInfo['RED_PACKET'].length // eslint-disable-line
        && bizInfo['RED_PACKET'].find((item: any) => item.rightType === 'RED_PACKET'); // eslint-disable-line
    } catch (e) {
      //  cashierBizExt 解析失败不阻塞下单流程
      ctx.logger.warn(`cashierBizExt 解析失败：${postData.cashierBizExt}`);
    }

    try {
      if (payTool === this.PAY_CHANNEL.CASH_ON_DELIVERY) {
        const { orderNo = '' } = postData.nodeExtra || {};
        await new TradeService(ctx).codPay(orderNo);
        const partnerReturnUrl = this.getPartnerReturnUrl(orderNo);
        payResult = { partnerReturnUrl, redirectUrl: partnerReturnUrl };
      } else {
        payResult = await new WscPayService(ctx).pay109(
          postData.partnerId,
          payData
        );
        // 银行卡页面迁移逻辑，后期会下线
        if (payTool === this.PAY_CHANNEL.BANK_CARD) {
          payResult.bankcardUrl = 'https://cashier.youzan.com/assets/bankcard';
        }
        payResult = this.formatPayResult(
          postData,
          mapKeysToCamelCase(payResult)
        );
      }

      ctx.r(0, '处理成功', payResult);
    } catch (err) {
      const errorContent = err.errorContent || {};

      ctx.r(
        errorContent.code || 99,
        errorContent.msg || '调用支付接口失败，请稍后重试',
        errorContent.data || {}
      );
    }
  }

  /**
   * 格式化支付结果数据
   * @param {Object} postData  支付请求入参
   * @param {Object} payResult 支付请求出参
   */
  private formatPayResult(postData: any, payResult: IPayRsp) {
    let payReturn = {} as any;
    const { payTool } = postData;
    // 分期支付接口判断跳转注册或分期详情页面
    const { availableStatus = '', kdtId = 0, period = '' } =
      postData.nodeExtra || {};

    // 改价
    if (payResult.operation === this.OPERATION.ADJUST_PRICE) {
      payReturn.newPrice = payResult.newPrice || -1;
      payReturn.adjustPrice = 1;
      payReturn.msg = `商家已将交易金额修改为${(
        payReturn.newPrice / 100
      ).toFixed(2)}元，是否继续支付?`;

      return payReturn;
    }

    if (payResult.operation === this.OPERATION.OFFER_MODIFY) {
      return payResult;
    }

    // 有赞各种卡余额类型支付
    if (this.isCardPayTool(payTool)) {
      payReturn.redirectUrl = payResult.partnerReturnUrl;
      payReturn.partnerReturnUrl = payResult.partnerReturnUrl;

      return payReturn;
    }

    if (payTool === this.PAY_CHANNEL.INSTALMENT) {
      return this.formatInstalmentPayResult(
        payTool,
        { ...postData, kdtId, availableStatus, period },
        payResult.partnerReturnUrl
      );
    }

    if (payTool === this.PAY_CHANNEL.UMP_PAY) {
      payReturn.redirectUrl = payResult.partnerReturnUrl;
      return payReturn;
    }

    if (payResult.deepLinkInfo && typeof payResult.deepLinkInfo === 'string') {
      try {
        switch (payTool) {
          case this.PAY_CHANNEL.WX_H5:
            {
              const awakePayUrl = JSON.parse(payResult.deepLinkInfo)
                .awake_pay_url;
              payReturn.redirectUrl = payResult.partnerReturnUrl;
              payReturn.payReturnUrl = payResult.partnerReturnUrl;
              payReturn.payData = {
                awakePayUrl,
                payTool: this.getWxH5PayTool(awakePayUrl),
              };
            }
            break;

          case this.PAY_CHANNEL.WX_JS:
            payReturn.payData = payResult.deepLinkInfo;
            payReturn.redirectUrl = payResult.partnerReturnUrl;
            payReturn.payReturnUrl = payResult.partnerReturnUrl;
            break;

          case this.PAY_CHANNEL.BANK_CARD: {
            let submitUrl = payResult.bankcardUrl;

            payReturn.payData = {
              submitUrl,
              submitMethod: 'GET',
              submitData: Object.assign(JSON.parse(payResult.deepLinkInfo), {
                partnerReturnUrl: payResult.partnerReturnUrl,
              }),
            };
            break;
          }

          case this.PAY_CHANNEL.CREDIT_CARD:
            payReturn.payData = {
              submitUrl: 'https://cashier.youzan.com/pay/wsctrade/bankcard',
              submitMethod: 'GET',
              submitData: JSON.parse(payResult.deepLinkInfo),
            };
            break;

          case this.PAY_CHANNEL.BAIFUBAO_WAP:
            {
              const bdMatch = (payResult.partnerReturnUrl || '').match(/^((https|http):\/\/)?([^\/]+)/i); // eslint-disable-line
              const bdHost = (bdMatch && bdMatch[0]) || '';
              payReturn.payData = JSON.parse(payResult.deepLinkInfo);
              payReturn.redirectUrl = `${bdHost}/pay/baiduwap/return`;
              payReturn.payReturnUrl = `${bdHost}/pay/baiduwap/return`;
            }
            break;

          default:
            payReturn.payData = JSON.parse(payResult.deepLinkInfo);
            payReturn.redirectUrl = payResult.partnerReturnUrl;
            payReturn.payReturnUrl = payResult.partnerReturnUrl;
            break;
        }
      } catch (e) {
        // eslint-disable-next-line no-console
        console.error(e);
      }
    }

    return payReturn;
  }

  /**
   * 获取 WX_H5 支付的 payTool
   * @param {String} url
   */
  private getWxH5PayTool(url: string = ''): string {
    let payTool = '';
    if (url.indexOf('https://') !== -1) {
      payTool = this.PAY_CHANNEL.WX_H5;
    } else if (url.indexOf('weixin://') !== -1) {
      payTool = this.PAY_CHANNEL.WX_WAP;
    }

    return payTool;
  }

  /**
   * 是否是卡类型支付
   * @param {String} paytool 支付类型
   */
  private isCardPayTool(paytool: string = ''): boolean {
    const cardPayToolList = [
      'ECARD',
      'CHANGE_PAY',
      'PREPAID_PAY',
      'VALUE_CARD',
      'GIFT_CARD',
      'ENCHASHMENT_GIFT_CARD',
      'BALANCE',
      'DISCOUNT_DEDUCTION',
    ];

    return cardPayToolList.indexOf(paytool) !== -1;
  }

  private supportPayWayExt({
    payEntity = '',
    payClientType = '',
    payClientVersion = '',
  }) {
    const userAgent = this.ctx.headers['user-agent'];
    const isIos = UA.isIos(userAgent);
    const isAndroid = UA.isAndroid(userAgent);
    const isYouzanwxd = UA.isYouzanwxd(userAgent);
    const isYouzanmars = UA.isYouzanmars(userAgent);
    let supportWxAppPay = false;

    if (isYouzanmars) {
      const version = UA.getYzAppVer(userAgent);
      if (isIos) {
        supportWxAppPay = CompareVersion.isGte(version, '3.2.4');
      } else if (isAndroid) {
        supportWxAppPay = CompareVersion.isGte(version, '3.3.1');
      }
    } else if (isYouzanwxd) {
      const version = UA.getWxdAppVer(userAgent);
      if (isIos) {
        supportWxAppPay = CompareVersion.isGte(version, '2.3.0');
      } else if (isAndroid) {
        supportWxAppPay = CompareVersion.isGte(version, '2.3.0');
      }
    }

    let supportChangePay = false;
    if (
      ['wsc'].indexOf(payEntity) >= 0 &&
      payClientType !== 'weapp' &&
      payClientType !== 'swanApp'
    ) {
      supportChangePay = true;
    }

    const cashierBizExt: any = {};
    supportWxAppPay && (cashierBizExt.WX_APP_PAY = 'v1');
    supportChangePay && (cashierBizExt.CHANGE_PAY = 'v1');

    // 支持支付宝小程序
    cashierBizExt.ALIPAY_APPLET_PAY = 'v1';

    return cashierBizExt;
  }

  // qq 小程序查询 appid 和 token 信息
  async getChannelToken(kdtId: number) {
    const params = {
      kdtId,
      accountType: CHANNEL_TOKEN_ACCOUNT_TYPE.qqApplet,
      businessType: CHANNEL_TOKEN_BUSINESS_TYPE.mall,
    };

    const result = await new TokenService(this.ctx).getChannelToken(params);

    this.ctx.logger.info('qqapp getChannelToken', null, { request: params, response: result });
    return result || {};
  }

  // 查询营销信息
  public async postQueryPayUmpJson() {
    const { ctx } = this;
    // TODO: log input
    let data = ctx.getPostData();
    const { partnerId } = data;
    delete data.partnerId;
    data.buyerId = this.buyer.buyerId;
    data.environment = this.getEnv();

    let umpInfo: any;

    try {
      umpInfo = await new WscPayService(ctx).getUmp(partnerId, data);
    } catch (e) {
      if (e instanceof Error) {
        // TODO: log error
        return ctx.r(
          500,
          'UNKNOWN_ERROR',
          e.message ? `${e.name}: ${e.message}` : JSON.stringify(e)
        );
      }
      const { errorContent: { code = 500, msg = '未知错误', extra } } = e; // eslint-disable-line
      return ctx.r(code, msg, extra || JSON.stringify(e.errorContent));
    }
    // TODO: log output
    ctx.r(0, '处理成功', umpInfo);
  }
}

export = WscPayController;
