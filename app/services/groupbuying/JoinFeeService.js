const BaseService = require('../base/BaseService');

class JoinFeeService extends BaseService {
  /* eslint-disable-next-line */
  get TRADE_SERVICE() {
    return 'com.youzan.ebiz.mall.rigel.api.groupbuy.service.order.TradeService';
  }

  /* eslint-disable-next-line */
  get RECRUIT_SETTING_SERVICE() {
    return 'com.youzan.ebiz.mall.rigel.api.groupbuy.service.shop.GroupBuyJoinSettingService';
  }

  /* eslint-disable-next-line */
  get GROUP_HEADER_SERVICE() {
    return 'com.youzan.ebiz.mall.rigel.api.groupbuy.service.header.GroupHeaderService';
  }

  /**
   *  团长缴费
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/285942
   *
   *  @param {Object} params -
   *  @param {number} params.kdtId -
   *  @param {number} params.buyerId -
   *  @return {Promise}
   */
  async payFee(params) {
    const dubboData = await this.invoke(this.TRADE_SERVICE, 'payFee', [params]);
    return dubboData;
  }

  async getSetting(params) {
    const dubboData = await this.invoke(this.RECRUIT_SETTING_SERVICE, 'get', [params]);
    return dubboData;
  }

  /**
   *  缴费时检查是否已经提交申请
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/287766
   *
   *  @param {Object} params -
   *  @param {number} params.kdtId -
   *  @param {number} params.buyerId -
   *  @return {Promise}
   */
  async checkHeaderState(params) {
    const dubboData = await this.invoke(this.GROUP_HEADER_SERVICE, 'checkHeaderState', [params]);
    return dubboData;
  }
}

module.exports = JoinFeeService;
