<template>
  <div class="cart-goods__card">
    <div class="cart-goods__img-wrap">
      <img alt="商品图片" class="cart-goods__img" :src="imgUrl" />
    </div>

    <div ref="desc-wrap" class="cart-goods__desc-wrap">
      <div
        class="cart-goods__title"
        :class="{
          'cart-goods__title-not-exist': !goods.title,
        }"
      >
        <span v-if="goodsTitleTag" class="cart-goods__title-tag">
          <img :src="goodsTitleTag" alt="" />
        </span>

        <span class="cart-goods__title-text">{{ goods.title | unescape }}</span>
      </div>

      <template v-if="!revive">
        <div v-if="skuStr" class="cart-goods__sku">
          <div
            class="cart-goods__sku-container"
            :class="{
              'cart-goods__sku-container--normal': !isCanChangeSku,
            }"
            @click.stop.prevent="changeSku"
          >
            <span>
              {{ skuStr }}
            </span>

            <van-icon v-if="isCanChangeSku" name="arrow-down" />
          </div>
        </div>

        <div
          v-if="isSevenDayUnconditionalReturn"
          class="cart-goods__seven-return"
        >
          <div class="cart-goods__seven-return-tag">7天无理由退货</div>
        </div>

        <div :style="priceStyle">
          <sale-countdown
            v-if="isNotStartSold"
            :start-sold-time="goods.startSoldTime"
            @hideCountdown="$emit('hide-sale-countdown')"
          />
          <!-- 商品图片标，目前只有有赞放心购，后续增加通过 列表渲染 -->
          <div
            v-if="goods.yzGuarantee && !IS_BANK_ABC"
            class="cart-goods__img-tags"
          >
            <!-- 有赞放心购 -->
            <img
              :src="GOODS_IMG_TAG_MAP.SECURED"
              alt="有赞放心购"
              class="cart-goods__secured"
            />
          </div>

          <div
            v-if="goodsTagList.length && !isEduIosOnlineGoods"
            class="cart-goods__tags"
            :class="{
              'cart-goods__tags--mb4':
                !periodInfo && !goods.isShowStockShort && !tariffPrice,
            }"
          >
            <van-tag
              v-for="(item, index) in goodsTagList"
              :key="index"
              class="cart-goods__tag"
              type="danger"
              round
              :plain="ACTIVITY_TAGS.includes(item)"
            >
              {{ item }}
            </van-tag>
          </div>

          <!-- 预售发货时间 -->
          <div v-if="presaleDate" class="cart-goods__presale-date">
            {{ presaleDate }}
          </div>

          <div
            v-if="goods.weight && !isEduIosOnlineGoods"
            class="cart-goods__weight"
          >
            重量: {{ goods.weight }}g
          </div>

          <div v-if="periodInfo && !isEduIosOnlineGoods" class="cart-goods__period-str">
            {{ periodInfo }}
          </div>

          <!-- 库存紧张 | 仅剩X件 -->
          <div
            v-if="!!stockLimitText && !isEduIosOnlineGoods"
            class="cart-goods__stock-less"
          >
            {{ stockLimitText }}
          </div>

          <!-- N件起售 -->
          <div
            v-if="startSaleNumAndLimitDesc && !isEduIosOnlineGoods"
            class="cart-goods__start-sale-num theme-color"
          >
            {{ startSaleNumAndLimitDesc }}
          </div>

          <div v-if="tariffPrice && !isEduIosOnlineGoods" class="cart-goods__tax">
            进口税：{{ tariffPrice }}
          </div>

          <div v-if="cutPrice && !isEduIosOnlineGoods" class="cart-goods__cut-price">
            比加入时便宜{{ cutPrice }}元
          </div>

          <div
            v-if="!isEduIosOnlineGoods"
            class="cart-goods__price-num"
            :class="{
              'cart-goods__price-num--can-stepper': isCanStepper,
            }"
          >
            <Price
              class="cart-goods__price"
              :price="goodsPrice"
              :origin-price="goods.originPrice"
              :estimated-price="showEstimatedPrice ? goods.estimatedPrice : ''"
            />

            <div class="cart-goods__num">
              <!-- @click.native.stop  阻止冒泡事件，改变数量时，进入商详页 -->
              <van-stepper
                v-if="isCanStepper && !isPlusBuyGoods"
                v-model="goodsNumber"
                :min="1"
                integer
                :max="goodsLimitNum"
                :disable-minus="stepperDisableMinus"
                @overlimit="overLimitAction"
                @click.native.stop=""
              />
              <span v-else class="common-num-font">x{{ goodsNumber }}</span>
            </div>
          </div>
          
          <p v-if="isEduIosOnlineGoods" class="cart-goods__edu-err-msg">iOS端小程序不支持在线课</p>
        </div>
      </template>

      <!-- 商品复活 -->
      <template v-else>
        <div class="cart-goods__revive" :style="priceStyle">
          <span>请重新选择商品规格</span>

          <van-button
            class="cart-goods__revive-btn"
            round
            plain
            type="primary"
            size="mini"
            @click.stop.prevent="changeSku"
          >
            重选
          </van-button>
        </div>
      </template>
    </div>

    <!-- 组合商品详情展示组件 -->
    <combo-detail v-if="comboDetail && comboDetail.length" :comboDetail="comboDetail" @open-combo-popup="$emit('open-combo-popup')" />
  </div>
</template>
<script>
import get from 'utils/get';
import image from '@youzan/utils/browser/image';
import urlHelper from '@youzan/utils/url/helper';
import moneyFormat from '@youzan/utils/money/format';
import { Stepper, Button, Toast, Icon, Tag } from 'vant';
import Price from '@biz/cart/components/Price';
import SaleCountdown from './SaleCountdown';
import ComboDetail from './ComboDetail';
import {
  FREQUENCY_MAP,
  DELIVER_MAP,
  IS_BANK_ABC,
  DRUG_QUANLITY_LIMIT,
  BUYER_ID,
} from '@biz/cart/constant';
import { getGoodsPropertiesStr, isEduIosOnlineGoods } from '@biz/cart/utils';
import { Logger } from 'common/log/logger';

const GOODS_TAG_MAP = {
  HAITAO:
    'https://b.yzcdn.cn/public_files/3a774609c08dc284f27ba5a64be85fa6.png', // 海淘
  PERIOD_BUY:
    'https://b.yzcdn.cn/public_files/61954b0fdd8319a9c5722f16ca2e31de.png', // 周期购
  MEMBER_DISCOUNT: '//b.yzcdn.cn/cdn/FkhVnpHh7ZwFAvBaUwO8B0F2Gf4V-1.png', // 会员折扣
  IS_DRUG_GOOD:
    'https://b.yzcdn.cn/path/to/cdn/dir/isDrugTag_3x.png', // 处方药
};

const GOODS_IMG_TAG_MAP = {
  // 有赞放心购
  SECURED: '//b.yzcdn.cn/security/fangxin-green-small.png',
};

const ACTIVITY_TAGS = ['换购', '赠品'];

export default {
  props: {
    goods: Object,
    // 活动类型
    activityType: Number,
    // 是否是附属商品
    isSub: {
      type: Boolean,
      default: false,
    },
    // 是否可以调整数量
    isCanStepper: {
      type: Boolean,
      default: true,
    },
    showEstimatedPrice: {
      type: Boolean,
      default: false,
    },
  },

  components: {
    Price,
    SaleCountdown,
    ComboDetail,
    [Tag.name]: Tag,
    [Icon.name]: Icon,
    [Button.name]: Button,
    [Stepper.name]: Stepper,
  },

  data() {
    const isAlipayApp = get(window, '_global.miniprogram.isAlipayApp', false);
    const isQQApp = get(window, '_global.miniprogram.isQQApp', false);

    return {
      GOODS_TAG_MAP,
      GOODS_IMG_TAG_MAP,
      ACTIVITY_TAGS,
      IS_BANK_ABC,
      priceStyle: {}, // 计算当右侧高度不足时，让价格落底
      isQQApp,
      isAlipayApp,
    };
  },

  computed: {
    comboDetail(){
      const comboData = [];
      if (this.goods?.comboDetail?.groupList?.length) {
         this.goods.comboDetail.groupList.forEach(({ subComboList }) => {
          subComboList.forEach(({ num, thumbUrl }) => {
            comboData.push({
              src: thumbUrl,
              num,
            });
          });
        });
      }
      return comboData;
    },  

    goodsTitleTag() {
      const { settlementRule } = this.goods;
      const { settlementMark } = settlementRule || {};

      // 如果是处方药就增加处方药的标

      if (this.goods?.bizExtension?.cartBizMark?.IS_PRESCRIPTION_DRUG === '1') {
        return GOODS_TAG_MAP.IS_DRUG_GOOD;
      }
      return GOODS_TAG_MAP[settlementMark] || '';
    },

    imgUrl() {
      return image.toWebp(
        urlHelper.getCdnImageUrl(this.goods.attachmentUrl, '!200x200.jpg')
      );
    },

    stockLimitText() {
      return this.goods.isShowStockShort
        ? '库存紧张'
        : this.goods.isShowStockNum
        ? `仅剩${this.goods.stockNum}件`
        : '';
    },

    // 待复活商品
    revive() {
      return get(this.goods, 'revive', false);
    },

    skuStr() {
      // 如果当前的商品有餐饮加料商品
      let feedListStr = '';
      const { sku, ingredientInfoList } = this.goods;
      (ingredientInfoList || []).forEach((feed) => {
        feedListStr += ' ' + feed.title;
      });

      const skuStr =
        ((sku && JSON.parse(sku)) || []).map((sku) => sku.v).join('，') +
        feedListStr;
      const propertiesStr = getGoodsPropertiesStr(this.goods.properties);

      return [skuStr, propertiesStr].filter((item) => !!item).join('，');
    },

    // 换购商品
    isPlusBuyGoods() {
      return +this.goods.activityType === 24;
    },

    startSaleNum() {
      // 如果是换购商品，不限制起售
      if (this.activityType === 24) {
        return 0;
      }

      return this.goods.startSaleNum || 0;
    },

    // 不满足起售数量
    isNotMeetStartSaleNum() {
      return this.startSaleNum && this.goods.num < this.startSaleNum;
    },

    // 是否可以切换sku
    isCanChangeSku() {
      return (
        !this.isPlusBuyGoods &&
        !this.isNotMeetStartSaleNum &&
        !this.isSub &&
        !this.goods.canyinId
      );
    },
    goodsLimitNum() {
      // 教育商品只能为1
      if(this.goods?.goodsType === 31){
        return 1;
      }
      const result = this.goods?.bizExtension?.cartBizMark?.IS_PRESCRIPTION_DRUG === '1'
        ? Math.min(DRUG_QUANLITY_LIMIT, this.goodsMaxNum)
        : this.goodsMaxNum;
        return result
    },

    goodsNumber: {
      get() {
        return this.goods.num;
      },
      set(val) {
        if (val === this.goods.num) return;

        if (+val <= 0) return;

        if (
          this.goods?.bizExtension?.cartBizMark?.IS_PRESCRIPTION_DRUG === '1' &&
          val > DRUG_QUANLITY_LIMIT
        ) {
          Toast('为保障用药安全，不能添加更多');
          // 商品数量设置为最大值
          this.goodsNumber = DRUG_QUANLITY_LIMIT;
          return;
        }
        //  超出库存
        if (val > this.goodsMaxNum) {
          Toast('数量超出范围');
          // 商品数量设置为最大值
          this.goodsNumber = this.goodsMaxNum;
          return;
        }
        this.$emit('num-change', +val);
      },
    },

    // 商品的最大购买量，如果有限购就为限购，没有就为库存
    goodsMaxNum() {
      return this.goods.limitNum > 0
        ? this.goods.limitNum
        : this.goods.stockNum;
    },

    isSevenDayUnconditionalReturn() {
      return this.goods.isSevenDayUnconditionalReturn;
    },

    // 商品待开售
    isNotStartSold() {
      const nowTime = new Date().getTime() / 1000;
      const sellTime = this.goods.startSoldTime;
      return sellTime && sellTime > nowTime;
    },

    activityTag() {
      if (this.activityType === 24 || this.goods.activityTag === '加价购') {
        return '换购';
      }

      if (this.goods.activityTag) {
        return this.goods.activityTag;
      }

      return '';
    },

    isMemberDiscount() {
      return this.activityTag && this.activityTag === '会员折扣';
    },

    goodsTagList() {
      const list = [];

      if (this.goods?.bizExtension?.cartBizMark?.PRE_SALE_TYPE === '0') {
        list.push('预售');
      }

      this.isMemberDiscount && list.push('会员价');

      if (this.goods.isInstallment && !this.isAlipayApp && !this.isQQApp) {
        list.push('分期支付');
      }

      if (this.activityTag && this.activityTag !== '会员折扣') {
        list.push(this.activityTag);
      }

      return list;
    },

    // 周期购
    isPeriodBuy() {
      return +this.goods.goodsType === 24;
    },

    periodInfo() {
      if (!this.isPeriodBuy || this.goods.errorMsg) {
        return '';
      }
      return this.handlePeriodInfo();
    },

    startSaleNumAndLimitDesc() {
      let desc = '';

      if (this.startSaleNum > 1 && this.goods.limitNum) {
        desc = `${this.startSaleNum}件起售，限购${this.goods.limitNum}件`;
      } else if (this.startSaleNum > 1) {
        desc = `${this.startSaleNum}件起售`;
      }

      return desc;
    },

    // 海购商品进口税
    tariffPrice() {
      const { tariffRule, tariffPrice, num } = this.goods;
      if (tariffRule === 0) {
        return `预计￥${((+tariffPrice * num) / 100).toFixed(2)}`;
      }

      if (tariffRule === 1) {
        return '商品已含税';
      }

      return '';
    },

    cutPrice() {
      if (!this.goods.cutPrice) return 0;

      return moneyFormat(this.goods.cutPrice);
    },

    // 等于起售数量时，禁用减少按钮
    stepperDisableMinus() {
      return !!this.startSaleNum && this.goods.num <= this.startSaleNum;
    },

    goodsPrice() {
      // 餐饮商品的价格是主商品价格 + 加料商品价格
      const feedList = this.goods.ingredientInfoList || [];
      let feedsPrice = 0;
      if (feedList.length > 0) {
        feedsPrice = feedList.reduce(
          (pre, feedItem) => pre + feedItem.payPrice,
          0
        );
      }

      return this.goods.payPrice + feedsPrice;
    },

    isEduIosOnlineGoods() {
      return isEduIosOnlineGoods(this.goods);
    },

    presaleDate() {
      return this?.goods?.bizExtension?.cartBizMark?.PRE_SALE_DATE || '';
    },
  },

  watch: {
    goods: {
      handler() {
        this.priceStyle = {
          paddingTop: '',
        };
        this.$nextTick().then(() => {
          const descWrap = this.$refs['desc-wrap'];
          if (descWrap.offsetHeight < 96) {
            this.priceStyle = {
              paddingTop: `${96 - descWrap.offsetHeight}px`,
            };
          }
        });
      },
      immediate: true,
    },
  },

  mounted() {
    if (+this.goods.activityType === 1) {
      Logger.log({
        et: 'view', // 事件类型
        ei: 'cart_limited_discount_view', // 事件标识
        en: ' 购物车限时折扣曝光', // 事件名称
        params: {
          item_type: 'limitdiscount',
          kdt_id: this.goods.kdtId,
          buyer_id: BUYER_ID,
        }, // 事件参数
      });
    }
  },

  methods: {
    changeSku() {
      if (!this.isCanChangeSku && !this.revive) return;
      this.$emit('change-goods-sku');
    },

    handlePeriodInfo() {
      // 配送频率
      if (!this.goods.period || !this.goods.period_num) return '';

      const frequency = FREQUENCY_MAP[this.goods.period];
      const deliverSkuStr =
        frequency + '配送，共' + this.goods.periodNum + '期，';
      const deliverTimeStr =
        DELIVER_MAP[this.goods.period][this.goods.deliverTime] || '';

      return deliverSkuStr + deliverTimeStr;
    },

    overLimitAction(type) {
      const isCourse = this.goods.goodsType === 31;
      if (this.goodsNumber === 1 && type === 'minus') {
        return Toast(isCourse? `最少购买1门课` : `最少购买1件哦`);
      }

      // 当前商品为处方药时，超过处方药限制购买数，toast提示
      if (
        this.goods?.bizExtension?.cartBizMark?.IS_PRESCRIPTION_DRUG === '1' &&
        this.goodsNumber >= DRUG_QUANLITY_LIMIT &&
        type === 'plus'
      ) {
        return Toast(`为保障用药安全，不能添加更多`);
      }

      // 不能低于起售
      if (type === 'minus' && this.stepperDisableMinus) {
        return Toast(`该商品${this.startSaleNum}件起售哦`);
      }

      // 超出最大购买量
      if (
        (this.goods.limitNum > 0 || type === 'plus') &&
        this.goodsNumber >= this.goodsMaxNum
      ) {
        return Toast(isCourse? `超出该课程可购买数量` : `该商品不能购买更多哦`);
      }
      //  超出库存
      if (this.goodsNumber >= this.goodsMaxNum) {
        return Toast('数量超出范围');
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.cart-goods {
  &__card {
    box-sizing: content-box;
    min-height: 96px;
    padding: 12px 12px 12px 0;
    margin-left: 44px;
    background: #fff;

    a {
      display: block;
    }
  }

  &__card--invalid {
    margin-left: 12px;
  }

  &__desc-wrap {
    margin-left: 104px;
  }

  &__title {
    margin-bottom: 8px;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    height: 40px;

    &-not-exist {
      margin-bottom: 0;
    }
  }

  &__title-text {
    color: #323233;
    font-size: 14px;
    line-height: 20px;
    vertical-align: middle;

    &_invalid {
      color: #969799;
    }
  }

  &__title-tag {
    color: #fff;
    margin-right: 2px;
    height: 16px;
    display: inline-block;
    vertical-align: middle;

    img {
      height: 100%;
    }
  }

  &__img-wrap {
    width: 96px;
    height: 96px;
    float: left;
    position: relative;
    margin-left: auto;
    margin-right: auto;
    border-radius: 8px;
    overflow: hidden;
    background: #fff;
    background-size: cover;
  }

  &__img {
    position: absolute;
    margin: auto;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: auto;
    height: auto;
    max-width: 100%;
    max-height: 100%;
  }

  &__img-tags {
    margin-bottom: 8px;
    padding-top: 8px;
  }

  &__secured {
    height: 14px;
  }

  &__sku {
    line-height: 16px;
    margin-bottom: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &__edu-err-msg{
    font-size: 12px;
    color: #323233;
    line-height: 16px;
    margin-bottom: 12px;
  }

  &__sku-container {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    background: #f7f8fa;
    border-radius: 4px;
    font-size: 12px;
    color: #969799;

    span {
      margin-right: 8px;
    }

    &--normal {
      padding: 0;
      background: transparent;

      span {
        margin: 0;
      }
    }
  }

  &__tags {
    margin-bottom: 8px;
    margin-top: 8px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    
    &--mb4 {
      margin-bottom: 8px;
    }
  }

  &__tag {
    margin-right: 8px;
    padding: 0 4px;
    flex-shrink: 0;
    font-size: 12px;
    line-height: 16px;
    height: 16px;
    background: var(--ump-tag-bg, #f2f2ff);
    color: var(--ump-tag-text, #323233);
  }

  &__tag-member-discount {
    height: 16px;
    width: auto;
    margin-right: 4px;
  }

  &__weight {
    font-size: 12px;
  }

  &__period-str {
    height: 16px;
    line-height: 16px;
    font-size: 12px;
    color: #666;
    word-break: keep-all;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &__price {
    display: block;
    flex-shrink: 1;
    word-break: break-all;
    color: var(--price, #323233);
  }

  &__tax {
    font-size: 12px;
    color: #323233;
    margin-bottom: 8px;
    line-height: 16px;
  }

  &__presale-date,
  &__stock-less,
  &__start-sale-num {
    font-size: 12px;
    margin-bottom: 8px;
    line-height: 16px;
  }
  &__stock-less {
    color: var(--ump-icon, #323233);
  }
  &__presale-date {
    color: #ed6a0c;
  }

  &__price-num {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;

    &--can-stepper {
      align-items: flex-start;

      .cart-goods__price {
        padding-top: 4px;
      }

      ::v-deep.price--origin {
        display: block;
        margin-top: 4px;
        margin-left: 0;
        font-size: 12px;
        line-height: 16px;
      }
    }
  }

  &__num {
    flex-shrink: 0;
    color: #666;
    font-size: 12px;
  }

  &__err-msg {
    font-size: 12px;
    color: #323233;
    line-height: 16px;
    margin-top: 3px;

    &--invalid {
      margin-top: 0;
    }
  }

  &__cut-price {
    font-size: 12px;
    line-height: 16px;
    color: var(--ump-icon, #323233);
    letter-spacing: 0;
    margin-bottom: 8px;
  }

  &__seven-return {
    font-size: 12px;
    letter-spacing: 0;
    line-height: 16px;
    margin-bottom: 8px;
    display: flex;
    &-tag {
      padding: 0 4px;
      border-radius: 2px;
      color: var(--brand-youzandanbao, #07C160);
      background: var(--brand-youzandaobao-bg, #E5F7EE);
    }
  }

  &__revive {
    font-size: 12px;
    color: #323233;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &__revive-btn {
    min-width: 0;
    width: 40px;
    height: 18px;
    line-height: 16px;
    font-size: 12px;
    color: var(--icon, #323233);
    border-color: var(--icon, #323233);
  }

  ::v-deep.van-stepper__plus,
  ::v-deep.van-stepper__minus {
    width: 24px;
    height: 24px;
  }

  ::v-deep.van-stepper__input {
    width: 34px;
    height: 24px;
    line-height: 24px;
  }
}
</style>