import BaseService from '../base/BaseService';
import {
  IItemEvaluationQueryParam,
  IItemEvaluationDetailModel,
  IEvaluationItemUpdateParam,
  IEvaluationUpdateParam,
  IEvaluationItemDeleteParam,
  IEvaluationDeleteParam,
  IAnonymousEvaluationParam,
  IEvaluationQuery4UserParam,
  IListWithPaginatorVO,
  IOrderEvaluationParam,
  IOrderItemEvaluationCreateParam,
  IItemEvaluationAdditionParam,
  IMapleAuthTokenParam,
  ICheckEvaluationFromMapleParams,
  ICheckWeappPluginByAppidParams,
  IEvaluationPresentationConfigQueryParam,
} from 'definitions/order/OrderEvaluate';

class OrderEvaluateService extends BaseService {
  get READ_SERVICE_NAME() {
    return 'com.youzan.review.service.ItemEvaluationReadService';
  }

  get WRITE_SERVICE_NAME() {
    return 'com.youzan.review.service.ItemEvaluationWriteService';
  }

  get MAPLE_TOKEN_SERVICE_NAME() {
    return 'com.youzan.bigdata.ad.dsp.api.service.maple.MapleTokenService';
  }

  get MAPLE_EVALUATION_NAME() {
    return 'com.youzan.bigdata.ad.dsp.api.service.maple.MapleEvaluationService';
  }

  get WEAPP_PLUGIN_INFOS_NAME() {
    return 'com.youzan.channels.apps.service.WeappPluginInfoService';
  }

  get EVALUATION_CONFIGURATION_NAME() {
    return 'com.youzan.review.service.EvaluationConfigurationService';
  }

  /**
   *  创建订单维度的评价
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/277666
   */
  async createOrderEvaluation(orderEvaluation: IOrderItemEvaluationCreateParam): Promise<number> {
    return this.invoke(this.WRITE_SERVICE_NAME, 'createOrderEvaluation', [orderEvaluation]);
  }

  /**
   *  创建评价维度的评价
   *  用于创建对评价的回复 评论 追评 操作
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/277667
   */
  async createItemAdditional(evaluationAdd: IItemEvaluationAdditionParam): Promise<any> {
    return this.invoke(this.WRITE_SERVICE_NAME, 'createItemAdditional', [evaluationAdd]);
  }

  /**
   *  已废弃
   *  创建评价维度的评价
   *  用于创建对评价的回复 评论 追评 操作
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/290859
   */
  async createOrderAdditionalV2(additionParam: IItemEvaluationAdditionParam): Promise<any> {
    return this.invoke(this.WRITE_SERVICE_NAME, 'createOrderAdditionalV2', [additionParam]);
  }

  /**
   *  获取订单评论列表
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/290399
   */
  async getOrderEvaluation(evaluationParam: IOrderEvaluationParam): Promise<IItemEvaluationDetailModel[]> {
    return this.invoke(this.READ_SERVICE_NAME, 'getOrderEvaluation', [evaluationParam]);
  }

  /**
   *  C端用户维度评价列表-分页
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/506704
   */
  async pageEvaluation4User(evaluationQuery4UserParam: IEvaluationQuery4UserParam): Promise<IListWithPaginatorVO> {
    return this.invoke(this.READ_SERVICE_NAME, 'pageEvaluation4User', [evaluationQuery4UserParam]);
  }

  /**
   *  评价匿名
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/511862
   */
  async anonymousEvaluation(anonymousEvaluationParam: IAnonymousEvaluationParam): Promise<any> {
    return this.invoke(this.WRITE_SERVICE_NAME, 'anonymousEvaluation', [anonymousEvaluationParam]);
  }

  /**
   *  取消匿名
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/511863
   */
  async cancelAnonymousEvaluation(anonymousEvaluationParam: IAnonymousEvaluationParam): Promise<any> {
    return this.invoke(this.WRITE_SERVICE_NAME, 'cancelAnonymousEvaluation', [anonymousEvaluationParam]);
  }

  /**
   *  删除追评
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/511864
   */
  async deleteEvaluation(evaluationDeleteParam: IEvaluationDeleteParam): Promise<any> {
    return this.invoke(this.WRITE_SERVICE_NAME, 'deleteEvaluation', [evaluationDeleteParam]);
  }

  /**
   *  删除评价
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/511864
   */
  async deleteItemEvaluation(evaluationDeleteParam: IEvaluationItemDeleteParam): Promise<any> {
    return this.invoke(this.WRITE_SERVICE_NAME, 'deleteItemEvaluation', [evaluationDeleteParam]);
  }

  /**
   *  修改评价 支持修改 追评 商家回复 评论 评论的回复
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/511859
   */
  async updateEvaluation(evaluationUpdateParam: IEvaluationUpdateParam): Promise<any> {
    return this.invoke(this.WRITE_SERVICE_NAME, 'updateEvaluation', [evaluationUpdateParam]);
  }

  /**
   *  修改商品评价 本接口只支持商品评价修改
   *  修改追评或者评论请用 {@link ItemEvaluationWriteService#updateItemEvaluation(EvaluationItemUpdateParam)}
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/511858
   */
  async updateItemEvaluation(evaluationItemUpdateParam: IEvaluationItemUpdateParam): Promise<any> {
    return this.invoke(this.WRITE_SERVICE_NAME, 'updateItemEvaluation', [evaluationItemUpdateParam]);
  }

  /**
   *  根据商品评价别名获取评价详情信息
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/281433
   */
  async getItemEvaluation(queryParam: IItemEvaluationQueryParam): Promise<IItemEvaluationDetailModel> {
    return this.invoke(this.READ_SERVICE_NAME, 'getItemEvaluation', [queryParam]);
  }

  // 获取枫树token
  async getMapleAuthToken(queryParam: IMapleAuthTokenParam) {
    return this.invoke(this.MAPLE_TOKEN_SERVICE_NAME, 'getMapleAuthToken', [queryParam]);
  }

  // 获取评价是否为枫树评价 & 是否是多商品订单
  async checkEvaluationFromMaple(queryParam: ICheckEvaluationFromMapleParams) {
    return this.invoke(this.MAPLE_EVALUATION_NAME, 'allowOpenMaplePlugin', [queryParam]);
  }

  // 判断商家小程序是否绑定枫树插件
  async checkWeappPluginByAppid(queryParam: ICheckWeappPluginByAppidParams) {
    return this.invoke(this.WEAPP_PLUGIN_INFOS_NAME, 'checkWeappPluginByAppid', [queryParam]);
  }

  /**
   *  * 获取商家配置的订单维度评价配置 查询评价展现配置
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1330866
   *
   *  @param {Object} param -
   *  @param {number} param.bizType - 业务类型
   *  @param {number} param.kdtId - 店铺kdtId
   *  @return {Promise}
   */
  async getEvaluationPresentationConfig(queryParam: IEvaluationPresentationConfigQueryParam) {
    return this.invoke(this.EVALUATION_CONFIGURATION_NAME, 'getEvaluationPresentationConfig', [queryParam]);
  }
}

export default OrderEvaluateService;
