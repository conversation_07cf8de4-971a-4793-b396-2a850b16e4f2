const URL = require('@youzan/iron-base/app/lib/URL');
const BaseController = require('../base/BaseController');
const JoinFeeService = require('../../services/groupbuying/JoinFeeService');

class HeaderOrderController extends BaseController {
  async init() {
    await super.init();
  }

  async pageAcl() {
    await this.acl({
      allowNotLogin: false,
      useAjaxLogin: false,
      forceOauthLogin: false,
      kdtId: this.ctx.kdtId,
      mpId: this.ctx.getLocalSession('mp_id')
    });
  }

  async getIndexHtml(ctx) {
    await this.pageAcl();
    if (ctx.query.kdtId) {
      ctx.kdtId = ctx.query.kdtId;
    }
    const kdtId = +ctx.kdtId;
    const joinFeeService = new JoinFeeService(ctx);
    const { applied, needPay, alreadyHeader } = await joinFeeService.checkHeaderState({
      kdtId,
      buyerId: this.buyerId
    });
    const { joinFee, feeName } = await joinFeeService.getSetting(kdtId);
    if (!applied || alreadyHeader || !needPay) {
      // 还没申请 -> 跳回招募页面 -> 状态查询 -> /index
      // 已经是团长 -> 跳回招募页面 -> 状态查询 -> /joined
      // 不需要交钱 -> 跳回招募页面 -> 状态查询 -> /joined or /index
      ctx.redirect(URL.site(`/wscump/groupbuying/recruit?kdtId=${kdtId}`, 'h5'));
      return;
    }
    // 需要缴费
    ctx.setGlobal({
      kdt_id: kdtId,
      joinFee,
      feeName
    });
    await ctx.render('groupbuying/join-fee.html');
  }

  async postPayFeeJson(ctx) {
    const kdtId = +ctx.kdtId;
    const params = {
      kdtId,
      buyerId: this.buyerId
    };
    const joinFeeService = new JoinFeeService(ctx);
    const result = await joinFeeService.payFee(params);
    ctx.json(0, '', result);
  }
}

module.exports = HeaderOrderController;
