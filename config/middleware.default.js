/**
 * 中间件配置文件
 */
const path = require('path');
const errorCodeHandler = require('../app/lib/ErrorCodeHandler').default;

module.exports = {
  'astroboy-security-csrf': {
    priority: 102,
    enable: true,
    match: ['/pay/wsctrade_cashier', '/pay/wsctrade/cashier'],
    options: {
      env: ['prod', 'pre', 'qa'],
      excluded: ['GET', 'HEAD', 'OPTIONS'],
      csrfSecretName: 'csrf-secret',
      csrfTokenName: 'csrf-token',
      saltLength: 10,
      secretLength: 18,
      maxAge: 3 * 3600 * 1000
    }
  },

  'astroboy-security-frameOptions': {
    enable: true,
    ignore: [/wsctrade\/cloud\/sandbox/, (ctx) => (ctx.userAgent || '').indexOf('xhsminiapp') > -1, (ctx) => ctx.query.isLiveHalfPage === '1']
  },

  // 仅在开发环境启用
  'astroboy-static': {
    enable: process.env.NODE_ENV !== 'prod',
    options: {
      root: path.resolve(__dirname, '../client/dist')
    }
  },

  'cache-control': {
    enable: true,
    priority: 200,
    ignore: [/wsctrade\/.+\.json\??/, /wsctrade\/order\/list/]
  },

  'sync-tracker': {
    enable: true,
    priority: 200,
    ignore: [/wsctrade\/.+\.json\??/, /wsctrade\/order\/list/]
  },

  'init-ua-session': {
    enable: true,
    priority: 101,
    options: {
      openJsonRedirect: false,
      getErrorLevel: errorCodeHandler,
      appId: 'wsc-h5-trade'
    }
  },

  // 只有小程序的请求才需要经过这个中间件
  'weapp-auth': {
    enable: true,
    priority: 102,
    ignore: [(ctx) => !ctx.isWeappNative || ctx.isSwanApp]
  },

  // 将h5.youzan.com中带有kdtId的url重定向到shop+kdtId.youzan.com
  'h5-host-redirect': {
    enable: true,
    priority: 296
  },

  // 特殊流量标识注入前端页面
  'h5-traffic-identify': {
    enable: true,
    priority: 297
  },
  // 自动调用成为销售员脚本
  'auto-be-salesman': {
    enable: true,
    priority: 201
  },
  'set-hummer-config': {
    enable: true
  },
  'set-console-config': {
    enable: true
  }
};
