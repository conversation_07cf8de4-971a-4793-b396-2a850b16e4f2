export = [
  ['GET', ['/pay/wsctrade_buy'], 'order.OrderBuyController', ['needPlatformShopTypeVerify', 'getIndexHtml']],
  ['GET', ['/pay/wsctrade_buy_prefetch'], 'order.OrderBuyController', ['getPrefetchIndexHtml']],
  [
    'GET',
    [
      '/pay/wsctrade_buy/address-edit',
      '/pay/wsctrade_buy/address-map',
      '/pay/wsctrade_buy/contact-edit',
      '/pay/wsctrade_buy/address-city',
      '/pay/wsctrade_buy/self-fetch-address-city',
      '/pay/wsctrade_buy/self-fetch-address',
      '/pay/wsctrade_buy/drug-message',
      '/pay/wsctrade_buy/drug-user',
      '/pay/wsctrade_buy/drug-user',
      '/pay/wsctrade_buy/drug-user',
      '/pay/wsctrade_buy/drug-history',
    ],
    'order.OrderBuyController',
    ['subPageRedirect'],
  ],

  ['GET', '/pay/wsctrade_pay', 'order.OrderBuyController', 'getPayHtml'],
  ['GET', '/pay/wsctrade/order/buy/getEarliestGuideOpenId.json', 'order.OrderBuyController', 'getEarliestGuideOpenId'],
  ['GET', '/pay/wsctrade/order/buy/pay-channels', 'order.OrderBuyController', 'getPayChannels'],
  ['POST', '/pay/wsctrade/order/buy/show.json', 'order.OrderBuyController', 'postShowJson'],
  ['POST', '/pay/wsctrade/order/buy/confirm.json', 'order.OrderBuyController', 'postConfirmJson'],
  ['POST', '/pay/wsctrade/order/buy/confirm-fast.json', 'order.OrderBuyController', 'postConfirmJsonFast'],
 
  ['POST', '/pay/wsctrade/order/buy/showPay.json', 'order.OrderBuyController', 'postShowPayJson'],
  ['POST', '/pay/wsctrade/order/buy/prepare.json', 'order.OrderBuyController', 'postPrepareJson'],
  ['GET', '/pay/wsctrade/order/buy/prepare-by-book-key.json', 'order.OrderBuyController', 'getPrepareByBookKeyJson'],
  ['POST', '/pay/wsctrade/order/buy/cross-store-re-cache-order-creation.json', 'order.OrderBuyController', 'crossStoreReCacheOrderCreation'],
  ['GET', '/pay/wsctrade/order/buy/cross-store-auto-enter-shop.json', 'order.OrderBuyController', 'crossStoreAutoEnterShop'],
  ['POST', '/pay/wsctrade/order/buy/bill.json', 'order.OrderBuyController', 'postBillJson'],
  ['POST', '/pay/wsctrade/order/buy/v2/bill.json', 'order.OrderBuyController', 'postBillV2Json'],
  ['POST', '/pay/wsctrade/order/buy/v2/bill-fast.json', 'order.OrderBuyController', 'postBillV2JsonFast'],
  ['POST', '/pay/wsctrade/order/buy/create-async-book-key.json', 'order.OrderBuyController', 'postCreateAsyncBookKey'],
  ['POST', '/pay/wsctrade/order/buy/postAsyncOrderResult.json', 'order.OrderBuyController', 'postAsyncOrderResult'],
  ['POST', '/pay/wsctrade/order/buy/prepay.json', 'order.OrderBuyController', 'postPrePayJson'],
  ['POST', '/pay/wsctrade/order/buy/getSelfFetchList.json', 'order.OrderBuyController', 'postSelfFetchListJson'],
  ['POST', '/pay/wsctrade/order/buy/v2/getSelfFetchList.json', 'order.OrderBuyController', 'postSelfFetchListV2Json'],
  ['POST', '/pay/wsctrade/order/buy/v3/getSelfFetchList.json', 'order.OrderBuyController', 'postSelfFetchListV3Json'],
  ['POST', '/pay/wsctrade/order/buy/getSelfFetch.json', 'order.OrderBuyController', 'postSelfFetchJson'],
  ['POST', '/pay/wsctrade/order/buy/v2/getSelfFetch.json', 'order.OrderBuyController', 'postSelfFetchV2Json'],
  ['POST', '/pay/wsctrade/order/buy/matchOffline.json', 'order.OrderBuyController', 'postMatchOfflineJson'],
  ['POST', '/pay/wsctrade/order/buy/getSelfFetchTime.json', 'order.OrderBuyController', 'postSelfFetchTimeJson'],
  ['POST', '/pay/wsctrade/order/buy/v2/getSelfFetchTime.json', 'order.OrderBuyController', 'postSelfFetchTimeV2Json'],
  ['POST', '/wsctrade/order/buy/weappbill.json', 'order.OrderBuyController', 'postWeappBillJson'],
  ['POST', '/wsctrade/order/buy/payChannels.json', 'order.OrderBuyController', 'postPayChannelsJson'],
  ['POST', '/wsctrade/order/buy/preorderPay.json', 'order.OrderBuyController', 'postPreorderPayJson'],
  ['GET', '/pay/wsctrade/order/buy/getAssetForOrder.json', 'order.OrderBuyController', 'getAssetForOrderJson'],
  ['POST', '/pay/wsctrade/order/buy/exchangeCoupon.json', 'order.OrderBuyController', 'postExchangeCoupon'],
  ['POST', '/pay/wsctrade/order/buy/rebateMessage.json', 'order.OrderBuyController', 'postRebateMessage'],
  ['POST', '/pay/wsctrade/order/buy/useAsset.json', 'order.OrderBuyController', 'postUseAssetJson'],
  ['POST', '/pay/wsctrade/order/buy/verifyIdcard.json', 'order.OrderBuyController', 'postVerifyIdcardJson'],
  ['POST', '/pay/wsctrade/order/buy/getDefaultSelfFetch.json', 'order.OrderBuyController', 'getDefaultSelfFetchJson'],
  ['POST', '/pay/wsctrade/order/buy/plusBuy.json', 'order.OrderBuyController', 'postPlusBuyJson'],
  ['POST', '/pay/wsctrade/order/buy/fetchOrderKeep.json', 'order.OrderBuyController', 'postFetchOrderKeepJson'],
  [
    'GET',
    '/pay/wsctrade/order/buy/getReceiverIdentityList.json',
    'order.OrderBuyController',
    'getReceiverIdentityList',
  ],
  ['GET', '/pay/wsctrade/order/buy/checkIsNewProcess.json', 'order.OrderBuyController', 'checkIsNewProcess'],
  ['POST', '/pay/wsctrade/order/buy/getLiveActivity.json', 'order.OrderBuyController', 'getLiveActivity'],
  ['GET', '/pay/wsctrade/order/buy/scene/check', 'order.OrderBuyController', 'sceneCheck'],
  ['GET', '/pay/wsctrade/order/buy/scene/check-compatible', 'order.OrderBuyController', 'sceneCheckCompatible'],
  ['GET', '/pay/wsctrade/order/buy/wx-order-info', 'order.OrderBuyController', 'getWxOrderInfo'],
  ['POST', '/pay/wsctrade/order/buy/modify-cache.json', 'order.OrderBuyController', 'modifyCacheOrderJson'],
  [
    'GET',
    '/pay/wsctrade/order/buy/query-order-preparation.json',
    'order.OrderBuyController',
    'queryMultiOrderPreparationV2',
  ],
  [
    'POST',
    '/pay/wsctrade/order/buy/prefetch-prepare.json',
    'order.OrderBuySubController',
    'prefetchPrepare',
  ],
  [
    'GET',
    ['/wsctrade/order/buy/getDefaultDeliveryData.json'],
    'order.OrderBuyController',
    'getDefaultDeliveryData',
  ],
  [
    'GET',
    ['/wsctrade/order/buy/setConfirmAddressHidden.json'],
    'order.OrderBuyController',
    'setConfirmAddressHidden',
  ],
];
