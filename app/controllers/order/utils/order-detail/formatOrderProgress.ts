import { IShelfOrderProgressResponse } from 'definitions/api/retail/misc/ShelfProgressService/queryOrderProgress';

/**
 * 等待状态枚举
 */
export const WAITING_STATUS = {
  /** 已下单 */
  PAID: 1,
  /** 制作中 */
  MAKING: 2,
  /** 制作完成 */
  DONE: 3,
} as const;

/**
 * 订单步骤信息
 */
export interface IOrderStep {
  /** 步骤文本 */
  text: string;
  /** 步骤键值 */
  key: number;
  /** 是否激活 */
  active: boolean;
}

/**
 * 格式化后的订单进度数据
 */
export interface IFormattedOrderProgress {
  /** 原始数据 */
  orderNo?: string;
  bizType?: number;
  isNeedDisplay?: boolean;
  currentGoodsNum?: number;
  orderNum?: number;
  goodsMakingStatus?: number;
  productTime?: number;
  goodsNum?: number;
  queueProcessNumSwitch?: boolean;
  queueProcessTimeSwitch?: boolean;
  queueShowUnit?: string;
  
  /** 格式化后的数据 */
  /** 状态描述 */
  statusDesc?: string;
  /** 订单步骤 */
  orderSteps?: IOrderStep[];
  /** 进度字符串 */
  progressStr?: string;
  /** 等待时间（分钟） */
  waitTime?: number;
}

/**
 * 格式化订单等待进度
 * 
 * @param orderWaitingProgress - 订单进度原始数据
 * @returns 格式化后的订单进度数据
 */
export function formatOrderWaitingProgress(
  orderWaitingProgress?: IShelfOrderProgressResponse
): IFormattedOrderProgress {
  // 没有数据返回空对象
  if (!orderWaitingProgress) {
    return {};
  }

  const {
    bizType,
    isNeedDisplay,
    goodsMakingStatus,
    orderNum,
    goodsNum,
    productTime,
    queueProcessTimeSwitch,
    queueShowUnit,
    queueProcessNumSwitch,
  } = orderWaitingProgress;

  // 1为自提，非自提不处理
  if (bizType !== 1) {
    return {};
  }

  // 如果不需要展示进度，返回空对象
  if (!isNeedDisplay) {
    return {};
  }

  const data: IFormattedOrderProgress = {
    ...orderWaitingProgress,
  };

  // 排队状态步骤条
  const waitingSteps: IOrderStep[] = [
    {
      text: '已下单',
      key: WAITING_STATUS.PAID,
      active: false,
    },
    {
      text: '制作中',
      key: WAITING_STATUS.MAKING,
      active: false,
    },
    {
      text: '请取货',
      key: WAITING_STATUS.DONE,
      active: false,
    },
  ];

  // 根据当前制作状态 goodsMakingStatus 来确定展示状态和激活状态
  if (goodsMakingStatus && goodsMakingStatus >= 1 && goodsMakingStatus <= 3) {
    const currentMakingStatus = waitingSteps[goodsMakingStatus - 1];
    data.statusDesc = currentMakingStatus.text;
    currentMakingStatus.active = true;
  }
  
  data.orderSteps = waitingSteps;

  // 排队信息格式化，有排队信息并且开关开启才返回
  if (orderNum && goodsNum && queueProcessNumSwitch) {
    const maxGoodsNum = 999; // 最大商品数限制
    const showNum = Math.min(goodsNum, maxGoodsNum);
    const unit = queueShowUnit || '杯'; // 默认单位为杯
    data.progressStr = `${orderNum}单/${showNum}${unit}`;
  }

  // 排队时间格式化，有排队时间并且开关开启才返回
  if (queueProcessTimeSwitch && productTime) {
    data.waitTime = Math.ceil(productTime / 60); // 转换为分钟并向上取整
  }

  return data;
}
