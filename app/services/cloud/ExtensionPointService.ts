import BaseService from '../base/BaseService';
import { DesignVO } from 'definitions/order/buy/DesignVO';

class ExtensionPointService extends BaseService {
  private get PAGE_EXTENSION_SERVICE() {
    return 'com.youzan.cloud.columbus.api.front.PageExtensionService';
  }

  private get BACKEND_EXTENSION_SERVICE() {
    return 'com.youzan.cloud.open.router.extension.impl.api.router.AppRouteQuery';
  }

  private get DEFAULT_CONFIG(): {
    [name: string]: DesignVO[];
  } {
    return {
      // 下单页默认配置
      buy: [
        { type: 'address-block' },
        { type: 'goods-block' },
        { type: 'ump-block' },
        { type: 'service-block' },
        { type: 'price-block' },
        { type: 'installment-block' },
        { type: 'order-info-block' },
        { type: 'copyright-block' },
        { type: 'submit-block' },
      ],
      // 订单详情页默认配置
      'order-detail': [
        /* { type: 'config' }, */
        /* { type: 'guide' }, */
        { type: 'order-tips' },
        { type: 'order-status' },
        { type: 'od__virtual-goods' },
        { type: 'order-address' },
        { type: 'od__special-feature' },
        { type: 'goods-info' },
        { type: 'od__virtual-ticket-tpl' },
        { type: 'od__service-block' },
        { type: 'od__education' },
        { type: 'pay-panel' },
        { type: 'order-base-info' },
        { type: 'cps-goods-recommend', cpsConfigKey: 'cps_goods_recommend_order_detail' },
        { type: 'recommend-block' },
        { type: 'order-footer' },
        { type: 'bottom-action' },
      ],
      // 订单列表页默认配置
      'order-list': [
        { type: 'search-bar' },
        { type: 'list-tabs' },
        { type: 'around-goods-order' },
        {
          type: 'list',
          children: [{ type: 'list-item-header' }, { type: 'order-list' }, { type: 'list-item-footer' }],
        },
        { type: 'no-more-tip' },
        { type: 'empty-tip' },
        { type: 'cps-goods-recommend', cpsConfigKey: 'cps_goods_recommend_order_list' },
        { type: 'recommend-block' },
      ],
      // 支付结果页
      'order-pay-result-new': [
        { type: 'msg-block' },
        { type: 'award-block' },
        { type: 'action-block' },
        { type: 'secret-free-pay-block' },
        { type: 'coupon-block' },
        { type: 'activity-block' },
        { type: 'ad-block' },
        { type: 'cps-goods-recommend', cpsConfigKey: 'cps_goods_recommend_pay_success' },
        { type: 'recommend-block' },
      ],
      'order-pay-result': [
        { type: 'msg-block' },
        { type: 'self-fetch-block' },
        { type: 'cell-block' },
        { type: 'action-block' },
        { type: 'activity-block' },
        { type: 'tips-block' },
        { type: 'cps-goods-recommend', cpsConfigKey: 'cps_goods_recommend_pay_success' },
        { type: 'recommend-block' },
      ],
      // 购物车
      cart: [
        { type: 'login-tips' },
        { type: 'valid-goods' },
        { type: 'invalid-goods' },
        { type: 'cps-goods-recommend', cpsConfigKey: 'cps_goods_recommend_shopping_cart' },
        { type: 'recommend-block' },
        { type: 'cart-bottom' },
      ],
      // 批量退款-选择商品页
      'batch-refund-select-goods': [{ type: 'select-goods' }],
      'express-detail': [{type: 'express'}],
    };
  }

  // 获取页面对应的 Design 配置，可以使用options.useDefault来跳过定制检查直接使用默认配置
  public async getDesignConfig(pageName: string, options?: any): Promise<[boolean, DesignVO[]]> {
    let config: DesignVO[] = [];
    const { ctx } = this;
    const kdtId = +this.ctx.kdtId;
    const { useDefault = false } = options || {};

    let customizedOut = false;
    try {
      // 使用diyWhiteListCheck方法检查页面是否定制
      const res = await this.diyWhiteListCheck(kdtId, pageName);

      // eslint-disable-next-line prefer-destructuring
      const app: any = this.app;

      if (res && !useDefault) {
        const pageType = ctx.isWeapp ? 'applet' : 'h5';
        const params = {
          kdtId,
          pageName,
          conditionContext: {
            pageType,
            platform: ctx.platform,
          },
        };

        const { customized, pageContent } = await this.invoke(this.PAGE_EXTENSION_SERVICE, 'discover', [params]);

        if (customized) {
          customizedOut = true;
          config = JSON.parse(pageContent);
        }
      }
    } catch (err) {
      ctx.logger.warn('extensionService WhiteListCheck invoke fail', err, {
        url: ctx.url,
        method: ctx.method,
        originalUrl: ctx.originalUrl,
        host: ctx.host,
        headers: ctx.headers,
        query: ctx.getQueryData(),
        requestBody: ctx.getPostData(),
      });
    }

    if (!Array.isArray(config) || config.length === 0) {
      config = this.DEFAULT_CONFIG[pageName];
    }

    return [customizedOut, config];
  }

  public async doCheckCloudRedirect(pageName: string): Promise<any> {
    const { ctx } = this;
    const kdtId = +this.ctx.kdtId;
    const pageType = ctx.isWeapp ? 'applet' : 'h5';
    const params = {
      kdtId,
      pageName,
      conditionContext: {
        pageType,
        platform: this.ctx.platform,
      },
    };
    let redirectUrl = '';
    try {
      const { data } = await this.redirectCheck(kdtId, pageName, params);
      if (data && data.needRedirect) {
        redirectUrl = data.redirectPath;
      }
    } catch (err) {
      ctx.logger.warn('redirectCheck  fail', err, {
        url: ctx.url,
        method: ctx.method,
        originalUrl: ctx.originalUrl,
        host: ctx.host,
        headers: ctx.headers,
        query: ctx.getQueryData(),
        requestBody: ctx.getPostData(),
      });
    }

    return redirectUrl;
  }

  public async checkCloudCustomized(kdtId: number, pageName: string) {
    const result = await this.httpCall({
      method: 'GET',
      url: `${this.getConfig('CLOUD_DESIGN')}/h5-extension-service/is-cloud-design?pageName=${pageName}&kdtId=${kdtId}`,
    });
    return result;
  }

  /**
   * 获取后端扩展点列表
   * 该接口在调用侧设置了RPC缓存
   * 见: https://ops.qima-inc.com/#/applications/1135/service_admin/rpc_cache?buId=1
   */
  public async getExtensionIdList(kdtId: number | string) {
    /**
     * 获取订阅者定制的扩展点
     * zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1276157
     * type=0: id就传kdtId
     * type=1: id就传商户号
     */
    const res: {
      extensionIds?: number[];
    } = await this.invoke(this.BACKEND_EXTENSION_SERVICE, 'querySubscribingExtensions', [
      {
        subscriberType: 0,
        subscriberId: String(kdtId),
      },
    ]);

    return res?.extensionIds || [];
  }
}

export = ExtensionPointService;
