import BaseService from '../../base/BaseService';

/**
 * com.youzan.trade.bridge.api.identity.model.IdentityDTO
 */
interface IIdentityDTO {
  /** 姓名 */
  name?: string;
  /** 是否绑定 */
  binding?: boolean;
  /** 认证账号 */
  identityAccount?: string;
  /** 身份证号 */
  identityCard?: string;
  /** 买家ID */
  buyerId?: number;
}

/**
 * com.youzan.trade.bridge.api.identity.identify.dto.request.KdtBuyerIdentificationRequestDTO
 */
interface IKdtBuyerIdentificationRequestDTO {
  /** 订单号 */
  orderNo?: string;
  /** 店铺ID */
  kdtId?: number;
  /** 扩展字段 */
  extra?: {};
  /** 最多可校验多少次 */
  identifiableTimesAtMost?: number;
  /** 身份信息 */
  identityDTO?: IIdentityDTO;
  /** 校验次数上限是否开启 */
  identificationTimesLimitEnable?: boolean;
  /** 请求来源 */
  requestSource?: string;
  /** bookKey */
  bookKey?: string;
}

// IKdtBuyerIdentificationRequestDTO
class KdtBuyerIdentificationService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.trade.bridge.api.identity.identify.KdtBuyerIdentificationService';
  }

  /**
   *  校验比对店铺买家姓名身份证号
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/323408
   *
   */
  async identifyKdtBuyer(kdtBuyerIdentificationRequestDTO: IKdtBuyerIdentificationRequestDTO) {
    return this.invoke(this.SERVICE_NAME, 'identifyKdtBuyer', [kdtBuyerIdentificationRequestDTO]);
  }
}

export default KdtBuyerIdentificationService;
