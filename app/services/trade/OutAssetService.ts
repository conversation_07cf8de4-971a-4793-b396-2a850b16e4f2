import BaseService from '../base/BaseService';
import mapKeysToCamelCase from '@youzan/utils/string/mapKeysToCamelCase';

class OutAssetService extends BaseService {
  get OUT_ASSET_SERVICE() {
    return 'com.youzan.ebiz.carmel.center.api.OutAssetService';
  }

  // 获取外部券信息
  async getAssetForOrder(params: any) {
    params = mapKeysToCamelCase(params);
    return await this.invoke(this.OUT_ASSET_SERVICE, 'getAssetForOrder', [params]);
  }

  async useAsset(params: any) {
    params = mapKeysToCamelCase(params);
    return await this.invoke(this.OUT_ASSET_SERVICE, 'useAsset', [params]);
  }
}

export = OutAssetService;
