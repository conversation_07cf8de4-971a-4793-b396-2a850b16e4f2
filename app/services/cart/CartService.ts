import BaseService from '../base/BaseService';
import { Context } from 'astroboy';
import {
  IGetListGoodsReqQuery,
  ICartBuyer,
  ICartGoodsReq,
  ICartMergeReq,
  ICartGoodsNumReq,
  ICartCountGoodsNums,
  ICartReselectGoodsReq,
  ICartToggleSelectAllGoods,
  ICartMultiRecommendGoods,
} from 'definitions/cart/Cart';

/* eslint-disable @typescript-eslint/ban-ts-comment, camelcase */
class CartService extends BaseService {
  get CART_VIEW_SERVICE() {
    return 'com.youzan.trade.shopping.cart.api.view.service.TradeCartViewService';
  }

  get CART_MAIN_SERVICE() {
    return 'com.youzan.trade.shopping.cart.api.main.TradeCartService';
  }

  get GOODS_PLUS_BUY_SERVICE() {
    return 'com.youzan.ump.goods.details.api.service.PlusBuyService';
  }

  get GOODS_SERVICE() {
    return 'com.youzan.bigdata.datacenter.consumer.api.service.GoodsService';
  }

  get TRADE_CART_PROMOTION_SERVICE() {
    return 'com.youzan.ebiz.social.facade.api.marketing.service.TradeCarPromotionService';
  }

  async getCartList(params: IGetListGoodsReqQuery) {
    const dubboData =
      (await this.invoke(this.CART_VIEW_SERVICE, 'listGoods', [params], {
        allowBigNumberInJSON: true,
      })) || [];
    return dubboData;
  }

  static getCartIdRequestData(ctx: Context, buyerId: number, oldUserId: number, isGuang: boolean) {
    const data: ICartBuyer = {
      buyerId,
      platform: isGuang ? 'guang' : 'wsc',
    };
    const { fans_type, youzan_fans_id, fans_id } = ctx.getLocalSession();
    let nobody =
      // @ts-ignore
      ctx.sessionId || ctx.cookies.get('nobody_sign') || ctx.getLocalSession().nobody_sign;

    // 百度小程序取
    if (ctx.isSwanApp) {
      nobody = ctx.KDTWEAPPSESSIONID;
    }

    if (oldUserId) {
      data.mockID = oldUserId;
    }
    if (youzan_fans_id) {
      data.youzanFansId = youzan_fans_id;
    }
    if (fans_id) {
      data.fansId = fans_id;
    }
    if (fans_type) {
      data.fansType = fans_type;
    }
    if (nobody) {
      data.nobody = nobody;
    }
    return data;
  }

  // 获取店铺自定义购物车名称
  async customCartName(params: number) {
    const result = await this.ctx.getShopConfigsWithKdtId(params, ['custom_cart_name']);
    return {
      value: result.custom_cart_name,
    };
  }

  // 批量删除和单个商品删除都用此服务
  async deleteGoodsBatchOrSingle(params: ICartGoodsReq) {
    const dubboData = await this.invoke(this.CART_VIEW_SERVICE, 'deleteGoods', [params]);
    return dubboData;
  }

  async updateCartGoodsNum(params: ICartGoodsNumReq) {
    const dubboData = await this.invoke(this.CART_VIEW_SERVICE, 'updateGoodsNum', [params]);
    return dubboData;
  }

  async selectGoodsService(params: ICartGoodsReq) {
    const dubboData = await this.invoke(this.CART_MAIN_SERVICE, 'selectCartGoods', [params]);
    return dubboData;
  }

  async unselectGoodsService(params: ICartGoodsReq) {
    const dubboData = await this.invoke(this.CART_MAIN_SERVICE, 'unSelectCartGoods', [params]);
    return dubboData;
  }

  async selectAllGoodsService(params: ICartToggleSelectAllGoods) {
    const dubboData = await this.invoke(this.CART_MAIN_SERVICE, 'selectAllGoods', [params]);
    return dubboData;
  }

  async unselectAllGoodsService(params: ICartToggleSelectAllGoods) {
    const dubboData = await this.invoke(this.CART_MAIN_SERVICE, 'unSelectAllGoods', [params]);
    return dubboData;
  }

  async mergeCartGoodsService(params: ICartMergeReq) {
    const dubboData = await this.invoke(this.CART_MAIN_SERVICE, 'mergeCart', [params]);
    return dubboData;
  }

  async batchAddGoodsService(params: ICartGoodsReq) {
    const dubboData = await this.invoke(this.CART_VIEW_SERVICE, 'batchAddGoods', [params]);
    return dubboData;
  }

  async countGoodsNums(params: ICartCountGoodsNums) {
    const dubboData = await this.invoke(this.CART_MAIN_SERVICE, 'countGoodsNums', [params]);
    return dubboData;
  }

  // 重选接口
  async reselectGoods(params: ICartReselectGoodsReq) {
    const dubboData = await this.invoke(this.CART_VIEW_SERVICE, 'reselectGoods', [params], {
      allowBigNumberInJSON: true,
    });
    return dubboData;
  }

  /**
   * 获取换购商品，以商品为维度
   * @param {number} kdtId 店铺id
   * @param {number} activityId 活动id
   * @param {number} offlineId 网点id
   */
  findAggregatedExchangeSkus(kdtId: number, activityId: number, offlineId: number) {
    return this.invoke(this.GOODS_PLUS_BUY_SERVICE, 'findAggregatedExchangeSkus', [+kdtId, +activityId, +offlineId]);
  }

  async getMultiRecommendGoods(params: ICartMultiRecommendGoods) {
    const dubboData = await this.invoke(this.GOODS_SERVICE, 'getMultiRecommendGoods', [params]);
    return dubboData;
  }

  async getCouponAddonInfo(params: any) {
    return await this.invoke(this.TRADE_CART_PROMOTION_SERVICE, 'getTradeCarPromotion', [params]);
  }

  async reselectGoodsActivity(params: any) {
    return await this.invoke(this.CART_VIEW_SERVICE, 'reselectGoodsActivity', [params]);
  }
}

export default CartService;
