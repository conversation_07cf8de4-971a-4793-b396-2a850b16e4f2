import _ from 'lodash';
import URL from '@youzan/iron-base/app/lib/URL';
import buildUrlWithCtx from '@youzan/utils/url/buildUrlWithCtx';
import WapUrl from '@youzan/iron-base/app/lib/WapUrl';
import mapKeysToCamelCase from '@youzan/utils/string/mapKeysToCamelCase';
import { PageException } from '@youzan/iron-base';
import {
  checkPureWscSingleStore,
  checkRetailMinimalistShop,
  checkRetailChainStore,
  checkRetailShop,
} from '@youzan/utils-shop';
import { isCloudDesign } from '@youzan/plugin-h5-ecloud';
import { initRanta } from '@youzan/plugin-h5-ranta-config';
import BaseController from '../base/BaseController';
import OrderListService from '../../services/order/OrderListService';
import SecureService from '../../services/order/SecureService';
import MultiStoreSettingService from '../../services/multistore/MultiStoreSettingService';
import ExtensionPointService from '../../services/cloud/ExtensionPointService';
import ShopConfigReadService from '../../services/shop/ShopConfigReadService';
import OrderPayResultService from '../../services/order/OrderPayResultService';
import ActivityQueryCobuildService from '../../services/api/ump/voucher/ActivityQueryCobuildService';
import { isInGrayReleaseByKdtId } from '../../lib/WhiteListUtils';
import { IOrderListReq, IOneOrderReq, OrderListType } from 'definitions/order/OrderList';
import { getDirectBuyAgainBtnConfig } from '../../lib/TradeConfig';
import { getTradeApolloConfig } from '../../lib/Apollo';
import ShopConfigurationService from '../../services/shop/ShopConfigurationService';
import { getBrandShopStyleToState } from '@youzan/plugin-h5-global-theme';
import CompareVersion from '../../lib/CompareVersion';
import {
  DEFAULT_POINTS_NAME,
  computedOrderActions,
} from './utils/order-buttons';

// 兼容 iron 传参
const TYPE: { [index: string]: OrderListType } = {
  all: 'all',
  topay: 'topay',
  tosend: 'tosend',
  send: 'send',
  sign: 'sign',
  totuan: 'togroup',
  safe: 'rights',
  toevaluate: 'toevaluate',
  going: 'going',
  current: 'current',
  history: 'history',
};

// 订单列表数据类型
const PAGE_TYPE = {
  SINGLE: 'single', // 单店列表
  ALL: 'all', // 全部列表
};

class OrderListController extends BaseController {
  public async init() {
    const hasKdtId = !!this.ctx.kdtId;
    await super.init({
      initMpData: hasKdtId,
      initShopSettings: hasKdtId,
      initOpenAppConfig: true,
      initMpAccount: hasKdtId,
      initGlobalTheme: true,
      initBaseFooter:
        !this.ctx.acceptJSON &&
        /\/wsctrade\/order\/list\?/.test(this.ctx.url) &&
        this.ctx.query.pageType !== 'all' &&
        !/from=ivr/.test(this.ctx.url),
    });
  }

  private orderListAcl(kosTokenVerified = false) {
    return kosTokenVerified ? this.basicAcl() : this.needPlatformAcl();
  }

  public async getSelffetchHtml() {
    const { ctx } = this;
    await this.orderListAcl();
    if (ctx.status === 302) {
      return;
    }
    ctx.setState('title', '待自提订单');
    const clientUrl = this._getClientUrl(ctx.kdtId);
    ctx.setGlobal('client_url', clientUrl);
    await ctx.render('order/selffetch.html');
  }

  public async getSelffetchJson() {
    const { ctx } = this;
    const { buyerId, fansId, fansType } = this._getBuyer();
    const { crossShopSearch } = ctx.validator({
      crossShopSearch: ctx.joi.boolean(),
    });

    const reqData = mapKeysToCamelCase(ctx.getQueryData() || {});
    const { page, pageSize, storeId } = reqData;
    let { selfFetchId } = reqData;
    const { kdtId } = ctx;
    // 检查是否为零售连锁店铺
    const isRetailChainShop = this._isRetailChainShop(kdtId);
    if (!selfFetchId || selfFetchId === 'undefined' || !isRetailChainShop) {
      selfFetchId = null;
    }
    const params = {
      kdtId,
      storeId,
      buyerId,
      fansId,
      fansType,
      state: 'tosend',
      page,
      pageSize,
      selfFetchId,
      crossShopSearch,
    };
    const result = await new OrderListService(ctx).getSelffetchOrder(params);
    ctx.r(0, '', result);
  }

  public async getSelffetchDetailHtml() {
    const { ctx } = this;
    const kosTokenVerified = await new SecureService(ctx).validKosToken();
    await this.orderListAcl(kosTokenVerified);

    if (ctx.status === 302) {
      return;
    }

    ctx.setState('title', '自提订单提货凭证');

    const reqData = mapKeysToCamelCase(ctx.getQueryData() || {});
    const { orderNo, selfFetchId } = reqData;
    const orderListService = new OrderListService(ctx);
    const isInvalidSelfFetchId = isNaN(Number(selfFetchId));
    const result = await orderListService.getSelffetchDetailByOrderNo(
      orderNo,
      ctx.kdtId,
      kosTokenVerified,
      isInvalidSelfFetchId ? null : selfFetchId // 如果转number是NaN，就传null
    );

    const { kdtId } = ctx;
    const clientUrl = this._getClientUrl(kdtId);
    // 核销页到店自提有礼需要shopInfo
    const shopMetaInfo = ctx.getState('shopMetaInfo' as never) || {};
    ctx.setGlobal('shopMetaInfo', shopMetaInfo);
    ctx.setGlobal('client_url', clientUrl);
    ctx.setGlobal('selffetch_info', result);
    await ctx.render('order/selffetch-detail.html');
  }

  public async getSelffetchDetailJson() {
    const { ctx, buyerId } = this;
    const reqData = mapKeysToCamelCase(ctx.getQueryData() || {});
    if (!buyerId) {
      return ctx.json(10000, '无权限查看');
    }
    const { orderNo } = reqData;
    const orderListService = new OrderListService(ctx);
    const result = await orderListService.getSelffetchDetailByOrderNo(orderNo, ctx.kdtId);
    ctx.r(0, '', result);
  }

  public async postVerifySelffetchJson() {
    const { ctx } = this;
    let { kdtId } = ctx;
    const { hqKdtId } = ctx.getPostData();
    const isRetailChainShop = this._isRetailChainShop(kdtId);
    if (hqKdtId && isRetailChainShop) {
      kdtId = hqKdtId;
    }
    const orderListService = new OrderListService(ctx);
    const params = {
      ...ctx.getPostData(),
      kdtId,
    };
    const result = await orderListService.verifySelffetch(params);
    ctx.r(0, '', result);
  }

  public async getSelfFetchCoupons() {
    const { ctx } = this;
    const result = await new OrderPayResultService(ctx).getSelfFetchCoupons({ kdtId: ctx.kdtId, userId: ctx.userId });
    ctx.json(0, '', result);
  }

  // 获取店铺核销开关配置
  public async getWriteOffConfigJson() {
    const { ctx } = this;
    let { kdtId } = ctx;
    const { selfPointKdtId, isMultiConfig } = ctx.query;
    // 零售连锁获取履约单元的kdtid
    const isRetailChainShop = this._isRetailChainShop(kdtId);
    // @ts-ignore
    if (isRetailChainShop) {
      kdtId = selfPointKdtId || ctx.kdtId;
    }
    const multiStoreSettingService = new MultiStoreSettingService(ctx);
    const shopConfigurationService = new ShopConfigurationService(ctx);
    const [writeOffType, supportCrossShop] = await Promise.all([
      multiStoreSettingService.getShopConfig(kdtId, 'self_write_off_type'),
      shopConfigurationService.queryShopConfig('self_fetch_verify_cross_shop').catch(() => '0'),
    ]);
    if (isMultiConfig) {
      ctx.r(0, 'ok', {
        writeOffType,
        supportCrossShop,
      });
    } else {
      ctx.r(0, 'ok', writeOffType);
    }
  }

  // 微商城订单列表页面
  public async getListHtml() {
    const { ctx } = this;
    await this.teeRantaAcl();
    if (ctx.status === 302) {
      return;
    }
    const { type = 'all', kdt_id, isWeapp, pageType, orderMark } = ctx.query;
    // 在全店铺场景kdtId存在为0的情况，后面依赖kdtId的注意防御
    const kdtId = ctx.kdtId || 0;
    if (kdt_id && kdtId) {
      ctx.kdtId = kdtId; // 此操作会写cookie，支持query.kdt_id重写入cookie
    }

    this.setExtensionParams(ctx, 'order-list');

    const buildUrl = buildUrlWithCtx(this.ctx);

    const loginWithCodeUrl =
      'https://passport.youzan.com/mobile?redirectUrl=' +
      encodeURIComponent(buildUrl('https://h5.youzan.com/wsctrade/order/list?type=all', '', kdtId));

    // @ts-ignore
    const wapHomeUrl = new WapUrl(ctx).getHomePageUrlByKdtId(kdtId);
    const clientUrl = {
      homePageUrl: kdtId ? wapHomeUrl : 'https://maijia.youzan.com',
      loginWithCodeUrl,
    };

    let title = '订单列表';
    switch (type) {
      case 'all':
        title = '所有订单';
        break;
      case 'topay':
        title = '待付款的订单';
        break;
      case 'totuan':
        title = '待接单的订单';
        break;
      case 'tosend':
        title = '待发货的订单';
        break;
      case 'send':
        title = '已发货的订单';
        break;
      case 'sign':
        title = '已完成的订单';
        break;
      case 'toevaluate':
        title = '待评价的订单';
        break;
      default:
        break;
    }
    ctx.setState('title', title);
    ctx.setGlobal({
      type,
      kdtId,
      isWeapp,
      pageType,
      orderMark,
      clientUrl,
      showFooter: true,
    });

    let logType;
    if (type === 'gift_receive') {
      // spm 统计埋点 我收到的礼物
      logType = 'uc';
    } else {
      // spm 统计埋点 订单列表
      logType = 'ol';
    }

    // 集中处理异步请求
    const response = await Promise.all([
      new ShopConfigReadService(ctx).queryShopPointsName(kdtId).catch(() => {}),
      new ShopConfigReadService(ctx).getOrderEnterShopPolicy().catch(() => {}), // 获取历史订单的进店配置
    ]);

    const [pointsName, orderEnterShopPolicy] = response;

    ctx.setGlobal({
      pointsName, // 积分自定义名称
      orderEnterShopPolicy, // 历史订单及其分享是否需要接入进店
    });
    if (kdtId) {
      const [shopMetaInfo, shopConfigs] = await Promise.all([
        this.callService('iron-base/shop.ShopMetaReadService', 'getShopMetaInfo', ctx.kdtId),
        // 爱逛商家存在没有kdtId的情况
        ctx.getShopConfigsWithKdtId(kdtId, ['cps_goods_recommend_opened_flag']).catch(() => {}),
      ]);
      ctx.setGlobal('shopConfigs', _.get(shopConfigs, 'configs', {}));
      ctx.setGlobal('shopMetaInfo', shopMetaInfo);
    }

    // 日志埋点模型
    this.setSpm(logType, kdtId);

    // 是否显示再来一单按钮[直接进入下单页]
    const directBuyAgainBtnConfig = await getDirectBuyAgainBtnConfig(ctx);
    ctx.setGlobal({
      directBuyAgainBtnConfig,
    });

    await this.renderOrderListTemplate();
  }

  // 查询列表
  public async getListJson() {
    const { ctx } = this;
    if (ctx.kdtId && !ctx.cookies.get('_kdt_id_')) {
      ctx.logger.warn('订单列表接口cookie_kdt_id分析', null, {
        ctxKdtId: ctx.kdtId,
        ua: ctx.userAgent,
      });
    }

    const {
      page_id = 'wsc',
      page = 1,
      page_size = 20,
      type = 'all',
      orderMark,
      from,
      knowledgeGoods,
      receivertel,
      receivername,
      goodstitle,
      keyword,
      isDrugList = 'false',
      caller,
      haveOfflineOrder: offlineOrderRequestVersion,
      canUseTradeUmpV1,
      resultFormat = 'snakeCase',
      retailFulfillDetail,
      withRepurchaseCoupon = 'true', // 是否在请求订单列表数据时带上复购券信息，用于兼容老版本小程序
    } = ctx.query;
    const { platform, kdtId = '', isXhsApp, isKsApp, isWeapp, isTTApp, isQQApp, isAlipayApp } = ctx;
    const pageType: string = ctx.query.page_type || PAGE_TYPE.SINGLE;
    const params: IOrderListReq = {
      buyerId: this.buyerId,
      offset: 0,
      pageId: page_id,
      page,
      pageSize: page_size,
      status: isDrugList === 'true' ? 'togroup' : TYPE[type], // togroup代表医药待开方、审核的单子
      // status: TYPE[type],
      platform: 'wsc',
      extra: {
        searchtag: 'wsc',
      },
      isDrugList,
      marketingMark: {
        source: 'order_list',
        bizName: 'order_list',
      },
    };

    // 根据 orderMark 筛选订单
    if (orderMark) {
      params.caller = 'H5';
      params.orderMark = orderMark;
    }

    caller && (params.caller = caller);

    // 小红书标识
    if (isXhsApp) {
      params.caller = 'xiaohongshu_mini_program';
    } else {
      // 除了小红书渠道，其他都需要隐藏本地生活订单
      params.isHideXhsMiniAppLocalLifeOrder = true;
    }

    // 快手标识
    if (isKsApp) {
      params.caller = 'kuaishou_xcx';
    }

    // 抖音标识
    if (isTTApp) {
      params.caller = 'dy_mini_program';
    }

    // 订单搜索参数
    receivertel && (params.extra.receivertel = receivertel);
    receivername && (params.extra.receivername = receivername);
    goodstitle && (params.extra.goodstitle = goodstitle);

    if (keyword) {
      const isOrderNo = /^E\d{23}$/.test(keyword); // 是否为订单号
      if (isOrderNo) {
        params.bizNo = keyword;
      } else {
        params.keyword = keyword;
      }
    }

    if (type === 'toevaluate') {
      params.extra.toevaluate = 0;
    }

    if (typeof knowledgeGoods !== 'undefined') {
      params.extra.knowledgeGoods = +knowledgeGoods;
    }

    // 传入 kdtId 即取的是单店的订单数据
    if (pageType === PAGE_TYPE.SINGLE && kdtId) {
      params.kdtId = kdtId;
    }

    // IVR订单
    if (from === 'ivr') {
      const { buyerPhone } = this.buyer;
      params.caller = 'IVR';
      params.receiverTel = buyerPhone;
      // @ts-ignore
      delete params.buyerId;
      delete params.kdtId;
    }

    if (isWeapp) {
      params.source = 'weapp';
      // 小程序是否展示CRM线下门店订单逻辑：
      // 使用offlineOrderRequestVersion为1的小程序版本有问题，修改后小程序的这个值改为了2
      // 但有在白名单内的商家，仍然在使用1的版本，需要白名单校验
      // haveOfflineOrder传1，是告诉后端当前版本的小程序支持展示CRM线下门店订单
      if (+offlineOrderRequestVersion === 2) {
        params.extra.haveOfflineOrder = '1';
      } else {
        const oldVersionShopList = getTradeApolloConfig('wsc-h5-trade.application', 'showOfflineOrder');
        const useOldVersion = oldVersionShopList?.split(',').includes(String(ctx.kdtId));
        if (useOldVersion) params.extra.haveOfflineOrder = '1';
      }
    }
    // 是否需要零售获取完整的订单详情
    if (+retailFulfillDetail === 1) {
      params.extra.showFulfillDetail = 1;
    }

    if (platform === 'kuaishou') {
      params.source = 'kuaishou';
    }
    let data = await new OrderListService(ctx).getListJson(params);

    if (data.list) {
      // 评价中心上线后，追评为商品维度，因此订单列表不再展示追评按钮
      // 为了兼容小程序，在 node 层直接处理掉
      data.list.forEach((item) => {
        if (item.orderPermission) {
          item.orderPermission.isShowAddEvaluate = false;
        }
      });
      try {
        // 判断是否应用删除订单按钮  后续去掉
        const shopMetaInfo = await this.callService('iron-base/shop.ShopMetaReadService', 'getShopMetaInfo', kdtId);
        data.list.forEach((item, key) => {
          if (item.orderPermission) {
            /**
             * 功能：根据店铺类型判断是否的确要显示【领优惠券】按钮
             * 本次改动，只支持微商城单店和连锁D - 20210607
             * 用途：如果不是微商城单店||连锁D，强制转成false
             * PRD链接：https://doc.qima-inc.com/pages/viewpage.action?pageId=319563848
             * 后续改进：如增加店铺类型，可直接在这里增加，或者删除
             */
            if (!checkPureWscSingleStore(shopMetaInfo) && !checkRetailMinimalistShop(shopMetaInfo)) {
              item.orderPermission.isAllowFissionCoupon = false;
            }
          }
        });
      } catch (error) {
        console.log(error);
      }

      if (withRepurchaseCoupon === 'true') {
        data = await this.getRepurchaseCoupon(ctx, data);
      }
    }

    let minVersionComputedOrderActions: string[] = [];
    try {
      const computedOrderActions = getTradeApolloConfig('wsc-h5-trade.application', 'computedOrderActions');
      minVersionComputedOrderActions = computedOrderActions.split(',');
    } catch(e) {
      minVersionComputedOrderActions = ['2.190.1', '3.145.1'];
    }
    const isGteMinVersion = minVersionComputedOrderActions
      .some(version => CompareVersion.isGte(ctx.mpVersion, version));
    if (!isWeapp || (isWeapp && isGteMinVersion)) {
      const isRetailShop = await this._isRetailChainShop(ctx.kdtId);
      const isIvr = from === 'ivr';
      const openAppConfig = ctx.getState('openAppConfig' as never);
      const [pointsName, orderEnterShopPolicy, directBuyAgainBtnConfig] = await Promise.all([
        new ShopConfigReadService(ctx).queryShopPointsName(kdtId).catch(() => DEFAULT_POINTS_NAME),
        new ShopConfigReadService(ctx).getOrderEnterShopPolicy().catch(() => {}),
        getDirectBuyAgainBtnConfig(ctx),
      ]);

      data = computedOrderActions(data, {
        canUseTradeUmpV1,
        openAppConfig,
        isIvr,
        directBuyAgainBtnConfig,
        pointsName,
        orderEnterShopPolicy,
        isRetailShop,
        isXhsApp,
        isKsApp,
        isWeapp,
        isQQApp,
        isAlipayApp,
        orderMark,
        isDrugList,
        withRenderData: !isWeapp || (isWeapp && ['2.192.1', '3.146.1'].some(version => CompareVersion.isGte(ctx.mpVersion, version))),
        isNewRetailOrderList: !!params.extra.showFulfillDetail, // 零售订单列表是否展示订单详情信息判断
      });
    }

    const isUseCtxJson = resultFormat === 'camelCase'; /* 增加这个判断是为了防止其他页面也在访问这个接口，影响其他页面处理 */
    this.reportAPIResponse('OrderListController', 'getListJson', data);
    if (isUseCtxJson) {
      ctx.json(0, '', data);
    } else {
      ctx.r(0, '', data);
    }
  }

  private getLogPrefix() {
    const { ctx } = this;

    // 生成prefix
    let { platform, platformVersion } = ctx;
    if (ctx.isWeapp) {
      platform = 'weapp';
      platformVersion = ctx.xExtraData && ctx.xExtraData.version;
    }

    // app 开店来源
    if (/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{10,}$/.test(platform)) {
      platform = 'yzcloudapp';
      platformVersion = platform;
    }

    return `[${ctx.traceCtx && (ctx.traceCtx as any).rootId}] [${platform} ${platformVersion}]`;
  }

  private reportAPIResponse(controllerName: string, methodName: string, result: any) {
    const { ctx } = this;
    const res = typeof result === 'string' ? result : JSON.stringify(result || {});
    const tag = `
      ${this.getLogPrefix()} - actionEnd=${controllerName}.${methodName},
      result=${res}
    `;
    ctx.logger.info(tag, null, {
      controllerName,
      methodName,
    });
  }

  // 查看订单数据接口
  public async getOneOrderJson() {
    const { ctx } = this;
    const { isXhsApp, isKsApp, isWeapp, isQQApp, isAlipayApp } = ctx;
    const {
      order_no: bizNo = '',
      from = '',
      canUseTradeUmpV1, orderMark = '',
      resultFormat = 'snakeCase',
      isDrugList = 'false',
      retailFulfillDetail
    } = ctx.query;

    const params: IOneOrderReq = {
      buyerId: this.buyerId || 0,
      bizNo,
      paltform: 'wsc',
    };

     // 是否需要零售获取完整的订单详情
     if (+retailFulfillDetail === 1) {
      params.extra = {
        showFulfillDetail: 1,
      };
    }

    let data = await new OrderListService(ctx).getListJson(params);
    data = await this.getRepurchaseCoupon(ctx, data);
    const isRetailShop = await this._isRetailChainShop(ctx.kdtId);
    const isIvr = from === 'ivr';
    const openAppConfig = ctx.getState('openAppConfig' as never);
    const [pointsName, orderEnterShopPolicy, directBuyAgainBtnConfig] = await Promise.all([
      new ShopConfigReadService(ctx).queryShopPointsName(ctx.kdtId).catch(() => DEFAULT_POINTS_NAME),
      new ShopConfigReadService(ctx).getOrderEnterShopPolicy().catch(() => {}),
      getDirectBuyAgainBtnConfig(ctx),
    ]);

    let minVersionComputedOrderActions: string[] = [];
    try {
      const computedOrderActions = getTradeApolloConfig('wsc-h5-trade.application', 'computedOrderActions');
      minVersionComputedOrderActions = computedOrderActions.split(',');
    } catch(e) {
      minVersionComputedOrderActions = ['2.190.1', '3.145.1'];
    }
    const isGteMinVersion = minVersionComputedOrderActions
      .some(version => CompareVersion.isGte(ctx.mpVersion, version));

    if (!isWeapp || (isWeapp && isGteMinVersion)) {
      data = computedOrderActions(data, {
        canUseTradeUmpV1,
        openAppConfig,
        directBuyAgainBtnConfig,
        pointsName,
        orderEnterShopPolicy,
        isRetailShop,
        isXhsApp,
        isKsApp,
        isWeapp,
        isQQApp,
        isAlipayApp,
        isIvr,
        orderMark,
        isDrugList,
        withRenderData: !isWeapp || (isWeapp && ['2.192.1', '3.146.1'].some(version => CompareVersion.isGte(ctx.mpVersion, version))),
        isNewRetailOrderList: (+retailFulfillDetail === 1), // 零售订单列表是否展示订单详情信息判断
      });
    }

    const isUseCtxJson = resultFormat === 'camelCase'; /* 增加这个判断是为了防止其他页面也在访问这个接口，影响其他页面处理 */
    if (isUseCtxJson) {
      ctx.json(0, '', data);
    } else {
      ctx.r(0, '', data);
    }
  }

  // 批量查复购券信息
  private async getRepurchaseCoupon(ctx: any, data: any) {
    const { kdtId } = ctx;
    // 发货后正常完成的订单
    const needShowStatus = [60, 70, 80, 100];
    try {
      let orderList = data.list.map((order: { orderItems: any[]; orderNo: any }) => {
        const orderItems = order.orderItems[0];
        const needShow = needShowStatus.indexOf(orderItems.status) > -1;
        const goodsIdList = orderItems.items.map((item: { goodsId: any }) => {
          return item.goodsId;
        });
        return {
          orderNo: order.orderNo,
          goodsIdList,
          needShow,
        };
      });
      // 不需要复购券信息的订单不请求接口查询
      orderList = orderList.filter((item: { needShow: any }) => item.needShow);
      if (orderList.length === 0) return data;
      const params = {
        bizName: 'order_list',
        kdtId,
        source: 'order_list',
        yzUid: this.buyerId,
        orderList,
      };
      const res = await new ActivityQueryCobuildService(ctx).batchQueryUserCanParticipateActivity(params);
      const repurchaseCouponInfo = Object.fromEntries(res.map((item: { orderNo: any }) => [item.orderNo, item]));
      // 根据订单号获取对应的复购券信息
      data.list.forEach((item: { repurchaseCoupon: any; orderNo: string }) => {
        item.repurchaseCoupon = repurchaseCouponInfo[item.orderNo];
      });
    } catch (error) {
      console.log(error);
    }

    return data;
  }

  async getRepurchaseCouponJson() {
    const { ctx } = this;

    const { kdtId } = ctx;
    const { orderList: queryOrderList = [] } = ctx.getPostData();

    let result = Object.fromEntries<null | {
      activityAlias: string;
      endTime: number;
      id: number;
      orderNo: string;
      startTime: number;
      unitCopywriting: string;
      useThresholdCopywriting: string;
      validTimeCopywriting: string;
      validTimeGenerateType: number;
      valueCopywriting: string;
    }>(queryOrderList.map((order: { orderNo: string }) => [order.orderNo, null as any]));

    try {
      // 发货后正常完成的订单
      const needShowStatus = [60, 70, 80, 100];
      let orderList = queryOrderList.map((order: { goodsIdList: number[]; status: number; orderNo: any }) => {
        const needShow = needShowStatus.indexOf(order.status) > -1;
        return {
          orderNo: order.orderNo,
          goodsIdList: order.goodsIdList,
          needShow,
        };
      });
      // 不需要复购券信息的订单不请求接口查询
      orderList = orderList.filter((item: { needShow: any }) => item.needShow);
      if (orderList.length > 0) {
        const params = {
          bizName: 'order_list',
          kdtId,
          source: 'order_list',
          yzUid: this.buyerId,
          orderList,
        };
        const batchQueryUserCanParticipateActivityResult = await new ActivityQueryCobuildService(ctx).batchQueryUserCanParticipateActivity(params);
        batchQueryUserCanParticipateActivityResult.forEach((item: any/* TODO 这个any要处理掉 */) => {
          result[item.orderNo] = item;
        });
      }
    } catch (error) {
      console.log(error);
    }

    ctx.json(0, '', result);
  }

  private _getBuyer() {
    const { buyer_id = 0, youzan_user_id = 0, fans_id = 0, fans_type = 0 } = this.ctx.getLocalSession();
    return {
      buyerId: String(buyer_id || youzan_user_id),
      fansId: String(fans_id),
      fansType: String(fans_type),
    };
  }

  private _getClientUrl(kdtId: string) {
    const buildUrl = buildUrlWithCtx(this.ctx);
    const clientUrl = {
      // @ts-ignore
      home_page_url: new WapUrl(this.ctx).getHomePageUrlByKdtId(kdtId),
      // @ts-ignore
      user_center_url: new WapUrl(this.ctx).getUserCenterUrlByKdtId(kdtId),
      shop_info_url: buildUrl(
        URL.simpleTinyUrl(WapUrl.getByUri(`/showcase/cert?kdt_id=${kdtId}`)),
        'wap',
        this.ctx.kdtId
      ),
    };
    return clientUrl;
  }

  // 判断是否为零售连锁店铺
  private async _isRetailChainShop(kdtId: string) {
    const shopMetaInfo = await this.callService('iron-base/shop.ShopMetaReadService', 'getShopMetaInfo', kdtId);
    const isRetailChainShop = checkRetailChainStore(shopMetaInfo);
    return isRetailChainShop;
  }

  /**
   * 是否使用中台化订单列表
   * @returns {boolean}
   */
  private async isUseRantaOrderListPage() {
    const { ctx } = this;
    const { force_shunt = '' } = ctx.query;
    const { kdtId } = ctx;

    // 如果强制指定版本（一般是内部开发者排查JIRA），则直接使用指定版本
    switch (force_shunt) {
      case 'v1':
        return false;
      case 'v2':
        return true;
      default:
        break;
    }

    const [
      isMatchedGrayReleaseRule,
      isCloudCustomizedV1,
      isCloudCustomizedV2 /* 定制开放2.0 */,
      useWebRantaBuyWithCloudDesign,
      shopMetaInfo,
    ] = await Promise.all([
      isInGrayReleaseByKdtId(ctx, { namespace: 'wsc-h5-trade.gray-release', key: 'ranta_web_order_list_shunt' }, kdtId),
      isCloudDesign(ctx as any, { kdtId: ctx.kdtId, pageName: 'order-list' }),
      isCloudDesign(ctx as any, { kdtId: ctx.kdtId, pageName: 'cloud_order-list' }),
      isInGrayReleaseByKdtId(
        ctx,
        { namespace: 'wsc-h5-trade.gray-release', key: 'useWebRantaOrderListWithCloudDesign' },
        ctx.kdtId
      ),
      this.callService('iron-base/shop.ShopMetaReadService', 'getShopMetaInfo', kdtId),
    ]);

    // 如果是零售店铺，并且apollo切流不在白名单中，则使用原生h5订单列表，否则使用中台化订单列表
    if (checkRetailShop(shopMetaInfo)) {
      const rootKdtId = shopMetaInfo.rootKdtId || kdtId;
      const isMatchedGrayReleaseRuleRetail = await isInGrayReleaseByKdtId(
        ctx,
        { namespace: 'wsc-h5-trade.gray-release', key: 'ranta_web_order_list_shunt_retail' },
        rootKdtId
      );
      if (!isMatchedGrayReleaseRuleRetail) return false;
    }

    // 如果定制了开放2.0则不允许回切到老版本
    if (isCloudCustomizedV2) return true;

    // 如果定制了开放1.0 并且 在云定制兼容的灰度规则内，则使用中台化版本
    if (isCloudCustomizedV1 && useWebRantaBuyWithCloudDesign) {
      return true;
    }

    // 其他店铺按照灰度切流规则匹配
    return isMatchedGrayReleaseRule;
  }

  /**
   * 渲染订单列表视图
   */
  private async renderOrderListTemplate() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const { version, from } = ctx.query;

    // 获取页面显示参数
    let customized;
    try {
      [customized] = await new ExtensionPointService(ctx).getDesignConfig('order-list');
    } catch (error) {
      // @ts-ignore
      const content = error.errorContent;
      if (content) {
        throw new PageException(content.code, content.msg);
      } else {
        throw error;
      }
    }
    const [useListV2, isUseRantaPage] = await Promise.all([
      await isInGrayReleaseByKdtId(ctx, { namespace: 'wsc-h5-trade.application', key: 'useListV2' }, kdtId),
      this.isUseRantaOrderListPage(),
    ]);
    const isUseListV2 = (useListV2 || version === '2') && !customized && version !== '1';

    const params: Record<string, any> = {};
    if (ctx.isTTApp) {
      params.footerHtml = false;
    }

    if (isUseRantaPage) {
      await initRanta(ctx as any, {
        framework: 'tee',
        appName: 'wsc-tee-h5',
        bizName: from === 'ivr' ? '@wsc-tee-h5-trade/order-list-ivr' : '@wsc-tee-h5-trade/order-list',
        ecloud: {
          setExtensionParams: [
            {
              kdtId: +ctx.kdtId,
              pageName: 'order-list',
              pageNameV2: 'order-list',
              conditionContext: this.getConditionContext(ctx),
            },
          ],
        },
      });
      const page = from === 'ivr' ? 'order/tee-order-list-ivr.html' : 'order/tee-order-list.html';
      await getBrandShopStyleToState(ctx as any, { useAppStyleIcon: true });
      return await ctx.render(page, params, {
        appName: 'wsc-tee-h5', // 新增
        skipLoadOpsWebConfig: true, // 中台化在插件中会加载配置并挂载全局
      });
    }

    params.pageName = isUseListV2 ? 'order/list-v2' : 'order/list';
    await ctx.render('order/list.html', params);
  }
}

export = OrderListController;
