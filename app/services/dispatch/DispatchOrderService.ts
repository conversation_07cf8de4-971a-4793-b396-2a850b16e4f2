import mapKeysToCamelCase from '@youzan/utils/string/mapKeysToCamelCase';
import BaseService from '../base/BaseService';

class DispatchOrderService extends BaseService {
  get SERVICE() {
    return 'com.youzan.retail.ofc.dispatcher.api.service.dispatch.DispatchOrderService';
  }

  // 根据坐标获取最近网店列表
  async localSwitchShopList(params: any) {
    return this.invoke(this.SERVICE, 'localSwitchShopList', [params]);
  }

  // 判断是否支持切换
  async isSupportLocalSwitchShop(params: any) {
    return this.invoke(this.SERVICE, 'isSupportLocalSwitchShop', [params]);
  }

  // 查询最近可配送店铺
  async localSaleShopAndTime(params: any) {
    return this.invoke(this.SERVICE, 'localSaleShopAndTime', [params]);
  }
}

export = DispatchOrderService;
