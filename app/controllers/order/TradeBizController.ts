import URL from '@youzan/iron-base/app/lib/URL';
import WapUrl from '@youzan/iron-base/app/lib/WapUrl';
import BusinessServiceError from '@youzan/iron-base/app/services/base/BusinessServiceError';
import BaseController from '../base/BaseController';
import ShopMultiStoreService from '../../services/shop/ShopMultiStoreService';
import OrderService from '../../services/trade/OrderService';
import TradeService from '../../services/trade/TradeService';
import {
  ICancelOrderReq,
  IRemindExpressReq,
  IBuyAgainReq,
  IDeleteOrderReq,
  IDirectBuyAgainReq
} from 'definitions/order/TradeBiz';
import buildUrlWithCtx from '@youzan/utils/url/buildUrlWithCtx';
import { getDirectBuyAgainBtnConfig } from '../../lib/TradeConfig';
import _ from 'lodash';
import { getTradeApolloConfig } from '../../lib/Apollo';
import { Context } from 'astroboy';
import { isInGrayReleaseByKdtId } from '../../lib/WhiteListUtils';

interface IShopNav {
  /**
   * 注意零售门店的情况
   */
  homePageUrl: string;
  userCenterUrl: string;
  /**
   * 店铺信息链接
   */
  shopInfoUrl: string;
  /**
   * 购物记录（第三方APP，微小店）
   */
  tradeRecordUrl: string;
  showStore?: boolean;
  offlineStore?: any;
}

interface IBuyAgainParams {
  orderNo: string;
  kdtId: number;
}

/**
 * 通用/公用交易相关业务
 */
class TradeBizController extends BaseController {
  async init() {
    // 该controller只有一个页面函数，且不需要任何初始化
    await super.init({
      initMpData: false,
      initMpAccount: false,
      initPlatform: false
    });
    this.ctx.biz = '通用/公用交易业务';
  }

  /**
   * 取消订单
   * order_no 订单编号
   * kdt_id 店铺ID
   * reason 取消理由
   * isBuyAgain 取消订单后是否订单商品加入购物车
   */
  public async postCancelOrderJson(): Promise<void> {
    const { ctx } = this;
    const {
      isBuyAgain = 1,
      reason = '',
      order_no: orderNo = '',
      kdt_id: kdtId = 0
    } = ctx.getPostData();
    const params: ICancelOrderReq = {
      orderNo,
      kdtId,
      reason,
      buyerId: this.buyerId || 0
    };

    const res = await new OrderService(ctx).cancelOrder(params);

    if (Number(isBuyAgain)) {
      try {
        await this.buyAgain({ orderNo, kdtId });
      } catch (e) {}
    }

    ctx.r(0, '', res);
  }

  // 删除订单
  public async postDeleteOrderJson(): Promise<void> {
    const { ctx } = this;
    const { order_no: orderNo = '' } = ctx.getPostData();
    const params: IDeleteOrderReq = {
      orderNo,
      buyerId: this.buyerId || 0
    };
    ctx.r(0, '', await new OrderService(ctx).deleteOrder(params));
  }

  // 提醒发货
  public async postRemindExpressJson() {
    const { ctx } = this;
    const { order_no: orderNo = '', kdt_id: kdtId = 0 } = ctx.getPostData();
    const params: IRemindExpressReq = {
      orderNo,
      kdtId
    };
    ctx.r(0, '', await new OrderService(ctx).remindExpress(params));
  }

  // 再来一单
  public async postBuyAgainJson() {
    const { ctx } = this;
    const { order_no: orderNo = '', kdt_id: kdtId = 0 } = ctx.getPostData();

    const params = {
      orderNo,
      kdtId
    };

    ctx.r(0, '', await this.buyAgain(params));
  }

  private async buyAgain({ orderNo, kdtId }: IBuyAgainParams) {
    const { ctx, buyer } = this;
    const { buyerId, nobody, fansId, fansType, youzanFansId } = buyer;
    const params: IBuyAgainReq = {
      orderNo,
      kdtId,
      buyer: {
        buyerId,
        nobody,
        fansId,
        fansType,
        youzanFansId,
        platform: 'wsc'
      },
      supportCancelSelected: true
    };

    return await new OrderService(ctx).buyAgain(params);
  }

  public async getFooterJson() {
    const { ctx } = this;
    const { kdt_id = 0, offline_id = 0 } = this.ctx.query;
    const kdtId = ctx.kdtId || 0;
    let offlineId = offline_id || ctx.offlineId || 0; // 线下门店
    const { platform } = ctx;

    if (!/^\d+$/.test(kdt_id)) {
      throw new BusinessServiceError(9999, '店铺ID非法');
    }

    // 集中处理异步请求
    let response;
    const detectStoreParams = {
      kdtId,
      storeId: null,
      sessionId: ctx.KDTSESSIONID || '',
      userId: this.buyerId || 0 // 一般取youzan_user_id，不过有buyerId时两者值一致，只有极个别异常情况不一致
    };
    try {
      const whiteListService = this.getService('iron-base', 'common.WhitelistService');
      response = await Promise.all([
        this.getService('iron-base', 'shop.ShopBaseReadService').getShopBaseInfoByKdtId(kdtId),
        this.getService('iron-base', 'shop.ShopSettingsService').getShopSettings(kdtId),
        this.getService('iron-base', 'yop.AppConfigRemoteService').getAppConfigInfo(platform),
        this.callService('iron-base/shop.ShopConfigService', 'getShopConfigs', kdtId, [
          'team_physical'
        ]),
        whiteListService.exists('hidden_power_by', kdtId),
        whiteListService.exists('ebiz_newhope_kdtids', kdtId),
        new ShopMultiStoreService(ctx).getMultiStore(detectStoreParams)
      ]);
    } catch (error) {
      const content = error.errorContent;
      if (content) {
        throw new BusinessServiceError(content.code, content.msg);
      } else {
        throw error;
      }
    }

    const [
      shopBaseInfo,
      shopSettings,
      openAppConfig,
      shopConfigs,
      hiddenPowerBy, // vip店铺隐藏版权
      isNewhopeKdt, // 新希望店铺
      offlineResult // 多网点信息
    ] = response;

    const buildUrl = buildUrlWithCtx(ctx);

    if (!Number(offlineId) && offlineResult) {
      // ctx.offlineId没有注入，offlineResult请求放到了外面发生引起重复请求 20180622
      offlineId = Number(offlineResult && offlineResult.storeId);
    }

    // 底部导航
    const shopNav: IShopNav = {
      // @ts-ignore
      homePageUrl: new WapUrl(ctx).getHomePageUrlByKdtId(kdtId), // 注意零售门店的情况
      // @ts-ignore
      userCenterUrl: new WapUrl(ctx).getUserCenterUrlByKdtId(kdtId),
      // 店铺信息链接
      shopInfoUrl: buildUrl(
        URL.simpleTinyUrl(WapUrl.getByUri(`/showcase/cert?kdt_id=${kdtId}`)),
        'wap',
        kdtId
      ),
      // 购物记录（第三方APP，微小店）
      tradeRecordUrl: buildUrl(
        WapUrl.getByUri(`/trade/record/index?source=weixin11&kdt_id=${kdtId}`),
        'wap',
        kdtId
      )
    };

    // 1、如果开启了多门店，则显示多门店信息
    // 2、如果未开启多门店，但设置了线下门店信息，则显示线下门店信息
    // 3、如果未开启多门店，并且未设置线下门店信息，则什么都不显示
    // 4、新希望店铺不显示
    const showStore = Number(shopConfigs.team_physical) && !isNewhopeKdt;
    if (showStore) {
      let offlineStore = null;
      if (offlineId) {
        offlineStore = {
          storeLink: '/ump/multistore',
          storeText: '其他分店'
        };
      } else {
        offlineStore = {
          storeLink: '/showcase/physicalstore',
          storeText: '线下门店'
        };
      }
      offlineStore.storeLink = buildUrl(
        WapUrl.getByUri(`${offlineStore.storeLink}?kdt_id=${kdtId}`),
        'wap',
        kdtId
      );
      shopNav.showStore = showStore; // 线下门店
      shopNav.offlineStore = offlineStore;
    }

    this.formatFooterNav(shopNav, openAppConfig, shopBaseInfo, kdtId);

    // 叮当app临时特殊处理 from iron
    const showCopyright =
      ['c2a7b9269fd095fa5b1467769433688'].indexOf(platform) < 0 && !openAppConfig.hideCopyright;
    const copyright = {
      copyrightPicUrl: shopSettings.isLogoCustomized ? shopSettings.customizedLogo : '',
      hiddenPowerBy, // vip店铺隐藏版权
      showCopyright
    };

    const footerData = {
      copyright,
      shopNav
    };
    ctx.r(0, 'ok', footerData);
  }

  private formatFooterNav(
    footerNav: IShopNav,
    openAppConfig: any,
    shopBaseInfo: any,
    kdtId: number
  ) {
    const { platform, isWeapp } = this.ctx;

    // 对特定的ua屏蔽底部版权
    const footerBlackUAMap = ['youzanmars'];
    const hideFooter = footerBlackUAMap.indexOf(platform) >= 0;

    // http://jira.qima-inc.com/browse/QTHREE-426 部分店铺隐藏关注我们，赫赫的需求
    const hideFollowUsList = [18984288, 19431122, 40408019, 40103053, 19341933];
    // 小程序环境 部分白名单用户隐藏“关注我们”
    const hideFollowUs = isWeapp || hideFollowUsList.indexOf(kdtId) >= 0;

    const shopType = Number(shopBaseInfo.shopType || 0);
    // 不显示底部店铺导航的店铺
    const kdtIdsShowShopNav = [371761, 391658, 13770441, 15662592];
    // 以下是批发店铺
    const kdtIdsPf = [13770441, 15662592];
    // 微小店 15年的白名单[371761, 391658]年代太久，暂不列入
    const showWxdShopNav = shopType === 1;
    // 是否显示底部店铺导航 不考虑品鉴商品
    const showShopNav =
      !showWxdShopNav &&
      kdtId > 0 &&
      shopType !== 2 &&
      kdtIdsShowShopNav.indexOf(kdtId) < 0 &&
      !openAppConfig.hideFootNav;
    const showPfShopNav = kdtId > 0 && kdtIdsPf.indexOf(kdtId) >= 0;

    Object.assign(footerNav, {
      hideFooter,
      hideFollowUs,
      showShopNav,
      showWxdShopNav,
      showPfShopNav,
      showShopInfo: true
    });
  }

  /**
   * 获取店铺信息
   * @param ctx Context
   * @returns
   */
  private async getShopMetaInfo(ctx: Context) {
    const { kdtId } = ctx;
    // 先从state中获取店铺信息 小程序场景下可能直接调接口 state还未初始化 在state中没有的情况下 调用服务获取店铺信息
    const shopMetaInfo =
      ctx.getState('shopMetaInfo' as never) ||
      this.callService('iron-base/shop.ShopMetaReadService', 'getShopMetaInfo', kdtId);
    return shopMetaInfo || {};
  }

  /**
   * 比对创建订单缓存时的kdtId
   * @param ctx
   * @param data
   * @returns
   */
  async patchCacheOrderKdtId(ctx: Context, data: Record<string, any>) {
    try {
      const { common } = data;
      const parseCommon = JSON.parse(common);
      const { kdt_id } = parseCommon;
      if (ctx.kdtId !== 0 && ctx.kdtId !== kdt_id) {
        ctx.logger.info(
          `[wsctrade/order/goodsBook.json]ctxKdtId与common.kdt_id不一致:${ctx.kdtId} ${kdt_id}`
        );

        const shopMetaInfo = await this.getShopMetaInfo(ctx);
        const { rootKdtId } = shopMetaInfo;

        if (rootKdtId === kdt_id) {
          //  若不一致，以 ctx.kdtId 为准
          parseCommon.kdt_id = ctx.kdtId;
          const stringifyCommon = JSON.stringify(parseCommon);
          ctx.logger.info(
            `[wsctrade/order/goodsBook.json]替换common的kdtId: ${ctx.kdtId} ${rootKdtId} ${kdt_id},parseCommon:${stringifyCommon}`
          );
          return stringifyCommon;
        }
      }
    } catch (error) {
      console.error('patchCacheOrderKdtId ~ error', error);
    }
  }

  public async postBookJson() {
    const { ctx } = this;
    const postData = ctx.getPostData();
    const { source } = postData;
    const tradeService = new TradeService(ctx);
    postData.buyer = this.buyer;
    // 新增kdtIdb比对替换
    const parseCommon = await this.patchCacheOrderKdtId(ctx, postData);
    if (parseCommon) {
      postData.common = parseCommon;
    }
    // 说明是支付宝中的小程序
    if (ctx.isAlipayApp) {
      postData.source = {
        ...source,
        orderMark: 'alipay_mini_program',
        platform: 'alipay'
      };
    }
    // 说明是QQ小程序
    if (ctx.isQQApp) {
      postData.source = {
        ...source,
        orderMark: 'qq_mini_program',
        platform: 'qq'
      };
    }

    const res = await tradeService.cacheOrderCreation(postData);
    ctx.json(0, '', res);
  }

  // 商品页下单用的缓存接口
  public async postCacheJson() {
    const { ctx } = this;
    const postData = ctx.getPostData();

    const postDataString = JSON.stringify(postData);

    const data = await new TradeService(ctx).cacheOrderCreationJson(postDataString);
    ctx.json(0, 'ok', data);
  }

  /**
   * 微信自有支付 - 钱款去向说明页面
   */
  public async getMoneyToWhereHtml() {
    await this.basicAcl();
    await this.ctx.render('order/money-to-where.html');
  }

  /**
   * 取消订单 - 获取原因&是否展示加入购物车按钮
   */
  public async getCancelReasonList() {
    const { ctx } = this;
    const { kdt_id: kdtId = 0, order_no: orderNo = '' } = ctx.getQueryData();

    const params = { orderNo, kdtId: +kdtId };
    const data = await new OrderService(ctx).getBuyerCancelOrderReason(params);
    ctx.json(0, 'ok', data);
  }

  // 生成再来一单直接进入下单页商品下单信息
  generateBuyOrderData(goodsList: any, orderExtInfo = {}) {
    const config = {
      canyinChannel: 1,
      canyinIds: []
    };
    const items: Array<any> = [];
    const itemSources: Array<any> = [];
    const sellers = [
      {
        kdtId: goodsList[0].kdtId,
        storeId: goodsList[0].storeId || 0
      }
    ];

    _.each(goodsList, (goods) => {
      let bizData;
      try {
        /* eslint-disable-next-line */
        bizData = JSON.parse(goods.extraAttribute || '{}').bizData;
      } catch (e) {
        /* eslint-disable-next-line */
        console.log(e);
      }
      const baseId = {
        kdtId: goods.kdtId,
        goodsId: goods.goodsId,
        skuId: goods.skuId,
        propertyIds: goods.propertyIds || []
      };

      // 外部订单来源
      const tpps = '';
      const item = {
        ...baseId,
        storeId: goods.storeId || 0,
        price: goods.payPrice || 0,
        num: goods.num,
        itemMessage: goods.messages,
        extensions: { tpps }
      };

      const itemSource = {
        ...baseId,
        bizTracePointExt: bizData
      };
      items.push(item);
      itemSources.push(itemSource);
    });
    return {
      config,
      items,
      sellers,
      source: {
        itemSources
      },
      ump: {},
      extensions: {
        ...orderExtInfo
      }
    };
  }

  // 再来一单，直接到下单页
  public async postDirectBuyAgainJson() {
    const { ctx } = this;
    const { order_no: orderNo = '', kdt_id: kdtId = 0, preToastDesc = '' } = ctx.getPostData();
    const params = {
      orderNo,
      kdtId
    };
    const result = await this.directBuyAgain(params);

    // 跳商详页面，不跳转下单页
    if (+result.destination === 2) {
      return ctx.r(0, '', result);
    }

    const postData: any = this.generateBuyOrderData(result.prevOrderInfo.items, {
      kdtId,
      // 用户选中分开结算的物流方式
      expressType: null,
      presentData: {
        IS_SELECT_PRESENT: 0,
        SELECTED_PRESENTS: []
      },
      DIRECT_BUY_AGAIN_PREV_ORDER_INFO: JSON.stringify({
        orderNo: result.prevOrderInfo.prevOrderNo,
        orderNote: result.prevOrderInfo.orderNote
      }),
      preToastDesc
    });
    const { source } = postData;
    const tradeService = new TradeService(ctx);
    postData.buyer = this.buyer;
    // 说明是支付宝中的小程序
    if (ctx.isAlipayApp) {
      postData.source = {
        ...source,
        orderMark: 'alipay_mini_program',
        platform: 'alipay'
      };
    }
    // 说明是QQ小程序
    if (ctx.isQQApp) {
      postData.source = {
        ...source,
        orderMark: 'qq_mini_program',
        platform: 'qq'
      };
    }
    const res = await tradeService.cacheOrderCreation(postData);
    ctx.r(0, '', { ...result, ...res });
  }

  private async directBuyAgain({ orderNo, kdtId }: IBuyAgainParams) {
    const { ctx, buyer } = this;
    const { buyerId } = buyer;
    const params: IDirectBuyAgainReq = {
      orderNo,
      kdtId,
      buyerId
    };

    return await new OrderService(ctx).directBuyAgain(params);
  }

  public async getDirectBuyAgainBtnConfigJson() {
    const { ctx } = this;
    const result = await getDirectBuyAgainBtnConfig(ctx);
    return ctx.r(0, 'ok', result);
  }

  // 获取Apollo静态配置文案，支持多个，keys以[deposit_pre_sale_agreement, agreement]传
  public async getStaticConfig() {
    const { ctx } = this;
    const { keys = [] } = ctx.getPostData();
    const staticCopy = {};
    try {
      keys.forEach((item: string) => {
        if (item) {
          const config = getTradeApolloConfig('wsc-h5-trade.application', item) || '';
          // @ts-ignore
          staticCopy[item] = config;
        }
      });
    } catch (err) {}

    return ctx.r(0, 'ok', staticCopy);
  }
}

export = TradeBizController;
