import BaseService from '../base/BaseService';
import { QrCodeUpdateRequestParam } from '../../../definitions/pay/Cashier';

/**
 * com.youzan.trade.business.qrcode.api.operate.QrCodeUpdateService
 */
export default class QrCodeUpdateService extends BaseService {
  /**
   * SERVICE_NAME
   */
  get SERVICE_NAME() {
    return 'com.youzan.trade.business.qrcode.api.operate.QrCodeUpdateService';
  }

  /**
   * 编辑二维码
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/125710
   */
  async editQrCode(qrCodeUpdateRequestDTO: QrCodeUpdateRequestParam) {
    return this.invoke('editQrCode', [qrCodeUpdateRequestDTO]);
  }
}
