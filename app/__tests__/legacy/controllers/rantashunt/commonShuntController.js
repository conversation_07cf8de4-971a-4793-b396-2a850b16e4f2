import { app, mock } from '@youzan/astroboy-mock/bootstrap';
import BaseController from '../../../controllers/base/BaseController';

jest.mock('../../../controllers/base/BaseController', () =>
  jest.requireActual('../../__mocks__/BaseController')
);

afterEach(() => {
  BaseController.mockClear();
});

test('getWeappShuntConfig', async () => {
  app.mockContext({
    buyerId: 8000035538,
    kdtId: 491391,
  });
  app.mockContext({
    getPostData: () => {
      return {
        config: {
          paid: 'useWeappRantaTeePaid',
          cart: 'useWeappRantaTeeCart',
        },
      };
    },
  });

  const res = await app
    .httpRequest()
    .post('/wsctrade/rantashunt/getWeappShuntConfig.json');

  expect(res.body).toEqual({
    code: 0,
    data: {
      paid: true,
      cart: true
    },
    msg: 'success',
  });
});
