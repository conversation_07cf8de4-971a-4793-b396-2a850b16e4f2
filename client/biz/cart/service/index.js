import * as api from '../api';
import { Dialog } from 'vant';
import Toast from '@biz/cart/common/toast';

const deleteGoods = ({ goods, isActivity, needConfirm }) => {
  return Promise.resolve(
    needConfirm &&
      new Promise((resolve, reject) => {
        const isCourse = goods.goodsType === 31;
        Dialog.confirm({
          message: `确定删除该${isCourse ? '课程' : '商品'}么？`,
        })
          .then((res) => {
            if (res === 'confirm') {
              return resolve();
            }
          })
          .catch(() => {
            Dialog.close();
            return reject();
          });
      })
  ).then(() => {
    return api
      .deleteGoods(goods)
      .then(() => {
        // dispatch('deleteAfterUpdateData', payload.goods);
        window.cartEventBus.$emit('cartNumUpdated');
        needConfirm && Dialog.close();
      })
      .catch((e) => {
        Toast('删除商品失败，请重试');
        return Promise.reject(e);
      });
  });
};

const updateSelectAllGoods = (...params) => {
  Toast.loading();
  return api.updateSelectAllGoods(...params).finally(() => {
    Toast.clear();
  });
};

const getCartGoodsList = (...params) => {
  Toast.loading();
  return api.getCartGoodsList(...params).finally(() => {
    Toast.clear();
  });
};

export { deleteGoods, updateSelectAllGoods, getCartGoodsList };
