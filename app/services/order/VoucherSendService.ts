import BaseService from '../base/BaseService';
import { IPlainResult } from '../type/common';

/**
 * com.youzan.ump.voucher.front.api.request.voucher.send.VoucherSendRequest
 * 优惠凭证发放请求
 */
export interface IVoucherSendRequest {
  /** 活动id */
  activityId: number;
  /** 粉丝id */
  fansId?: number;
  /** 店铺kdtId */
  kdtId?: number;
  /** 业务名称 */
  bizName?: string;
  /** 请求id */
  requestId: string;
  /** 来源 */
  source?: string;
  /** 用户id */
  userId?: number;
  /** 指定面额，单位：分 */
  assignValue?: number;
  /** 粉丝类型 */
  fansType?: number;
}

/**
 * com.youzan.ump.voucher.front.api.dto.voucher.VoucherIdentityDTO
 * 凭证身份
 */
interface IVoucherIdentityDTO {
  /** 老凭证类型，0：优惠券，1：优惠码 */
  couponType?: number;
  /** 老凭证id */
  couponId?: number;
}

/**
 * com.youzan.ump.voucher.front.api.dto.voucher.VoucherDTO
 * 优惠凭证
 */
interface IVoucherDTO {
  /** 发放来源 */
  sendSource?: string;
  /** 优惠方式，1：代金券，2：折扣券，3：兑换券 */
  preferentialMode?: number;
  /** 核销码 */
  verifyCode?: string;
  /** 有效开始时间 */
  validStartTime?: string;
  /** 店铺kdtId */
  kdtId?: number;
  /** 发放时间 */
  sentAt?: string;
  /** 凭证身份标识 */
  voucherIdentity?: IVoucherIdentityDTO;
  /** 码值 */
  codeValue?: string;
  /** 活动id */
  activityId?: number;
  /** 有效结束时间 */
  validEndTime?: string;
  /** 面额 */
  value?: number;
  /** 领取消息文案  优惠券存在，优惠码不存在为null */
  takenMessage?: string;
  /** 凭证状态，1：正常 2：已冻结，3：已核销，4：不可用，5：已删除 */
  status?: number;
}

/**
 * com.youzan.ump.voucher.core.api.dto.shop.ShopDTO
 * description: 店铺信息DTO(如果ShopDTO不为空，则shopRole也需要传)
 */
interface IShopDTO {
  /** 当前店铺kdtId */
  kdtId?: number;
  /** 总店kdtId */
  hqKdtId?: number;
  /** 0单店（非连锁模式）  1总店  2分店 */
  shopRole?: number;
}

/**
 * com.youzan.ump.voucher.core.api.request.voucher.send.VoucherSendCheckRequest
 * 凭证发放
 */
export interface IVoucherCheckRequest {
  /** 活动id */
  activityId?: number;
  /** 发券店铺信息 */
  shop?: IShopDTO;
  /** 粉丝id */
  fansId?: number;
  /** 发券店铺kdtId */
  kdtId?: number;
  /** 来源 */
  source?: string;
  /** 用户id */
  userId?: number;
  /** 粉丝类型 */
  fansType?: number;
}

class VoucherSendService extends BaseService {
  get VOUCHER_SEND_SERVICE() {
    return 'com.youzan.ump.voucher.front.api.service.voucher.VoucherSendService';
  }

  get CORE_VOUCHER_SEND_SERVICE() {
    return 'com.youzan.ump.voucher.core.api.service.voucher.VoucherSendService';
  }

  /**
   * 领取优惠券
   * 接口文档  http://zanapi.qima-inc.com/site/service/view/395706
   * @param params
   */
  send(params: IVoucherSendRequest): Promise<IPlainResult<IVoucherDTO>> {
    return this.invoke(this.VOUCHER_SEND_SERVICE, 'send', [params]);
  }

  check(params: IVoucherCheckRequest): Promise<IPlainResult<boolean>> {
    return this.invoke(this.CORE_VOUCHER_SEND_SERVICE, 'check', [params]);
  }
  /**
   *  根据活动别名 发放优惠凭证
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/880729
   *
   *  @param {Object} request - 发放请求
   *  @param {number} request.kdtId - 店铺kdtId
   *  @param {string} request.bizName - 业务名称
   *  @param {string} request.requestId - 请求id
   *  @param {string} request.sign - 活动签名sign
   *  @param {string} request.alias - 活动别名
   *  @param {string} request.source - 来源
   *  @param {string} request.secondarySource - 次级来源
   *  @param {number} request.userId - 用户id
   *  @param {number} request.assignValue - 指定面额，单位：分
   *  @return {Promise}
   */

  async sendByAlias(request: any) {
    return this.invoke(this.CORE_VOUCHER_SEND_SERVICE, 'sendByAlias', [request]);
  }
}

export default VoucherSendService;
