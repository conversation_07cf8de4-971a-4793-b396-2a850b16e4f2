import { Context } from 'astroboy';
import BaseController from '../base/BaseController';
import DyTradeService from '../../services/dy/DyTradeService';
import GpsServices from '../../services/delivery/GpsService';
import CouponService from '../../services/api/retail/misc/CouponService';

class IndexController extends BaseController {
  async placeDyOrder(ctx: Context) {
    const { kdtId, orderNo } = ctx.getPostData();
    const { buyerId } = ctx;
    const result = await new DyTradeService(ctx).placeDyOrder({ kdtId, orderNo, buyerId });
    return ctx.json(0, 'ok', result);
  }

  async exchangeByThirdOrderId(ctx: Context) {
    const { kdtId, orderId, supportCombo } = ctx.getPostData();
    const { buyerId: userId } = ctx;
    const result = await new CouponService(ctx).exchangeByThirdOrderId({
      kdtId,
      orderId,
      supportCombo,
      userId,
    });
    return ctx.json(0, 'ok', result);
  }

  async getApplyRefundParam(ctx: Context) {
    const { kdtId, orderNo, refundNo } = ctx.getPostData();
    const result = await new DyTradeService(ctx).getApplyRefundParam({ kdtId, orderNo, refundNo });
    return ctx.json(0, 'ok', result);
  }

  async getGpsInfo(ctx: Context) {
    const { city, address } = ctx.getQueryData();
    const result = await new GpsServices(ctx).getGpsInfo([address, city]);
    return ctx.json(0, 'ok', result);
  }
}

export = IndexController;
