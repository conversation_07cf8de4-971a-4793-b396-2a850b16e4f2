import { app, mock } from '@youzan/astroboy-mock/bootstrap';
import BaseController from 'app/controllers/base/BaseController';
import SecureService from 'app/services/order/SecureService';
import UicEncryptService from 'app/services/uic/UicEncryptService';

jest.mock('app/controllers/base/BaseController', () =>
  jest.requireActual('app/__tests__/__mocks__/BaseController')
);
jest.mock('app/services/order/SecureService');
jest.mock('app/services/uic/UicEncryptService');

afterEach(() => {
  BaseController.mockClear();
  SecureService.mockClear();
  UicEncryptService.mockClear();
});

test('postVerifyPhoneJson', async () => {
  app.mockContext({
    telephone: 13819210652,
    orderNo: 'E20200615204544002600023',
  });

  SecureService.mockImplementation(() => {
    return {
      verifyOrderReceiverMatch: jest.fn().mockResolvedValue([]),
    };
  });

  UicEncryptService.mockImplementation(() => {
    return {
      aesDecrypt: jest.fn().mockResolvedValue(''),
    };
  });

  try  {
    const res = await app
      .httpRequest()
      .post('/wsctrade/order/secure/verifyphone.json');
  } catch(e) {
    expect(e.code).toEqual(40206);
    expect(e.message).toEqual('你没有权限操作');
  }
});
