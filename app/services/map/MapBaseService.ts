import BaseService from '../base/BaseService';
import formatParams from './helper/formatParams';
import { MAP_METHODS, MAP_VERSION, REQUEST_URLS, MAP_DEVELOP } from '../../constants/map';
import { getMapVersion } from './helper/utils';
import formatResponse from './helper/formatResponse';
import {
  IGeocoderQuery,
  IGetLocationByIpQuery,
  IGetSuggestionQuery,
  IReverseGeocoderQuery,
  ISearchNearByQuery,
} from 'definitions/map/index';

class MapBaseService extends BaseService {
  // 指定版本，默认拿apollo计算的版本
  public get v(): MAP_VERSION {
    return getMapVersion(this.ctx);
  }

  // 接口返回数据是否保持不变
  public get isKeep() {
    return false;
  }

  // 接口返回数据格式化函数集合
  protected getFormat(v: MAP_VERSION) {
    return formatResponse[v];
  }

  // 执行请求方法
  private async callMethod({ methodName, query: clientQuery }: { methodName: MAP_METHODS; query: any }) {
    // 这边会对参数修改，所以重新定义一个对象
    const query = { ...clientQuery };
    const v = (query.v || this.v) as MAP_VERSION;
    delete query.v;

    const params = formatParams[v][methodName](query);
    const url = REQUEST_URLS[v][methodName];
    const ajaxMethod = MAP_DEVELOP ? 'httpCall' : 'proxyCall';

    // 详细日志上报，query为接口参数，url为请求第三方服务商的链接，params为请求第三方服务商的参数
    const logArgs: any = { query, url, params };
    const loggerKeyArgs = {
      mapVersion: v,
      biz: clientQuery.biz || 'unkonwn',
    };
    try {
      this.reportDubboStart('MapService', methodName, loggerKeyArgs, { ...logArgs });
      console.log('MapService.request', logArgs);

      const result = await this[ajaxMethod]({
        method: 'GET',
        url,
        data: params,
      });

      this.reportDubboEnd('MapService', methodName, loggerKeyArgs, result);
      console.log('MapService.response', result);

      return result;
    } catch (err) {
      const tag = `地图服务api调用报错：${JSON.stringify({
        query: logArgs,
        err,
        message: err?.message,
        stack: err?.stack,
      })}`;
      // 请求第三方接口报错则上报 error
      this.ctx.logger.error(tag, err, {
        serviceName: 'MapService',
        methodName,
      });
    }
  }

  public async geocoder(query: IGeocoderQuery) {
    const result = await this.callMethod({
      methodName: MAP_METHODS.geocoder,
      query,
    });
    console.log('isKeep', this.isKeep);
    return this.getFormat(query.v || this.v).geocoder(result, this.isKeep);
  }

  public async reverseGeocoder(query: IReverseGeocoderQuery) {
    const result = await this.callMethod({
      methodName: MAP_METHODS.reverseGeocoder,
      query,
    });
    return this.getFormat(query.v || this.v).reverseGeocoder(result, this.isKeep);
  }

  public async searchNearBy(query: ISearchNearByQuery) {
    const result = await this.callMethod({
      methodName: MAP_METHODS.searchNearBy,
      query,
    });
    return this.getFormat(query.v || this.v).searchNearBy(result, this.isKeep);
  }

  public async getSuggestion(query: IGetSuggestionQuery) {
    const result = await this.callMethod({
      methodName: MAP_METHODS.getSuggestion,
      query,
    });
    return this.getFormat(query.v || this.v).getSuggestion(result, this.isKeep);
  }

  public async getLocationByIp(query: IGetLocationByIpQuery) {
    const result = await this.callMethod({
      methodName: MAP_METHODS.getLocationByIp,
      query,
    });
    return this.getFormat(query.v || this.v).getLocationByIp(result, this.isKeep);
  }
}

export default MapBaseService;
