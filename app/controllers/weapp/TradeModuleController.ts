import { Context } from 'astroboy';
import WeappTradeModuleService from '../../services/channels/app/WeappTradeModuleService';

import BaseController = require('../base/BaseController');

class TradeModuleController extends BaseController {

  // 获取微信小程序交易组件开通信息
  async getWeappTradeModuleStatusJson(ctx: Context) {
    let res = {};

    try {
      const tradeModuleInfo = await new WeappTradeModuleService(ctx).getTradeModuleStatus({
        kdtId: ctx.kdtId,
      });

      res = {
        // 交易组件已经开通
        WEAPP_TRADE_MODULE_IS_OPEN: tradeModuleInfo.alreadyApply,
        // 交易组件已经启用
        WEAPP_TRADE_MODULE_IS_ENABLE: tradeModuleInfo.accessFinish,
      };
    } catch (error) {
      ctx.logger.info(`[微信自定义版购物组件] 获取交易组件开通状态信息失败 buyerId: ${ctx.buyerId} kdtId: ${ctx.kdtId}`, null, {
        error
      });
    }

    return ctx.json(0, 'ok', res);
  }
}

export = TradeModuleController;
