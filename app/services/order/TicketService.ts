import BaseService from '../base/BaseService';
import {
  IVirtualTicketDTO,
  IVirtualTicketDetailVO,
  IFrontPageRedirectPathReqDTO,
  IFrontPageRedirectPathResponseDTO,
} from 'definitions/order/OrderTicket';

class TicketService extends BaseService {
  get VIRTUAL_TICKET_SERVICE() {
    return 'com.youzan.ebiz.mall.trade.buyer.api.service.VirtualTicketService';
  }

  get EXTENSION_POINT_SERVICE() {
    return 'com.youzan.cloud.columbus.api.extension.ExtensionPointService';
  }

  get ORDER_FEATURE_SERVICE() {
    return 'com.youzan.ebiz.mall.trade.buyer.api.service.OrderFeatureService';
  }

  /**
   * 获取电子卡券信息
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/342700
   */
  async queryByOrderNo(params: IVirtualTicketDTO): Promise<IVirtualTicketDetailVO> {
    const result = await this.invoke(this.VIRTUAL_TICKET_SERVICE, 'get', [params]);
    return result;
  }

  /**
   *  查询前端页面跳转url
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/263109
   */
  async queryFrontPathRedirectPath(params: IFrontPageRedirectPathReqDTO): Promise<IFrontPageRedirectPathResponseDTO> {
    return this.invoke(this.EXTENSION_POINT_SERVICE, 'queryFrontPathRedirectPath', [params]);
  }

  /**
   *  增加到卡包
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/141539
   */
  async getSyncWx(kdtId: number, orderNo: string, cardNo: string) {
    return this.invoke(this.ORDER_FEATURE_SERVICE, 'getSyncWx', [kdtId, orderNo, cardNo]);
  }
}

export default TicketService;
