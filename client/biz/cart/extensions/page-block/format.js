import cloneDeep from '@youzan/utils/object/clone-deep';

/**
 * 根据购物车列表数据获取商品的id
 * @param {array} clientCartWrap 购物车列表数据
 */
export const getGoodsAliasByCartList = (clientCartWrap) => {
  const groupList = clientCartWrap?.goodsGroupList || [];
  return groupList.map(groupItem => {
    return (groupItem.goodsList || []).map(item => item.alias);
  }).flat();
}

// 数组转对象
export const guaranteeDataToMap = (data = []) => {
  return data.reduce((obj, item) => {
    return {
      ...obj,
      [item.alias]: item.yzSecured
    }
  }, {})
};

/**
 *  格式化有赞担保信息
 * @param {array} clientCartWrap 购物车列表数据
 * @param {array} itemsYzGuaranteeData 购物车内商品对应的有赞担保数据
 */
export const formatYzGuaranteeValue = (clientCartWrap, itemsYzGuaranteeData) => {
  const goodsGuarantee = guaranteeDataToMap(itemsYzGuaranteeData);
  const newCart = cloneDeep(clientCartWrap);
  newCart.goodsGroupList.forEach(groupItem => {
    groupItem.goodsList.forEach(goods => {
      goods.yzGuarantee = goodsGuarantee[goods.alias];
    })
  });
  return newCart;
}
