<template>
  <div v-if="showLoginTips" class="login-tip">
    <template v-if="!isAppCart">
      <span class="close" @click="closeLoginTips" />
      <span class="desc">绑定手机号可保存购物车中所有商品</span>
      <span class="login-btn" @click="loginRightNow">立即绑定</span>
    </template>

    <template v-else>
      <span class="desc app-desc">登录后你可以保存购物车中的所有商品</span>
      <span class="login-btn app-login-btn" @click="loginRightNow">登录</span>
    </template>
  </div>
</template>

<script>
import LS from 'utils/browser/local-storage';
import { action } from '@youzan/zan-jsbridge';
import userInfoAuthorize from '@youzan/user-info-authorize';
import {
  IS_APP_CART,
  IS_MAIJIABAN,
  NEED_LOGIN,
  IS_LOGIN,
} from '@biz/cart/constant';

export default {
  name: 'login-tips',

  props: ['ctx'],

  data() {
    return {
      isAppCart: IS_APP_CART,
      // 关闭登陆提示时间
      closeLoginTipTimestamp: +LS.get('close-tip-time') || 0,
      shopCart: {},
    };
  },

  created() {
    this.unwatchShopCart = this.ctx.watch('shopCart', (val) => {
      this.shopCart = val;
    });
  },

  destroyed() {
    this.unwatchShopCart && this.unwatchShopCart();
  },

  computed: {
    showLoginTips() {
      const hasValidGoods = (this.shopCart.goodsGroupList || []).some(
        (goodsGroup) => !!goodsGroup.goodsList.length
      );

      // 展示登录提示: 购物车中有商品，但是没有登录，且一天之内没有取消
      const now = new Date().getTime();
      const oneDay = 24 * 3600 * 1000;

      return (
        hasValidGoods &&
        NEED_LOGIN &&
        !IS_LOGIN &&
        (!this.closeLoginTipTimestamp ||
          this.closeLoginTipTimestamp + oneDay < now)
      );
    },
  },

  methods: {
    closeLoginTips() {
      this.$dialog
        .confirm({
          title: '提醒',
          confirmButtonText: '明天再提醒',
          cancelButtonText: '我知道了',
          message: '绑定手机号，有助于订单查询哦～',
          closeOnClickOverlay: true,
        })
        .then(() => {
          this.$dialog.close();
          this.closeLoginTipTimestamp = Date.now();
        })
        .catch(() => {
          this.$dialog.close();
        });
    },

    loginRightNow() {
      if (IS_MAIJIABAN) {
        action.gotoNative({
          page: 'login',
        });
      } else {
        userInfoAuthorize
          .open({
            authTypeList: ['mobile'], // 只手机号授权
          })
          .then(() => {
            window.location.reload();
          });
      }
    },
  },
};
</script>

<style lang="scss">
.login-tip {
  height: 38px;
  line-height: 38px;
  padding: 0 10px;
  margin-bottom: 10px;
  font-size: 12px;
  position: relative;
  background: #fff;

  .close {
    display: inline-block;
    background: url('https://img01.yzcdn.cn/v2/image/wap/trade/cart/<EMAIL>')
      no-repeat;
    background-size: 15px 15px;
    background-position: center center;
    width: 15px;
    height: 38px;
    margin-right: 6px;
  }

  .desc {
    display: inline-block;
    vertical-align: top;
    color: #333;
  }

  .app-desc {
    margin-left: 10px;
  }

  .login-btn {
    float: right;
    height: 18px;
    padding: 3px 5px;
    margin-top: 6px;
    border: 1px solid #f60;
    color: #f60;
    line-height: 18px;
    border-radius: 3px;
  }

  .app-login-btn {
    text-align: center;
    width: 30px;
    padding: 3px 10px;
  }

  .van-popup {
    border-radius: 4px;
  }
}
</style>
