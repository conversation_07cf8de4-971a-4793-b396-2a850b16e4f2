// @ts-nocheck
const QR_TYPE_DYNAMIC = 0; // 定额的一次性二维码
const QR_TYPE_FIXED = 1; // 默认自助二维码一个kdt_id只有1个
const QR_TYPE_FENXIAO = 2; // 分销商付款二维码
const QR_TYPE_TRUENAME = 3; // 实名认证二维码
const QR_TYPE_NOLIMIT = 4; // 固定金额二维码
const QR_TYPE_3RD = 5; // 一次性二维码
const QR_TYPE_FIXED_BY_PERSON = 6; // 人工的自助二维码
const QR_TYPE_ONETIME_BIND_ORDER = 7; // 定额一次性二维码且绑定已有订单
const QR_TYPE_WXAPP_QR_TYPE_VARIABLE = 8; // 小程序自助收款码
const QR_TYPE_FENXIAO_ENTERPRISE_PURCHASE = 51; // 购买企业版
const QR_TYPE_FENXIAO_SUPPLIER_DEPOSIT = 52; // 供应商交保证金
const QRCODE_IS_DISCOUNT_YES = 1; // 默认享受优惠
const QRCODE_IS_DISCOUNT_NO = 0; // 不享受优惠
const QRCODE_LABEL_MAX_NUM = 5; // 二维码标签数量最大值;

export function isFixedMoney(qrType: number = 999) {
  return qrType != QR_TYPE_FIXED && qrType != QR_TYPE_FIXED_BY_PERSON && qrType != QR_TYPE_WXAPP_QR_TYPE_VARIABLE;
}

export function isAnonymous(qrType: number = 999, fansId: number = 0) {
  return [QR_TYPE_NOLIMIT, QR_TYPE_FIXED, QR_TYPE_DYNAMIC].indexOf(qrType) > -1 && (!fansId || fansId <= 0);
}

export function isMutiPay(qrType: number = 999) {
  return [QR_TYPE_NOLIMIT].indexOf(qrType) > -1;
}

export function isExpireQrcode(qrType: number = 999) {
  return !(
    [
      QR_TYPE_FENXIAO,
      QR_TYPE_TRUENAME,
      QR_TYPE_NOLIMIT,
      QR_TYPE_FENXIAO_ENTERPRISE_PURCHASE,
      QR_TYPE_FENXIAO_SUPPLIER_DEPOSIT,
      QR_TYPE_FIXED
    ].indexOf(qrType) > -1
  );
}

export function isWxOrder(qrType: number = 999) {
  return qrType === QR_TYPE_WXAPP_QR_TYPE_VARIABLE;
}
