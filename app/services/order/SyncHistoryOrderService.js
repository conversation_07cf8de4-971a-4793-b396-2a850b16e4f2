const BaseService = require('../base/BaseService');

class SyncHistoryOrderService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.trade.process.api.order.service.SyncHistoryOrderService';
  }

  sync(params) {
    return this.invoke(this.SERVICE_NAME, 'sync', [params]);
  }

  queryRecord(params) {
    return this.invoke(this.SERVICE_NAME, 'queryRecord', [params]);
  }
}

module.exports = SyncHistoryOrderService;
