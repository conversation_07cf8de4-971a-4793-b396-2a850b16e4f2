const BaseService = require('../../../base/BaseService');


	/** com.youzan.ump.marketing.paidpromotion.service.PaidPromotionDubboService -  */
class PaidPromotionDubboService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.ump.marketing.paidpromotion.service.PaidPromotionDubboService';
  }
  
	/**
   *  支付成功页, 处理支付有礼发放流程
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/961421
   *  天网: https://ops.qima-inc.com/v3/skynet/#/main/prod/log/search/all-log?appName=wsc-h5-trade&order=DESC&tags=methodName%3DuseActivityInOrder&tags=serviceName%***************************
   *
   *  @param {Object} param -
   *  @param {string} param.orderNo - 订单号
   *  @param {number} param.fansId - 用户粉丝id
   *  @param {number} param.kdtId - 店铺id
   *  @param {number} param.buyerId - 用户id
   *  @param {number} param.fansType - 用户粉丝类型
   *  @return {Promise}
   */
  async useActivityInOrder(param) {
    return this.invoke('useActivityInOrder', [param]);
  }

  async triggerActivity(param) {
    return this.invoke("triggerActivity", [param]);
  }
}

module.exports = PaidPromotionDubboService;