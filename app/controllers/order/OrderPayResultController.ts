import { Context } from 'astroboy';
import { get, escape as lodashEscape } from 'lodash';
import { PageException } from '@youzan/iron-base';
import BaseController from '../base/BaseController';
import OrderPayResultService from '../../services/order/OrderPayResultService';
import OrderGuideCouponService from '../../services/order/OrderGuideCouponService';
import TicketService from '../../services/order/TicketService';
import AdExchangeService from '../../services/cps/AdExchangeService';
import { ShopPayResultGroup, PayResultParam } from 'definitions/pay/PayResult';
import UA from '@youzan/iron-base/app/lib/UA';
import { getTradeApolloConfig } from '../../lib/Apollo';
import { isInGrayReleaseByKdtId } from '../../lib/WhiteListUtils';
import { parseCdnImgUrl } from '../../lib/UrlUtils';
import { checkPureWscSingleStore, checkRetailMinimalistShop } from '@youzan/utils-shop';
import { getDirectBuyAgainBtnConfig } from '../../lib/TradeConfig';
import { isCloudDesign } from '@youzan/plugin-h5-ecloud';
import { initRanta } from '@youzan/plugin-h5-ranta-config';
import { getBrandShopStyleToState } from '@youzan/plugin-h5-global-theme';

/**
 * 后端返回key，对应前端模板value
 */
const PAY_RESULT_TEMPLATE: IPureObject = {
  NORMAL: 'success',
  WAIT: 'wait',
  PAY_ERROR: 'error',
  OVERSALE_ERROR: 'oversale-error',
  /**
   * 降价拍
   */
  AUCTION: 'auction',
  AUCTION_WAIT: 'auction-wait',
  /**
   * 新希望定制
   */
  NEWHOPE: 'newhope',
};

const PAY_RESULT_TITLE_MAP: IPureObject = {
  AUCTION: '付款成功',
  AUCTION_WAIT: '等待确认',
};

interface IImprovementRecommendGrayRule {
  threshold?: number;
  whitelist?: number[];
  blacklist?: number[];
  keywords?: {
    force?: boolean;
    match?: string[];
    block?: string[];
  };
}

interface IFissionCoupon {
  customShareDescstring?: string;
  id?: number;
  orderNo?: string;
  quantity: number;
  shareLinkImgUrl?: string;
  styleVersion?: 'v1' | 'v2';
}

// 降价拍路由
const AUCTION_PATH = '/wsctrade/order/payresult/auction';

class OrderPayResultController extends BaseController {
  get buyerDTO() {
    const { buyerId, fansId, fansType } = this.buyer;

    return {
      buyerId,
      fansId,
      fansType,
    };
  }

  async init() {
    const { kdtId, acceptJSON } = this.ctx;

    const initConfig = {
      initMpData: true,
      initMpAccount: false,
      initLifecycle: false,
      initOpenAppConfig: true,
      initGlobalTheme: !!kdtId,
      initCopyrightFooter: !acceptJSON,
    };

    await super.init(initConfig);
  }

  async getCheckPayJson(ctx: Context) {
    const { phase, requestNo } = ctx.query;
    const data = await new OrderPayResultService(ctx).checkPay({
      buyerDTO: this.buyerDTO,
      requestNo,
      phase: Number(phase),
    });
    ctx.json(0, '', data);
  }

  async getCheckPayGuideCouponJson(ctx: Context) {
    const { orderNo } = ctx.query;
    const { kdtId } = this.ctx;
    const data = await new OrderGuideCouponService(ctx).findPromotion({
      orderNo,
      shopId: Number(kdtId),
    });
    ctx.json(0, '', data);
  }

  async getCouponMessageJson(ctx: Context) {
    const { requestNo } = ctx.query;
    const data = await new OrderPayResultService(ctx).getCouponMessage({
      buyerDTO: this.buyerDTO,
      requestNo,
    });
    ctx.json(0, '', data);
  }

  // 内购订单支付成功页图片读取Apollo
  public getImg() {
    let webImg = 'https://img01.yzcdn.cn/upload_files/2021/12/23/FoW8WHxBn8HLKASvZDuB9PjfYn7M.png';
    let weappImg = 'https://img01.yzcdn.cn/upload_files/2021/12/23/FoW8WHxBn8HLKASvZDuB9PjfYn7M.png';
    try {
      const internalPurchaseImg = getTradeApolloConfig('wsc-h5-trade.application', 'internalPurchaseImg') || '{}';
      const ImgObj = JSON.parse(internalPurchaseImg);
      if (ImgObj.web) {
        webImg = ImgObj.web;
      }
      if (ImgObj.weapp) {
        weappImg = ImgObj.weapp;
      }
    } catch (err) {
      console.log(err);
    }
    return {
      webImg,
      weappImg,
    };
  }

  async getIndexHtml(ctx: Context) {
    const query = ctx.getQueryData();
    const { request_no, order_no, orderNo: orderNoQuery } = query;
    const requestNo = request_no || order_no || orderNoQuery;

    // 预售订单
    const phase = query.phase ? Number(query.phase) : null;
    this.setPageCloudBizIds('orderPayResult', 'orderNo', requestNo);

    if (!requestNo) {
      throw new PageException(10500, '无此订单！');
    }

    const orderPayResultService = new OrderPayResultService(ctx);

    const params: PayResultParam = {
      kdtId: ctx.kdtId,
      buyerDTO: this.buyerDTO,
      requestNo,
      phase: phase || 0,
    };
    // 说明是支付宝中的小程序
    if (ctx.isAlipayApp) {
      params.orderMark = 'alipay_mini_program';
    }
    // 说明是QQ小程序
    if (ctx.isQQApp) {
      params.orderMark = 'qq_mini_program';
    }
    const payResultRedirectIsInGrey = await isInGrayReleaseByKdtId(
      ctx,
      {
        namespace: 'wsc-h5-trade.gray-release',
        key: 'appPayResultConfirm',
      },
      ctx.kdtId
    ).catch(() => {});
    const payResult = (await orderPayResultService.getPayResult({ ...params, newMemberGuideFlag : true })) || {};

    const { payResultVO = {}, shopPayResultGroup } = payResult;
    const { pageTemplate } = payResultVO;
    // 如果app开店环境支付还没有完成，则判断是否需要跳转支付中间页
    // 如果用户已经在中间页主动选择了支付完成，则userChecked有值，不再重定向
    if (
      payResultRedirectIsInGrey &&
      ctx.isAppSdk &&
      pageTemplate.toLowerCase() === PAY_RESULT_TEMPLATE.WAIT &&
      !ctx.query.userChecked
    ) {
      return ctx.redirect(`/wsctrade/order/pay-result-confirm?request_no=${requestNo}&phase=${phase || ''}`);
    }
    const webPageRedirectUrl = get(payResultVO, 'redirectDTO.webPageRedirectUrl');
    const headShop = get(shopPayResultGroup, '[0]', {});
    const shopConfig = get(headShop, 'shopConfig', {});
    const order = get(headShop, 'orderPayResultGroup[0]');
    const { shareConfig, orderNo, isFxZppOrder = false } = order;
    const tpl = PAY_RESULT_TEMPLATE[pageTemplate] || PAY_RESULT_TEMPLATE.PAY_ERROR;
    const isFxZpp = isFxZppOrder; // 赞拼拼订单

    const hasPrescriptionDrugGood = shopPayResultGroup.some((one: { orderPayResultGroup: any[] }) => {
      return one.orderPayResultGroup.some((item: { prescriptionOrder: boolean }) => item.prescriptionOrder);
    });

    if (payResultVO.needRedirect && webPageRedirectUrl) {
      ctx.redirect(webPageRedirectUrl);
      return;
    }
    // 降价拍重定向使用新路由 @卓威
    if (
      (tpl === PAY_RESULT_TEMPLATE.AUCTION || tpl === PAY_RESULT_TEMPLATE.AUCTION_WAIT) &&
      ctx.href.indexOf(AUCTION_PATH) < 0
    ) {
      return ctx.redirect(AUCTION_PATH + ctx.href.slice(ctx.href.indexOf('?')));
    }

    // 更新kdtId
    if (headShop.kdtId > 0 && String(headShop.kdtId) !== ctx.kdtId) {
      ctx.kdtId = headShop.kdtId;
    }

    // 灰度发布新的支付成功页面
    // @ts-ignore
    const mpData: any = ctx.getState('mpData') || {};

    const [
      isThirdApp,
      appSdkHistoryBack,
      useWebRantaPayResult,
      isCloudCustomized = false,
      useWebRantaPayResultWithCloudDesign,
    ] = await Promise.all([
      ctx.isThirdApp(),
      isInGrayReleaseByKdtId(
        ctx,
        {
          namespace: 'wsc-h5-trade.application',
          key: 'appSdkPaySuccessPageHistoryBack',
        },
        ctx.kdtId
      ).catch(() => {}),
      isInGrayReleaseByKdtId(
        ctx,
        {
          namespace: 'wsc-h5-trade.gray-release',
          key: 'useWebRantaPayResult',
        },
        ctx.kdtId
      ).catch(() => {}),
      isCloudDesign(ctx as any, { pageName: 'order-pay-result', kdtId: ctx.kdtId }),
      isInGrayReleaseByKdtId(
        ctx,
        {
          namespace: 'wsc-h5-trade.gray-release',
          key: 'useWebRantaPayResultWithCloudDesign',
        },
        ctx.kdtId
      ).catch(() => {}),
    ]);

    this.setExtensionParams(ctx, 'order-pay-result', { orderNo: requestNo });

    // 裂变券信息
    try {
      const fissionCoupon: IFissionCoupon = get(payResult, 'shopPayResultGroup[0].shopCoupons[0].orderCoupons[0]');
      if (fissionCoupon) {
        payResult.shopPayResultGroup[0].shopCoupons[0].orderCoupons[0] = {
          ...(await this.formatFissionCoupon(fissionCoupon)),
        };
      }
    } catch (e) {}

    // 支付有礼跳转链
    orderPayResultService.patchPaidPromotion(get(order, 'paidPromotion') || {}, orderNo);

    // 判断当前是否需要展示商品推荐
    const checkKeys = ['goodsRecommend', 'goodsRecommendPay'];
    const isShowRecommendGoods = checkKeys.every((key) => +get(shopConfig, key) === 1);

    this.escapePayResult(shopPayResultGroup);

    const { webImg } = this.getImg();
    // 获取展示免密支付的平台配置
    const secretPayPlatforms = getTradeApolloConfig('wsc-h5-assets.alipay-channel', 'channel-list');
    ctx.setGlobal({
      env: {
        isWeixin: ctx.isWeixin,
        isKuaiShou: UA.isKuaishou(ctx.userAgent),
        isYouzanke: UA.isYouzanke(ctx.userAgent),
        isXmlyApp: ctx.platform === 'iting',
        isDouYinApp: ctx.platform === 'douyin',
        isAlipay: ctx.isAlipay,
        isWeappWebview: ctx.isWeapp && !ctx.isWeappNative,
        isYouzanmars: ctx.isYouzanmars,
        isYouzanwxd: ctx.isYouzanwxd,
        isThirdApp,
        isSwanApp: ctx.isSwanApp, // 百度小程序
        isYzCloudApp: ctx.isYzCloudApp, // 是否为APP开店环境
        isAlipayApp: ctx.isAlipayApp, // 支付宝小程序
        isQQApp: ctx.isQQApp, // QQ小程序
        isFxZpp, // 赞拼拼
      },
      hasPrescriptionDrugGood,
      payResult,
      requestNo,
      phase,
      isShowRecommendGoods,
      appSdkHistoryBack,
      theme: ctx.getState('globalTheme' as never), // 店铺风格
      isWeixin: ctx.isWeixin,
      webImg, // 内购支付成功页图片
      secretPayPlatforms, // 展示免密支付的平台配置
      currentPlatform: ctx.platform,
      // 是否使用加强的支付宝免密开通推荐策略
      '@cashier/improve-alipay-free': this.matchImprovementRecommendGrayRule(),
      // 收银台组件埋点metadata
      '@cashier/metadata': {
        shop_name: mpData && mpData.shopName,
        shop_type: mpData && mpData.shopType,
        order_no: requestNo,
      },
      payResultRedirectIsInGrey,
    });

    // @ts-ignore
    let defaultTitle = mpData.shopName || '支付结果';
    if (defaultTitle.length > 10) defaultTitle = defaultTitle.slice(0, 10) + '...';
    // 只有成功状态下才展示骨架屏
    if (tpl === PAY_RESULT_TEMPLATE.NORMAL) {
      this.ctx.setState('showSkeleton', true);
    }

    ctx.setState({
      // 渲染页面
      tpl,
      title: PAY_RESULT_TITLE_MAP[pageTemplate] || defaultTitle,
      isCloudCustomized,
    });

    // 分享参数
    if (shareConfig) {
      shareConfig.link = shareConfig.goodsUrl;
      ctx.setShare(shareConfig);
    }

    // 日志埋点模型
    this.setSpm('paySuccess', ctx.kdtId);

    // 是否显示再来一单按钮[直接进入下单页]
    const directBuyAgainBtnConfig = await getDirectBuyAgainBtnConfig(ctx);
    ctx.setGlobal({
      directBuyAgainBtnConfig,
    });

    // 切流
    if (
      (!ctx.query.tee && useWebRantaPayResult && !isCloudCustomized) ||
      (!ctx.query.tee && useWebRantaPayResultWithCloudDesign && isCloudCustomized) ||
      ctx.query.tee === '1'
    ) {
      await initRanta(ctx as any, {
        framework: 'tee',
        bizName: '@wsc-tee-h5-trade/order-paid',
        appName: 'wsc-tee-h5',
        ecloud: {
          setExtensionParams: [
            {
              kdtId: +ctx.kdtId,
              pageName: 'order-pay-result',
              pageNameV2: 'order-paid',
              conditionContext: this.getConditionContext(ctx, { orderNo: requestNo }),
            },
          ],
        },
      });
      await getBrandShopStyleToState(ctx as any, { useAppStyleIcon: true });
      await ctx.render(
        'order/tee-pay-result',
        {},
        {
          appName: 'wsc-tee-h5',
          skipLoadOpsWebConfig: true,
        }
      );
    } else {
      await ctx.render('order/pay-result');
    }
  }

  /**
   * 裂变券信息，目前主要是由于「搜索发券&裂变券」项目需要根据店铺类型显示不同样式做的处理
   * fissionCoupon.styleVersion : 'v1' | 'v2'
   * v2 微商城单店&有赞连锁D
   * v1 其他
   * @param fissionCoupon
   * @private
   */
  private async formatFissionCoupon(fissionCoupon: IFissionCoupon): Promise<IFissionCoupon> {
    const { ctx } = this;
    if (!fissionCoupon) {
      return fissionCoupon;
    }

    try {
      const shopMetaInfo = await this.callService('iron-base/shop.ShopMetaReadService', 'getShopMetaInfo', ctx.kdtId);

      return {
        ...fissionCoupon,
        styleVersion: checkPureWscSingleStore(shopMetaInfo) || checkRetailMinimalistShop(shopMetaInfo) ? 'v2' : 'v1',
      };
    } catch (e) {
      return fissionCoupon;
    }
  }

  async getSelfFetchMessageJson(ctx: Context) {
    const { phase, requestNo } = ctx.query;
    const data = await new OrderPayResultService(ctx).getSelfFetchMessage({
      buyerDTO: this.buyerDTO,
      phase: Number(phase),
      requestNo,
    });
    ctx.json(0, '', data);
  }

  async getPayResultJson(ctx: Context) {
    let { requestNo } = ctx.query;
    const { phase, source, newMemberGuideFlag } = ctx.query;
    const { buyerDTO } = this;
    const isVOrder = requestNo.startsWith('V'); // 判断是否V单,控制跳转逻辑
    const orderPayResultService = new OrderPayResultService(ctx);

    try {
      const requestNos = JSON.parse(requestNo);
      if (Array.isArray(requestNos)) {
        requestNo = requestNos[0];
      }
    } catch (error) {
      // do nothing
    }

    const payResult = await orderPayResultService.getPayResult({
      buyerDTO,
      requestNo,
      phase: Number(phase),
      source,
      newMemberGuideFlag,
    });

    // 判断当前是否需要展示商品推荐
    const firstShopPayResult = get(payResult, 'shopPayResultGroup[0]');
    const firstOrderPayResultGroup = get(firstShopPayResult, 'orderPayResultGroup[0]');
    const shopConfig = get(firstShopPayResult, 'shopConfig', {});
    const checkKeys = ['goodsRecommend', 'goodsRecommendPay'];

    const isShowRecommendGoods = checkKeys.every((key) => +get(shopConfig, key) === 1);

    const memberCard = get(firstShopPayResult, 'memberCards[0]');

    const { weappImg } = this.getImg();

    if (firstOrderPayResultGroup?.paidPromotion?.imgUrl) {
      firstOrderPayResultGroup.paidPromotion.imgUrl = parseCdnImgUrl(firstOrderPayResultGroup.paidPromotion.imgUrl);
    }
    if (firstOrderPayResultGroup?.shareMessage?.imgUrl) {
      firstOrderPayResultGroup.shareMessage.imgUrl = parseCdnImgUrl(firstOrderPayResultGroup.shareMessage.imgUrl);
    }

    // 裂变券信息
    let shopCoupons: IFissionCoupon = get(firstShopPayResult, 'shopCoupons[0].orderCoupons[0]');
    if (shopCoupons) {
      shopCoupons = await this.formatFissionCoupon(shopCoupons);
      firstShopPayResult.shopCoupons[0].orderCoupons[0] = {
        ...shopCoupons,
      };
    }

    // 是否显示再来一单按钮[直接进入下单页]
    const directBuyAgainBtnConfig = await getDirectBuyAgainBtnConfig(ctx);

    // 减轻firstShopPayResult返回数据负担
    delete firstShopPayResult.orderPayResultGroup;
    const resolvedPayResult = {
      isVOrder,
      memberCard,
      ...get(payResult, 'payResultVO'),
      /**
       * 数据拍平
       * 目前只显示单个订单的支付
       */
      ...firstOrderPayResultGroup,
      shopPayResult: firstShopPayResult,
      award: {
        cashInfo: firstShopPayResult.cashInfo,
        credit: firstShopPayResult.credit,
        hasMemberCard: firstShopPayResult.hasMemberCard,
        memberCards: firstShopPayResult.memberCards,
      }, // 支付优惠信息
      // 老版本小程序使用v2.92.x之前
      shopCoupons, // 裂变券信息
      isShowRecommendGoods, // 是否展示商品推荐
      weappImg, // 内购支付成功页图片
      directBuyAgainBtnConfig,
    };

    ctx.json(0, '', resolvedPayResult);
  }

  //  商家小票支付结果接口
  async getWxPayResultHtml(ctx: Context) {
    // 加载中台化配置
    await this.loadRantaConfig('@wsc-h5-trade/payresult-wx');
    // 日志埋点模型
    this.setSpm('businessReceipts', ctx.kdtId);

    await ctx.render('order/pay-result-wx', {
      pageName: 'order/pay-result-wx',
    });
  }

  async getWxPayResultJson(ctx: Context) {
    const isThirdApp = await ctx.isThirdApp();
    const { requestNo, kdtId } = ctx.query;
    const orderPayResultService = new OrderPayResultService(ctx);

    const { webImg } = this.getImg();
    const payResult = await orderPayResultService.getWxPayResult({
      requestNo,
      kdtId,
    });
    payResult.webImg = webImg;

    // 裂变券信息，由于「搜索发券&裂变券」项目要区分新老样式，新样式仅针对「微商城单店&连锁D」
    try {
      const fissionCoupon: IFissionCoupon = get(payResult, 'shopPayResult.shopCoupons[0].orderCoupons[0]');
      if (fissionCoupon) {
        payResult.shopPayResult.shopCoupons[0].orderCoupons[0] = {
          ...(await this.formatFissionCoupon(fissionCoupon)),
        };
      }
    } catch (e) {}

    payResult.isThirdApp = isThirdApp;
    ctx.json(0, '', payResult);
  }

  escapePayResult(shopPayResultGroup: ShopPayResultGroup[]) {
    shopPayResultGroup &&
      shopPayResultGroup.forEach((shopPayResult) => {
        const orderPayResultGroup = shopPayResult.orderPayResultGroup || [];
        orderPayResultGroup.forEach((orderPayResult) => {
          const { orderItems = [] } = orderPayResult;
          orderItems.forEach((orderItem) => {
            orderItem.title = lodashEscape(orderItem.title);
          });
        });
      });
  }

  /**
   * 获取电子卡券详情
   * 格式化输出+支持自定义source值
   */
  async getTicketDetailJson(ctx: Context) {
    const { kdtId } = ctx;
    const { orderNo, order_no: orderNoOther, source = 'weapp' } = ctx.query;
    const finalOrderNo = orderNo || orderNoOther;
    // eslint-disable-next-line youzan/youzan-standard-words
    this.validator.required(finalOrderNo, '订单号不能为空');
    const ticketService = new TicketService(ctx);
    const params = {
      orderNo: finalOrderNo,
      kdtId,
      buyerId: this.buyerId,
      from: source,
    };
    const result = await ticketService.queryByOrderNo(params);
    ctx.json(0, '', result);
  }

  async getShopAd(ctx: Context) {
    let shopAd = {};
    const { kdtId, userId } = ctx;
    const { app_id } = ctx.getQueryData();
    try {
      const {
        adType,
        resourceResponse,
        couponAdContentList = [],
        secKillAdContentList = [],
      } = await new AdExchangeService(ctx).getAdExchangeContent({
        kdtId,
        userId,
        resourceId: 84,
      });
      // 如果商家正在进行广告互换
      if (adType && couponAdContentList && secKillAdContentList) {
        shopAd = {
          adType,
          couponAdContentList,
          secKillAdContentList,
          resourceResponse,
        };

        // 如果在weapp中
        if (app_id) {
          const list = couponAdContentList.concat(secKillAdContentList);
          const isShowWeapp = list.every((item: any) => {
            const { adImageConfig = '{}' } = item;
            const { weappUrl, appId } = JSON.parse(adImageConfig);
            return weappUrl && appId;
          });
          // 如果不是所有店铺广告都包含了微信小程序
          if (!isShowWeapp) {
            shopAd = {};
          }
        }
      }
    } catch (err) {}
    ctx.json(0, '', shopAd);
  }

  /**
   * 支付宝免密加强推荐策略的灰度规则匹配
   * @private
   */
  matchImprovementRecommendGrayRule() {
    const kdtId = Number(this.ctx.kdtId);
    try {
      const { threshold = 0, whitelist = [], blacklist = [], keywords }: IImprovementRecommendGrayRule = JSON.parse(
        getTradeApolloConfig('wsc-h5-assets.alipay-channel', 'improvement-recommend')
      );

      const inWhitelist = whitelist.includes(kdtId);
      const inBlacklist = blacklist.includes(kdtId);
      return {
        enabled: (!inBlacklist && inWhitelist) || threshold > kdtId % 100,
        keywords,
      };
    } catch (error) {
      console.log(error);
      this.ctx.logger.error('订单结果页：：获取支付宝免密推荐规则失败', error, {});
      return {
        enabled: false,
        display: { force: false, match: [] },
      };
    }
  }

  async getSelfFetchTakeGoodsMessage(ctx: Context) {
    const { requestNo } = ctx.query;
    const data = await new OrderPayResultService(ctx).getSelfFetchTakeGoodsMessage({
      buyerDTO: this.buyerDTO,
      requestNo,
    });
    ctx.json(0, '', data);
  }

  // app开店支付中间页
  async getPayResultConfirmHtml(ctx: Context) {
    await ctx.render('order/pay-result-confirm');
  }
}

export = OrderPayResultController;
