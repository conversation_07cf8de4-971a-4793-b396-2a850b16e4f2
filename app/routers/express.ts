export = [
  [
    'GET',
    '/wsctrade/express/periodbuy',
    'express.ExpressPeriodBuyController',
    'getIndexHtml'
  ],
  [
    'GET',
    '/wsctrade/express/periodbuy/postpone',
    'express.ExpressPeriodBuyController',
    'getPostponeHtml'
  ],
  [
    'GET',
    '/wsctrade/express/periodbuy/cancelpostpone',
    'express.ExpressPeriodBuyController',
    'getCancelPostponeHtml'
  ],
  [
    'GET',
    '/wsctrade/express/periodbuy/issuecalendar.json',
    'express.ExpressPeriodBuyController',
    'getIssueCalendarJson'
  ],
  [
    'GET',
    '/wsctrade/express/periodbuy/issue.json',
    'express.ExpressPeriodBuyController',
    'getIssueDetailJson'
  ],
  [
    'POST',
    '/wsctrade/express/periodbuy/postponeissue.json',
    'express.ExpressPeriodBuyController',
    'postponeIssueJson'
  ],
  [
    'POST',
    '/wsctrade/express/periodbuy/cancelpostponeissue.json',
    'express.ExpressPeriodBuyController',
    'cancelPostponeIssueJson'
  ],
  [
    'GET',
    '/wsctrade/express/detail',
    // 'checkRedirectToShopDomain', // 自带kdtId
    'express.ExpressDetailController',
    'getIndexHtml'
  ],
  [
    'GET',
    '/wsctrade/express/getCityDetail.json',
    'express.ExpressDetailController',
    'getCityDetail'
  ]
];
