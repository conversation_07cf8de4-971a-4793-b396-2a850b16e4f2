import BaseService from '../base/BaseService';

class BillService extends BaseService {
  get BILL_SERVICE() {
    return 'com.youzan.trade.api.bill.BillService';
  }

  async createWeappOrder(postData: any) {
    const billCustomer: IPureObject = {
      fansId: this.ctx.getLocalSession('fans_id') || 0,
      fansType: this.ctx.getLocalSession('fans_type')
    };
    // 只有有 buyer_id 的时候，才需要传
    if (this.ctx.getLocalSession('youzan_user_id')) {
      billCustomer.buyerId = this.ctx.getLocalSession('youzan_user_id');
    }
    postData.billCustomer = billCustomer;

    // 优惠券 id 和 type 需要临时格式成字符串形式
    const billShopList = postData.billShopList || [];
    const shopData = billShopList[0] || {};
    shopData.couponId = `${shopData.couponId || ''}`;
    shopData.couponType = `${shopData.couponType || ''}`;

    // 调用 java 下单
    const billData = await this.invoke(
      this.BILL_SERVICE,
      'create',
      [postData]
    );

    return { billData };
  }
}

export = BillService;
