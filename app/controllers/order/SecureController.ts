import BusinessServiceError from '@youzan/iron-base/app/services/base/BusinessServiceError';
import BaseController from '../base/BaseController';
import UicEncryptService from '../../services/uic/UicEncryptService';
import SecureService from '../../services/order/SecureService';
import SmsCaptchaService from '../../services/uic/SmsCaptchaService';
import { isInGrayReleaseByKdtId } from '../../lib/WhiteListUtils';

const MAX_FAILURE_TIMES = 5;

class SecureController extends BaseController {
  public async init() {
    await super.init({
      validKdtId: true,
    });
  }

  public async getVerifyPhoneHtml() {
    const { ctx } = this;
    const {
      kdt_id: kdtId = 0,
      order_no: orderNo,
      kos_token: kosToken = '',
      redirect_uri: redirectUri = '',
    } = this.ctx.query;

    if (!redirectUri) {
      throw new BusinessServiceError(21402, '参数错误');
    }

    ctx.setGlobal({
      kdtId,
      orderNo,
      kosToken,
      redirectUri,
    });
    // 日志埋点模型
    this.setSpm('verifyphone', kdtId);
    await ctx.render('order/verify-phone.html');
  }

  public async postVerifyPhoneJson() {
    const { ctx } = this;
    const { kosToken: kosTokenStr, orderNo, phone, code, biz } = ctx.getPostData();

    const captchaRes = await new SmsCaptchaService(ctx).validSmsCaptcha({
      biz,
      mobile: phone,
      smsCaptcha: code,
    });

    if (!captchaRes.success) {
      // 验证码验证失败
      return ctx.json(0, 'ok', captchaRes);
    }

    let verifyPhoneNum = this.getVerifyPhoneNum();
    if (isNaN(verifyPhoneNum)) {
      verifyPhoneNum = 0;
    }
    if (verifyPhoneNum && verifyPhoneNum >= MAX_FAILURE_TIMES) {
      throw new BusinessServiceError(21400, '失败次数过多，请稍后重试');
    }

    // 接口直接校验
    const verifyRes = await new SecureService(ctx).verifyOrderReceiverMatch({
      orderNo,
      telephone: phone,
    });

    if (!verifyRes) {
      await this.writeBackVerifyNum(++verifyPhoneNum);
      throw new BusinessServiceError(21400, '手机号码不匹配');
    }

    // 到了这里就是校验成功
    await this.getService('iron-base', 'uic.OpenSessionService').set('verified_order', {
      orderNo,
      // 3 分钟超时
      expireAt: Date.now() + 180000,
    });
    ctx.r(0, '', { success: true });
  }

  async verifyKosTokenInLocal(token: string) {
    const { ctx } = this;
    let kosToken;
    const decryptObj = await new UicEncryptService(ctx).aesDecrypt(token);
    // console.log(decryptObj);
    if (decryptObj && Object.keys(decryptObj).length > 0) {
      try {
        kosToken = decryptObj.decryptValue && JSON.parse(decryptObj.decryptValue);
      } catch (e) {
        console.error(e);
      }
      if (kosToken && Object.keys(kosToken).length > 0) {
        return kosToken;
      }
    }
  }

  async writeBackVerifyNum(num: number) {
    await this.getService('iron-base', 'uic.OpenSessionService').set('verify_phone_num', String(num));
  }

  async sendVerifyCode() {
    const { ctx } = this;
    const { biz, phone, orderNo } = ctx.getPostData();
    const verifyRes = await new SecureService(ctx).verifyOrderReceiverMatch({
      orderNo,
      telephone: phone,
    });
    if (!verifyRes) {
      throw new BusinessServiceError(21400, '手机号码不匹配');
    }
    const res = await new SmsCaptchaService(ctx).sendSmsCaptcha({
      biz,
      mobile: phone,
    });
    return ctx.json(0, 'ok', res);
  }

  public getVerifyPhoneNum() {
    return Number(this.ctx.getLocalSession('verify_phone_num'));
  }
}

export = SecureController;