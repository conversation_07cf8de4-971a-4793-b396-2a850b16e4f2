// @youzan/scrm-common 按需引入，内部有window对象
import { LevelColorCode, LevelColorCodeMap } from '@youzan/scrm-common/lib/constants/level';
import { NEW_COLORS, COLOR_MAP, INewCardColorCode } from '@youzan/scrm-common/lib/constants/membercard';
import formatDate from '@youzan/utils/date/formatDate';
import isSameDay from '@youzan/utils/date/isSameDay'
import travel from '@youzan/utils/date/travel';
import { IDisplayCardVO } from 'definitions/order/buy/SimpleOrderPreparationVO';
import { cdnImage } from '../../../lib/UrlUtils';

type IExpiryDate = {
  expiryDate?: string;
  expiryDateDesc?: string;
  expiryDateIsRange?: 1 | 0;
}

type IFormatPrice = {
  formatDiscountPrice?: string;
  formatVirtualDiscountPrice?: string;
  formatPrice?: string;
  formatPostagePrice?: string;
}

const getTimeRange = (timestamp: number): string => {
  const year = new Date().getFullYear();
  const curYearStartTimeStamp = new Date(year, 1, 1).getTime();
  const curYearEndTimeStamp = new Date(year, 11, 31, 23, 59, 59).getTime();

  // 在今年的不展示年份
  if (timestamp >= curYearStartTimeStamp && timestamp < curYearEndTimeStamp) {
    return formatDate(timestamp, 'MM.DD');
  }
  return formatDate(timestamp, 'YY.MM.DD');
}

const levelExpiryDateMap: Record<number, string> = {
  1: '月',
  3: '3月',
  12: '年',
  36: '3年',
  60: '5年'
};

const getExpiryDate = (data: IDisplayCardVO): IExpiryDate => {
  const { cardType, lifeTimeDTO, termCycleDTO } = data;
  const result: IExpiryDate = {};
  if (cardType === 1) {
    switch (lifeTimeDTO?.termType) {
      case 1:
        result.expiryDate = '永久';
        result.expiryDateDesc = '永久有效';
        break;
      case 2:
        result.expiryDate = `${getTimeRange(lifeTimeDTO.termBeginAt)}至${getTimeRange(
          lifeTimeDTO.termEndAt
        )}`;
        result.expiryDateDesc = `有效期: ${result.expiryDate}`;
        result.expiryDateIsRange = 1;
        break;
      case 3:
        result.expiryDate = `${lifeTimeDTO.termDays}天`;
        result.expiryDateDesc = `有效期至${formatDate(travel(lifeTimeDTO.termDays), 'YYYY-MM-DD')}`;
        break;
    }
  } else if (termCycleDTO?.termMonths) {
    result.expiryDate =
      levelExpiryDateMap[termCycleDTO.termMonths] || `${termCycleDTO.termMonths}月`;
    result.expiryDateDesc = `有效期至${formatDate(
      travel(termCycleDTO.termMonths, new Date(), 'month'),
      'YYYY-MM-DD'
    )}`;
  }
  return result;
}

const formatPrice = (price: number | undefined): any[] => {
  if (price === undefined) return [];
  const result = [];
  result[0] = Math.floor(price / 100);
  let decimals = ('' + (price % 100)).padStart(2, '0');
  decimals = decimals.replace(/0+$/g, '');
  if (decimals) result[1] = decimals;
  return result;
}
const getFormatPrice = (data: IDisplayCardVO): IFormatPrice => {
  const { totalRecommendedDiscountPrice, displayVirtualCouponDiscountPrice = 0, price, displayMemberCardDiscountPostage = 0, couponNum = 0 } = data;
  const result: IFormatPrice = {};

  if (totalRecommendedDiscountPrice && totalRecommendedDiscountPrice > 0) {
    result.formatDiscountPrice = formatPrice(totalRecommendedDiscountPrice + displayMemberCardDiscountPostage).join('.');
  }
  if (couponNum > 0 && totalRecommendedDiscountPrice === 0) {
    result.formatVirtualDiscountPrice = formatPrice(displayVirtualCouponDiscountPrice + displayMemberCardDiscountPostage).join('.');
  }
  if (displayMemberCardDiscountPostage) {
    result.formatPostagePrice = formatPrice(displayMemberCardDiscountPostage).join('.');
  }
  result.formatPrice = formatPrice(price).join('.');
  return result;
}

type IColorMap = keyof typeof COLOR_MAP;

export const formatDisplayCard = (data: IDisplayCardVO): IDisplayCardVO => {
  const { coverUrl, colorCode = 'Color200', ...rest } = data
  const result: IDisplayCardVO = {
    ...rest,
    ...getExpiryDate(data),
    ...getFormatPrice(data),
  };
  // 优先取图，没图取色
  if (coverUrl) {
    // 卡面背景图
    result.coverUrl = cdnImage(coverUrl);
  } else if (rest.cardType === 1) {
    // colorCode如果不在【NEW_COLORS】内，就先用【COLOR_MAP】转换下；
    const { colors, direction = '135deg' } =
      NEW_COLORS[colorCode as INewCardColorCode] || NEW_COLORS[COLOR_MAP[colorCode as IColorMap] as INewCardColorCode] || {};

    result.bgColor = `linear-gradient(${direction}, ${colors[0]}, ${colors[1]})`;
  } else {
    result.bgColor = (LevelColorCodeMap[colorCode as LevelColorCode] || {}).pureColor;
  }

  return result;
}

/** 计算距离 */
export function formatDistanceToStr(distance: number) {
  if (distance > 1000) {
    return `${(distance / 1000).toFixed(1)}km`;
  }
  if (distance > 100) {
    return `${distance}m`;
  }
  if (distance < 100 && distance > 0) {
    return '<100m';
  }
}

export function formatPreSaleTips(orderItems: any, deliveryTimeBucket: any) {
  const hasFullPayPreSale = (orderItems || []).some((it: any) => it.presale && it.presaleType === 0)
  const { startTime } = deliveryTimeBucket || {};
  if (hasFullPayPreSale && startTime && !isSameDay(new Date(), new Date(startTime))) {
    const formatStartTime = formatDate(new Date(startTime), 'MM-DD');
    return {
      selfFetchTips: `订单含预售商品，最早${formatStartTime}号开始可自提`,
      deliveryTips: `订单含预售商品，最早${formatStartTime}号开始可配送`
    }
  }
  return null;
}