import BaseController from '../base/BaseController';
import RegionService from '../../services/delivery/RegionService';
import GpsService from '../../services/delivery/GpsService';
import { Context } from 'astroboy';

class RegionController extends BaseController {
  // 资产也维护了一份代码，如果有改动，麻烦通知一下资产， @小濛
  public async getRegionModelByName(ctx: Context) {
    const { lng, lon, lat } = ctx.query;
    // 兼容 lng 和 lon 两种写法
    const params = [+(lng || lon), +lat, 3];
    try {
      // 根据经纬度获取地区名称
      const addressInfo = await new GpsService(ctx).getReGpsInfo(params);
      // 根据地区名称获取城市code
      const regionParmas = {
        provinceName: addressInfo.province as string,
        cityName: addressInfo.city as string,
        countyName: addressInfo.county as string,
        fromApp: 'wsc-h5-trade',
      };
      const result = await new RegionService(ctx).getRegionModelByName(
        regionParmas
      );
      ctx.json(0, '', result || {});
    } catch (err) {
      ctx.logger.warn('使用经纬度获取地址详情失败, ' + err.toString());
      ctx.json(0, '使用经纬度获取地址详情失败');
    }
  }
}

export = RegionController;
