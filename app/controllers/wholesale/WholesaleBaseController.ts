import BaseController from '../base/BaseController';
import WholesalerQueryService from '../../services/wholesale/WholesalerReadService';
import { checkSupportWholesale } from '@youzan/wholesale-plugin';

class WholesaleBaseController extends BaseController {
  async getIndexHtml() {
    const { ctx } = this;
    // @ts-ignore
    await checkSupportWholesale(ctx);
    await ctx.render('wholesale/wholesale.html');
  }

  async isWholesaler() {
    const { ctx } = this;
    const result = await new WholesalerQueryService(ctx).isWholesaler({
      kdtId: ctx.kdtId,
      userId: this.buyerId,
    });

    ctx.json(0, 'success', result);
  }
}

export = WholesaleBaseController;
