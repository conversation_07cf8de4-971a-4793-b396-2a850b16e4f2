/* stylelint-disable */

export const enum PayToolEnum {
  WX_BARCODE = '微信条码',
  WX_JS = '微信公众号',
  ALIPAY_BARCODE = '支付宝条码',
  ALIPAY_WAP = '支付宝WAP',
  PREPAID_PAY = '储值卡',
  BALANCE = '余额',
  BANK = '银行卡',
  ECARD = 'E卡',
  GIFT_CARD = '礼品卡',
  DEBIT_CARD = '借记卡',
  CREDIT_CARD = '贷记卡',
  PEERPAY = '找人代付',
  CASH_ON_DELIVERY = '货到付款',
  YIPING_SWIPECARD = '一鸣支付',
  COMMON_PREPAY_CARD = '通用储值卡',
  WX_H5 = '微信H5支付',
  WX_APPLET = '小程序支付',
  WX_NATIVE = '微信扫码支付',
  MULTI_PRE_CARD = '多用途预付卡',
  VALUE_CARD = '储值卡支付',
  WX_TRANSFER = '微信企业付款',
  TRANSFER_VOUCHER = '线下转账凭证支付',
  CASH_PAY = '现金支付',
  MARK_PAY = '标记支付',
  ALIPAY_NATIVE = '支付宝扫码支付',
  ENCHASHMENT_GIFT_CARD = '新礼品卡支付',
  UN_SETTLED_AMOUNT_PAY = '待结算金额支付',
  DISCOUNT_DEDUCTION = '优惠抵扣',
  ALLIN_SWIPECARD = '通联支付工具',
  QQ_PAY = 'qq钱包支付工具',
  WX_APP = '微信APP支付',
  INSTALMENT = '分期支付',
  CHANGE_PAY = '零钱支付',
  UMP_PAY = '营销支付'
}

const enum ColorEnum {
  WX_GREEN = '#44BB00',
  ALI_BLUE = '#3388FF'
}

export const WeixinEnvPayToolColor = {
  WX_H5: ColorEnum.WX_GREEN,
  WX_JS: ColorEnum.WX_GREEN,
  WX_APP: ColorEnum.WX_GREEN,
  WX_APPLET: ColorEnum.WX_GREEN,
  ALIPAY_WAP: ColorEnum.ALI_BLUE
}

export const CommonEnvPayToolColor = {
  ALIPAY_WAP: ColorEnum.ALI_BLUE
}

export const PayToolColorMap: any = {
  weixin: WeixinEnvPayToolColor
}
