<template>
  <div class="combo-goods" @click.stop="$emit('open-combo-popup')">
    <div class="title">
      <div class="left">套餐包含以下商品</div>
      <div class="right">
        <span>详情</span>
        <van-icon name="arrow" />
      </div>
    </div>
    <div class="combo-list">
      <div class="combo-list-box" @touchmove.stop>
        <div class="item" v-for="(item, index) in comboDetail" :key="index">
          <img class="item-img" :src="item.src" />
          <div class="item-number">x{{ item.num }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { Icon } from 'vant';

export default {
  name: 'combo-detail',
  components: {
    [Icon.name]: Icon,
  },
  props: {
    comboDetail: {
      type: [Array],
      required: true,
      default: [],
    },
  },
};
</script>

<style scoped lang="scss">
::-webkit-scrollbar {
  display: none;
}
.combo-goods {
  margin-top: 8px;
  .title {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #323233;
    .right {
      color: #969799;
    }
  }
  .combo-list {
    display: flex;
    flex-direction: row;
    margin-top: 4px;
    flex: 1;
    overflow: hidden;
    &-box {
      overflow-x: scroll;
      display: flex;
      flex-direction: row;
      .item {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 40px;
        margin-right: 4px;
        &:last-of-type {
          margin-right: 0px;
        }
        &-img {
          width: 40px;
          height: 40px;
          object-fit: cover;
          border-radius: 2px;
        }
        &-number {
          color: #969799;
          font-size: 12px;
          margin-top: 4px;
        }
      }
    }
  }
}
</style>