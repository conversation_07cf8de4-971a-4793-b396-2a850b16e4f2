import get from 'lodash/get';
import args from '@youzan/utils/url/args';
import buildUrl from '@youzan/utils/url/buildUrl';
import {IOrderListItem, IOrderListResp} from "definitions/order/OrderList";
import { pushButton, BTN_CONF } from './order-buttons';
import { DEFAULT_POINTS_NAME, CRM_OFFLINE_TYPE, getTagConfig } from './constants';
import { cdnImage } from '../../../../lib/UrlUtils';
import formatDate from '@youzan/utils/date/formatDate';
import formatMoney from "@youzan/utils/money/format";

type ProcessOrderActionBtnsParams = {
  // 这个query是为了告诉我自己(尼莫)，这里的参数以后都是客户端传给node的
  query: {
    canUseTradeUmpV1: boolean;
    directBuyAgainBtnConfig: Record<string, any>;
    orderMark: string;
  };
  // 这个ctx是node上的ctx，不是中台化的ctx，这里定义的所有参数都是node.ctx直接可以拿到的
  nodeCtx: {
    isXhsApp: boolean;
    isKsApp: boolean;
    isRetailShop: boolean /* 店铺类型的判断在node端可以完成 */;
    isWeapp: boolean;
    isQQApp: boolean;
    isAlipayApp: boolean;
  };
};
export const processOrderActionBtns = (
  listOrder: Record<string, any>,
  params: ProcessOrderActionBtnsParams
) => {
  const { canUseTradeUmpV1, directBuyAgainBtnConfig, orderMark } = params.query;
  const {
    isXhsApp,
    isKsApp,
    isRetailShop,
    isWeapp,
    isAlipayApp,
    isQQApp,
  } = params.nodeCtx;
  const orderItem = get(listOrder, 'orderItems', [])[0] || {};
  const { orderPermission } = listOrder;
  const { orderExtra = {} as Record<string, any> } = orderItem;

  let actionBtns: any[] = [];

  // 针对美业订单 屏蔽所有按钮
  if (orderExtra.isBeauty) {
    return [];
  }

  /**
   * 这里插入按钮的时候，只考虑顺序对不对，是否展示的条件由按钮本身的condition控制
   */
  pushButton(actionBtns, BTN_CONF.shared)({ orderPermission, isWeapp, isAlipayApp, isQQApp, isXhsApp });
  pushButton(actionBtns, BTN_CONF.deleteOrder)({ orderPermission });
  pushButton(actionBtns, BTN_CONF.directBuyAgain)({ orderPermission, directBuyAgainBtnConfig, isAlipayApp, isQQApp, isXhsApp, isKsApp });
  pushButton(actionBtns, BTN_CONF.buyAgain)({ orderPermission, orderExtra, isXhsApp, isKsApp });
  pushButton(actionBtns, BTN_CONF.lotteryResult, { url: orderItem.lotteryResultUrl })({
    orderPermission,
    orderItem,
  });
  pushButton(actionBtns, BTN_CONF.viewCardDetail)({ orderPermission, isWeapp });
  pushButton(actionBtns, BTN_CONF.selfFetchCode)({ orderPermission, isWeapp });
  pushButton(actionBtns, BTN_CONF.courseDetail, { url: orderItem.courseDetailUrl })({
    orderPermission,
    orderItem,
    isWeapp,
    orderMark,
  });
  pushButton(actionBtns, BTN_CONF.groupDetail, { url: orderItem.groupDetailUrl })({
    orderPermission,
    orderItem,
    orderExtra,
    isWeapp,
    isQQApp,
    isAlipayApp,
  });
  pushButton(actionBtns, BTN_CONF.gift, { url: orderItem.giftUrl })({ orderItem, isWeapp });
  pushButton(actionBtns, BTN_CONF.expressReminderHotel)({ orderPermission, orderExtra });
  pushButton(actionBtns, BTN_CONF.expressReminder)({ orderPermission, orderExtra });
  pushButton(actionBtns, BTN_CONF.laterReceive)({ orderPermission });
  pushButton(actionBtns, BTN_CONF.cancelPayOnDelivery)({ orderItem, orderMark });
  pushButton(actionBtns, BTN_CONF.cancel)({ orderPermission });
  pushButton(actionBtns, BTN_CONF.transport, { url: orderItem.expressUrl })({
    orderPermission,
    orderItem,
  });
  pushButton(actionBtns, BTN_CONF.peerpay, { url: orderItem.peerpayUrl })({ orderItem, isWeapp });
  pushButton(actionBtns, BTN_CONF.inviteHelp)({ orderPermission });
  pushButton(actionBtns, BTN_CONF.topayRetainageDisabled)({ orderPermission });
  pushButton(actionBtns, BTN_CONF.topayRetainage)({ orderPermission });
  pushButton(actionBtns, BTN_CONF.topayDeposit)({ orderPermission });
  pushButton(actionBtns, BTN_CONF.topay, { url: orderItem.confirmUrl })({
    orderPermission,
    orderItem,
  });
  pushButton(actionBtns, BTN_CONF.confirmReceiveHotel)({ orderPermission, orderExtra });
  pushButton(actionBtns, BTN_CONF.grouponDetail)({ orderPermission, isWeapp });
  pushButton(actionBtns, BTN_CONF.confirmReceive)({ orderPermission, orderExtra });
  pushButton(actionBtns, BTN_CONF.evaluate, { url: orderItem.evaluateUrl })({
    orderPermission,
    orderItem,
  });
  pushButton(actionBtns, BTN_CONF.viewEvaluate, { url: orderItem.viewEvaluateUrl })({
    orderPermission,
    orderItem,
    isWeapp,
  });
  if (!isXhsApp && !isKsApp) {
    // NOTE: 这两个参数需要node端感知到
    const { kdtId, orderNo, paidPromotion = {} } = listOrder;
    const { paidPromotionType = '' } = paidPromotion;
    let hasNewPaidPromotion = false;
    if (canUseTradeUmpV1) {
      hasNewPaidPromotion = !!paidPromotionType;
    }
    if (hasNewPaidPromotion) {
      const { paidPromotionValue } = paidPromotion;
      const type = `${paidPromotionType}${
        paidPromotionType === 'liveQrCode' ? paidPromotionValue.qrCodeType : ''
      }` as keyof typeof BTN_CONF;
      pushButton(actionBtns, BTN_CONF[type], {
        url: `/wscump/paid-promotion/fetch?kdtId=${kdtId}&orderNo=${orderNo}&source=order_list`,
      });
    } else if (orderExtra.paidPromotion && !orderExtra.hasRefund) {
      pushButton(actionBtns, BTN_CONF.paidPromotion, {
        url: `/wscump/paid-promotion/fetch?kdtId=${kdtId}&orderNo=${orderNo}&source=order_list`,
      });
    }
  }
  pushButton(actionBtns, BTN_CONF.fissionCoupon, { url: orderItem?.fissionCouponUrl })({
    orderPermission,
  });
  pushButton(actionBtns, BTN_CONF.hotelComboUse)({ orderPermission, isWeapp });

  if (isRetailShop) {
    const retailBtns = [
      BTN_CONF.confirmReceiveHotel,
      BTN_CONF.buyAgain,
      { ...BTN_CONF.directBuyAgain, showCoupon: false },
    ];
    // 零售店铺, 确认按钮显示在最右侧
    const [btns, matched] = actionBtns.reduce(
      (prev, btn) => {
        const [btns, matched] = prev;
        const matchedRetailBtn = retailBtns.find((item) => item.value /* 按钮类型 */ === btn.value);
        if (!matchedRetailBtn) {
          btns.push(btn);
        } else {
          matched.push(matchedRetailBtn);
        }
        return [btns, matched];
      },
      [[] /* 其他按钮 */, [] /* 匹配的按钮 */]
    );
    actionBtns = [...btns, ...matched];
  }

  return actionBtns;
};

const computeTagList = (
  orderFlag: Record<string, any> = {},
  goodsItem: Record<string, any> = {},
  { pointsName, isNewRetailOrderList }: Record<string, any> = {}
) => {
  const TAG_CONF = getTagConfig({ pointsName });
  const tagList = [];
  orderFlag.isEnjoyBuy && tagList.push(TAG_CONF.enjoyBuy);
  orderFlag.isFcode && tagList.push(TAG_CONF.fcode);
  orderFlag.isKnowledgeGift && tagList.push(TAG_CONF.knowledgeGift);
  (orderFlag.isPresaleOrder || orderFlag.isPresale) &&
    goodsItem?.preSale === '1' &&
    tagList.push(TAG_CONF.presale);
  orderFlag.isPeriod && tagList.push(TAG_CONF.period);
  orderFlag.isGroup && tagList.push(TAG_CONF.group);
  // isNewRetailOrderList 为 true 时，不显示自提标签
  orderFlag.isSelfFetch && !isNewRetailOrderList && tagList.push(TAG_CONF.selfFetch);
  orderFlag.isPeerpay && tagList.push(TAG_CONF.peerpay);
  orderFlag.isLotteryGroup && tagList.push(TAG_CONF.lotteryGroup);
  goodsItem.isTimeLimitSeckill && tagList.push(TAG_CONF.limitSeckill); // 限时秒杀
  orderFlag.isGiftOrder && tagList.push(TAG_CONF.giftOrder);

  goodsItem.isPresent && tagList.push(TAG_CONF.present);
  goodsItem.isPlusBuy && tagList.push(TAG_CONF.plusBuy);
  goodsItem.isUseGoodsExchangeCoupon && tagList.push(TAG_CONF.goodsExchange);
  goodsItem.isRecommendGift && tagList.push(TAG_CONF.recommendGift);

  return tagList;
};

type LambdaFormatterOptions = {
  openAppConfig: Record<string, any>;
  pointsName: string;
  isWeapp: boolean;
  isIvr: boolean;
  orderEnterShopPolicy: unknown;
  isNewRetailOrderList: boolean;
};
const processOrderData = (order: any, options: LambdaFormatterOptions) => {
  const {
    openAppConfig,
    pointsName,
    isWeapp,
    isIvr,
    orderEnterShopPolicy,
    isNewRetailOrderList,
  } = options;
  const firstOrderItem = order.orderItems && order.orderItems[0];

  order.orderItems.map((orderItem: { detailUrl: string; items: any }) => {
    if (isIvr) {
      orderItem.detailUrl = args.add(orderItem.detailUrl, {
        from: 'ivr',
      });
    }

    // 初始化商品标签
    (orderItem.items || []).forEach((goods: Record<string, any>) => {
      goods.tagList = computeTagList(firstOrderItem.orderExtra, goods, { pointsName, isNewRetailOrderList });
    });

    return orderItem;
  });

  if (!isWeapp) { /* ifdef web */
    // 非CRM线下门店订单支持点击店铺跳转
    if (order.crmOfflineType !== CRM_OFFLINE_TYPE.OFFLINE) {
      if (isIvr) {
        order.homeUrl = '';
      } else {
        order.homeUrl = buildUrl(
          args.add('/showcase/homepage', {
            kdt_id: order.kdtId,
            ...(orderEnterShopPolicy ? { shopAutoEnter: 1 } : {}),
          }),
          'wap',
          order.kdtId,
          { notReplaceDomain: true }
        );
      }
    }
  }

  if (openAppConfig && openAppConfig.hideShopLink) {
    order.shopName = '';
  }

  return order;
};

type ProcessApiGetOrderListResponseDataOptions = {
  canUseTradeUmpV1: any;
  directBuyAgainBtnConfig: any;
  pointsName: string;
  openAppConfig: Record<string, any>;
  isRetailShop: boolean;
  isXhsApp: boolean;
  isKsApp: boolean;
  isWeapp: boolean;
  isQQApp: boolean;
  isAlipayApp: boolean;
  isIvr: boolean;
  orderEnterShopPolicy: unknown;
  orderMark: string;
  isDrugList: 'true' | 'false';
  withRenderData: boolean;
  isNewRetailOrderList: boolean;
};

const formatStatueStr = function(listItem: IOrderListItem) {
  const {
    status,
    presaleVoucherStatus,
    orderItems: [orderItem] = [],
  } = listItem;
  const hotelOrderType = get(orderItem, 'orderExtra.hotelOrderType', '');
  let message = '';
  if (hotelOrderType === '3') {
    switch (true) {
      case status === 100 && presaleVoucherStatus === 100: // 交易完成 && 已使用
        message = '交易成功';
        break;
      case status === 99:
        message = '已关闭';
        break;
      case status === 60 && presaleVoucherStatus === 20: // 已发货 && 已预约
        message = '已预约';
        break;
      case status === 60 && presaleVoucherStatus === 10: // 已发货 && 预约中
        message = '预约中';
        break;
      case status === 60 && presaleVoucherStatus === 0: // 已发货 && 待使用
        message = '待使用';
        break;
      default:
        message = '买家下单';
        break;
    }
  }

  return message;
}

const formatShowBrandCert = function(listItem: IOrderListItem) {
  const brandCertType = +get(listItem, 'brandCertType', 0);
  // 品牌认证类型 0 初始化状态 1 旗舰店 2专卖店 3直营店 4 专营店 99 认证被驳回
  return [1, 2, 3, 4].includes(brandCertType);
}

/**
 * 判断是否是批发订单
 */
const formatIsWholesaleOrder = function(listItem: IOrderListItem) {
  return get(listItem, 'orderItems[0].orderExtra.isWholesaleOrder', false);
}

const computedOrderActions = (
  data: IOrderListResp,
  options: ProcessApiGetOrderListResponseDataOptions
) => {
  const {
    isRetailShop,
    openAppConfig,
    pointsName,
    canUseTradeUmpV1,
    directBuyAgainBtnConfig,
    isXhsApp,
    isKsApp,
    isWeapp,
    isQQApp,
    isAlipayApp,
    isIvr,
    orderEnterShopPolicy,
    orderMark,
    isDrugList,
    withRenderData: boolean,
    isNewRetailOrderList = false,
  } = options;
  if (data.list) {
    (data as any).list = data.list.map((listItem: Record<string, any>) => {
      listItem = processOrderData(listItem, {
        openAppConfig,
        pointsName,
        isWeapp,
        isIvr,
        orderEnterShopPolicy,
        isNewRetailOrderList,
      });
      listItem.btnList = processOrderActionBtns(listItem, {
        query: {
          canUseTradeUmpV1,
          directBuyAgainBtnConfig,
          orderMark,
        },
        nodeCtx: {
          isXhsApp,
          isKsApp,
          isRetailShop,
          isWeapp,
          isQQApp,
          isAlipayApp,
        },
      });
      if (options.withRenderData) {
        listItem.renderData = computedRenderData(listItem as any, {
          pointsName,
          isDrugList,
        });
      }
      listItem.statueStr = formatStatueStr(listItem as IOrderListItem);
      listItem.showBrandCert = formatShowBrandCert(listItem as IOrderListItem);
      listItem.isWholesaleOrder = formatIsWholesaleOrder(listItem as IOrderListItem);
      return listItem;
    });
  }

  return data;
};

/**
 * 计算直接渲染UI的数据
 * 这里计算出来的数据一定要保证能够被UI直接渲染，否则就失去了意义
 * listItem -> orderItem -> goodsItem
 * isDrugList 是否为医药订单列表
 */
const computedRenderData = (listItem: IOrderListItem, {
  pointsName = '积分',
  isDrugList = 'false',
}) => {
  const renderData = {
    orderItems: [] as any[],
  };
  const orderPermission = get(listItem, 'orderPermission', {});
  renderData.orderItems = listItem.orderItems.map((orderItem) => {
    const orderMark = get(orderItem, 'orderMark', '');
    const orderExtra = get(orderItem, 'orderExtra', {});
    const isHotel = get(orderItem, 'orderExtra.isHotel', false);
    const isCrossBorder = get(orderItem, 'orderExtra.isCrossBorder', false);
    const isPeriod = get(orderItem, 'orderExtra.isPeriod', false);
    const hotelDetail = get(orderItem, 'hotel', {});
    const isHotelPresale = get(orderItem, 'orderExtra.hotelOrderType', Infinity) === 3;
    const originalItems = isHotel ? get(orderItem, 'items', []).slice(0, 1) : get(orderItem, 'items', []);
    const goodsItems = originalItems.map((goodsItem: any) => {
      const isPrescriptionDrugGoods = goodsItem.isPrescriptionDrugGoods || isDrugList === 'true';
      const isNewHotelOrder = get(goodsItem, 'newHotelGoods', '') === '1';

      // titleTag
      const titleTag = {
        ...{ isShow: false },
        ...(isCrossBorder && {
          isShow: true,
          src: cdnImage('/public_files/3a774609c08dc284f27ba5a64be85fa6.png'),
          className: ['goods-card__title-tag', 'haitao'],
          alt: '海淘商品',
        }),
        ...(isPeriod && {
          isShow: true,
          src: cdnImage('/public_files/61954b0fdd8319a9c5722f16ca2e31de.png'),
          className: ['goods-card__title-tag', 'period'],
          alt: '周期购商品',
        }),
        ...(isPrescriptionDrugGoods && {
          isShow: true,
          src: cdnImage('/path/to/cdn/dir/isDrugTag_3x.png'),
          className: ['goods-card__title-tag', 'drug'],
          alt: '处方药',
        }),
      };

      // skuStr
      let skuStr = get(goodsItem, 'skuStr', '');
      const sku = get(goodsItem, 'sku', []);
      if (sku.length > 0) {
        const skuValueList = sku.reduce((prev: string[], cur: any) => {
          return [...prev, cur.v || cur.value];
        }, []);
        const propValueList = get(goodsItem, 'properties', [])
          .reduce((prev: string[], { propValueList = [] }) =>
              prev.concat(propValueList.map(({ propValueName }) => propValueName))
            , []);
        skuStr = [...skuValueList, ...propValueList].join('；');
      }

      // hotelDesc
      let hotelDesc = '';
      const hotelPackageOrderEndTime = get(orderExtra, 'hotelPackageOrderEndTime', '');
      if (isHotelPresale && hotelPackageOrderEndTime) {
        // 新酒店订单：预售、预约
        hotelDesc = `预约有效期至${formatDate(hotelPackageOrderEndTime, 'YYYY年MM月DD日')}`; // TODO: 需要检查下格式化出来的是否正确
      } else if (isHotel) {
        hotelDesc = `${hotelDetail.checkInTime} - ${hotelDetail.checkOutTime} 共${orderItem.items.length}晚/${goodsItem.num}间`;
      }

      // orderExplanation
      const orderExplanation = [
        get(orderItem, 'orderDesc.preSaleDesc.preSaleFinalPaymentDesc', ''), // 定金预售
        get(goodsItem, 'preSaleExpressTimeDesc', ''), // 定金预售发货时间
        get(orderItem, 'orderDesc.effectiveTimeDesc', ''), // 电子卡券失效时间
        get(orderItem, 'orderDesc.groupAgencyReceiveDesc', ''), // 拼团描述
        goodsItem.isSevenDayUnconditionalReturn ? '7天无理由退货' : '', // 7天无理由
      ].filter(item => !!item);

      // statusStr
      let statusStr = '';
      const isShipped = get(goodsItem, 'isShipped', false);
      const feedback = get(goodsItem, 'feedback', Infinity);
      if (isShipped) {
        statusStr = '已发货';
      } else if (feedback === 201/* 退款中 */) {
        statusStr = '退款中';
      }

      const originalTagList = get(goodsItem, 'tagList', []);
      const tagList = [
        originalTagList.filter((tag: any) => !tag.isSub),
        originalTagList.filter((tag: any) => tag.isSub),
      ];

      let itemPrice = '';
      if (isHotel && !isHotelPresale) {
        itemPrice = getTotalPrice(goodsItem.price * (goodsItem.num || 0), goodsItem.pointsPrice * goodsItem.num, pointsName);
      } else {
        itemPrice = getTotalPrice(goodsItem.price || 0, goodsItem.pointsPrice, pointsName);
      }

      return { // TODO: 重新定义一个renderData的type
        image: goodsItem.image,
        // thumb: goodsItem.image, // TODO: 找一下方法看是否能实现node端压缩图片
        title: isNewHotelOrder ? `${hotelDetail.hotelName}` : goodsItem.title,
        subTitle: isNewHotelOrder
          ? `${hotelDetail.roomTypeName} ${hotelDetail.saleProjectName}`
          : '',
        titleTag,
        skuStr,
        hotelDesc,
        orderExplanation,
        isShowItemNum: !isHotel || isHotelPresale, // 是否展示商品数量
        num: goodsItem.num,
        statusStr,
        tagList,
        itemPrice,
      }
    });

    // pickUpCode
    const pickUpCode = get(orderItem, 'orderDesc.pickUpCode', '');
    let pickUpCodeDesc = pickUpCode ? `取货码：${pickUpCode} ` : '';
    if (orderMark !== 'retail_minapp_shelf') {
      pickUpCodeDesc = '';
    }

    // yZGuarantee 有赞放心购
    const LIFE_CYCLE_STATE = {
      INIT: '保障待生效',
      USING: '保障生效中',
      DISCARD: '保障未生效',
      INVALID: '保障已结束',
      EXPIRED: '保障已结束',
      PREPAID: '有赞放心购已优先垫付',
      PRE_EXPIRED: '保障已结束',
      PRE_DISCARD: '保障未生效',
      UNSECURED: 'UNSECURED',
    } as const;
    const isShowYzGuarantee = Boolean(
      get(orderItem, 'financeServiceVO.yzSecuredServiceVO.isSecured', null) ??
      get(orderItem, 'orderExtra.isYZGuarantee', false)
    );
    const yzGuaranteeStatus = get(
      orderItem,
      'financeServiceVO.yzSecuredServiceVO.securedLifeCycleStatus',
      'USING'
    ) as keyof typeof LIFE_CYCLE_STATE;
    const yzGuarantee = {
      isShow: isShowYzGuarantee,
      statusDesc: LIFE_CYCLE_STATE[yzGuaranteeStatus],
      textDesc: '商家赠送',
      icon: cdnImage('/security/fangxin-green-small.png'),
      iconAlt: '有赞放心购',
    }

    // orderFinalPrice 订单金额
    const _promotionPrice = get(orderItem, 'promotionPrice', 0);
    const realPointPay = get(orderItem, 'realPointPay', '');
    const payInfo = get(orderItem, 'payInfo', {});
    const pointsPrice = realPointPay ? `${realPointPay}${pointsName}` : '';
    const orderFinalPrice = {
      isShow: (orderPermission as any).isShowTotalPrice,
      isShowPlusIcon: Boolean(pointsPrice && payInfo.payAmount),
      isShowPriceComponent: Boolean(payInfo.payAmount || !pointsPrice),
      promotionPriceDesc: _promotionPrice > 0 ? `优惠 ¥${formatMoney(_promotionPrice)}` : '',
      payInfo,
      pointsPrice,
      realPointPay,
      pointsName,
    }

    return {
      orderNo: listItem.orderNo,
      goodsItems,
      arriveTimeDesc: get(orderItem, 'orderDesc.parcelArriveTimeDesc', ''),
      pickUpCodeDesc,
      yzGuarantee,
      orderFinalPrice,
    };
  });
  return renderData;
}

const getTotalPrice = (price: number, points: number, pointsName = '积分') => {
  const arr = [];
  if (points) {
    arr.push(points + pointsName);
  }
  if (price || !points) {
    arr.push('¥' + (price / 100).toFixed(2));
  }
  return arr.join(' + ');
};

export {
  computedOrderActions,
  DEFAULT_POINTS_NAME,
}