/**
 * 处理多种运费场景的文案
 */

// 运费支付模式类型
export const EXPRESS_PAY_MODE_MAPS = {
  NORMAL: 'NORMAL', // 自费
  TOPAY: 'TOPAY', // 到付
  MIXSURE: 'MIXSURE', // 到付&自费
};

// 快递配送方式不同情况下，运费展示文案区分
const postageFormatMaps = {
  [EXPRESS_PAY_MODE_MAPS.NORMAL](postage) {
    return postage === 0 ? ' 免运费' : ' 运费 ¥' + (postage / 100).toFixed(2);
  },
  // 商品全部为运费到付，直接返回“运费到付”
  [EXPRESS_PAY_MODE_MAPS.TOPAY]() {
    return ' 运费到付';
  },
  // 商品部分为运费到付，根据常规文案后增加“(不含运费到付)”
  [EXPRESS_PAY_MODE_MAPS.MIXSURE](postage) {
    const normalText = postageFormatMaps[EXPRESS_PAY_MODE_MAPS.NORMAL](postage);
    return `${normalText}（不含运费到付）`;
  },
};
// 获取运费支付类型 1 运费到付， 2 部分运费到付， 0 普通
export function getExpressPayType(expressPayMode) {
  switch (expressPayMode) {
    case 1: {
      return EXPRESS_PAY_MODE_MAPS.TOPAY;
    }
    case 2: {
      return EXPRESS_PAY_MODE_MAPS.MIXSURE;
    }
  }
  return EXPRESS_PAY_MODE_MAPS.NORMAL;
}
// 获取格式化后的配送方式的运费
export function formatDeliveryPostage(postage, expressPayMode) {
  const type = getExpressPayType(expressPayMode);
  // 如果不存在sportFreightType，则默认常规渲染
  return (postageFormatMaps[type] || postageFormatMaps[EXPRESS_PAY_MODE_MAPS.NORMAL])(postage);
}

// 获取运费价格详情
const formatPriceDetail = (price = 0, operator) => {
  const symb = operator ? `${operator} ¥` : '¥';
  return `${symb}${(Math.abs(price) / 100).toFixed(2)}`;
};

// 获取运费标题 - 价格区域展示
export function getPostageLabel(expressPayMode) {
  const type = getExpressPayType(expressPayMode);
  if (type === EXPRESS_PAY_MODE_MAPS.MIXSURE) {
    return '运费（不含运费到付）';
  }
  return '运费';
}
// 获取运费的值 - 价格区域展示
export function getPostageValue(expressPayMode, postage) {
  const type = getExpressPayType(expressPayMode);
  if (type === EXPRESS_PAY_MODE_MAPS.TOPAY) {
    return '运费到付';
  }
  return formatPriceDetail(postage, '+');
}

