const BaseController = require('../base/BaseController');
const NewHopeOrderDetailService = require('../../services/newhope/NewHopeOrderDetailService');
const mapKeysToSnakeCase = require('@youzan/utils/string/mapKeysToSnakeCase');
const PageException = require('@youzan/iron-base/app/exceptions/PageException');

class NewHopeOrderDetailController extends BaseController {
  async orderDetailAcl() {
    await this.acl({
      allowNotLogin: false,
      useAjaxLogin: false,
      forceOauthLogin: false,
      kdtId: this.ctx.kdtId
    });
  }

  async getIndexHtml(ctx) {
    await this.orderDetailAcl();
    const newHopeOrderDetailService = this._getNewHopeOrderDetailService(ctx);
    const orderNo = ctx.getQueryData('order_no');
    try {
      const orderDetail = await newHopeOrderDetailService.orderDetail(orderNo);
      ctx.setGlobal('orderDetail', mapKeysToSnakeCase(orderDetail));
    } catch (e) {
      console.error(e);
      const content = e.errorContent || {};
      throw new PageException(content.code || 10500, content.msg || '订单数据异常');
    }
    await ctx.render('newhope/detail.html');
  }

  _getNewHopeOrderDetailService(ctx) {
    if (!this.newHopeOrderDetailService) {
      this.newHopeOrderDetailService = new NewHopeOrderDetailService(ctx);
    }
    return this.newHopeOrderDetailService;
  }

  async getOrderListJson(ctx) {
    const newHopeOrderDetailService = this._getNewHopeOrderDetailService(ctx);
    const queryData = ctx.getQueryData();
    if (!queryData.user_id) {
      ctx.r(-1, 'user_id不能为空');
      return;
    }
    if (!queryData.kdt_id) {
      ctx.r(-1, 'kdt_id不能为空');
      return;
    }
    try {
      const orderList = await newHopeOrderDetailService.orderList(queryData);
      ctx.r(0, 'success', mapKeysToSnakeCase(orderList));
    } catch (e) {
      const {errorContent} = e;
      if (!errorContent) {
        console.error('createOrderList unexpected error:', e);
        throw e;
      } else {
        console.error(e.message, e);
        ctx.r(errorContent.code, errorContent.msg);
      }
    }
  }
}

module.exports = NewHopeOrderDetailController;
