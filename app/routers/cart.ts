export = [
  [
    'GET',
    ['/wsctrade/cart', '/wsctrade/cart/index'],
    'cart.CartController',
    ['checkVipDomain', 'initAcl', 'checkRedirectToShopDomain', 'getIndexHtml'],
  ],
  [
    'GET',
    ['/wsctrade/cart/goodsList.json'],
    'cart.CartController',
    'getGoodsListJson',
  ],
  [
    'GET',
    ['/wsctrade/cartGoodstList.json'],
    'cart.CartController',
    'getCartGoodsListJson',
  ],
  [
    'GET',
    ['/wsctrade/cartQueryShopIsDrug.json'],
    'cart.CartController',
    'queryShopIsDrugJson',
  ],
  [
    'POST',
    ['/wsctrade/cart/deleteBatchList.json'],
    'cart.CartController',
    'postDeleteBatchListJson',
  ],
  [
    'POST',
    ['/wsctrade/cart/deleteGoods.json'],
    'cart.CartController',
    'postDeleteGoodsJson',
  ],
  [
    'POST',
    ['/wsctrade/cart/updateCartGoodsNum.json'],
    'cart.CartController',
    'postUpdateCartGoodsNumJson',
  ],
  [
    'POST',
    ['/wsctrade/cart/selectGoods.json'],
    'cart.CartController',
    'postSelectGoodsJson',
  ],
  [
    'POST',
    ['/wsctrade/cart/unselectGoods.json'],
    'cart.CartController',
    'postUnselectGoodsJson',
  ],
  [
    'POST',
    ['/wsctrade/cart/batchSelectGoods.json'],
    'cart.CartController',
    'postBatchSelectGoodsJson',
  ],
  [
    'POST',
    ['/wsctrade/cart/batchUnselectGoods.json'],
    'cart.CartController',
    'postBatchUnselectGoodsJson',
  ],
  [
    'POST',
    ['/wsctrade/cart/selectAllGoods.json'],
    'cart.CartController',
    'postSelectAllGoodsJson',
  ],
  [
    'POST',
    ['/wsctrade/cart/unselectAllGoods.json'],
    'cart.CartController',
    'postUnselectAllGoodsJson',
  ],
  [
    'POST',
    ['/wsctrade/cart/batchAddGoods.json'],
    'cart.CartController',
    'postBatchAddGoodsJson',
  ],
  [
    'GET',
    ['/wsctrade/cart/countGoodsNums.json'],
    'cart.CartController',
    'getCountGoodsNums',
  ],
  [
    'GET',
    ['/wsctrade/cart/selectedGoods.json'],
    'cart.CartController',
    'getSelectedGoodsJson',
  ],
  [
    'GET',
    ['/wsctrade/cart/find-exchange-goods.json'],
    'cart.CartController',
    'findExchangeGoods',
  ],
  [
    'POST',
    ['/wsctrade/cart/reselect-goods.json'],
    'cart.CartController',
    'reselectGoods',
  ],
  [
    'POST',
    ['/wsctrade/cart/reselect-goods.json'],
    'cart.CartController',
    'reselectGoods',
  ],
  [
    'POST',
    ['/wsctrade/cart/getMultiRecommendGoods.json'],
    'cart.CartController',
    'getMultiRecommendGoods',
  ],
  [
    'GET',
    ['/wsctrade/cart/getWeappCartShuntConfig.json'],
    'cart.CartController',
    'getWeappCartShuntConfig',
  ],
  [
    'GET',
    ['/wsctrade/cart/getWeappCartShuntConfigByCustomer.json'],
    'cart.CartController',
    'getWeappCartShuntConfigByCustomer',
  ],
  [
    'POST',
    ['/wsctrade/cart/getVoucher.json'],
    'cart.CartController',
    'getVoucher',
  ],
  [
    'GET',
    ['/wsctrade/cart/getCouponAddOnInfo.json'],
    'cart.CartController',
    'getCouponAddOnInfo',
  ],
  [
    'POST',
    ['/wsctrade/cart/reselectGoodsActivity.json'],
    'cart.CartController',
    'reselectGoodsActivity',
  ],
  // 获取购物车异步数据
  [
    'POST',
    '/wsctrade/cart/cart-async-data.json',
    'cart.CartController',
    'getCartAsyncData',
  ],
];
