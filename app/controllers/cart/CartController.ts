import _ from 'lodash';
import { Context } from 'astroboy';
import Args from '@youzan/utils/url/args';
import { PageException } from '@youzan/iron-base';
import mapKeysToSnakeCase from '@youzan/utils/string/mapKeysToSnakeCase';
import mapKeysToCamelCase from '@youzan/utils/string/mapKeysToCamelCase';
import BaseController from '../base/BaseController';
import CartService from '../../services/cart/CartService';
import NewCartService from '../../services/cart/NewCartService';
import ExtensionPointService from '../../services/cloud/ExtensionPointService';
import ShopAbilityInfoService from '../../services/shop/ShopAbilityInfoService';
import ShopConfigurationService from '../../services/shop/ShopConfigurationService';
import ShopConfigReadService from '../../services/shop/ShopConfigReadService';
import { FRESH_FOOD } from '../../constants/business';
import buildUrlWithCtx from '@youzan/utils/url/buildUrlWithCtx';
import { enterShopForH5 } from '@youzan/plugin-h5-enter-shop';
import CompareVersion from '../../lib/CompareVersion';
import { IVoucherSendRequest } from '../../services/order/VoucherSendService';
import {
  IGetListGoodsReqQuery,
  ICartBuyer,
  IPostBatchAddGoodsBodyItem,
  ICartListItem,
  IGoodsGroup,
  IGood,
  ComboItem,
  SkuItem,
  Propertie,
} from 'definitions/cart/Cart';
import { isNewhopeKdtShop } from '../../constants/NewHopeShopWhiteListUtil';
import { umpActivityEnum } from '../../constants/umpActivity.enum';
import { isInGrayReleaseByKdtId, isInGrayReleaseByKdtIdForGoodsApollo } from '../../lib/WhiteListUtils';
import { isCloudDesign } from '@youzan/plugin-h5-ecloud';
import { initRanta } from '@youzan/plugin-h5-ranta-config';
import { getBrandShopStyleToState } from '@youzan/plugin-h5-global-theme';
import buildUrl from "@youzan/utils/url/buildUrl";
import args from "@youzan/utils/url/args";
import ExceptionHandler from '../../lib/ExceptionHandler';
import DeliveryService from '../../services/trade/DeliveryService';
import GoodsYzGuaranteeService from '../../services/goods/GoodsYzGuaranteeService';
import { IGuaranteeParams } from 'definitions/other/GoodsYzGuarantee';
import {
  IAddressItem,
  ICartAsyncRequestBody,
  IItemInfoParam,
  ILocation,
  IShippingAddress,
} from '../../../definitions/cart/NewCart';

/* eslint-disable @typescript-eslint/ban-ts-comment, camelcase */
/**
 * 购物车需要登录白名单key
 */
const CART_NEED_LOGIN = 'cart_need_login';

/**
 * 是否查询有赞担保扩展字段 NEED_YZ_GUARANTEE 的值， 允许查询为1，不允许为0
 */
const NEED_YZ_GUARANTEE_MAP = {
  disable: '0',
  enable: '1',
};

class CartController extends BaseController {
  exceptionHandler = new ExceptionHandler();

  async init(): Promise<void> {
    const { ctx } = this;
    const hasKdtId = !!this.ctx.kdtId;
    const initOptions = {
      initGlobal: true,
      validKdtId: false,
      initMpData: false,
      initMpAccount: false,
      initPlatform: true,
      initGlobalTheme: false,
      initShopMetaInfo: true,
      initShopSettings: false,
      initOpenAppConfig: true,
      initCopyrightFooter: !this.ctx.acceptJSON && this.ctx.query.cart_type !== 'all',
    };

    if (hasKdtId) {
      Object.assign(initOptions, {
        validKdtId: true,
        initMpData: true,
        initMpAccount: true,
        initShopSettings: true,
        initGlobalTheme: true,
      });
    }

    await super.init(initOptions);
    // 接口请求不做多网点检测
    if (this.ctx.acceptJSON) {
      return;
    }
    // 精选没有kdtId
    if (hasKdtId) {
      // @ts-ignore
      const routeInfo = await enterShopForH5(ctx);
      // 返回空，说明已经进店，继续走其他业务流程
      if (routeInfo && routeInfo.routeKdtId) {
        /**
         * 返回routeKdtId，说明需要更新kdtId后重定向，重新初始化数据
         * enterShopForH5 进店方法的kdtId取值逻辑为 ctx.query.sub_kdt_id || ctx.kdtId, 因此需要过滤原url上的kdtId信息。
         * shopAutoEnter 用于判断是否需要执行进店逻辑，已经获取进店结果后可删除该参数
         * 商品详情页面等通过alias解析kdtId信息的，需要更新alias
         */
        const subKdtId = routeInfo.routeKdtId;
        const prevQuery = _.omit(ctx.query, ['kdt_id', 'sub_kdt_id']);
        const redirectQuery = {
          ...prevQuery,
          kdt_id: subKdtId,
        };
        const subShopUrl = buildUrlWithCtx(this.ctx)('/wsctrade/cart', 'h5', subKdtId);

        return ctx.redirect(Args.add(subShopUrl, redirectQuery));
      }
    }
  }

  async initAcl(ctx: Context): Promise<void> {
    const { kdtId, isYouzanmars } = ctx;
    const whitelistService = this.getService('iron-base', 'common.WhitelistService');
    if (!kdtId || isYouzanmars) {
      return;
    }
    const needPlatformLogin = await whitelistService.exists(CART_NEED_LOGIN, kdtId).catch(() => false);
    if (needPlatformLogin) {
      await this.teeRantaAcl();
    }
  }

  async getIndexHtml(ctx: Context): Promise<void> {
    const { kdtId, platform, nobody, isSwanApp, isQQApp, isAlipayApp } = ctx;
    const { kdt_id, cart_type: cartType, source, version, hide_skeleton = false, pdlive = '' } = ctx.getQueryData();
    if (kdt_id && kdtId) {
      ctx.kdtId = kdtId; // 此操作会写cookie，支持query.kdt_id重写入cookie
    }

    // 是否隐藏骨架屏，默认是展示骨架屏
    const hideSkeleton = Boolean(hide_skeleton);

    // 爱逛存在跨店仍使用老购物车
    let isUseCartV2 = !this.isGuang && cartType !== 'all';
    // 判断kdtid是否存在，否则爱逛报错
    if (cartType !== 'all' && ctx.kdtId) {
      // 多网点
      await this.initMultiStore();
    }

    const { buyerId } = this;

    const params = this.getListGoodsQuery(ctx);

    // 支持组合商品 这里query拿不到参数，直接加上
    params.supportCombo = true;
    params.extension = { NEED_YZ_GUARANTEE: '0', excludedComboSubType: JSON.stringify([]) };

    this.setPageCloudBizIds('cart');

    // @ts-ignore
    const shopLogo = _.get(ctx.getState('shopSettings'), 'customizedLogo', '');
    // @ts-ignore
    const mpData = ctx.getState('mpData') || {};

    // 生鲜果蔬店铺才加载导航
    if (
      // @ts-ignore
      mpData.business &&
      // @ts-ignore
      +mpData.business === FRESH_FOOD &&
      !isSwanApp &&
      !isQQApp &&
      !isAlipayApp
    ) {
      ctx.setGlobal('loadCartNav', true);
    }

    let response;
    // 购物车列表数据
    try {
      const cartService = new CartService(ctx);
      const grayReleaseService = this.getService(
        'iron-base',
        // @ts-ignore common.GrayReleaseService没在定义白名单内，先忽略
        'common.GrayReleaseService'
      );

      // 登陆合并匿名添加的购物车商品
      if (buyerId) {
        await cartService.mergeCartGoodsService({
          accountContext: this.getCartIdRequest(ctx),
          platFormType: this.platFormType,
        });
      }
      const shopConfiguration = new ShopConfigurationService(ctx);
      const cartRequestList = [
        cartService.getCartList(params),
        ctx.isThirdApp(),
        new ExtensionPointService(ctx).getDesignConfig('cart'),
        grayReleaseService
          // @ts-ignore
          .isInGrayReleaseByKdtId('wsc_cart_new_book', kdtId)
          .catch(() => false),
        new ShopAbilityInfoService(ctx) // 调用店铺能力判断是否是医药店铺
          .queryShopAbilityInfo([kdtId, 'prescription_ability'])
          .catch(() => {
            return {};
          }),
        shopConfiguration.queryShopConfig('goods_expect_price_show'),
        isInGrayReleaseByKdtIdForGoodsApollo(
          ctx,
          { namespace: 'wsc-h5-goods.goods-release', key: 'goods_estimated_price' },
          ctx.kdtId
        ),
      ];

      // 非爱逛购物车，获取购物车自定义名称
      if (cartType !== 'all' && kdtId) {
        cartRequestList.push(cartService.customCartName(+kdtId));
      }

      response = await Promise.all(cartRequestList);
    } catch (e) {
      if (this.exceptionHandler.handle(e, ctx)) return;
      const content = e.errorContent;
      if (content) {
        throw new PageException(content.code, content.msg);
      } else {
        throw e;
      }
    }

    this.setExtensionParams(ctx, 'cart');

    const [
      cartList,
      isThirdApp,
      [customized],
      isUseNewBoookJson,
      drugShopInfo,
      goodsExpectPriceShowValue,
      isWhite,
      customCartName = {},
    ] = response;

    if (cartList && cartList[0]) {
      cartList[0].goodsGroupList = this.modifyGoodsGroupList(cartList);
      // 处理一下套餐商品
      cartList[0].goodsGroupList = this.formatCartComboData(cartList);
      // 白名单并且店铺开启显示
      cartList[0].isShowEstimatedPrice = isWhite && !!Number(goodsExpectPriceShowValue);
    }

    // 如果已经是v2 && 非定制 那么apollo也是v2才行
    isUseCartV2 = ((isUseCartV2 && !customized) || version === '2') && version !== '1';

    this.escapeCartList(cartList);

    // 处理满减送和实付赠叠加活动的情况
    this.handleActivityList(cartList);

    const { valid } = drugShopInfo || {};

    let global = {
      source,
      isDrugShop: valid, // 是否医药店铺
      isThirdApp,
      kdtId,
      platform,
      buyerId,
      nobody,
      shopLogo,
      cartList,
      customCartName,
      isNewhopeKdt: isNewhopeKdtShop(kdtId), // 新希望店铺
      // @ts-ignore
      openAppConfig: _.pick(ctx.getState('openAppConfig'), 'hideStroll'),
      // @ts-ignore
      theme: ctx.getState('globalTheme'),
      pdlive, // 私域直播间ID标
    };

    // 新购物车使用驼峰，不转换
    if (!isUseCartV2) {
      global = mapKeysToSnakeCase(global);
    }

    ctx.setGlobal(global);
    ctx.setGlobal({ isUseNewBoookJson });

    // 日志埋点模型
    this.setSpm('cart', kdtId);

    const isUseRantaPage = await this.isUseRantaPage();

    if (isUseRantaPage) {
      // 中台化页面中部分逻辑依赖账号全局数据，仅设置中台化账号数据，不执行 ACL
      // https://qima.feishu.cn/wiki/Jskvwt8UwiOdZPkccwicdJJXnlf#LfwndMGkjorE11xFqJgcsKvHn73
      await this.rantaAcl({ onlySetGlobal: true });
      await initRanta(ctx as any, {
        framework: 'tee',
        appName: 'wsc-tee-h5',
        bizName: '@wsc-tee-h5-trade/cart',
        ecloud: {
          setExtensionParams: [
            {
              kdtId: +ctx.kdtId,
              pageName: 'cart',
              pageNameV2: 'cart',
              conditionContext: this.getConditionContext(ctx),
            },
          ],
        },
      });
      await getBrandShopStyleToState(ctx as any, { useAppStyleIcon: true });
    }

    await ctx.render(
      !isUseRantaPage ? 'cart/cart.html' : 'cart/tee-cart.html',
      {
        pageName: isUseCartV2 ? 'cart/v2' : 'cart/v1',
        hideSkeleton,
      },
      {
        appName: 'wsc-tee-h5',
        skipLoadOpsWebConfig: true,
      }
    );
  }

  // 获取当前店铺是不是医药店铺
  async queryShopIsDrugJson(ctx: Context): Promise<void> {
    const { kdtId } = ctx;
    const result = await new ShopAbilityInfoService(ctx) // 调用店铺能力判断是否是医药店铺
      .queryShopAbilityInfo([kdtId, 'prescription_ability'])
      .catch(() => {
        return {};
      });
    ctx.r(0, 'success', result);
  }

  // 获取小程序购物车切流结果
  async getWeappCartShuntConfig(ctx: Context): Promise<void> {
    let useNewCart = false;

    try {
      useNewCart = await isInGrayReleaseByKdtId(
        ctx,
        { namespace: 'wsc-h5-trade.application', key: 'useWeappRantaTeeCart' },
        ctx.kdtId
      );
    } catch (err) {}

    ctx.r(0, 'success', { useNewCart });
  }

  /**
   * 获取大客定制跳转的购物车版本
   * @param ctx
   */
  async getWeappCartShuntConfigByCustomer(ctx: Context): Promise<void> {
    let useNewCart = false;

    try {
      useNewCart = await isInGrayReleaseByKdtId(
        ctx,
        { namespace: 'wsc-h5-trade.gray-release', key: 'useWeappRantaTeeCartByCustomer' },
        ctx.kdtId
      );
    } catch (err) {}

    ctx.r(0, 'success', { useNewCart });
  }

  // 获取购物车请求所需的参数，两处需要用到
  getListGoodsQuery(ctx: Context): IGetListGoodsReqQuery {
    const query = mapKeysToCamelCase(ctx.getQueryData());
    const {
      cartType,
      channelId = 0,
      supportReviveGroup,
      groupId,
      supportCombo,
      excludedComboSubType,
      disableSearchYzGuarantee,
      selectedPromotions,
      isXhsLocalLife,
      pdlive,
    } = query;
    const params: IGetListGoodsReqQuery = {
      buyer: this.getCartIdRequest(ctx),
      channelId,
      supportReviveGroup: true,
      searchParams: { groupId },
    };

    // 如果是微信小程序
    if (ctx.isWeapp) {
      params.source = {
        orderMark: 'weapp',
      };
    }

    // 如果是抖音小程序
    if (ctx.isTTApp) {
      params.source = {
        channelPlatForm: 'dy_mini_program',
      };
    }

    // 如果是微信h5
    if (!ctx.isMiniProgram && ctx.pageType === 'h5') {
      params.source = {
        orderMark: 'weixin',
      };
    }

    // 如果是小红书
    if (ctx.isXhsApp) {
      params.channelId = 100;
    }

    // 如果是快手小程序
    if (ctx.isKsApp) {
      params.channelId = 110;
    }

    // 如果是爱逛
    if (this.isGuang) {
      params.channelId = 40;
    }

    const { isYouzanmars, kdtId, isWeappNative } = ctx;

    // 老版本小程序使用旧分组
    if (isWeappNative && !this.isGuang && supportReviveGroup === undefined) {
      params.supportReviveGroup = false;
    }

    // 跨店购物车处理
    if (cartType === 'all' || isYouzanmars) {
      params.isYouzanApp = true;
    } else {
      params.kdtId = kdtId;
      params.storeId = ctx.offlineId;
      params.isYouzanApp = false;
    }

    // 说明是支付宝中的小程序
    if (ctx.isAlipayApp) {
      params.source = {
        orderMark: 'alipay_mini_program',
        platform: 'alipay',
      };
    }
    // 说明是QQ小程序
    if (ctx.isQQApp) {
      params.source = {
        orderMark: 'qq_mini_program',
        platform: 'qq',
      };
    }
    // 私域直播购物车
    if (pdlive) {
      params.source = {
        orderMark: 'pl_live',
      };
    }
    // 有赞担保标识增加
    params.extension = {
      NEED_YZ_GUARANTEE: disableSearchYzGuarantee ? NEED_YZ_GUARANTEE_MAP.disable : NEED_YZ_GUARANTEE_MAP.enable,
    };
    // 网店支持商品套餐，需要多传两个字段
    params.supportCombo = supportCombo;
    if (excludedComboSubType) {
      params.extension.excludedComboSubType = excludedComboSubType;
    }
    if (ctx.isXhsApp && (isXhsLocalLife === 'true' || ctx.xExtraData.bizEnv === 'xhsLocalLife')) {
      params.source = {
        ...params.source,
        extendParams: {
          MULTI_PLAT_OUT_CHANNEL: 'XHS_MINIAPP_LOCAL_LIFE',
        }
      };
    }
    if (selectedPromotions && selectedPromotions !== 'undefined') {
      try {
        const selectedPromotion = JSON.parse(selectedPromotions);
        params.selectedPromotions = [selectedPromotion];
      } catch (error) {
        this.ctx.logger.warn('购物车指定营销活动参数解析失败', error as any);
      }
    }
    return params;
  }

  // 获取购物车商品列表，驼峰版
  async getGoodsListJson(ctx: Context): Promise<void> {
    const cartService = new CartService(ctx);
    const params = this.getListGoodsQuery(ctx);
    const cartList = await cartService.getCartList(params);
    ctx.json(0, 'success', cartList);
  }

  async checkGuarantee() {
    // @ts-ignore
    const { apolloClient } = global.getRuntime();
    const blackGoldList = await apolloClient.getConfig({
      appId: 'yz-fin-aigis',
      namespace: 'yz-fin-aigis.black-gold-shop-list',
      key: 'yzdb.shield',
    });
    return blackGoldList;
  }

  // 获取购物车商品列表，下划线版
  async getCartGoodsListJson(ctx: Context): Promise<void> {
    const cartService = new CartService(ctx);
    const shopConfiguration = new ShopConfigurationService(ctx);
    const params = this.getListGoodsQuery(ctx);

    const [cartList, isNewHopeShop = false, goodsExpectPriceShowValue, isWhite, isGoodsImgCover, pointsName] = await Promise.all([
      cartService.getCartList(params).catch((e) => {
        if (e.code === 429) throw e;
      }),
      isInGrayReleaseByKdtId(ctx, { namespace: 'wsc-h5-trade.application', key: 'newHopeShop' }, ctx.kdtId),
      shopConfiguration.queryShopConfig('goods_expect_price_show'),
      isInGrayReleaseByKdtIdForGoodsApollo(
        ctx,
        { namespace: 'wsc-h5-goods.goods-release', key: 'goods_estimated_price' },
        ctx.kdtId
      ),
      isInGrayReleaseByKdtIdForGoodsApollo(
        ctx,
        { namespace: 'wsc-h5-goods.goods-release', key: 'goods-img-cover' },
        ctx.kdtId
      ),
      new ShopConfigReadService(ctx).queryShopPointsName(ctx.kdtId),
    ]);

    if (cartList && cartList[0]) {
      cartList[0].isNewHopeShop = isNewHopeShop;
      // 购物车商品图片是否填充
      cartList[0].isGoodsImgCover = isGoodsImgCover;
      const list = this.modifyGoodsGroupList(cartList);
      const blackGoldList = await this.checkGuarantee();
      cartList[0].goodsGroupList = list?.map((res: any) => {
        return {
          ...res,
          goods_list: res?.goodsList?.map((g: any) => {
            return {
              ...g,
              hideGuarantee: blackGoldList.includes(g.kdtId.toString()),
            };
          }),
        };
      });
      // 套餐商品处理一下
      cartList[0].goodsGroupList = this.formatCartComboData(cartList);
      // 白名单并且店铺开启显示
      cartList[0].isShowEstimatedPrice = isWhite && !!Number(goodsExpectPriceShowValue);
      cartList[0].pointsName = pointsName;
    }

    // 处理满减送和实付赠叠加活动的情况
    this.handleActivityList(cartList);

    ctx.r(0, 'success', cartList);
  }

  // 批量删除商品
  async postDeleteBatchListJson(ctx: Context): Promise<void> {
    const cartService = new CartService(ctx);
    const goodsList = ctx.getPostData('ids');
    if (!goodsList) {
      ctx.r(12100, '请选择要删除的商品');
      return;
    }
    const params = {
      items: mapKeysToCamelCase(goodsList),
      platform: ctx.platform,
      buyer: this.getCartIdRequest(ctx),
      mode: 'manual',
    };
    const flag = await cartService.deleteGoodsBatchOrSingle(params);
    if (flag === true) {
      ctx.r(0, '删除成功', flag);
    } else {
      ctx.r(flag.code || 500, '删除失败', flag);
    }
  }

  // 单个删除商品
  async postDeleteGoodsJson(ctx: Context): Promise<void> {
    const cartService = new CartService(ctx);
    const requestBody = mapKeysToCamelCase(ctx.getPostData());
    if (!requestBody.goodsId) {
      ctx.r(12100, '请选择要删除的商品');
      return;
    }
    const params = {
      items: [requestBody],
      mode: 'manual',
      buyer: this.getCartIdRequest(ctx),
    };
    const flag = await cartService.deleteGoodsBatchOrSingle(params);
    if (flag === true) {
      ctx.r(0, '删除成功', flag);
    } else {
      ctx.r(flag.code || 500, '删除失败', flag);
    }
  }

  // 获取购物车用户信息
  getCartIdRequest(ctx: Context): ICartBuyer {
    // bind_old_user_id 不在名单中，暂时忽略
    const oldUserId = this.ctx.getLocalSession('bind_old_user_id');
    return CartService.getCartIdRequestData(ctx, this.buyer.buyerId, oldUserId, this.isGuang);
  }

  // 更新购物车商品数量
  async postUpdateCartGoodsNumJson(ctx: Context): Promise<void> {
    const cartService = new CartService(ctx);
    const cartGoods = mapKeysToCamelCase(ctx.getPostData());
    const params = {
      item: cartGoods,
      buyer: this.getCartIdRequest(ctx),
    };
    const flag = (await cartService.updateCartGoodsNum(params)) || {};
    if (flag === true) {
      ctx.r(0, '操作成功', flag);
    } else {
      ctx.r(flag.code || 500, '操作失败', flag);
    }
  }

  // 单个勾选商品记录
  async postSelectGoodsJson(ctx: Context): Promise<void> {
    const cartService = new CartService(ctx);
    const requestBody = ctx.getPostData();
    const params = {
      accountContext: this.getCartIdRequest(ctx),
      cartGoodsList: [
        {
          ...requestBody,
        },
      ],
      platFormType: this.platFormType,
    };
    const flag = await cartService.selectGoodsService(params);
    if (flag === true) {
      ctx.r(0, '操作成功', flag);
    } else {
      ctx.r(flag.code || 500, '操作失败', flag);
    }
  }

  // 单个取消勾选商品记录
  async postUnselectGoodsJson(ctx: Context): Promise<void> {
    const cartService = new CartService(ctx);
    const requestBody = ctx.getPostData();
    const params = {
      accountContext: this.getCartIdRequest(ctx),
      cartGoodsList: [
        {
          ...requestBody,
        },
      ],
      platFormType: this.platFormType,
    };
    const flag = await cartService.unselectGoodsService(params);
    if (flag === true) {
      ctx.r(0, '操作成功', flag);
    } else {
      ctx.r(flag.code || 500, '操作失败', flag);
    }
  }

  // 批量勾选商品记录
  async postBatchSelectGoodsJson(ctx: Context): Promise<void> {
    const cartService = new CartService(ctx);
    const requestBody = ctx.getPostData();
    const params = {
      accountContext: this.getCartIdRequest(ctx),
      cartGoodsList: requestBody.goodsList || [],
      platFormType: this.platFormType,
    };
    const flag = await cartService.selectGoodsService(params);
    if (flag === true) {
      ctx.r(0, '操作成功', flag);
    } else {
      ctx.r(flag.code || 500, '操作失败', flag);
    }
  }

  // 批量取消勾选商品记录
  async postBatchUnselectGoodsJson(ctx: Context): Promise<void> {
    const cartService = new CartService(ctx);
    const requestBody = ctx.getPostData();
    const params = {
      accountContext: this.getCartIdRequest(ctx),
      cartGoodsList: requestBody.goodsList || [],
      platFormType: this.platFormType,
    };
    const flag = await cartService.unselectGoodsService(params);
    if (flag === true) {
      ctx.r(0, '操作成功', flag);
    } else {
      ctx.r(flag.code || 500, '操作失败', flag);
    }
  }

  // 全部勾选商品记录
  async postSelectAllGoodsJson(ctx: Context): Promise<void> {
    const cartService = new CartService(ctx);
    const { kdtId, channelId, isPlatCart } = ctx.getPostData();
    const params = {
      accountContext: this.getCartIdRequest(ctx),
      kdtId,
      channelId,
      platformCart: isPlatCart,
      platFormType: this.platFormType,
    };
    const flag = await cartService.selectAllGoodsService(params);
    if (flag === true) {
      ctx.r(0, '操作成功', flag);
    } else {
      ctx.r(flag.code || 500, '操作失败', flag);
    }
  }

  // 全部取消勾选商品记录
  async postUnselectAllGoodsJson(ctx: Context): Promise<void> {
    const cartService = new CartService(ctx);
    const { kdtId, channelId, isPlatCart } = ctx.getPostData();
    const params = {
      accountContext: this.getCartIdRequest(ctx),
      kdtId,
      channelId,
      platformCart: isPlatCart,
      platFormType: this.platFormType,
    };
    const flag = await cartService.unselectAllGoodsService(params);
    if (flag === true) {
      ctx.r(0, '操作成功', flag);
    } else {
      ctx.r(flag.code || 500, '操作失败', flag);
    }
  }

  async reselectGoods(ctx: Context): Promise<void> {
    const cartService = new CartService(ctx);
    const goods = ctx.getPostData();

    const params = {
      cartId: goods.cartId,
      reselectGoods: {
        buyer: this.getCartIdRequest(ctx),
        item: goods,
      },
    };

    const flag = await cartService.reselectGoods(params);
    ctx.json(0, 'ok', flag);
  }

  // 批量添加到购物车
  async postBatchAddGoodsJson(ctx: Context): Promise<void> {
    const { items, itemSource } = mapKeysToCamelCase(ctx.getPostData());

    _.each(items, (item: IPostBatchAddGoodsBodyItem = {}): void => {
      const { tpps } = item;
      // 记录外部订单来源
      item.cartBizMarkDTO = _.merge(item.cartBizMarkDTO, {
        cartBizMark: {
          tpps: tpps || ctx.cookies.get('tpps') || '',
        },
      });
    });

    const params = {
      buyer: this.getCartIdRequest(ctx),
      items,
      itemSource: itemSource || '',
    };
    const flag = await new CartService(ctx).batchAddGoodsService(params);
    ctx.json(0, 'ok', flag);
  }

  async getCountGoodsNums(ctx: Context): Promise<void> {
    const { kdtId } = ctx;
    const { channelId, selected = true } = mapKeysToCamelCase(ctx.getQueryData());
    const accountContext = this.getCartIdRequest(ctx);
    const { buyerId, nobody } = accountContext;

    // 处理每天都有少量 buyerId 和 nobody不存在导致的请求报错
    if (!buyerId && !nobody) {
      ctx.json(0, 'ok', {
        count: 0,
      });
      return;
    }

    const params = {
      accountContext,
      kdtId,
      channelId,
      selected: Boolean(+selected),
      source: 'wsc-h5-trade',
      platFormType: this.platFormType,
    };
    const cartService = new CartService(ctx);
    let result = await cartService.countGoodsNums(params);
    if (!(+result > 0)) {
      result = 0;
    }
    ctx.r(0, 'ok', {
      count: +result,
    });
  }

  // 获取购物车商品列表
  async getSelectedGoodsJson(ctx: Context): Promise<void> {
    const cartService = new CartService(ctx);
    const params = this.getListGoodsQuery(ctx);
    const cartList = await cartService.getCartList(params);
    const selectedCardList: ICartListItem[] = [];
    _.forEach(cartList, (shop) => {
      const selectedItems = _.filter(shop.items, (goods) => !!goods.selectState);
      if (selectedItems.length) {
        shop.items = selectedItems;
        selectedCardList.push(shop);
      }
    });
    ctx.json(0, '', selectedCardList);
  }

  /**
   * 获取换购商品 - 新
   */
  async findExchangeGoods(ctx: Context) {
    const { kdtId, query = {} } = ctx;
    const { activityId, offlineId = 0 } = query;
    const cartService = new CartService(ctx);

    const result = (await cartService.findAggregatedExchangeSkus(kdtId, activityId, offlineId)) || [];
    ctx.json(0, 'ok', result);
  }

  // 获取失效商品关联的推荐商品
  async getMultiRecommendGoods(ctx: Context): Promise<void> {
    const cartService = new CartService(ctx);
    const { kdtId, offlineId } = ctx;
    const { goodsIds } = ctx.getPostData();
    const { youzan_user_id: yzUid } = ctx.getLocalSession();
    const params = {
      yzUid,
      pageSize: 3,
      pageNum: 1,
      extraParams: {
        kdtId,
        storeId: offlineId,
        isMini: false,
        goodsIds,
      },
      scene: 'wsc~tc~eg',
    };
    let res;
    try {
      res = await cartService.getMultiRecommendGoods(params);
    } catch (e) {
      // do nothing
    }
    ctx.json(0, '', res);
  }

  escapeCartList(cartList: ICartListItem[]): void {
    cartList &&
      cartList.forEach((cartItem): void => {
        const { items = [] } = cartItem;
        items.forEach((item): void => {
          item.title && (item.title = _.escape(item.title));
        });
      });
  }

  handleActivityList(cartList: ICartListItem[]): void {
    cartList &&
      cartList.forEach((cartItem): void => {
        // 如果同时存在满减赠(101)和实付赠(261)
        const { activities = [], goodsGroupList = [] } = cartItem;
        if (
          activities.find((item: IPureObject) => item.activityType === 101) &&
          activities.find((item: IPureObject) => item.activityType === 261)
        ) {
          goodsGroupList.forEach((item: IGoodsGroup) => {
            const { goodsList } = item;
            const goodsIdList = goodsList.map((goods: IGood) => goods.goodsId);
            const activityIdList: number[] = [];
            // 处理活动叠加场景
            activities.forEach((activityInfo: IPureObject) => {
              const { activityItems, activityId } = activityInfo;
              activityItems.forEach((a: IPureObject) => {
                if (goodsIdList.includes(a.goodsId)) {
                  activityIdList.push(activityId);
                }
              });
            });
            item.groupActivityInfoList = Array.from(new Set([...activityIdList])).map(
              (activityId) => activities.find((a: IPureObject) => a.activityId === activityId) || {}
            );
          });
        }
      });
  }

  // 购物车自动领券
  async getVoucher(ctx: Context) {
    const { kdtId, userId } = ctx;
    const postData = ctx.getPostData();
    const param: IVoucherSendRequest = {
      ...postData,
      /** 用户id */
      userId,
      /** 店铺kdtId */
      kdtId,
      requestId: `${Date.now()}_${userId}`,
    };
    const { alias } = postData;
    const data = await this.sendCoupon(param, alias, '/wsctrade/cart/getVoucher.json');
    ctx.json(0, 'ok', data);
  }

  // 对QQ小程序和支付宝小程序的旧版本 不展示活动栏
  modifyGoodsGroupList(cartList: ICartListItem[]) {
    const { goodsGroupList } = cartList[0];
    const { isQQApp, isAlipayApp, miniprogramVersion } = this.ctx;
    const isQQOlderVersion =
      // @ts-ignore
      isQQApp && CompareVersion.isGt('1.14.10', miniprogramVersion);
    const isAlipayOlderVersion =
      // @ts-ignore
      isAlipayApp && CompareVersion.isGt('0.0.66', miniprogramVersion);

    if (!goodsGroupList || !miniprogramVersion) {
      return goodsGroupList;
    }
    if (isQQOlderVersion || isAlipayOlderVersion) {
      return goodsGroupList.map((item: IGoodsGroup) => {
        const { groupActivityInfo } = item;
        const hasActivity =
          groupActivityInfo &&
          ([umpActivityEnum.MEET_REDUCE, umpActivityEnum.PLUS_BUY, umpActivityEnum.SECOND_HALF].includes(
            groupActivityInfo.activityType
          ) ||
            ([umpActivityEnum.PACKAGE_BUY].includes(groupActivityInfo.activityType) && isQQOlderVersion));
        if (hasActivity) {
          item.groupActivityInfo = undefined;
        }

        return item;
      });
    }
    return goodsGroupList;
  }

  async getCouponAddOnInfo(ctx: Context) {
    const params = {
      ...this.getListGoodsQuery(ctx),
    };
    params.source = {
      ...params.source,
      channelPlatForm: this.uaPlatform,
    };

    if (params.source.orderMark === 'pl_live') {
      params.source.channelPlatForm = 'pl_live';
    }


    // kdtId = 0情况
    if (params.kdtId === 0) {
      ctx.json(0, 'ok', {
        canUseCouponAddOn: false,
        isInExperiment: false,
        reason: 'kdtId 0',
      });
      return;
    }

    let data;
    const isInCouponAddOnGray = await this.isInCouponAddOnGray(ctx);

    if (!isInCouponAddOnGray) {
      ctx.json(0, 'ok', {
        canUseCouponAddOn: false,
        isInExperiment: false,
        reason: 'not in gray release',
      });
      return;
    }

    try {
      data = await new NewCartService(ctx).cartMakeUpPromotionCalculate(params);
    } catch (e) {
      ctx.json(0, 'ok', {
        canUseCouponAddOn: false,
        isInExperiment: false,
        reason: 'service error',
      });
      return;
    }

    // 如果没有可用数据，不走AB实验
    if (!data || !Object.keys(data).length) {
      ctx.json(0, 'ok', {
        canUseCouponAddOn: false,
        isInExperiment: false,
        reason: 'no valid add-on data',
      });
      return;
    }

    // 如果凑单条没有文案，不会走AB实验
    if (!data?.addOnCopywriting) {
      ctx.json(0, 'ok', {
        canUseCouponAddOn: true,
        isInExperiment: false,
        couponAddOnData: data,
      });
      return;
    }

    // ABTest
    const uuid = ctx.cookies.get('yz_log_uuid');
    const abConfig = await ctx.ABTestClient?.getTest('cart-coupon-addon', uuid || ctx.userId);
    const { configurations: { hasAddon = false } = {} } = abConfig;
    if (!hasAddon) {
      ctx.json(0, 'ok', {
        canUseCouponAddOn: false,
        abConfig,
        isInExperiment: true,
        reason: 'in base experiment',
      });
    } else {
      ctx.json(0, 'ok', {
        couponAddOnData: data,
        abConfig,
        isInExperiment: true,
        canUseCouponAddOn: hasAddon,
      });
    }
  }

  // 重选优惠
  async reselectGoodsActivity(ctx: Context) {
    const { kdtId, userId } = ctx;
    const { items } = ctx.getPostData();
    const param = {
      buyer: this.getCartIdRequest(ctx),
      items,
    };
    const data = await new CartService(ctx).reselectGoodsActivity(param);
    ctx.json(0, 'ok', data);
  }

  async isInCouponAddOnGray(ctx: Context): Promise<boolean> {
    const isInCouponAddOnGray = await isInGrayReleaseByKdtId(
      ctx,
      {
        namespace: 'wsc-h5-trade.application',
        key: 'canUseCartCouponAddOn',
      },
      ctx.kdtId
    );

    return isInCouponAddOnGray;
  }

  get platFormType() {
    return this.isGuang ? 'GUANG' : 'COMMON';
  }

  get isGuang() {
    const { weappType } = mapKeysToCamelCase(this.ctx.getQueryData());

    // ctx.isGuang 依赖 cookie，所以需要通过 weappType 补充判断
    return (this.ctx.isGuang || weappType === 'guang') && this.ctx.isWeappWebview;
  }

  // 格式化价格，原价和预估价
  formatPrice = (price: number) => {
    if (!price) {
      return '';
    }
    const format = (price: number) => {
      let n = 2;
      if (price % 10 === 0) n = 1;
      if (price % 100 === 0) n = 0;
      return `¥${(Math.abs(price) / 100).toFixed(n)}`;
    };
    // 支持负数
    return price > 0 ? `${format(price)}` : `-${format(price)}`;
  };

  // 统一处理组合商品
  formatCartComboData(cartList: ICartListItem[]) {
    const { goodsGroupList = [] } = cartList[0];
    try {
      goodsGroupList.forEach((goodsGroup: IGoodsGroup) => {
        goodsGroup?.goodsList?.forEach((item: IGood) => {
          const { comboDetail } = item;
          comboDetail?.groupList?.forEach((group: ComboItem) => {
            group?.subComboList?.forEach((subCombo) => {
              let skuDesc = null;
              if (subCombo.skuDesc) {
                try {
                  skuDesc = JSON.parse(subCombo.skuDesc);
                } catch (error) {}
              }
              const skuDescriptions = skuDesc?.reduce((pre: SkuItem[], cur: SkuItem) => {
                pre.push({ sn: cur.v, sp: this.formatPrice(subCombo.addPrice) });
                return pre;
              }, []);
              // 得到处理后的规格信息
              subCombo.skuDescArr = skuDescriptions || [];
              const propDesc = subCombo.properties?.map((propValue: Propertie) => ({
                pn: propValue.valName,
                pp: this.formatPrice(propValue.price),
              }));
              // 得到处理后的属性信息
              subCombo.propDescArr = propDesc || [];
            });
          });
        });
      });
    } catch (error) {}
    return goodsGroupList;
  }

  async isUseRantaPage() {
    const { ctx } = this;
    const { force_shunt = '' } = ctx.query;

    switch (force_shunt) {
      case 'v1':
        return false;
      case 'v2':
        return true;
      default:
        break;
    }

    try {
      const isUseRantaPage = await isInGrayReleaseByKdtId(
        ctx,
        { namespace: 'wsc-h5-trade.gray-release', key: 'useWebRantaTeeCart' },
        ctx.kdtId
      );
      let isCloudCustomized = false;

      if (isUseRantaPage) {
        isCloudCustomized = await isCloudDesign(ctx as any, { kdtId: ctx.kdtId, pageName: 'cart' });
      }
      ctx.setState({ isCloudCustomized });

      const useWebRantaCartWithCloudDesign = isInGrayReleaseByKdtId(
        ctx,
        {
          namespace: 'wsc-h5-trade.gray-release',
          key: 'useWebRantaCartWithCloudDesign',
        },
        ctx.kdtId
      );

      return (
        (!isCloudCustomized && isUseRantaPage) ||
        (isCloudCustomized && useWebRantaCartWithCloudDesign)
      );
    } catch (err) {
      // skip
    }

    return false;
  }

  // 获取购物车列表获取配送信息
  async getDeliveryInfoForCartList(ctx: Context) {
    const { buyerId } = this;
    const { kdtId } = ctx;
    const postData: ICartAsyncRequestBody = ctx.getPostData();
    const {
      currentAddress = {} as IAddressItem,
      itemInfoList: itemInfoParamList,
      currentExpressType,
      currentLocation,
    } = postData;
    const isSelfFetch = currentExpressType === 1;

    // 定位和地址都没有经纬度，不请求配送信息，判断lat即可，经纬度是同步出现
    if (!currentLocation?.lat && !currentAddress?.lat) {
      return {};
    }

    // 针对套餐商品做处理
    itemInfoParamList.forEach((item: IItemInfoParam) => {
      // 如果有组合商品，处理数据
      if (item.comboDetail) {
        const {comboType, groupList} = item.comboDetail;
        item.combo = {
          type: comboType,
          groups: groupList.map((group) => {
            const {id, subComboList} = group;
            return {
              groupId: id,
              combos: subComboList.map((item) => {
                const {goodsId, skuId, num} = item;
                return {
                  goodsId,
                  skuId,
                  num,
                }
              }),
            };
          }),
        };
        delete item.comboDetail;
      }
    });

    const params: {
      kdtId: number;
      buyerId: number;
      itemInfoParamList: IItemInfoParam[];
      logisticType: number;
      shippingAddress: IShippingAddress | ILocation;
    } = {
      kdtId,
      buyerId,
      itemInfoParamList,
      logisticType: currentExpressType,
      shippingAddress: isSelfFetch ? {
        lon: String(currentLocation.lon || ''),
        lat: String(currentLocation.lat || ''),
      } : {
        countryName: currentAddress.country,
        provinceName: currentAddress.province,
        cityName: currentAddress.city,
        countyName: currentAddress.county,
        address: currentAddress.addressDetail,
        regionId: currentAddress.areaCode,
        lon: String(currentAddress.lon || ''),
        lat: String(currentAddress.lat || ''),
      }
    };

    
    const result = await new NewCartService(ctx).queryCartItemDeliveryTime(params);

    return result;
  }

  // 获取有赞放心购数据
  async getYzGuaranteeByAliases(ctx: Context) {
    const postData: ICartAsyncRequestBody = ctx.getPostData();
    const { itemInfoList } = postData;
    const aliases = itemInfoList.map((item) => item.alias);
    const service = new GoodsYzGuaranteeService(ctx);
    const params = {
      aliases,
    } as IGuaranteeParams;

    const result = await service.getYzGuarantee(params);

    // 快手小程序强制不展示放心购
    if (this.ctx?.isKsApp) {
      result.securedItems = [];
    }

    // 小红书本地生活场景强制不展示放心购
    if (this.ctx?.xExtraData?.bizEnv === 'xhsLocalLife') {
      result.securedItems = [];
    }

    return result;
  }

  // 获取购物车异步数据接口
  async getCartAsyncData(ctx: Context) {
    // 异步接口请求，请求异常则降级返回空对象，不影响其他接口
    const [yzGuaranteeRes, deliveryInfoRes] = await Promise.all([
      this.getYzGuaranteeByAliases(ctx).catch((err) => {
        console.log('getYzGuaranteeByAliases err: ', err);
        return {};
      }),
      this.getDeliveryInfoForCartList(ctx).catch((err) => {
        console.log('getDeliveryInfoForCartList err: ', err);
        return {};
      }),
    ]);

    const result = {
      ...yzGuaranteeRes,
      ...deliveryInfoRes,
    };

    ctx.json(0, '', result);
  }
}

export = CartController;
