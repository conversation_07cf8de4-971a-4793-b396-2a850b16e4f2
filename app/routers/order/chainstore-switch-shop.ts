export = [
  [
    'POST',
    '/wsctrade/order/localSale.json',
    'order.OrderChainStoreController',
    ['localSaleShopAndTime']
  ],
  [
    'GET',
    '/wsctrade/order/chainstore-switch-shop',
    'order.OrderChainStoreController',
    ['getIndexHtml']
  ],
  [
    'POST',
    '/wsctrade/order/get-switchable-shopList.json',
    'order.OrderChainStoreController',
    ['getSwitchableShopList']
  ],
  [
    'POST',
    '/wsctrade/order/is-support-local-switch-shop.json',
    'order.OrderChainStoreController',
    ['isSupportLocalSwitchShop']
  ],
  [
    'POST', '/wsctrade/order/buy/cross-store-re-cache-order-creation.json',
    'order.OrderBuyController',
    'crossStoreReCacheOrderCreation'
  ],
];