// @ts-ignore
const apolloClient = global.getRuntime().apolloClient;

const genApooloConfigGetter = (appId: string) => {
  return (namespace: string, key?: string) => {
    if (key) {
      return apolloClient.getConfig({
        appId,
        namespace,
        key,
      });
    }
  
    return apolloClient.getConfig({
      appId,
      namespace,
    });
  }
}

export const getTradeApolloConfig =  genApooloConfigGetter('wsc-h5-trade');

export const getH5GoodsApolloConfig = genApooloConfigGetter('wsc-h5-goods');

export const getH5FenxiaoApolloConfig = genApooloConfigGetter('wsc-h5-fenxiao');