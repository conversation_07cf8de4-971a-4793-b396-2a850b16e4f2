/* eslint-disable @typescript-eslint/no-explicit-any */
import args from '@youzan/utils/url/args';
import pick from 'lodash/pick';
import { IOrderInfoDto } from 'definitions/order/detail/OrderInfo';
import { IOrderBizExtraDto } from 'definitions/order/detail/OrderBizExtra';
import { ISourceInfoDto } from 'definitions/order/detail/SourceInfo';
import OrderContext from '../../../../lib/OrderContext';

export enum LocalDeliveryRefundActionTypeEnum {
  CALL_SERVICE = 'callService',
  MODIFY_ADDRESS = 'modifyAddress',
  CALL_RIDER = 'callRider',
  TO_REFUND = 'toRefund',
  CANCEL = 'cancel',
}

export enum LocalDeliveryFulfillDetailStatusEnum {
  // 商家已接单
  MERCHANT_ACCEPTED = 3,
  // 正在呼叫骑手
  CALLING_RIDER = 4,
  // 骑手赶往商家
  RIDER_GOING_TO_MERCHANT = 5,
  // 骑手已到店
  RIDER_AT_MERCHANT = 6,
  // 配送中
  DELIVERING = 7,
}

class OrderInfoFormatter {
  public static format(orderInfoDo: any, orderExtra: any, remarkInfo: any = {}): IOrderInfoDto {
    const orderInfoDto: IOrderInfoDto = pick(orderInfoDo, [
      'pointsPrice',
      'orderNo',
      'kdtId',
      'headKdtId',
      'shopName',
      'bizNo',
      'state',
      'stateStr',
      'stateDetailStr',
      'orderType',
      'orderGoodsType',
      'activityType',
      'buyWay',
      'buyWayStr',
      'buyWayExtendDesc',
      'payTime',
      'createTime',
      'successTime',
      'updateTime',
      'expressTime',
      'expiredTime',
      'closeTime',
      'autoReceiveOrderTime',
      'expressType',
      'closeType',
      'closeTypeStr',
      'expressTypeDesc',
      'num',
      'progressBar',
      'isVirtual',
      'virtualType',
      'isRetailOrder',
      'isWholesaleOrder',
      'isAllowLaterReceive',
      'isAllowShowBuyWay',
      'isShowSelfetchScancode',
      'isAllowBuyAgain',
      'isAllowDirectBuyAgain',
      'isAllowCancelOrder',
      'isAllowConfirmReceive',
      'isAllowPeerpay',
      'isAllowShareOrder',
      'isAllowGroupon',
      'isAllowShareCoupon',
      'isShowRefundInfo',
      'freight',
      'afterSaleContact',
      'isShowPostponeShip',
      'virtualResponse',
      'marketingOrderSource',
      'virtualTicketResponse',
      'yzGuarantee',
      'feedback',
      'wxChannelsOrder',
      'allowShowDeleteOrder',
      'channelType',
      'allowShowModifyAddress',
      'outBizNo',
      'pickUpCode',
      'bizType',
      'deliveryAllowShow',
    ]) as IOrderInfoDto;

    // 新增一些业务状态
    // 订单状态：已发货 维权状态：维权已创建
    orderInfoDto.showOrderStatusSended =
      orderInfoDo.state === 60 && ![185, 31].includes(orderInfoDo.orderGoodsType) && orderInfoDo.feedback !== 201;
    // 订单状态：待成团，其他订单持续时间较短，不包含抽奖拼团订单
    orderInfoDto.showOrderStatusToGroup = orderInfoDo.state === 30 && orderInfoDo.activityType !== 23;
    // 显示售后-拨打电话
    orderInfoDto.showAfterSaleMobile =
      orderInfoDo.afterSaleContact &&
      orderInfoDo.afterSaleContact.isShownToBuyer &&
      !!(orderInfoDo.afterSaleContact.phoneNumber || orderInfoDo.afterSaleContact.mobileNumber);

    // 提取remark信息到订单对象
    orderInfoDto.buyerMemo = remarkInfo.buyerMemo || ''; // 买家留言

    orderInfoDto.extra = {
      WECHAT_SYNC_SHOPPING_LIST: orderExtra.WECHAT_SYNC_SHOPPING_LIST,
      rxNo: orderExtra.PRESCRIPTION_NO,
      PRIOR_USE_COMPLETED: +orderExtra.PRIOR_USE_COMPLETED,
    };

    // 同城配送申请退款拦截弹窗配置
    if (orderInfoDo.expressType === 2) {
      orderInfoDto.localDeliveryRefundConfig = this.getLocalDeliveryRefundConfig();
    }

    return orderInfoDto;
  }

  public static formatOrderBiz(
    orderInfoDto: IOrderInfoDto,
    orderBizExtra: IOrderBizExtraDto,
    orderBizUrl: any,
    orderInfoDo: any,
    orderExtra: any,
    sourceInfo: ISourceInfoDto,
    ctx: any
  ): void {
    // 显示订单流转状态
    orderBizExtra.showSteps = !!(orderInfoDo.progressBar && orderInfoDo.progressBar.length > 0);

    // 钱款去向按钮：自有支付才展示
    if (orderInfoDo.buyWay === 1) {
      orderBizUrl.moneyToWhereUrl = OrderContext.buildUrl('/wsctrade/order/money-to-where', 'h5');
    }

    // 有赞担保专属客服
    if (orderInfoDo.yzGuarantee) {
      orderBizUrl.yzGuaranteeUrl = OrderContext.buildUrl('/v3/help/online/mars/index', 'h5');
    }

    const isGuang = sourceInfo.orderMark === 'weapp_guang';
    const { isWeapp } = ctx;

    const from_biz = isGuang ? 'guang' : 'wsc';
    let from_scene = args.get('im_from_scene', ctx.url) || '';
    if (isGuang && isWeapp) {
      from_scene = 'weapp';
    } else if (isGuang) {
      from_scene = 'app';
    }

    // 对此订单有疑问
    orderBizUrl.orderQuestionUrl = args.add(
      OrderContext.buildUrl(isGuang ? '/v3/help/online/guangMars/index' : '/v3/help/online/mars/index', 'h5'),
      { from_biz, from_scene }
    );

    // 分期支付（单独分期支付或组合支付（礼品卡+分期支付））
    if (orderInfoDo.buyWay === 40 || orderInfoDo.buyWayStr === '分期支付') {
      orderBizUrl.buyWayDetailUrl = `https://finance.youzan.com/credit/bill/detail?trade_no=${orderInfoDo.orderNo}`;
    }

    // 运费险标记
    const { freight } = orderInfoDto;
    if (freight) {
      const { freightInsuranceType } = orderBizExtra;
      let url;
      if (freightInsuranceType === 2) {
        url = OrderContext.buildUrl(`/wscassets/beneficiary/home?bizNo=${orderInfoDo.orderNo}`, 'h5');
      } else if (freightInsuranceType === 1) {
        url = OrderContext.buildUrl(
          `/trade/insurance/detail?order_no=${orderInfoDo.orderNo}&kdt_id=${orderInfoDo.kdtId}`,
          'wap',
          orderInfoDo.kdtId
        );
      } else {
        url = 'javascript:;';
      }
      freight.url = url;
    }

    // 知识付费送礼退款
    if (orderInfoDo.isShowRefundInfo) {
      orderBizUrl.knowledgePaymentRefundUrl = OrderContext.buildUrl(
        `/trade/refund/fundprocessbyorder?order_no=${orderInfoDo.orderNo}&kdt_id=${orderInfoDo.kdtId}`,
        'wap'
      );
    }

    // 返还储值金
    try {
      orderBizExtra.assertBusinessDetail = JSON.parse(orderExtra.ASSET_BUSINESS_DETAIL) || [];
    } catch (err) {
      orderBizExtra.assertBusinessDetail = [];
    }

    // 去掉node层处理逻辑，统一放到wsc的orderExtra里处理
    // 取货号
    if (orderExtra.PICK_UP_CODE) {
      orderBizExtra.takeGoodsCode = orderExtra.PICK_UP_CODE;
    }

    // 视频号小店订单 不展示自动确认收货
    try {
      const BIZ_ORDER_ATTRIBUTE = JSON.parse(orderInfoDo?.extra?.BIZ_ORDER_ATTRIBUTE || '{}');
      const { MULTI_PLAT_OUT_CHANNEL = '' } = BIZ_ORDER_ATTRIBUTE;
      if (MULTI_PLAT_OUT_CHANNEL === 'WX_VIDEO_XIAO_DIAN') {
        orderInfoDto.showOrderStatusSended = false;
      }
    } catch (err) {}
  }

  public static getLocalDeliveryRefundConfig() {
    const actions = {
      cancel: {
        type: LocalDeliveryRefundActionTypeEnum.CANCEL,
        text: '再等等',
      },
      toRefund: {
        type: LocalDeliveryRefundActionTypeEnum.TO_REFUND,
        text: '仍要退款',
      },
      callService: {
        type: LocalDeliveryRefundActionTypeEnum.CALL_SERVICE,
        text: '联系商家，协商解决',
      },
      modifyAddress: {
        type: LocalDeliveryRefundActionTypeEnum.MODIFY_ADDRESS,
        text: '改地址，立即生效',
      },
      callRider: {
        type: LocalDeliveryRefundActionTypeEnum.CALL_RIDER,
        text: '联系配送员，了解进度',
      },
    };

    // 同城配送申请退款拦截弹窗配置
    const localDeliveryRefundConfig = {
      [LocalDeliveryFulfillDetailStatusEnum.MERCHANT_ACCEPTED]: {
        title: '商家正在呼叫骑手，请您再耐心等待一下',
        backgroundImage: 'https://b.yzcdn.cn/bg/CALLING_RIDER_v2.jpg',
        actions: [actions.callService, actions.modifyAddress, actions.cancel, actions.toRefund],
        buttonType: 'plain'
      },
      [LocalDeliveryFulfillDetailStatusEnum.CALLING_RIDER]: {
        title: '商家正在呼叫骑手，请您再耐心等待一下',
        backgroundImage: 'https://b.yzcdn.cn/bg/CALLING_RIDER_v2.jpg',
        actions: [actions.callService, actions.modifyAddress, actions.cancel, actions.toRefund],
        buttonType: 'plain'
      },
      [LocalDeliveryFulfillDetailStatusEnum.RIDER_GOING_TO_MERCHANT]: {
        title: '配送员已接单，请您再耐心等待一下',
        backgroundImage: 'https://b.yzcdn.cn/bg/CALLING_RIDER_v2.jpg',
        actions: [
          actions.modifyAddress,
          actions.callRider,
          actions.callService,
          actions.toRefund,
        ],
        buttonType: 'plain'
      },
      [LocalDeliveryFulfillDetailStatusEnum.RIDER_AT_MERCHANT]: {
        title: '配送员已接单，请您再耐心等待一下',
        backgroundImage: 'https://b.yzcdn.cn/bg/CALLING_RIDER_v2.jpg',
        actions: [
          actions.modifyAddress,
          actions.callRider,
          actions.callService,
          actions.toRefund,
        ],
        buttonType: 'plain'
      },
      [LocalDeliveryFulfillDetailStatusEnum.DELIVERING]: {
        title: '商品正在配送中，请您再耐心等待一下',
        backgroundImage: 'https://b.yzcdn.cn/bg/DELIVERING.jpg',
        actions: [actions.callService, actions.callRider, actions.cancel, actions.toRefund],
        buttonType: 'plain'
      },
    };
    return localDeliveryRefundConfig;
  }
}

export default OrderInfoFormatter;
