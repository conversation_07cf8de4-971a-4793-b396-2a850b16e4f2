import BaseController from '../base/BaseController';
import { Context } from 'astroboy';
import HotelQueryService = require('../../services/order/HotelQueryService');

class HotelController extends BaseController {
  // 获取酒店预订信息
  async getHotelNotice(ctx: Context) {
    const { id: itemId } = ctx.getQueryData();
    const resp = await new HotelQueryService(ctx).getHotelAggDetailBySaleId({ itemId });
    ctx.json(0, 'ok', resp);
  }
}

export = HotelController;
