const BaseService = require('../base/BaseService');

class FoodEatService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.ebiz.mall.rigel.api.eatinorder.service.FoodEatService';
  }

  /**
   *  扫码后获得跳转路径
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/209139
   *
   *  @param {object} param -
   *  @param {number} param.mode -
   *  @param {number} param.kdtId -
   *  @param {string} param.alias - 餐桌别名，扫码二维码获取
   *  @param {number} param.storeId -
   *  @param {number} param.buyerId - 扫码人的buyerId，单人模式必传。多人模式不传
   *  @param {number} param.foodSeatId -
   *  @return {object}
   */

  async getRedirectPath(param) {
    return this.invoke('getRedirectPath', [param]);
  }
}

module.exports = FoodEatService;
