{"extensionId": "@wsc-h5-trade/valid-goods", "name": "@wsc-h5-trade/valid-goods", "version": "1.7.6", "pathInBundle": "valid-goods", "bundle": "https://b.yzcdn.cn/wsc-h5-trade/ext/ranta-extension_f58c0119.js", "hasWidget": true, "data": {"provide": {"presentData": ["r", "w"], "checkedGoodsList": ["r", "w"], "isCheckedAll": ["r", "w"]}, "consume": {"editMode": ["r"], "shopCart": ["r", "w"]}}, "event": {"emit": ["changeSku", "changePresentSku", "updateCartGoodsList"], "listen": ["sku<PERSON><PERSON><PERSON>", "presentSkuChanged", "toggleCheckedAll", "deleteCartItems", "cartGoodsListDidUpdate"]}, "component": {"consume": ["GoodsGroupUmp", "GoodsItemUmp"]}, "sandbox": {"id": "", "level": "Unsafe"}}