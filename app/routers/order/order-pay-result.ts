export = [
  [
    'GET',
    // 下面两个路由这样用比较骚，不过可以解同一业务页面不同业务场景没有使用SPA时，加载各自的中台化配置
    ['/wsctrade/order/payresult', '/wsctrade/order/payresult/auction'],
    'order.OrderPayResultController',
    ['teeRantaAcl', 'checkRedirectToShopDomain', 'getIndexHtml'],
  ],
  ['GET', '/wsctrade/order/payresult-wx', 'order.OrderPayResultController', 'getWxPayResultHtml'],
  ['GET', '/wsctrade/order/pay-result-confirm', 'order.OrderPayResultController', 'getPayResultConfirmHtml'],
  ['GET', '/wsctrade/order/wxPayresult.json', 'order.OrderPayResultController', 'getWxPayResultJson'],
  ['GET', '/wsctrade/order/payresult.json', 'order.OrderPayResultController', 'getPayResultJson'],
  ['GET', '/wsctrade/order/payresult/checkPay.json', 'order.OrderPayResultController', 'getCheckPayJson'],
  [
    'GET',
    '/wsctrade/order/payresult/checkPayGuideCoupon.json',
    'order.OrderPayResultController',
    'getCheckPayGuideCouponJson',
  ],
  ['GET', '/wsctrade/order/payresult/couponMessage.json', 'order.OrderPayResultController', 'getCouponMessageJson'],
  [
    'GET',
    '/wsctrade/order/payresult/selfFetchMessage.json',
    'order.OrderPayResultController',
    'getSelfFetchMessageJson',
  ],
  ['GET', ['/wsctrade/order/payresult/ticket-detail.json'], 'order.OrderPayResultController', 'getTicketDetailJson'],
  ['GET', '/wsctrade/order/payresult/shopad.json', 'order.OrderPayResultController', 'getShopAd'],
  [
    'GET',
    '/wsctrade/order/payresult/getSelfFetchTakeGoodsMessage.json',
    'order.OrderPayResultController',
    'getSelfFetchTakeGoodsMessage',
  ],
];
