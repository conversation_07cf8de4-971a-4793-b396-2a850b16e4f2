import { Context } from 'astroboy';
import BaseController from '../base/BaseController';
import WeShopOrderBenefitService from '../../services/weshop/WeShopOrderBenefitService';
import ShopConfigReadService from '../../services/shop/ShopConfigReadService';
import ChannelCoreAccountService from '../../services/uic/ChannelCoreAccountService';
import {
  transformSelectedBenefitList,
  transformToUpperKeyName,
  parseBenefitList,
  transformToUnderScoreKeyName,
} from './utils';
import { IOrderBenefitCalcResponse } from './types';
import { uniqueId } from 'lodash';

function isNullOrEmptyObject(value: any): boolean {
  return value == null || (typeof value === 'object' && Object.keys(value).length === 0);
}

class IndexController extends BaseController {
  async weshopBenefitsInfo(ctx: Context) {
    const shopMetaInfo = await this.callService(
      'iron-base/shop.ShopMetaReadService',
      'getShopMetaInfo',
      ctx.kdtId
    );
    const { kdtId, userId } = ctx;
    const rootKdtId = shopMetaInfo.rootKdtId || kdtId;

    // * 上报天网时单次请求的标识
    const uuid = uniqueId();

    this.ctx.logger.info('[weshop]微信小店权益选择页面传参', null, ctx.getPostData());
    this.ctx.logger.info(
      '[weshop]微信小店权益选择页面传参JSON stringify' +
        uuid +
        JSON.stringify(ctx.getPostData() || {})
    );

    // 从 ctx.getPostData() 获取数据
    const postData = ctx.getPostData();

    // 解构顶层属性，包括 isFirstEnter 和 allBenefits
    const { isFirstEnter, allBenefits } = postData;
    let finalOptions = {}; // 初始化最终的 options

    // 1. 优先尝试获取顶层的 options
    if (postData.options && !isNullOrEmptyObject(postData.options)) {
      finalOptions = postData.options;
    } else if (postData.referrerInfo) {
      // 2. 如果顶层 options 不可用，则尝试从 referrerInfo 中获取
      finalOptions = postData.referrerInfo;
    }

    const {
      isUsePointDeduction,
      costPoints,
      carrierId,
      appointCouponInfos,
    } = transformSelectedBenefitList(allBenefits);

    const { referrerInfo = {} } = finalOptions as any;
    const { extraData = {} } = referrerInfo;
    let { gen_order_info = '{}' } = extraData;

    try {
      gen_order_info = JSON.parse(gen_order_info);
    } catch (error) {
      this.ctx.logger.error('[weshop]Failed to parse gen_order_info: ' + error);
      throw new Error('Invalid gen_order_info format');
    }
    const { shop_appid, order_session_id, product_infos, price_info } = gen_order_info;

    // * 如果 kdtId 等于 rootKdtId，则使用 shop_appid 作为 appId 查询绑定关系获取小店绑定的真实网店 kdtId
    let updatedKdtId = kdtId;
    this.ctx.logger.info(
      '[weshop]0-0.微信小店连锁初始化kdtId 修正 JSONStringify' +
        uuid +
        JSON.stringify({
          kdtId,
          rootKdtId,
          shop_appid,
          rawEqual: kdtId === rootKdtId,
          parsedEqual: +kdtId === +rootKdtId,
        })
    );
    if (+kdtId === +rootKdtId && shop_appid) {
      try {
        const result = await new ChannelCoreAccountService(ctx).queryMpBindInfoBySelf({
          appId: shop_appid,
          accountType: 19, // 固定参数，代表查询微信小店的绑定关系
        });
        this.ctx.logger.info(
          '[weshop]0-0.微信小店连锁初始化kdtId 请求结果 JSONStringify' +
            uuid +
            JSON.stringify(result || {})
        );
        if (result && result.externalId) {
          // 使用查询结果中的 externalId 作为新的 kdtId
          updatedKdtId = result.externalId;
        }
      } catch (error) {
        this.ctx.logger.error('[weshop]Failed to query MP bind info: ' + error);
      }
    }

    const params: any = {
      orderSessionId: order_session_id,
      shopAppId: shop_appid,
      weShopProductInfos: transformToUpperKeyName(product_infos),
      priceInfo: transformToUpperKeyName(price_info),
      kdtId: updatedKdtId,
      hqKdtId: rootKdtId,
      userId,
      isFirstEnter,
      ...(isFirstEnter
        ? {}
        : {
            isUsePointDeduction,
            costPoints,
            carrierId,
            appointCouponInfos,
          }),
    };

    if (!params.carrierId) {
      delete params.carrierId;
    }

    if (!params.appointCouponInfos) {
      delete params.appointCouponInfos;
    }

    this.ctx.logger.info('[weshop]1.微信小店权益选择dubbo入参', null, params);
    this.ctx.logger.info(
      '[weshop]1-1.微信小店权益选择dubbo入参JSONStringify' + uuid + JSON.stringify(params || {})
    );

    const [result, pointsName] = await Promise.all([
      new WeShopOrderBenefitService(ctx).calculateBenefitPrice(params) as IOrderBenefitCalcResponse,
      new ShopConfigReadService(ctx).queryShopPointsName(kdtId).catch(() => '积分'),
    ]);

    this.ctx.logger.info('[weshop]2.微信小店权益选择dubbo返回值', null, result);
    this.ctx.logger.info(
      '[weshop]2-2.微信小店权益选择dubbo返回值JSONStringify' + uuid + JSON.stringify(result || {})
    );

    const parsedBenefitList = parseBenefitList(pointsName, result.billingResponseItem);
    this.ctx.logger.info('[weshop]3.微信小店权益选择解析后的返回值', null, parsedBenefitList);
    this.ctx.logger.info(
      '[weshop]3-3.微信小店权益选择解析后的返回值JSONStringify' +
        uuid +
        JSON.stringify(parsedBenefitList || {})
    );

    return ctx.json(0, 'ok', {
      parsedBenefitList,
      couponOverlyingSuperposedTotalNum:
        result?.billingResponseItem?.couponOverlyingSuperposedTotalNum || 3,
      extraData: {
        vip_discounted_info: {
          vip_discounted_price: result.vipDiscountedPrice,
          product_infos: transformToUnderScoreKeyName(result.productInfos || []),
          unuse_shop_discount: result.unuseShopDiscount,
        },
      },
      ruleType: result.ruleType,
      isZeroPriceOrder: result.isZeroPriceOrder,
    });
  }
}

export = IndexController;
