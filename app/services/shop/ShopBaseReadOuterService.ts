import BaseService from '../base/BaseService';

class ShopBaseReadOuterService extends BaseService {
  get SHOP_BASE_SERVICE() {
    return 'com.youzan.shopcenter.outer.service.shop.ShopBaseReadOuterService';
  }

  /**
   *  查询店铺基础信息
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/850202
   *
   *  @param {number} kdtId - 店铺kdtId
   *  @return {Promise}
   */
  async queryShopBaseInfo(kdtId: number) {
    return this.invoke(this.SHOP_BASE_SERVICE, 'queryShopBaseInfo', [kdtId]);
  }
}

export = ShopBaseReadOuterService;
