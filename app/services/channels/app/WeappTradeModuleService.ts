import BaseService from '../../base/BaseService';


interface IGetTradeModuleStatusDTO {
  kdtId?: number;
  mpId?: number;
}

interface ITradeModuleStatusDTO {
  /** 是否成功申请接入 */
  alreadyApply?: boolean;
  /** 完成接入,启用完成 */
  accessFinish?: boolean;
}

class WeappTradeModuleService extends BaseService {
  get WEAPP_TRADE_MODULE_SERVICE_NAME(): string {
    return 'com.youzan.channels.apps.service.WeappTradeModuleService';
  }



  /**
  *  获取交易组件的状态
  1、支持两个维度，mpId或者kdtId
  2、当传入两者时，优先mpId
  *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1147056 
  *
  *  @param {Object} dto - 
  *  @param {number} dto.kdtId - 
  *  @param {number} dto.mpId - 
  *  @return {Promise}
  */
  async getTradeModuleStatus(dto: IGetTradeModuleStatusDTO): Promise<ITradeModuleStatusDTO> {
    return this.invoke(this.WEAPP_TRADE_MODULE_SERVICE_NAME, 'getTradeModuleStatus', [dto]);
  }

}

export default WeappTradeModuleService;
