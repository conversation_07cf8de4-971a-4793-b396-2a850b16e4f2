export enum CouponTypeEnum {
  /** 优惠券 */
  CARD = 'card',
  /** 优惠码 */
  CODE = 'code',
}

export interface IAppointCouponInfoDTO {
  /** 优惠券类型,card：优惠券，code：优惠码 */
  couponType?: CouponTypeEnum;
  /** 用户优惠券ID */
  couponId?: number;
  /** 选中顺序 (支持多优惠券叠加下单时有值) */
  chosenOrder?: number;
}

export interface IWeShopProductInfoDTO {
  /** 是否首次进入权益选择页走默认权益请求，首次进入只需传小店商品信息，其他信息不传 */
  isFirstEnter?: boolean;
  /** 是否使用积分抵扣 */
  isUsePointDeduction?: boolean;
  /** 会员权益载体选择，卡/等级，代表的是对应的会员价及会员折扣 */
  carrierId?: string;
  /** 用户ID */
  userId?: number;
  /** 积分抵扣使用积分数量 */
  costPoints?: number;
  /** 当前选择的优惠券信息 */
  appointCouponInfos?: IAppointCouponInfoDTO[];
}

// * 权益类型：1：权益卡 2：等级
export enum CarrierTypeEnum {
  /** 权益卡 */
  CARD = 1,
  /** 等级 */
  LEVEL = 2,
}

// *****************************===========================
/**
 * com.youzan.ebiz.video.channels.trade.api.model.weshop.WeShopOrderBenefitProductInfoResponse
 */
interface IWeShopOrderBenefitProductInfoResponse {
  /** 商品ID */
  productId?: string;
  /** 会员优惠金额 */
  vipDiscountedPrice?: number;
  /** 商品skuId */
  skuId?: string;
}

/**
 * com.youzan.ump.billing.dto.Coupon
 */
interface ICoupon {
  originCondition?: number;
  /** 是否使用 */
  isUse?: boolean;
  reason?: string;
  /** 1: 优惠券  2: 优惠码 */
  activityTypeGroup?: number;
  /** 活动类型  7:优惠券 9:一卡一码 10:通用码 11:社区团购券 12:三方券 13:兑换券2.0 */
  groupType?: number;
  /** 是否可叠加 */
  isCanOverlying?: boolean;
  code?: string;
  /** 面额生成类型  * 1:固定值 ==>   value字段 （默认值）  * 3:范围随机 ==> 随机范围为minValue ~ maxValue之间 */
  couponValueGenerateType?: number;
  /** 可兑换的itemId列表 */
  optimalPreferentialOrderItemIds?: number[];
  num?: number;
  available?: number;
  denominations?: number;
  discount?: number;
  type?: string;
  /** 来源活动id(送券场景使用) */
  activityId?: number;
  /** 抵扣金额 */
  deductionAmount?: number;
  /** 扩展信息 */
  extra?: Record<string, unknown>;
  thresholdPiece?: number;
  id?: number;
  reasonCode?: number;
  value?: number;
  startAt?: string;
  /** 默认通用描述文案 */
  formativeContext?: string;
  usedValue?: number;
  /** 优惠券展示文案 */
  couponText?: string;
  /** 抵扣优惠金额 */
  deductionDecreaseAmount?: number;
  endAt?: string;
  condition?: string;
  /** 单位描述 */
  unitDesc?: string;
  /** 面值描述 */
  valueDesc?: string;
  name?: string;
  /** 来源活动类型(送券场景使用) */
  activityType?: number;
}

/**
 * * 权益卡专项权益项
 */
export interface ICustomerDiscountCarrierBenefitDTO {
  /** 权益是否开启次数设置 */
  applyStock?: boolean;
  /** 权益类型 */
  benefitType: BenefitTypeEnum;
  /** 权益值 */
  benefitValue: number;
  /** 权益次数 */
  stockNum?: number;
}

/**
 * 权益类型枚举
 */
export enum BenefitTypeEnum {
  /** 1-折扣 */
  Discount = 1,
  /** 2-包邮 */
  FreeShipping = 2,
  /** 3-积分倍率 */
  PointsMultiplier = 3,
}

/**
 * * feature 权益相关
 */
export interface ICustomerDiscountCarrierFeature {
  carrierType: CarrierTypeEnum;
  customerDiscountActivityType: CustomerDiscountActivityTypeEnum;
}

/**
 * 客户折扣活动类型枚举
 */
export enum CustomerDiscountActivityTypeEnum {
  /** 1-自定义会员价 */
  CustomMemberPrice = 1,
  /** 2-会员折扣 */
  MemberDiscount = 2,
}

/**
 * * 会员权益
 */
interface ICustomerDiscountCarrierDTO {
  /** 载体状态 */
  carrierStatus?: number;
  /** 载体名称 */
  carrierName?: string;
  /** 扩展信息 */
  feature: ICustomerDiscountCarrierFeature;
  /** 是否可选  0：不可选  1：可选 */
  visual?: number;
  /** 载体权益列表 */
  carrierBenefitDTOList: ICustomerDiscountCarrierBenefitDTO[];
  /** 载体标识，该标识=载体本身的标识+类型 */
  carrierId?: string;
  /** 是否为当前选中的载体 */
  chosen?: boolean;
}

/**
 * * 积分抵现相关数据
 */
export interface IPointDeductionInfoDTO {
  /** 能够消费的积分 */
  canCostPoints: number;
  minAmount?: number;
  /** 抵现所需积分  pointDeductionRatioType = 2时使用，这两个参数支持积分抵现自定义金额  x积分 = x元，deductionRequiredPoints = deductionAmount  仅CRM店铺支持 */
  deductionRequiredPoints?: number;
  userPoint?: number;
  maxDeductionAmount?: number;
  /** 抵现金额  pointDeductionRatioType = 2时使用，这两个参数支持积分抵现自定义金额  x积分 = x元，deductionRequiredPoints = deductionAmount  仅CRM店铺支持 */
  deductionAmount?: number;
  rate: number;
  /** 积分抵现比例类型 */
  pointDeductionRatioType?: number;
  maxDeductionAmountType?: number;
  maxDeductionRate?: number;
  extraInfo?: string;
}

interface IPromotionInfo {
  promotionTypeId: number;
  extra: string;
}

/**
 * * 优惠计算结果模型实体
 */
export interface IBillingResponseItem {
  /** 可用的优惠券列表 */
  coupons?: ICoupon[];
  orderPreference?: IPromotionInfo[];

  /** 券叠加最大数量 */
  couponOverlyingSuperposedTotalNum?: number;

  /** 用户可选的会员权益载体列表 */
  customerDiscountCarrierDTOList?: ICustomerDiscountCarrierDTO[];
  kdtId?: number;

  pointDeductionInfo?: IPointDeductionInfoDTO;
}

/**
 * * 订单权益返回值
 */
export interface IOrderBenefitCalcResponse {
  /** 是否叠加优惠 */
  unuseShopDiscount?: number;
  /** 商品优惠信息 */
  productInfos?: IWeShopOrderBenefitProductInfoResponse[];
  /** 会员优惠金额 */
  vipDiscountedPrice?: number;
  /** 优惠叠加互斥规则类型 */
  ruleType?: number;
  /** 优惠叠加互斥规则类型 */
  isZeroPriceOrder?: boolean;
  /** 营销计算结果 */
  billingResponseItem?: IBillingResponseItem;
}

/**
 * * 三种权益类型枚举定义
 */
export enum SelectedBenefitTypeEnum {
  /** 会员权益 */
  MEMBER = 'MEMBER',
  /** 优惠券 */
  COUPON = 'COUPON',
  /** 积分 */
  POINTS = 'POINTS',
}

interface BenefitItem {
  id: number | string;
  valueDesc: string | number;
  name: string;
  extraInfo?: {
    validPeriodCopywriting: string;
  };
  chosen: boolean;
  /** 优惠券类型,card：优惠券，code：优惠码 */
  couponType?: CouponTypeEnum;
  type?: CouponTypeEnum;
  couponOrder?: number;
}

/**
 * * 已选中项
 */
export interface ISelectedItem {
  type: SelectedBenefitTypeEnum;
  title: string;
  list: BenefitItem[];
  chosenList: BenefitItem[];
}
