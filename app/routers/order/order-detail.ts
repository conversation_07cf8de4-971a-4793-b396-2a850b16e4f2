export = [
  [
    'GET',
    ['/wsctrade/order/tee-detail', '/wsctrade/order/detail-crm'],
    'order.OrderDetailController',
    ['teeRantaNoLoginAcl', 'getIndexHtml'],
  ],
  [
    'GET',
    ['/wsctrade/order/detail', '/wsctrade/order/detail/index'],
    'order.OrderDetailController',
    ['checkRedirectToShopDomain', 'getIndexHtml'],
  ],
  [
    'GET',
    '/wsctrade/order/guide',
    'order.OrderDetailController',
    'getGuideHtml',
  ],
  [
    'GET',
    '/wsctrade/order/confirmReceive.json',
    'order.OrderDetailController',
    'orderConfirmReceive',
  ],
  [
    'POST',
    '/wsctrade/order/confirmReceive.json',
    'order.OrderDetailController',
    'orderConfirmReceive',
  ],
  [
    'POST',
    '/wsctrade/order/delayReceive.json',
    'order.OrderDetailController',
    'orderDelayReceive',
  ],
  [
    'POST',
    '/wsctrade/order/checkOrderDelayReceive.json',
    'order.OrderDetailController',
    'checkOrderDelayReceive',
  ],
  [
    'POST',
    '/wsctrade/order/detail/getOrderInfo.json',
    'order.OrderDetailController',
    'getOrderInfo',
  ],
  
  [
    'POST',
    '/wsctrade/order/detail/getVoucher.json',
    'order.VoucherSendController',
    'getVoucher',
  ],
  [
    'POST',
    '/wsctrade/order/voucher-check.json',
    'order.VoucherSendController',
    'checkVoucher',
  ],
  [
    'POST',
    '/wsctrade/order/triggerActivity.json',
    'order.OrderDetailController',
    'triggerActivity',
  ],
  [
    'POST',
    '/wsctrade/order/confirmReceiveV2.json',
    'order.OrderDetailController',
    'orderConfirmReceiveV2',
  ],
  [
    'GET',
    '/wsctrade/order/getOrderInfoByTrade.json',
    'order.OrderDetailController',
    'getOrderInfoByTrade',
  ],
];
