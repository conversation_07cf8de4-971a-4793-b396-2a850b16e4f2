{% extends 'common/base.html' %}

{% block head_script %}
  {% include 'console/init.html' %}
  {% include 'lang-i18n/init.html' %}
{% endblock %}

{% block cdn_fallback %}
  {{ inlineJs('@youzan/cdn-fallback', 'dist/index.js') | safe }}
{% endblock %}

{% block base_script %}
  {% include './sentry_script.html' %}
{% endblock %}

{% block project_script %}
  <script src="{{versionUrl}}"></script>
{% endblock %}
