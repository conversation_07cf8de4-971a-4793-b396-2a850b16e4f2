import BaseService from '../base/BaseService';

interface IOrderSyncRequest {
  /** 订单号 */
  orderSessionId: string;
  shopAppId: string;
  /** 店铺ID */
  kdtId: number;
  weShopProductInfos: any;
  priceInfo: any;
  isFirstEnter: boolean;
  isUsePointDeduction?: boolean;
}

class WeShopOrderBenefitService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.ebiz.video.channels.trade.api.service.weshop.WeShopOrderBenefitService';
  }

  /**
   *  权益选择页使用会员权益计算接口
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1597388
   *
   *  @param {Object} request -
   *  @param {string} request.orderSessionId - 微信小店订单session_id，权益场景均使用此字段串联
   *  @param {Array.<Object>} request.weShopProductInfos[] - 微信小店下单商品信息
   *  @param {Array.<Object>} request.appointCouponInfos[] - 当前选择的优惠券信息
   *  @param {number} request.kdtId - 店铺ID
   *  @param {string} request.shopAppId - 微信小店APPID
   *  @param {number} request.hqKdtId - 总部店铺ID
   *  @param {boolean} request.isFirstEnter - 是否首次进入权益选择页走默认权益请求，首次进入只需传小店商品信息，其他信息不传
   *  @param {boolean} request.isUsePointDeduction - 是否使用积分抵扣
   *  @param {string} request.carrierId - 会员权益载体选择，卡/等级，代表的是对应的会员价及会员折扣
   *  @param {number} request.userId - 用户ID
   *  @param {number} request.costPoints - 积分抵扣使用积分数量
   *  @return {Promise}
   */
  async calculateBenefitPrice(request: IOrderSyncRequest) {
    return this.invoke('calculateBenefitPrice', [request]);
  }
}

export default WeShopOrderBenefitService;
