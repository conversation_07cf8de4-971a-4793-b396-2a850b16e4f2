<template>
  <van-popup v-model="show" class="activity-info-popup" safe-area-inset-bottom get-container="body" position="bottom">
    <div class="activity-info-popup__title">活动详细规则</div>

    <div class="activity-info-popup__body">
      <div class="activity-info-popup__subtitle">活动说明</div>

      <div class="activity-info-popup__explain">{{showExplain}}</div>

      <div class="activity-info-popup__subtitle">活动时间</div>

      <div class="activity-info-popup__time">
        {{ activityInfo.activityDuration }}
      </div>

      <div class="activity-info-popup__subtitle">活动内容</div>

      <div class="activity-info-popup__desc">
        <van-tag
          v-for="(tag, index) in activityInfo.activityTags"
          :key="index"
          class="activity-info-popup__tag theme-color theme-background-alpha-10"
          round
          color="#FDE2E6"
          text-color="#f44"
          size="medium"
        >
          {{ tag }}
        </van-tag>
        <span class="activity-info-popup__text">{{ activityInfo.activityDesc }}</span>
      </div>
    </div>
    <div class="activity-info-popup__btn theme-background" @click="show = false">知道了</div>
  </van-popup>
</template>
<script>
import { Popup, Tag } from 'vant';
import { ACTIVITY_TYPE_ALIAS_NUMBER } from '@biz/cart/constant';
export default {
  props: {
    activityInfo: {
      type: Object,
    },
    showPopup: {
      type: Boolean,
      default: false,
    },
  },

  components: {
    [Tag.name]: Tag,
    [Popup.name]: Popup,
  },

  data() {
    return {
      show: false,
    };
  },
  watch: {
    showPopup: {
      handler(val) {
        this.show = val;
      },
      immediate: true,
    },
    show(val) {
      if (val === false) {
        this.$emit('close');
      }
    },
  },
  computed: {
    showExplain() {
      if (this.activityInfo.activityType === ACTIVITY_TYPE_ALIAS_NUMBER.MEET_SEND) {
        return '1）参与的实付满赠活动仅含虚拟商品时不送实物赠品；2）实付金额门槛计算不含运费，商品优惠扣减积分抵现之后即为实付金额。商城订单使用储值余额支付时仍可享受实付满赠。';
      }
      return '参与的满减满赠活动仅含虚拟商品时不送实物赠品。';
    },
  },
};
</script>
<style scoped lang="scss">
.activity-info-popup {
  padding-left: 16px;
  padding-right: 16px;
  border-radius: 20px 20px 0 0;
  box-sizing: border-box;

  &__title {
    padding: 11px 0;
    text-align: center;
    font-size: 16px;
    color: #323233;
  }

  &__body {
    overflow: auto;
    box-sizing: border-box;
    // 弹层最低0.5*屏幕高，最高0.8*屏幕高，body部分需减去122px（标题栏和行动按钮栏高度和）,
    min-height: calc(50vh - 122px);
    min-height: calc(50vh - 122px - constant(safe-area-inset-bottom));
    min-height: calc(50vh - 122px - env(safe-area-inset-bottom));
    max-height: calc(80vh - 122px);
    max-height: calc(80vh - 122px - constant(safe-area-inset-bottom));
    max-height: calc(80vh - 122px - env(safe-area-inset-bottom));
  }

  &__subtitle {
    font-size: 14px;
    margin-bottom: 12px;
    font-weight: bold;
    color: #323233;
  }

  &__time,
  &__explain {
    font-size: 13px;
    margin-bottom: 20px;
    color: #323233;
  }

  &__desc {
    font-size: 13px;
    color: #323233;
  }

  &__tag {
    margin-right: 4px;
    padding: 0 4px;
    line-height: 16px;
    vertical-align: middle;
  }

  &__text {
    line-height: 17px;
    vertical-align: middle;
  }

  &__btn {
    margin: 32px auto 10px;
    height: 36px;
    line-height: 36px;
    width: 90%;
    border-radius: 18px;
    color: #fff;
    text-align: center;
    font-size: 14px;
  }
}
</style>
