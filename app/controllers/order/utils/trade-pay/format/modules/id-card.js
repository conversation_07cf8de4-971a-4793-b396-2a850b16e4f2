export default function ({ state }) {
  const { idCard = {}, needIdCardPhoto } = state.order;
  const { list = [] } = state.goods;
  const { shopName = '' } = state.shop;
  const { tradeTag } = state;
  const { hasOverseaGoods } = tradeTag;
  return {
    idcardGoods: {
      list,
    },
    idcardOrder: {
      needIdCardPhoto,
    },
    idcard: {
      name: idCard.idCardName || '',
      number: idCard.idCardNumber || '',
      binding: (idCard.idCardName && idCard.idCardNumber) || false,
      frontPhoto: idCard.idCardFrontPhoto,
      backPhoto: idCard.idCardBackPhoto,
    },
    idcardHasHaitaoGoods: hasOverseaGoods,
    idcardShop: {
      shopName,
    },
  };
}