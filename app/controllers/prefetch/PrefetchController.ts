import BaseController from '../base/BaseController';

const initConfig = {
  initMpData: false,
  initMpAccount: false,
  initPlatform: false
};

class PrefetchController extends BaseController {
  public async init() {
    await super.init(initConfig);
  }

  public async getIndexHtml() {
    const { ctx } = this;
    const versionMap = this.app.versionMap;
    const cdnSite = 'https://b.yzcdn.cn/';
    const loadList = [
      versionMap['version_css']['dll/component'],
      versionMap['version_js']['dll/framework'],
      versionMap['version_js']['dll/library'],
      versionMap['version_js']['dll/component'],
    ].map(item => cdnSite + item);

    ctx.setState('loadList', loadList);
    await ctx.render('common/prefetch.html');
  }
}

export = PrefetchController;
