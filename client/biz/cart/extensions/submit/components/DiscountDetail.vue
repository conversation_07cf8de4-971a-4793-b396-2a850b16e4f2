<template>
  <div>
    <van-tag round color="#F2F3F5" text-color="#323233" @click="showPopup">
      优惠明细
      <van-icon :name="arrowClassName" />
    </van-tag>
    <van-popup
      v-model="visible"
      overlay-class="discount-detail-overlay"
      class="discount-detail-popup"
      closeable
      safe-area-inset-bottom
      get-container="body"
      position="bottom"
      @close="onClose"
    >
      <div class="discount-detail-popup__title">优惠明细</div>
      <div class="discount-detail-popup__body">
        <div class="discount-detail-popup__price">
          <div class="discount-detail-popup__left">商品总额</div>
          <div class="discount-detail-popup__right font-semibold">￥{{ toFixedPrice(goodsTotalPrice) }}</div>
        </div>
        <div class="discount-detail-popup__price margin-bottom">
          <div class="discount-detail-popup__left">共优惠</div>
          <div class="discount-detail-popup__right dicsount-amount font-semibold">
            -￥{{ toFixedPrice(totalDiscount) }}
          </div>
        </div>
        <van-divider hairline class="divider" content-position="left">
          <i class="arrow-up-divider"></i>
        </van-divider>

        <div class="discount-detail-popup__item grey-dark" v-for="item in promotionDetails" :key="item.appType">
          <div class="discount-detail-popup__left">{{ item.appName === '优惠卡券' ? '优惠券' : item.appName }}</div>
          <div class="discount-detail-popup__right">-￥{{ toFixedPrice(item.preferentialValue) }}</div>
        </div>

        <div class="discount-detail-popup__total">
          <div class="discount-detail-popup__left font-medium">合计</div>
          <div class="discount-detail-popup__right font-semibold">
            <span>￥{{ toFixedPrice(totalPrice) }}</span>
          </div>
        </div>
        <div class="discount-detail-popup__desc">以上优惠不包含运费，实际优惠金额请以确认订单页为准</div>
      </div>
    </van-popup>
  </div>
</template>
<script>
import { Tag, Popup, Divider, Icon } from 'vant';

export default {
  name: 'discount-detail',

  components: {
    [Tag.name]: Tag,
    [Icon.name]: Icon,
    [Popup.name]: Popup,
    [Divider.name]: Divider,
  },

  props: {
    totalPrice: {
      type: Number,
      default: 0,
    },
    totalDiscount: {
      type: Number,
      default: 0,
    },
    promotionDetails: {
      type: Array,
      default: () => [],
    },
    goodsTotalPrice: {
      type: Number,
      default: 0,
    },
  },

  data() {
    return {
      visible: false,
    };
  },

  computed: {
    arrowClassName() {
      return this.visible ? 'arrow-down' : 'arrow-up';
    },
  },

  methods: {
    showPopup() {
      this.visible = !this.visible;
      this.$emit('click-discount-detail', this.visible);
    },
    onClose() {
      this.$emit('click-discount-detail', this.visible);
    },
    toFixedPrice(price) {
      return (price / 100).toFixed(2);
    },
  },
};
</script>
<style scoped lang="scss">
@import 'var.scss';
.discount-detail-popup {
  padding: 16px;
  border-radius: 16px 16px 0 0;
  box-sizing: border-box;
  // height: 375px;
  margin-bottom: 56px;
  z-index: 99 !important;

  &__title {
    font-family: PingFangSC-Medium;
    text-align: center;
    font-size: 16px;
    color: #323233;
    padding-bottom: 27px;
  }

  &__body {
    overflow: auto;
    box-sizing: border-box;
    // 弹层最低0.5*屏幕高，最高0.8*屏幕高，body部分需减去122px（标题栏和行动按钮栏高度和）,
    min-height: calc(50vh - 122px);
    min-height: calc(50vh - 122px - constant(safe-area-inset-bottom));
    min-height: calc(50vh - 122px - env(safe-area-inset-bottom));
    max-height: calc(80vh - 122px);
    max-height: calc(80vh - 122px - constant(safe-area-inset-bottom));
    max-height: calc(80vh - 122px - env(safe-area-inset-bottom));
  }

  &__price {
    font-family: PingFangSC-Medium;
    font-size: 16px;
    color: #323233;
    display: flex;
    line-height: 22px;
    margin-bottom: 24px;
  }
  .margin-bottom {
    margin-bottom: 8px;
  }
  &__left {
    flex: 1;
    justify-content: flex-start;
    font-size: 14px;
    span {
      font-family: PingFangSC-Medium;
      color: #ee0a24;
      line-height: 20px;
    }
  }

  &__right {
    flex: 1;
    justify-content: flex-end;
    text-align: right;
    span {
      font-family: PingFangSC-Medium;
      font-size: 16px;
    }
  }
  .dicsount-amount {
    color: var(--ump-icon, #323233);
  }
  &__item {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #323233;
    display: flex;
    margin-bottom: 12px;
  }
  .grey-dark {
    color: $gray-dark;
  }
  &__total {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #323233;
    display: flex;
    margin-top: 24px;
    line-height: 22px;
    margin-bottom: 8px;
  }

  &__desc {
    font-family: PingFangSC-Regular;
    font-size: 12px;
    color: $gray-dark;
    letter-spacing: 0;
    line-height: 16px;
  }
  .divider {
    position: relative;
    margin-top: 12px;
    margin-bottom: 8px;
    &::before {
      margin-right: 0;
    }
    &::after {
      margin-left: 0;
    }
  }
  .font-medium {
    font-family: PingFangSC-Medium;
  }
  .font-semibold {
    font-family: PingFangSC-Semibold;
  }

  .arrow-up-divider::before,
  .arrow-up-divider::after {
    content: '';
    position: absolute;
    left: 6%;
    bottom: 0;
    width: 0;
    height: 0;
    margin-left: -5px;
    border: 5px transparent solid;
  }

  .arrow-up-divider::before {
    border-bottom-color: #ebedf0;
    opacity: 0.5;
  }
  .arrow-up-divider::after {
    border-width: 3px;
    margin-left: -3px;
    border-bottom-color: #fff;
  }
}
</style>
<style>
.discount-detail-overlay {
  z-index: 98 !important;
}
</style>
