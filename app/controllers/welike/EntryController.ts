import { Context } from 'astroboy';
import BaseController from '../base/BaseController';
import WelikeMomentService from '../../services/api/welike/WelikeMomentService';
import WelikeMomentWhitelistService from '../../services/api/welike/WelikeMomentWhitelistService';
import { createWscIsolateCheck } from '@youzan/utils-shop';

/**
 * 动态入口请求响应DTO类
 */
interface IMomentEntranceResponseDTO {
  /** 动态包含的用户数 */
  userCount?: number;
  /** 头像列表 */
  avatars?: string[];
}

interface IEntryInfoResponse extends IMomentEntranceResponseDTO {
  showEntry: boolean;
}

interface IWelikeEntrySwitchConfig {
  enable: boolean;
  pageList: string[];
}

class EntryController extends BaseController {
  async checkShopTypeSupport(ctx: Context): Promise<boolean> {
    const shopMetaInfo = await this.callService('iron-base/shop.ShopMetaReadService', 'getShopMetaInfo', ctx.kdtId);

    // 微商城单店
    const { isPureWscSingleStore } = createWscIsolateCheck(shopMetaInfo);

    return isPureWscSingleStore;
  }

  async checkInWhiteList(ctx: Context): Promise<boolean> {
    const params = ctx.kdtId;

    return new WelikeMomentWhitelistService(ctx).isInGrayReleaseByKdtId(params);
  }

  async checkShopSwitch(ctx: Context): Promise<boolean> {
    /**
     * sourcePage：页面名称，名称定义见 welikeEntryPageEnum
     * @link https://gitlab.qima-inc.com/wsc-node/wsc-pc-v4/blob/master/client/pages/setting/team/constants/index.js
     */
    const { sourcePage = '' } = ctx.query;
    const shopConfig = await ctx.getShopConfigs(['welike_entry_switch_config']);

    let welikeEntrySwitchConfig: IWelikeEntrySwitchConfig = { enable: false, pageList: [] };
    try {
      welikeEntrySwitchConfig = JSON.parse(shopConfig.welike_entry_switch_config);
    } catch (error) {
      this.ctx.logger.warn('大家喜欢入口店铺配置解析失败', null, { shopConfig });
    }

    const { enable = false, pageList = [] } = welikeEntrySwitchConfig;
    /**
     * TODO
     * 老版本没有sourcePage参数，只能根据开关来判断
     */
    if (sourcePage) {
      return enable && pageList.includes(sourcePage);
    }
    return enable;

    /**
     * TODO
     * 新版本有sourcePage参数，可以根据开关和sourcePage来判断
     */
    // const shopSwitch = enable && pageList.includes(sourcePage);

    // return shopSwitch;
  }

  async getMomentEntranceInfo(ctx: Context): Promise<IEntryInfoResponse> {
    const { from = 'wsc-h5-trade' } = ctx.query;
    const params = {
      from,
      kdtId: ctx.kdtId,
      operatorId: this.buyerId,
    };
    const momentEntrance = await new WelikeMomentService(ctx).momentEntrance(params);
    return {
      showEntry: !!momentEntrance.userCount,
      userCount: momentEntrance.userCount ?? 0,
      avatars: (momentEntrance.avatars ?? []).slice(0, 10),
    };
  }

  async getEntryInfoJson(ctx: Context) {
    if (!this.buyerId || !ctx.kdtId) {
      ctx.json(0, 'success', {
        showEntry: false,
        reason: { buyerId: this.buyerId },
      });
      return;
    }

    const [isShopTypeSupport, isInWhiteList, shopSwitch] = await Promise.all([
      this.checkShopTypeSupport(ctx),
      this.checkInWhiteList(ctx),
      this.checkShopSwitch(ctx),
    ]);

    if (!isShopTypeSupport || !isInWhiteList || !shopSwitch) {
      ctx.json(0, 'success', {
        showEntry: false,
        reason: { isShopTypeSupport, isInWhiteList, shopSwitch },
      });
      return;
    }

    const momentEntranceInfo = await this.getMomentEntranceInfo(ctx);
    ctx.json(0, 'success', momentEntranceInfo);
  }
}

export = EntryController;
