import BaseService from '../base/BaseService';
import { getGoodsInfoDto, getBuyerInfoDto, getMobileInfoDto } from '../../../definitions/order/OrderShare';

class OrderShareService extends BaseService {
  get SERVER_NAME() {
    return 'com.youzan.ic.service.ItemQueryService';
  }

  get ACCOUNT_SERVER_NAME() {
    return 'com.youzan.uic.api.user.service.UserInfoService';
  }

  /**
   * 批量查询商品信息
   * 接口文档  http://zanapi.qima-inc.com/site/service/view/463073
   */
  async getGoodsInfo(params: getGoodsInfoDto) {
    return await this.invoke(this.SERVER_NAME, 'listItemsRequireIds', [params]);
  }

  /**
   * 根据userid获取用户信息
   * 接口文档  http://zanapi.qima-inc.com/site/service/view/169996
   */
  async getBuyerInfo(params: getBuyerInfoDto) {
    return await this.invoke(this.ACCOUNT_SERVER_NAME, 'getLatestPlatformUserInfoByUserId', [params]);
  }

  /**
   * 根据用户ID、手机号等，查询用户的手机账号信息
   * 接口文档  http://zanapi.qima-inc.com/site/service/view/4174
   */
  async getMobileInfo(param: getMobileInfoDto) {
    return this.invoke(this.ACCOUNT_SERVER_NAME, "getMobileInfo", [param]);
  }
}

export = OrderShareService;
