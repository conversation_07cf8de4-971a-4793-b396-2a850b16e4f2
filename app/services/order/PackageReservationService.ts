import BaseService from '../base/BaseService';
import { IPackageReservationReq, IPackageReservation } from 'definitions/order/PackageReservation';

class PackageReservationService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.ump.marketing.packagereservation.PackageReservationService';
  }

  queryPackageReservation(params: IPackageReservation): Promise<IPackageReservationReq> {
    return this.invoke('queryPackageReservation', [params]);
  }
}

export default PackageReservationService;
