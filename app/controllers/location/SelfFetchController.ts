import BaseController from '../base/BaseController';
import escape from '@youzan/utils/string/escape';

class SelfFetchController extends BaseController {
  public async getSelfFetchHtml() {
    const { ctx } = this;
    const query = Object.keys(ctx.query).reduce((prev, key) => {
      return {
        ...prev,
        [key]: escape(ctx.query[key]),
      }
    }, {})
    ctx.setGlobal('self_loaction', query);
    await ctx.render('location/self-fetch.html');
  }
}

export = SelfFetchController;
