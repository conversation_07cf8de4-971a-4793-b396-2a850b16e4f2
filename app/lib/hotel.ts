import { HotelType } from '../constants/hotel';

// 是否酒店订单
export const isHotelOrder = (type: HotelType) => {
  return [HotelType.CALENDAR_ROOM, HotelType.PRESALE, HotelType.RESERVE].includes(type);
};

// 是否预售单
export const isHotelPresaleOrder = (type: HotelType) => {
  return HotelType.PRESALE === type;
};

// 是否预约单
export const isHotelPeserveOrder = (type: HotelType) => {
  return HotelType.RESERVE === type;
};

// 是否日历房
export const isHotelCalendarOrder = (type: HotelType) => {
  return HotelType.CALENDAR_ROOM === type;
};
