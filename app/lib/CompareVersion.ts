// 待移至 zan-utils
class CompareVersion {
  public static compare(a: string, b: string): string {
    if (!a || !b) {
      return '';
    }

    a = a.startsWith('v') ? a.slice(1, a.length) : a;
    b = b.startsWith('v') ? b.slice(1, b.length) : b;

    const _a = a.split('.');
    const _b = b.split('.');

    let res = '';

    for (let i = 0, len = Math.max(_a.length, _b.length); i < len; i++) {
      const aNumber = Number(_a[i]) || 0;
      const bNumber = Number(_b[i]) || 0;

      if (aNumber > bNumber) {
        res = 'gt';
        break;
      } else if (aNumber < bNumber) {
        res = 'lt';
        break;
      } else if (aNumber === bNumber) {
        res = 'eq';
      }
    }

    return res;
  }

  /**
   * a === b
   * @param a
   * @param b
   */
  public static isEq(a: string, b: string): boolean {
    const res = CompareVersion.compare(a, b);
    return res === 'eq';
  }

  /**
   * a > b
   * @param a
   * @param b
   */
  public static isGt(a: string, b: string): boolean {
    const res = CompareVersion.compare(a, b);
    return res === 'gt';
  }

  /**
   * a < b
   * @param a
   * @param b
   */
  public static isLt(a: string, b: string): boolean {
    const res = CompareVersion.compare(a, b);
    return res === 'lt';
  }

  /**
   * a >= b
   * @param a
   * @param b
   */
  public static isGte(a: string, b: string): boolean {
    const res = CompareVersion.compare(a, b);
    return !!(res === 'gt' || res === 'eq');
  }

  /**
   * a <= b
   * @param a
   * @param b
   */
  public static isLte(a: string, b: string): boolean {
    const res = CompareVersion.compare(a, b);
    return !!(res === 'lt' || res === 'eq');
  }
}

export default CompareVersion;
