<template>
  <div>
    <div v-if="isWholesaleOrder" class="list-item-wholesale-flag">批发订单</div>
    <div class="list-item-header">
      <div class="list-item-header__left" @click="goHome">
        <template v-if="listItem.shopName">
          <span class="list-item-header__icon">
            <img
              v-if="showBrandCert"
              class="list-item-header__icon-brand"
              :src="SHOP_ICON_MAP['brand']"
            />
          </span>

          <span class="list-item-header__shopname">
            {{ `${listItem.shopName}` }}
          </span>

          <van-icon
            v-if="listItem.homeUrl && !isTTApp"
            name="arrow"
            class="list-item-header__link"
          />
        </template>
      </div>

      <div
        class="list-item-header__state"
        style="color: #323233"
        @click="goDetail"
      >
        {{ statueStr }}
      </div>
    </div>
  </div>
</template>

<script>
import * as SafeLink from '@youzan/safe-link';
import ZNB from '@youzan/znb';
import { Toast, Icon } from 'vant';
import get from 'utils/get';
import { isDrugList } from '../../../common/url';

const SHOP_ICON_MAP = {
  // 品牌
  brand: '//b.yzcdn.cn/public_files/80c25e13ccef83035c6f16d31bc93c55.png',
};

export default {
  name: 'list-item-header',

  props: {
    listIndex: {
      type: Number,
      default: 0,
    },
    listItem: Object,
  },

  components: {
    [Icon.name]: Icon,
  },

  data() {
    return {
      SHOP_ICON_MAP,
      isDrugList: isDrugList()
    };
  },

  computed: {
    showBrandCert() {
      const brandCertType = +get(this.listItem, 'brandCertType', 0);
      return [1, 2, 3, 4].includes(brandCertType);
    },
    statueStr() {
      const { medicalRx, status, presaleVoucherStatus,  orderStateStr, orderItems: [orderItem] = [] } = this.listItem;
      const { orderExtra: { hotelOrderType } } = orderItem || {};
      let message = '';
      if (hotelOrderType === '3') {
        switch (true) {
          case status === 100 && presaleVoucherStatus === 100: // 交易完成 && 已使用
            message = '交易成功';
            break;
          case status === 99:
            message = '已关闭';
            break;
          case status === 60 && presaleVoucherStatus === 20: // 已发货 && 已预约
            message = '已预约';
            break;
          case status === 60 && presaleVoucherStatus === 10: // 已发货 && 预约中
            message = '预约中';
            break;
          case status === 60 && presaleVoucherStatus === 0: // 已发货 && 待使用
            message = '待使用';
            break;
          default:
            message = '买家下单';
            break;
        }
      } else {
        message = this.isDrugList ? medicalRx.rxStatusDesc : orderStateStr
      }
      
      return message;
    },

    /**
     * 判断是否是批发订单
     */
    isWholesaleOrder() {
      // 根据每个订单的第一个item的orderExtra.isWholesaleOrder字段判断
      const isWholesale = get(this, 'listItem.orderItems[0].orderExtra.isWholesaleOrder', false);
      return isWholesale;
    },

    isTTApp() {
      return get(window, '_global.miniprogram.isTTApp', false);
    }
  },

  methods: {
    goHome() {
      if (!this.listItem.shopName || !this.listItem.homeUrl) {
        return;
      }

      if (this.isTTApp) {
        return;
      }

      const isAlipayApp = get(window, '_global.miniprogram.isAlipayApp', false);
      const isQQApp = get(window, '_global.miniprogram.isQQApp', false);
      const isXhsApp = get(window, '_global.miniprogram.isXhsApp', false);
      const isKsApp = get(window, '_global.miniprogram.isKsApp', false);

      // 支付宝 QQ小程序 跳转到原生首页
      if (isAlipayApp || isQQApp || isXhsApp || isKsApp) {
        ZNB.navigate({
          aliappUrl: `/pages/home/<USER>/index`,
          qqUrl: `/pages/home/<USER>/index`,
          xhsUrl: `/pages/home/<USER>/index`,
          ksUrl: `/pages/home/<USER>/index`,
          type: 'switchTab',
        });
      } else {
        const { kdtId } = this.listItem;
        SafeLink.redirect({
          url: this.listItem.homeUrl,
          kdtId,
        });
      }
    },

    goDetail() { // 跳转订单详情
      const { orderItems = [] } = this.listItem;
      const { detailUrl } = orderItems[0] || {};
      const orderItem = orderItems[0] || {};
      const isAlipayApp = get(window, '_global.miniprogram.isAlipayApp', false);
      const isQQApp = get(window, '_global.miniprogram.isQQApp', false);
      const isXhsApp = get(window, '_global.miniprogram.isXhsApp', false);
      const isKsApp = get(window, '_global.miniprogram.isKsApp', false);

      if(isAlipayApp || isQQApp || isXhsApp || isKsApp) {
        if (+orderItem.buyWay === 7 && +orderItem.status !== 99) {
          Toast('此渠道暂不支持查看代付订单');
          return;
        }
      }
      // 小红书小程序需要打开新的页面
      if (isXhsApp || isKsApp) {
        ZNB.navigate({
          url: detailUrl,
          xhsUrl: `/pages/web-view/index?refreshOnShow=1&src=${encodeURIComponent(detailUrl)}`,
          ksUrl: `/pages/web-view/index?refreshOnShow=1&src=${encodeURIComponent(detailUrl)}`,
        });
        return;
      }

      SafeLink.redirect({
        url: detailUrl,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.list-item-wholesale-flag {
  width: 56px;
  height: 16px;
  font-size: 12px;
  color: #323233;
  line-height: 16px;
  text-align: center;
  margin-bottom: -8px;
  border-radius: 8px 0 8px 0;
  background-color: #EBEDF0;
}
.list-item-header {
  padding: 16px 12px 4px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &__left {
    display: flex;
    align-items: center;
    width: 0;
    flex: 1;
  }

  &__icon {
    line-height: 0;
    margin-right: 4px;
    flex: none;

    img {
      vertical-align: middle;

      &:not(:last-child) {
        margin-right: 4px;
      }
    }
  }

  &__icon-company {
    height: 19px;
  }

  &__icon-brand {
    height: 16px;
  }

  &__shopname {
    color: #323233;
    line-height: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 15px;
  }

  &__link {
    color: #969799;
    margin-left: 4px;
    font-size: 14px;
  }

  &__state {
    font-family: PingFangSC-Medium;
    text-align: right;
    line-height: 18px;
    font-size: 14px;
    color: #969799;
  }
}
</style>
