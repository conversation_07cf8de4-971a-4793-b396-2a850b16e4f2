const BaseService = require('../base/BaseService');
const mapKeysToCamelCase = require('@youzan/utils/string/mapKeysToCamelCase');

class WechatService extends BaseService {
  get WECHAT_SERVICE() {
    return 'com.youzan.trade.business.wechat.api.WechatPageDispatchService';
  }

  get ACCOUNT_SERVICE() {
    return 'com.youzan.channels.service.WeappAccountService';
  }

  get ORDER_DETAIL_SERVICE() {
    return 'com.youzan.trade.business.wechat.api.OrderDetailPageService';
  }

  async decide(params) {
    const data = await this.invoke(this.WECHAT_SERVICE, 'decide', [params]);
    return mapKeysToCamelCase(data);
  }

  async getWeappAccountBykdtId(params) {
    return this.invoke(this.ACCOUNT_SERVICE, 'getWeappAccountByKdtId', [params]);
  }

  async getWeappCode(accessToken, params) {
    const resp = await this.fetch({
      url: `https://api.weixin.qq.com/wxa/getwxacode?access_token=${accessToken}`,
      method: 'POST',
      data: params
    });
    return resp;
  }

  async checkPhone(params) {
    return this.invoke(this.ORDER_DETAIL_SERVICE, 'checkPhone', [params]);
  }

  async showCheckPhone(params) {
    return this.invoke(this.ORDER_DETAIL_SERVICE, 'showCheckPhone', [params]);
  }
}

module.exports = WechatService;
