/** ************************************************************************************************
 * 阶段支付状态枚举值
 * 参考自: https://gitlab.qima-inc.com/trade-platform/trade-core/blob/fa39a88deae995db71b4a2ce25e655c1aa3e7067/trade-core-api/src/main/java/com/youzan/trade/core/service/constant/PhasePaymentStatusEnum.java
 ************************************************************************************************ */
const PhasePaymentStatusEnum = {
  WAIT_PAY: 10,
  PAID: 20,
  CLOSE: 99,
  SUCCESS: 100,
} as const;

type PhasePaymentStatusEnumKey = keyof typeof PhasePaymentStatusEnum;
type PhasePaymentStatusEnumType = typeof PhasePaymentStatusEnum[keyof typeof PhasePaymentStatusEnum];

class PhasePaymentStatus {
  private static descriptions: { [key in PhasePaymentStatusEnumType]: string } = {
    [PhasePaymentStatusEnum.WAIT_PAY]: "待支付",
    [PhasePaymentStatusEnum.PAID]: "已支付",
    [PhasePaymentStatusEnum.CLOSE]: "已关闭",
    [PhasePaymentStatusEnum.SUCCESS]: "已完成",
  };

  static getValue(type: PhasePaymentStatusEnumType): PhasePaymentStatusEnumType {
    return type;
  }

  static getDesc(type: PhasePaymentStatusEnumType): string {
    return this.descriptions[type];
  }

  static getEnumName(value: PhasePaymentStatusEnumType): string | null {
    const entry = Object.entries(PhasePaymentStatusEnum).find(([_, val]) => val === value);
    return entry ? entry[0] : null;
  }

  static findByValue(value: number): PhasePaymentStatusEnumType | null {
    const entry = Object.entries(PhasePaymentStatusEnum).find(([_, val]) => val === value);
    return entry ? entry[1] as PhasePaymentStatusEnumType : null;
  }

  static getValueByKey(key: PhasePaymentStatusEnumKey): PhasePaymentStatusEnumType | undefined {
    return PhasePaymentStatusEnum[key];
  }
}

export {
  PhasePaymentStatusEnum,
  PhasePaymentStatus,
  PhasePaymentStatusEnumKey,
  PhasePaymentStatusEnumType,
};