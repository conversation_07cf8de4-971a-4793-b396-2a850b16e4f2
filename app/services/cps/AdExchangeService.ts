import BaseService from '../base/BaseService';

interface IAdContentQuery {
  /** 店铺id  必填 */
  kdtId: number;
  /** 用户id */
  userId?: number;
  /** 资源位id  用于获取抽奖图片 */
  resourceId?: number;
}

class AdExchangeService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.ebiz.mall.mediator.api.platform.ad.AllianceAdContentFacade';
  }

  /**
   *  获取广告内容
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1254788
   *
   *  @param {Object} query -
   *  @param {number} query.kdtId - 店铺id 必填
   *  @return {Promise}
   */
  async getAdExchangeContent(query: IAdContentQuery) {
    return this.invoke('getAdExchangeContent', [query]);
  }
}

export default AdExchangeService;
