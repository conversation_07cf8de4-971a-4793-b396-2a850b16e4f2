import BaseService from '../base/BaseService';

interface IOrderExtRequest {
  /** 是否需要订单备注 */
  withRemark?: boolean;
  /** 订单号列表 */
  orderNoList?: string[];
}

class QttOrderService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.qtt.order.api.service.OrderServiceProvider';
  }

  /**
   *  查找订单扩展信息
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1377463
   *
   *  @param {Object} request -
   *  @param {boolean} request.withRemark - 是否需要订单备注
   *  @param {number} request.offset -
   *  @param {string} request.sortType -
   *  @param {string} request.fromApp -
   *  @param {number} request.pageSize - 每页数量 默认20
   *  @param {string} request.sortBy -
   *  @param {number} request.page - 页码
   *  @param {string} request.pageId -
   *  @param {Array.<Array>} request.orderNoList[] - 订单号列表
   *  @param {string} request.operator -
   *  @return {Promise}
   */
  async listOrderExt(request: IOrderExtRequest) {
    return this.invoke('listOrderExt', [request]);
  }
}

export = QttOrderService;
