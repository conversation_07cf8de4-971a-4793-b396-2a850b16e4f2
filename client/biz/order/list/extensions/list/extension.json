{"extensionId": "@wsc-h5-trade/order-list__list", "name": "@wsc-h5-trade/order-list__list", "version": "1.6.3", "pathInBundle": "list-block", "bundle": "https://b.yzcdn.cn/wsc-h5-trade/ext/ranta-extension_70863473.js", "hasWidget": true, "data": {"consume": {"activityTab": ["r", "w"], "showRecommend": ["r"]}}, "event": {"emit": ["doOrderFooterAction"]}, "process": {"invoke": ["fetchMoreOrder"]}, "component": {"provide": ["EmptyTip", "NoMoreTip", "ListItemTag", "ListItemBody", "ListItemHeader", "ListItemTimeTips", "ListItemLogistics"], "consume": ["EmptyTip", "NoMoreTip", "ListItemTag", "ListItemBody", "ListItemHeader", "ListItemTimeTips", "ListItemLogistics"]}, "sandbox": {"id": "", "level": "Unsafe"}}