<template>
  <div class="order-list-item" @click="onItemClick">
    <!-- 商品信息展示区域 -->
    <div ref="body" class="order-list-item__body">
      <goods-card
        v-for="(item, index) in goods"
        :key="index"
        :goods="item"
        :is-prescription-drug-goods="item.isPrescriptionDrugGoods || isDrugList"
        :hotel-detail="hotelDetail"
        :order-item="orderItem"
      />

      <div ref="more-goods" class="order-list-item__more-goods">
        <goods-card
          v-for="(item, index) in moreGoods"
          :key="index"
          :goods="item"
          :hotel-detail="hotelDetail"
          :order-item="orderItem"
        />
      </div>

      <div
        v-if="moreGoods.length && !orderExtra.isHotel"
        ref="more-btn"
        class="order-list-item__more-btn"
        :class="{
          'order-list-item__more-btn_has-show': handleShowMoreGoods,
        }"
        @click.stop="handleShowMoreGoods"
      >
        <div class="order-list-item__more-btn-container">
          <span class="order-list-item__more-text"
            >{{ hasShowMoreGoods ? '收起' : '查看' }}全部{{
              orderItemGoods.length
            }}件商品</span
          >
          <van-icon
            color="#969799"
            :name="`${hasShowMoreGoods ? 'arrow-up' : 'arrow-down'}`"
          />
        </div>
      </div>
    </div>

    <div class="order-list-item__cell">
      <youzan-secured :order-item="orderItem"></youzan-secured>
    </div>

    <div
      v-if="permission.isShowTotalPrice"
      class="order-list-item__total-price"
    >
      <span v-if="promotionPrice" class="order-list-item__discount">
        优惠 ¥{{ promotionPrice }}，
      </span>
      <span> {{ payInfo.amountDesc }}&nbsp; </span>

      <span v-if="pointsPrice">
        {{ pointsPrice }}
      </span>

      <span v-if="pointsPrice && payInfo.payAmount"> + </span>

      <cap-price
        class="order-list-item__pay-amount"
        v-if="payInfo.payAmount || !pointsPrice"
        :price="payInfo.payAmount"
      />
      <span v-if="payInfo.last"> ，{{ payInfo.last }} </span>
    </div>
  </div>
</template>

<script>
import { Icon, Toast } from 'vant';
import { Price } from '@youzan/captain';
import * as SafeLink from '@youzan/safe-link';
import GoodsCard from './GoodsCard';
import YouzanSecured from './YouzanSecured';
import get from 'utils/get';
import formatMoney from '@youzan/utils/money/format';
import buildUrl from '@youzan/utils/url/buildUrl';
import ZNB from '@youzan/znb';
import { isDrugList } from '../../../../common/url';
import { getLevel } from '../../../../scrm-level';
import { CRM_OFFLINE_TYPE } from '../../../../common/constant';
import { Logger } from 'common/log/logger';

const ACTIVITY_TYPE = {
  blindBoxBuy: 401, // 盲盒购买订单
  blindBoxVerification: 402, // 盲盒核销订单
};

export default {
  name: 'order-list-item',

  components: {
    [Icon.name]: Icon,
    [Price.name]: Price,
    [GoodsCard.name]: GoodsCard,
    [YouzanSecured.name]: YouzanSecured,
  },

  props: {
    listIndex: {
      type: Number,
      default: 0,
    },
    orderIndex: {
      type: Number,
      default: 0,
    },
    listItem: Object,
  },

  data() {
    return {
      hasShowMoreGoods: false,
      pointsName: get(window, '_global.pointsName', '积分'),
      isDrugList: isDrugList(),
    };
  },

  computed: {
    permission() {
      return this.listItem.orderPermission || {};
    },

    orderItem() {
      return (
        (this.listItem.orderItems &&
          this.listItem.orderItems[this.orderIndex]) ||
        {}
      );
    },

    orderItemGoods() {
      if (this?.orderExtra.isHotel) return this.orderItem.items.slice(0, 1);

      return this.orderItem.items;
    },

    goods() {
      if (this.orderItemGoods.length <= 10) return this.orderItemGoods;
      return this.orderItemGoods.slice(0, 10);
    },

    moreGoods() {
      if (this.orderItemGoods.length <= 10) return [];

      return this.orderItemGoods.slice(10);
    },

    promotionPrice() {
      const { promotionPrice } = this.orderItem;

      if (promotionPrice) {
        return formatMoney(promotionPrice);
      }

      return 0;
    },

    hotelDetail() {
      // 新酒店商品内容
      return this.orderItem.hotel || {};
    },

    orderExtra() {
      return this.orderItem?.orderExtra || {};
    },
    
    payInfo() {
      return this.orderItem.payInfo || {};
    },

    pointsPrice() {
      const points = this.orderItem.realPointPay;

      if (points) {
        return points + this.pointsName;
      }

      return '';
    },
  },

  mounted() {
    this.initMoreGoods();
    this.initMoreBtnHeight();
  },

  methods: {
    handleShowMoreGoods() {
      const moreGoodsEl = this.$refs['more-goods'];

      if (this.hasShowMoreGoods) {
        moreGoodsEl.style.height = 0;
        this.hasShowMoreGoods = false;
        this.initMoreBtnHeight();
      } else {
        const height = moreGoodsEl.getAttribute('data-height');
        moreGoodsEl.style.height = `${height}px`;
        this.hasShowMoreGoods = true;
        this.removeMoreBtnStyle();
      }
    },

    initMoreGoods() {
      const moreGoodsEl = this.$refs['more-goods'];
      const height = moreGoodsEl.offsetHeight;
      moreGoodsEl.setAttribute('data-height', height);
      moreGoodsEl.style.height = 0;
    },

    initMoreBtnHeight() {
      const listBody = this.$refs.body;
      const moreBtn = this.$refs['more-btn'];
      const goodsCardNo10 = listBody.querySelectorAll('.goods-card')[9];
      if (goodsCardNo10 && moreBtn) {
        const height = goodsCardNo10.offsetHeight;
        moreBtn.style.height = `${height}px`;
        moreBtn.style['margin-top'] = `-${height}px`;
      }
    },

    removeMoreBtnStyle() {
      const moreBtn = this.$refs['more-btn'];
      if (moreBtn) {
         moreBtn.style = '';
      }
    },

    onItemClick() {
      // 跳转订单详情
      const { kdtId: newKdtId = 0, kdt_id: oldKdtId = 0 } = window._global;
      const kdtId = newKdtId || oldKdtId;
      const isAlipayApp = get(window, '_global.miniprogram.isAlipayApp', false);
      const isQQApp = get(window, '_global.miniprogram.isQQApp', false);
      const isXhsApp = get(window, '_global.miniprogram.isXhsApp', false);
      const isKsApp = get(window, '_global.miniprogram.isKsApp', false);

      if (isAlipayApp || isQQApp || isXhsApp || isKsApp) {
        if (+this.orderItem.buyWay === 7 && +this.orderItem.status !== 99) {
          Toast('此渠道暂不支持查看代付订单');
          return;
        }
      }

      // 增加 连续包月会员商品，修改跳转链接
      const payGradeCard = get(this.listItem,'orderItems[0].items[0].payGradeCard', false)
      if (payGradeCard) {
        const goodsId = get(this.listItem, 'orderItems[0].items[0].goodsId', '')
        const skuId = get(this.listItem, 'orderItems[0].items[0].sku[0].vId', '')
        getLevel({kdtId,goodsId, skuId}).then((h5Url) =>{
          if (h5Url) {
          // 跳转到会员页面 需要确认拼接方法
            ZNB.navigate({
              url: h5Url,
              weappUrl: `/pages/common/webview-page/index?src=${encodeURIComponent(
                h5Url
              )}`,
            });
            return;
          }
        })
      }
      // todo：增加判断是否为盲盒订单，修改跳转链接
      const {
        orderNo = '',
        kdtId: orderKdtId = 0,
        statusCode = '',
      } = this.listItem;
      if (
        (+this.orderItem.activityType === ACTIVITY_TYPE.blindBoxBuy ||
          +this.orderItem.activityType ===
            ACTIVITY_TYPE.blindBoxVerification) &&
        statusCode !== 'topay'
      ) {
        if (statusCode === 'cancel') {
          Toast('订单已关闭');
          return;
        }
        const h5Url = buildUrl(
          `/wscump/blind-box/detail?orderNo=${orderNo}&kdtId=${orderKdtId}`,
          'h5',
          orderKdtId
        );
        ZNB.navigate({
          url: h5Url,
          weappUrl: `/pages/common/webview-page/index?src=${encodeURIComponent(
            h5Url
          )}`,
        });
        return;
      }

      if (isAlipayApp && this?.orderExtra.isOfflineOrder) {
        ZNB.navigate({
          aliappUrl: `packages/freego/order-detail/index?kdt_id=${kdtId}&order_no=${this.orderItem.orderNo}`,
        });
        return;
      }

      // 小红书小程序需要打开新的页面
      if (isXhsApp || isKsApp) {
        ZNB.navigate({
          url: this.orderItem.detailUrl,
          xhsUrl: `/pages/web-view/index?refreshOnShow=1&src=${encodeURIComponent(this.orderItem.detailUrl)}`,
          ksUrl: `/pages/web-view/index?refreshOnShow=1&src=${encodeURIComponent(this.orderItem.detailUrl)}`,
        });
        return;
      }

      // CRM线下门店订单详情跳转链接
      if (this.listItem.crmOfflineType === CRM_OFFLINE_TYPE.OFFLINE) {
        const h5Url = buildUrl(
          `/wsctrade/order/detail-crm?order_no=${orderNo}&kdt_id=${orderKdtId}&isCrmOfflineOrder=true&tee_page=true`,
          'h5',
          orderKdtId
        );
        Logger.log({
          et: 'click',
          ei: 'click_crm_offline_order',
          en: '点击线下门店订单',
          pt: 'orderlist',
        });
        ZNB.navigate({
          url: h5Url,
          weappUrl: `/packages/crm/order-detail/index?order_no=${orderNo}&kdt_id=${kdtId}`,
        });
        return;
      }


      SafeLink.redirect({
        url: this.orderItem.detailUrl,
        kdtId,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.order-list-item {
  line-height: 1.4;

  &__cell {
    margin-top: 8px;

    ::v-deep .van-cell:not(:last-child) {
      &::before {
        position: absolute;
        box-sizing: border-box;
        content: ' ';
        pointer-events: none;
        bottom: 0;
        left: 12px;
        right: 12px;
        border-bottom: 1px solid #ebedf0;
        transform: scaleY(0.5);
      }
    }
  }

  &__more-goods {
    overflow: hidden;
    transition: height 0.3s linear;
  }

  &__more-btn {
    line-height: 18px;
    position: relative;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    color: #323233;
    letter-spacing: 0;
    font-size: 12px;
    background: linear-gradient(rgba(255, 255, 255, 0.64), #fff);

    &_has-show {
      margin-top: 12px;
    }
  }

  &__more-btn-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__more-text {
    margin-right: 4px;
  }

  .van-tag + .van-tag {
    margin-left: 4px;
  }

  &__total-price {
    height: 48px;
    padding: 0 12px;
    color: #323233;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-size: 12px;
  }

  &__pay-amount {
    font-size: 0;

    ::v-deep .cap-price {
      color: #323233;

      &__integer {
        font-size: 18px;
      }
    }
  }
}
</style>
