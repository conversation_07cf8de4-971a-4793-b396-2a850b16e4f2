/**
 * 零售订单进度查询接口请求参数
 */
export interface IShelfOrderProgressRequest {
  /** 订单号 */
  orderNo?: string;
  /** 请求来源 */
  retailSource?: string;
  /** 当前店铺id */
  kdtId?: number;
  /** 当前用户id */
  adminId?: number;
}

/**
 * 零售订单进度查询接口响应数据
 */
export interface IShelfOrderProgressResponse {
  /** 是否成功 */
  success?: boolean;
  /** 错误码 */
  code?: string;
  /** 错误信息 */
  message?: string;
  /** 订单进度数据 */
  data?: IOrderProgressData;
}

/**
 * 订单进度数据
 */
export interface IOrderProgressData {
  /** 订单号 */
  orderNo?: string;
  /** 订单状态 */
  orderStatus?: string;
  /** 进度状态 */
  progressStatus?: string;
  /** 进度描述 */
  progressDesc?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 扩展信息 */
  extInfo?: Record<string, any>;
}
