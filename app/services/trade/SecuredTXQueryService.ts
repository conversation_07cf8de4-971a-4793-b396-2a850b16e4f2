import BaseService from '../base/BaseService';

class SecuredTXQueryService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.trade.business.secured.api.SecuredTXQueryService';
  }

  /**
   *  担保交易状态查询
   *  @param {number} kdtId
   *  @return {Promise}
   */
  async querySecuredTransactionsStatus(kdtId: number) {
    return this.invoke('querySecuredTransactionsStatus', [kdtId]);
  }
}

export = SecuredTXQueryService;
