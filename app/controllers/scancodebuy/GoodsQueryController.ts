import BaseController from '../base/BaseController';
import SellProductSearchApiService from '../../services/scancodebuy/SellProductSearchApiService';

class GoodsQueryController extends BaseController {
  async init() {
    await super.init({
      initMpData: false,
      initMpAccount: false,
      initPlatform: false,
      initGlobalTheme: false,
    });
  }

  /**
   * 根据条形码查询商品列表
   */
  async getListProductByBarcodeJson() {
    const { ctx, buyerId } = this;
    const { barcode, offlineId, store_id }: Record<string, any> = ctx.query;
    const { kdtId } = ctx;

    const result = await new SellProductSearchApiService(
      ctx
    ).listProductByBarcode({
      barcode,
      kdtId,
      offlineId: +(offlineId || store_id) || null,
      adminId: buyerId,
      /** 是否展示隐藏商品 */
      withHideProduct: false
    });

    ctx.json(0, 'ok', result);
  }
}

export = GoodsQueryController;
