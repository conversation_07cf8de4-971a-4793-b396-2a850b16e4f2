import { Context } from 'astroboy';
import { ShopCorePlugin } from '@youzan/plugin-h5-shop';
import PosterBaseController from './PosterBaseController';
import OrderDetailService from '../../services/order/OrderDetailService';
import OrderShareService from '../../services/order/OrderShareService';
import { getTradeApolloConfig } from '../../lib/Apollo';
import { IOrderDetailReq } from 'definitions/order/OrderDetail';
import FinComponentQueryService from '../../services/pay/FinComponentQueryService';
import get from 'lodash/get';
import { formatPosterThemeColor } from './utils/format';
/**
 * 订单分享海报
 */
class PosterOrderShareController extends PosterBaseController {
  async init() {
    await super.initMpData();
    await super.initShopMetaInfo();
    await this.checkQueryUrl(this.ctx);
  }

  // 获取订单详情
  async getOrderDetail() {
    const { ctx } = this;
    const { buyer } = this;
    const { query, kdtId = 0 } = ctx;
    const orderNo = query.order_no;

    const params: IOrderDetailReq = {
      orderNo,
      kdtId,
      buyerId: buyer.buyerId,
      customerId: buyer.fansId,
      customerType: buyer.fansType,
      sourceName: this.sourceName,
    };
    const data = await new OrderDetailService(ctx).lightDetailByOrderNo(params);

    return data;
  }

  // 初始化绘制海报所需信息
  async initState(ctx: Context) {
    // @ts-ignore
    const mpData: { shopName?: string } = ctx.getState('mpData') || {}; // 获取店铺信息
    const orderDetail = await this.getOrderDetail(); // 获取订单信息

    // 取订单中的第一件商品绘制海报
    const firstGoods = orderDetail.itemInfo[0];
    // 商品标题和图片url
    const { title = '', imgUrl = '' , alias } = firstGoods.goodsInfo;
    // 商品价格与原价（以小数点分割）
    const unitPrice = (firstGoods.unitPrice / 100).toFixed(2).split('.') as [
      string,
      string
    ];
    const originUnitPrice = (firstGoods.originUnitPrice / 100)
      .toFixed(2)
      .split('.') as [string, string];
    // 店铺名
    const shopName = mpData.shopName
      ? mpData.shopName.length >= 10
        ? mpData.shopName.slice(0, 9) + '...'
        : mpData.shopName
      : '';

    const orderShareShowOriginPrice = getTradeApolloConfig(
      'wsc-h5-trade.application',
      'orderShareShowOriginPrice'
    );
    const showOriginPrice =
      // @ts-ignore
      orderShareShowOriginPrice
        .split(/[\t\s,]+/)
        .indexOf(orderDetail.mainOrderInfo.kdtId.toString()) > -1;

    // 订单没有优惠价格的时候展示划线价格
    let crossLinePrice = '';
    let showCrossLinePrice = false;
    if (+firstGoods.unitPrice === +firstGoods.originUnitPrice) {
      const goodsParams = {
        containItemMark: true,
        containsContent: false,
        itemIds: [firstGoods.goodsId],
        kdtId: ctx.kdtId,
        withDelete: false,
      };
      const goodInfo = await new OrderShareService(ctx).getGoodsInfo(
        goodsParams
      );
      if (
        goodInfo instanceof Array &&
        goodInfo.length === 1 &&
        goodInfo[0].origin
      ) {
        showCrossLinePrice = true;
        crossLinePrice = goodInfo[0].origin;
      }
    }
   const params = {
    aliases: [
      alias
    ]
   }
   const assertSecuredInfo = await new FinComponentQueryService(ctx).batchQuerySupportSecured(params).catch(() => ({}))
   const isAssertSecured = get(assertSecuredInfo,'securedItems[0].yzSecured',false);
    // 全局颜色
   const { globalTheme } = await new ShopCorePlugin(ctx as any).readGlobalTheme();
   const themeColor = formatPosterThemeColor(globalTheme);
    ctx.setState({
      title,
      imgUrl,
      shopName,
      isAssertSecured,
      unitPrice,
      themeColor,
      originUnitPrice,
      showOriginPrice,
      showCrossLinePrice,
      crossLinePrice,
    });
    ctx.posterOpt = {
      type: 'order-share',
      more: {
        width: 300,
        height: 456,
      },
    };
  }
}

export = PosterOrderShareController;
