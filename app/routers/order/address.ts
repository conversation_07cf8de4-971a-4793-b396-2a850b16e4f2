export = [
  [
    'GET',
    '/wsctrade/order/address/list',
    'order.OrderAddressController',
    ['needPlatformAcl', 'getListHtml']
  ],
  [
    'GET',
    '/wsctrade/order/address/edit',
    'order.OrderAddressController',
    ['needPlatformAcl', 'getEditHtml']
  ],
  [
    'GET',
    '/wsctrade/order/tee-address/edit',
    'order.OrderAddressController',
    ['getRantaEditHtml']
  ],
  [
    'GET',
    [
      '/wsctrade/order/tee-address/edit/address-map',
      '/wsctrade/order/tee-address/edit/address-city',
    ],
    'order.OrderAddressController',
    ['subPageRedirect'],
  ],
  [
    'GET',
    '/wsctrade/order/address/address-list.json',
    'order.OrderAddressController',
    ['getAddressList']
  ],
  [
    'POST',
    '/wsctrade/order/address/modify.json',
    'order.OrderAddressController',
    ['modifyOrderAddress']
  ],
  [
    'POST',
    '/wsctrade/order/getShopConfigs.json',
    'order.OrderAddressController',
    ['getShopConfigs']
  ],
  [
    'GET',
    '/wsctrade/order/address/address-list-v2.json', // 包含同城配送订单校验的地址列表
    'order.OrderAddressController',
    ['getOrderAddressList']
  ],
  [
    'GET',
    '/wsctrade/order/address/delivery-time-bucket.json',
    'order.OrderAddressController',
    ['getDeliveryTimeBucket']
  ],
  [
    'POST',
    '/wsctrade/order/address/order-address-info.json',
    'order.OrderAddressController',
    ['updateOrderAddressInfo']
  ]
];
