{% extends 'common/base.html' %}

{% block head_script %}
  {% include 'hummer/init.html' %}
  {% include 'console/init.html' %}
  {% include 'lang-i18n/init.html' %}
{% endblock %}

{% block cdn_fallback %}
  {{ inlineJs('@youzan/cdn-fallback', 'dist/index.js') | safe }}
{% endblock %}

{% block base_style %}
  {{ loadCss('dll/component') | safe }}
  {{ loadCss('vendors') | safe }}
  {% if globalThemeVars %}
  <style type="text/css">
    html { 
      {{ globalThemeVars }}
     }
  </style>
  {% endif %}
{% endblock %}

{% block base_script %}
  {% block before_dll_script %}{% endblock %}
  {% include './sentry_script.html' %}
  {{ loadJs('dll/framework') | safe }}
  {{ loadJs('dll/library') | safe }}
  {{ loadJs('dll/component') | safe }}
  {{ loadJs('vendors') | safe }}
  {% include 'yun-shop-config/extra-operating-area.html' %}
{% endblock %}

{% block project_script %}
  <script src="{{versionUrl}}"></script>
{% endblock %}

{% block base_footer %}
  {% if footerHtml %}
    <div class="common-footer-components">
      {% block page_biz_footer %}
      {% endblock %}
      {{ footerHtml | safe }}
      <style>.common-footer-components .footer .copyright { background-color: #f7f8fa; }</style>
    </div>
  {% endif %}
{% endblock %}
