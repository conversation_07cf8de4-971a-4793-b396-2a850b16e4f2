import { Context } from 'astroboy';
import get from 'lodash/get';
import BaseController = require('../base/BaseController');
import OrderPayPromptService = require('../../services/order/OrderPayPromptService');

type orderPayPromptParams = {
  kdtId: number;
  buyerId: number;
  source?: string;
};

class OrderPayPromptController extends BaseController {
  /**
   * 获取催单弹窗数据
   * @param ctx
   */
  public async orderPayPromptReceive(ctx: Context): Promise<void> {
    const { kdtId, isWeapp } = ctx;
    const { source } = ctx.getQueryData();
    const orderPayPrompt = this.getOrderPayPromptTime();
    const hidePromptPopup = orderPayPrompt && orderPayPrompt.kdtId === kdtId && Date.now() < orderPayPrompt.expireTime;

    // 如果30分钟内&同店铺已显示过弹窗，则直接阻止二次弹出(排除微信小程序限制)
    if (!isWeapp && hidePromptPopup) {
      return ctx.json(0, 'ok', {
        popup: false,
        abTraceId: null,
      });
    }

    const params = {
      kdtId,
      buyerId: this.buyerId,
    } as orderPayPromptParams;

    if (source) {
      params.source = source;
    }

    try {
      const data = (await new OrderPayPromptService(ctx).getOrderPayPrompt(params)) || {
        popup: false,
        abTraceId: null,
      };

      if (data?.popup) {
        const abConfig = (await this.ctx.ABTestClient?.getTest('order_prompt_popup', String(this.buyerId))) || {};
        const hasPrompt = abConfig.isValid ? get(abConfig, 'configurations.hasPrompt', false) : false;
        data.abTraceId = abConfig?.abTraceId;

        // 如果后端允许弹出&abtest不曝光，则改变popup = false
        if (!hasPrompt) {
          data.popup = false;
        }
      }

      ctx.json(0, 'ok', data);
    } catch (e) {
      ctx.json(0, 'ok', {
        popup: false,
        abTraceId: null,
      });
    }
  }

  /**
   * 记录催单弹窗提醒时间（by kdtId）
   * @param ctx
   */
  public async recordOrderPayPromptTime(ctx: Context): Promise<void> {
    const { kdtId } = ctx;

    await this.getService('iron-base', 'uic.OpenSessionService').set('od_pay_prompt', {
      kdtId,
      expireTime: Date.now() + 30 * 60 * 1000,
    });

    ctx.json(0, 'ok');
  }

  /**
   * 设置催单弹窗时间(by kdtId)
   * @param kdtId 店铺id
   * @returns 催单提醒时间戳（毫秒）
   */
  public getOrderPayPromptTime() {
    return this.ctx.getLocalSession('od_pay_prompt');
  }
}

export = OrderPayPromptController;
