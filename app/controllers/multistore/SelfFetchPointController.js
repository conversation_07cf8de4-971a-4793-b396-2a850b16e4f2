const BaseController = require('../base/BaseController');
const GpsService = require('../../services/delivery/GpsService');
const DeliveryService = require('../../services/trade/DeliveryService');

class SelfFetchPointController extends BaseController {
  async getCityJson(ctx) {
    const {query} = ctx.request;

    // 兼容 lng 和 lon 两种写法
    const params = [+(query.lng || query.lon), +query.lat, 3];

    const data = await new GpsService(ctx).getReGpsInfo(params);

    ctx.json(0, '', {
      cityName: data.city,
      cityCode: String(data.adCode)
    });
  }

  async getCityMapJson(ctx) {
    const data = await new DeliveryService(ctx).getCityMap(ctx.kdtId);
    ctx.json(0, '', data);
  }
}

module.exports = SelfFetchPointController;
