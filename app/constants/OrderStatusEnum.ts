/** ************************************************************************************************
 * 订单状态枚举值
 * 参考自: https://gitlab.qima-inc.com/trade-platform/trade-core/blob/33062c164a1576d80a333ef27704b647efdd9c85/trade-core-api/src/main/java/com/youzan/trade/core/service/constant/OrderStatusEnum.java
 ************************************************************************************************ */
enum OrderStatusEnum {
  CREATED = 1,
  WAIT_PAY = 10,
  PAID = 20,
  WAIT_CONFIRM = 30,
  CONFIRMED = 40,
  WAIT_SHIPPED = 50,
  SHIPPED = 60,
  WAIT_RECEIVED = 70,
  RECEIVED = 80,
  UNVISIBLE = 98,
  CLOSED = 99,
  SUCCESS = 100,
}

class OrderStatus {
  private static descriptions: { [key in OrderStatusEnum]: string } = {
    [OrderStatusEnum.CREATED]: "已下单",
    [OrderStatusEnum.WAIT_PAY]: "待支付",
    [OrderStatusEnum.PAID]: "已支付",
    [OrderStatusEnum.WAIT_CONFIRM]: "待接单",
    [OrderStatusEnum.CONFIRMED]: "已接单",
    [OrderStatusEnum.WAIT_SHIPPED]: "待发货",
    [OrderStatusEnum.SHIPPED]: "已发货",
    [OrderStatusEnum.WAIT_RECEIVED]: "待收货",
    [OrderStatusEnum.RECEIVED]: "已收货",
    [OrderStatusEnum.UNVISIBLE]: "不可见",
    [OrderStatusEnum.CLOSED]: "已关闭",
    [OrderStatusEnum.SUCCESS]: "已完成",
  };

  static getDesc(type: OrderStatusEnum): string {
    return this.descriptions[type];
  }

  static getEnumName(value: number): string | null {
    const entry = Object.entries(OrderStatusEnum).find(([_, val]) => val === value);
    return entry ? entry[0] : null;
  }
}

export {
  OrderStatusEnum,
  OrderStatus,
}