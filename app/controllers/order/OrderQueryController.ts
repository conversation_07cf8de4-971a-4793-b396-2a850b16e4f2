import BaseController from '../base/BaseController';
import { Context } from 'astroboy';
import OrderQueryService = require('../../services/order/OrderQueryService');
import DeliveryQueryService = require('../../services/delivery/DeliveryQueryService');
import PayTradingQueryService = require('../../services/order/PayTradingQueryService');

class OrderQueryController extends BaseController {
  // 获取实时订单状态
  async querySimpleOrderByOrderNo(ctx: Context) {
    const { orderNo } = ctx.getQueryData();
    const result = await new OrderQueryService(ctx).querySimpleOrderByOrderNo(orderNo);

    ctx.json(0, 'ok', result);
  }

  async convertPaymentOrderNo(ctx: Context) {
    const { orderNo } = ctx.getQueryData();
    const result = await new PayTradingQueryService(ctx).querySalemanUpgradeResult({
      assetDetailNo: orderNo,
    });

    ctx.json(0, 'ok', result);
  }

  async queryMpShipState(ctx: Context) {
    const { orderNo, outerTransactionNo } = ctx.getQueryData();
    const result = await new DeliveryQueryService(ctx).queryMpShipState({
      orderNo,
      outerTransactionNo,
      kdtId: +ctx.kdtId,
    });

    ctx.json(0, 'ok', result);
  }
}

export = OrderQueryController;
