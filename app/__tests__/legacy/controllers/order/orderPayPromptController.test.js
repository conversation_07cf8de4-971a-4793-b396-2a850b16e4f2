import { app } from '@youzan/astroboy-mock/bootstrap';
import BaseController from 'app/controllers/base/BaseController';
import OrderPayPromptService from 'app/services/order/OrderPayPromptService';

jest.mock('app/controllers/base/BaseController', () => jest.requireActual('app/__tests__/__mocks__/BaseController'));

jest.mock('app/services/order/OrderPayPromptService');

afterEach(() => {
  BaseController.mockClear();
  OrderPayPromptService.mockClear();
});

const API = {
  payPromptReceive: '/wsctrade/order/pay-prompt-receive.json',
  recordOrderPayPromptTime: '/wsctrade/order/record-prompt-time.json',
};

const kdtId = 59415891; // 测试的指定店铺

const requestResult = (data = { popup: false, abTraceId: null }, code = 0, msg = 'ok') => {
  const res = {
    code,
    msg,
  };
  if (data) {
    res.data = data;
  }
  return res;
};

const mockService = (data = null) => {
  OrderPayPromptService.mockImplementation(() => {
    return {
      getOrderPayPrompt: jest.fn().mockResolvedValue(data),
    };
  });
};

const mockSession = (kdtId, expireTime) => {
  app.mockLocalSession({
    od_pay_prompt: {
      kdtId,
      expireTime,
    },
  });
};

test('should response when request pay-prompt-receive.json', async () => {
  app.mockContext({
    kdtId,
  });

  mockService(null);

  const res = await app.httpRequest().get(API.payPromptReceive);
  expect(res.body).toEqual(requestResult());
});

test('should return correct result when request pay-prompt-receive.json', async () => {
  app.mockContext({
    kdtId,
  });

  mockService();
  let res = await app.httpRequest().get(API.payPromptReceive);
  expect(res.body).toEqual(requestResult());

  // 不是当前店铺
  mockSession(123, Date.now() + 30 * 60 * 1000);
  res = await app.httpRequest().get(API.payPromptReceive);
  expect(res.body).toEqual(requestResult());

  // 当前店铺 但已经过期
  mockSession(kdtId, Date.now() - 1 * 1000);
  res = await app.httpRequest().get(API.payPromptReceive);
  expect(res.body).toEqual(requestResult());

  // 当前店铺 && 未过期
  mockSession(kdtId, Date.now() + 30 * 60 * 1000);
  res = await app.httpRequest().get(API.payPromptReceive);
  expect(res.body).toEqual(
    requestResult({
      abTraceId: null,
      popup: false,
    })
  );
});

test('should record od_pay_prompt when request record order-pay-prompt-time.json', async () => {
  app.mockContext({
    kdtId,
  });

  const res = await app.httpRequest().post(API.recordOrderPayPromptTime);
  expect(res.body).toEqual(requestResult(null));
});
