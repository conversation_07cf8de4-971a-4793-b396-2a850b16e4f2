// 是否为对象
function isObj(x) {
  const type = typeof x;
  return x !== null && (type === 'object' || type === 'function');
}
// 是否为空对象
export function isEmptyObject(obj) {
  return isObj(obj) && !Object.keys(obj).length;
}

function padZero(num) {
  return (num < 10 ? '0' : '') + num;
}

export function formatTime(date, format = 'hh:mm') {
  return format
    .replace('hh', padZero(date.getHours()))
    .replace('mm', padZero(date.getMinutes()))
    .replace('ss', padZero(date.getSeconds()));
}

export function formtDate(date, format = 'yyyy-mm-dd') {
  return format
    .replace('yyyy', date.getFullYear())
    .replace('mm', padZero(date.getMonth() + 1))
    .replace('dd', padZero(date.getDate()));
}

export function getMonthDay(date, format = 'yy月dd日') {
  return format.replace('yy', padZero(date.getMonth() + 1)).replace('dd', padZero(date.getDate()));
}

// 格式化总价格 (金额 + 积分)
export const formatTotalPrice = (price = 0, points = 0, pointsName) => {
  const totalArr = [];
  (price || !points) && totalArr.push(`￥${(price / 100).toFixed(2)}`);
  points && totalArr.push(`${points}${pointsName || '积分'}`);
  return totalArr.join(' + ');
};

function getGoodsPropertiesStr(properties = []) {
  const propertiesArr = [];

  properties.forEach((currentProperty) => {
    const propValueList = currentProperty?.propValueList || [];
    propValueList.forEach((currentValue) => {
      // @ts-ignore
      propertiesArr.push(currentValue.propValueName);
    });
  });

  return propertiesArr.join('，');
}

export function getGoodDesc(item = {}) {
  // 会员卡续费，显示时间范围
  const startTime = item.memberCardStartTime;
  const endTime = item.memberCardEndTime;
  if (startTime && endTime) {
    return startTime + '-' + endTime;
  }

  let { sku } = item;
  // eslint-disable-next-line
  sku = Array.isArray(sku) ? sku : sku && typeof sku === 'string' ? JSON.parse(sku) : [];

  const skuStr = sku
    .filter((item) => item.v)
    .map((item) => item.v)
    .join(', ');

  const goodsPropertiesStr = getGoodsPropertiesStr(item.properties);

  return [skuStr, goodsPropertiesStr].filter((item) => !!item).join(', ');
}

// 拼团代收方式
export const grouponType = {
  forbidReceive: 0,
  optionalReceive: 1,
  forceReceive: 2,
};

export const HOTEL_ORDER_TYPE = {
  // 日历房
  calendar: 1,
  // 预订单
  preOrder: 2,
  // 预售单
  preSale: 3,
};

export const activityMap = {
  3: '降价拍',
  4: '拼团',
  6: '秒杀',
  8: '赠品',
  10: '会员折扣',
  11: '限时折扣',
  23: '抽奖团',
  24: '换购',
  26: '拼团', // 阶梯团
};
