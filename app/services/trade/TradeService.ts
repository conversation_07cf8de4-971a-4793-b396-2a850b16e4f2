import mapKeysToCamelCase from '@youzan/utils/string/mapKeysToCamelCase';
import BaseService from '../base/BaseService';
import { OrderPaymentPreparationDTO } from 'definitions/order/buy/OrderPaymentPreparationDTO';
import { SimpleOrderPreparationVO } from 'definitions/order/buy/SimpleOrderPreparationVO';
import { SimpleOrderConfirmationVO } from 'definitions/order/buy/SimpleOrderConfirmationVO';
import { SimpleOrderPaymentPreparationVO } from 'definitions/order/buy/SimpleOrderPaymentPreparationVO';
import { TradePayRequestDTO } from 'definitions/order/buy/TradePayRequestDTO';
import { TradePayResponseVO } from 'definitions/order/buy/TradePayResponseVO';
import { OrderCreationVO } from 'definitions/order/buy/OrderCreationVO';
import { AsyncOrderCreateVO } from 'definitions/order/buy/AsyncOrderCreateVO';
import { QueryExchangeGoodsParamsDTO } from 'definitions/order/buy/QueryExchangeGoodsParamsDTO';
import { IPlusBuyExchangeVO } from 'definitions/order/buy/IPlusBuyExchangeVO';
import { INewHotelExtensions } from 'definitions/order/buy/INewHotelExtensions';
import { getDiscountPlatform } from '../../utils/index';

interface IKdtId {
  kdtId: number;
}

function parseJSON(str: string) {
  if (str && typeof str === 'string') {
    try {
      return JSON.parse(str);
    } catch (err) {
      // eslint-disable-next-line no-console
      console.log(err);
      return '';
    }
  }
  return str;
}

// 下单接口单独定义超时，主要处理商品超多时等接口超时严重情况
const BILL_INVOKE_TIMEOUT_CONFIG = {
  timeout: 4000, // ajax超时
  headers: {
    'X-Timeout': 4000, // tether超时
  },
};

class TradeService extends BaseService {
  get MALL_TRADE_SERVICE() {
    return 'com.youzan.ebiz.mall.trade.buyer.api.service.TradeService';
  }

  get MALL_TRADE_PAY_OPEN_SERVICE() {
    return 'com.youzan.ebiz.mall.trade.buyer.api.service.TradePayOpenService';
  }

  get MALL_RIGEL_HOTEL() {
    return 'com.youzan.ebiz.mall.rigel.api.hotel.service.HotelBookingService';
  }

  get DELIVERY_APOLLO_CONFIG() {
    return 'com.youzan.ebiz.mall.trade.buyer.api.service.DeliveryApolloConfigService';
  }

  // 判断是否关注后才能购买
  public async assertCanBuyWithoutSubscribe() {
    const { kdtId, platform, userAgent } = this.ctx;
    const { is_fans } = this.ctx.getLocalSession();
    const params = {
      kdtId,
      platform,
      fans: is_fans === 1, // 0：取消关注 1：已关注 2：其他
      userAgent,
    };

    return this.invoke(this.MALL_TRADE_SERVICE, 'assertCanBuyWithoutSubscribe', [params]);
  }

  // 获取下单页数据
  public async prepare(params: any) {
    const data = await this.invoke(this.MALL_TRADE_SERVICE, 'prepare', [params], BILL_INVOKE_TIMEOUT_CONFIG);

    return this.formatShopItem(data);
  }

  // 获取下单页数据
  public async prepareV2(params: any): Promise<SimpleOrderPreparationVO> {
    const data = await this.invoke(this.MALL_TRADE_SERVICE, 'prepareV2', [params], {
      allowBigNumberInJSON: true,
      ...BILL_INVOKE_TIMEOUT_CONFIG,
    });
    return this.formatShopItem(data);
  }

  /**
   *  根据bookKey跨网店构建下单信息缓存
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1306486
   *
   *  @param {Object} orderReCacheDTO -
   *  @param {number} orderReCacheDTO.targetKdtId - 目标店铺ID
   *  @param {string} orderReCacheDTO.bookKey - 当前下单页bookKey
   *  @param {number} orderReCacheDTO.buyerId - 买家ID（越权校验）
   *  @return {Promise}
   */
   async crossStoreReCacheOrderCreation(orderReCacheDTO: Record<string, string|number>) {
    return this.invoke(this.MALL_TRADE_SERVICE, "crossShoreReCacheOrderCreation", [orderReCacheDTO]);
  }

  // 获取下单页数据 返回字符串格式
  public async prepareFast(params: any): Promise<string> {
    const data = await this.invokeFast(this.MALL_TRADE_SERVICE, 'prepareV2', [params], {
      responseType: 'text',
      ...BILL_INVOKE_TIMEOUT_CONFIG,
    });
    return data;
  }

  // 更新下单页数据
  public async confirm(params: any) {
    params = this.formatDeliveryParams(mapKeysToCamelCase(params));
    const data = await this.invoke(this.MALL_TRADE_SERVICE, 'confirm', [params], BILL_INVOKE_TIMEOUT_CONFIG);
    return this.formatShopItem(data);
  }

  // 更新下单页数据
  public async confirmV2(params: any): Promise<SimpleOrderConfirmationVO> {
    params = this.formatDeliveryParams(params);
    params = this.formatPlatformParams(params);
    const data = await this.invoke(this.MALL_TRADE_SERVICE, 'confirmV2', [params], {
      allowBigNumberInJSON: true,
      ...BILL_INVOKE_TIMEOUT_CONFIG,
    });
    return this.formatShopItem(data);
  }

  // 更新下单页数据
  public async confirmV2Fast(params: any): Promise<string> {
    params = this.formatDeliveryParams(params);
    params = this.formatPlatformParams(params);
    const data = await this.invokeFast(this.MALL_TRADE_SERVICE, 'confirmV2', [params], {
      responseType: 'text',
      ...BILL_INVOKE_TIMEOUT_CONFIG,
    });
    return data;
  }

  // 读取物流的Apollo配置【同城无现货预定是否走新逻辑】
  public async getDeliveryApolloConfig(params: IKdtId): Promise<string> {
    return this.invoke(this.DELIVERY_APOLLO_CONFIG, 'isNewProcess', [params]);
  }

  // 下单挽留
  public async letBuyerStay(params: any): Promise<string> {
    return this.invoke(this.MALL_TRADE_SERVICE, 'letBuyerStay', [params]);
  }

  // 创建订单
  public async create(params: any): Promise<OrderCreationVO> {
    params = this.formatDeliveryParams(mapKeysToCamelCase(params));
    return this.invoke(this.MALL_TRADE_SERVICE, 'create', [params], BILL_INVOKE_TIMEOUT_CONFIG);
  }

  // 创建订单 https://zanapi.qima-inc.com/site/service/view/44853
  public async createFast(params: any): Promise<OrderCreationVO> {
    params = this.formatDeliveryParams(mapKeysToCamelCase(params));
    return this.invokeFast(this.MALL_TRADE_SERVICE, 'create', [params], {
      responseType: 'text',
      ...BILL_INVOKE_TIMEOUT_CONFIG,
    });
  }

  // 异步创建订单
  public async createAsync(params: any): Promise<AsyncOrderCreateVO> {
    params = this.formatDeliveryParams(mapKeysToCamelCase(params));
    return this.invoke(this.MALL_TRADE_SERVICE, 'createAsync', [params]);
  }

  // 查询异步创建订单结果
  public async queryAsyncOrderResult(params: any): Promise<OrderCreationVO> {
    return this.invoke(this.MALL_TRADE_SERVICE, 'queryAsyncOrderResult', [params]);
  }

  // 支付收单
  public async prepay(params: TradePayRequestDTO): Promise<TradePayResponseVO> {
    return this.invoke(this.MALL_TRADE_SERVICE, 'pay', [params]);
  }

  /**
   * 获取支付页数据
   * @deprecated
   */
  public async preparePayment(params: any) {
    const data = await this.invoke(this.MALL_TRADE_SERVICE, 'preparePayment', [params]);
    return this.formatShopItem(data);
  }

  // 获取支付页数据
  public async preparePaymentV2(params: OrderPaymentPreparationDTO): Promise<SimpleOrderPaymentPreparationVO> {
    const data = await this.invoke(this.MALL_TRADE_SERVICE, 'preparePaymentV2', [params], {
      allowBigNumberInJSON: true,
    });
    return this.formatShopItem(data);
  }

  // 获取附近可送门店
  public async matchOffline(params: any) {
    params = mapKeysToCamelCase(params);
    return this.invoke(this.MALL_TRADE_SERVICE, 'matchOffline', [params]);
  }

  /**
   *  缓存需要下单的信息
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/212661
   *
   *  @param {Object} params -
   *  @param {Object} params.delivery - 配送
   *  @param {Object} params.ump - 优惠
   *  @param {Object} params.source - 来源
   *  @param {Array.<Object>} params.items[] - 商品
   *  @param {Object} params.config - 设置
   *  @param {Array.<Object>} params.sellers[] - 卖家
   *  @param {Object} params.buyer - 买家
   *  @return {Promise}
   */
  public async cacheOrderCreation(params: any) {
    return this.invoke(this.MALL_TRADE_SERVICE, 'cacheOrderCreation', [params]);
  }

  /**
   *  商品页缓存需要下单的信息
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/329561
   *
   *  @param {string} postJson -
   *  @return {Promise}
   */
  public async cacheOrderCreationJson(postJson: string) {
    return this.invoke(this.MALL_TRADE_SERVICE, 'cacheOrderCreationJson', [postJson]);
  }

  // 格式化店铺数据
  private formatShopItem(data: any = {}) {
    if (data.displayConfig) {
      data.displayConfig.serverTime = Date.now();
    }

    const shopItem = data.tradeConfirmation || data.shopItem;
    if (shopItem && shopItem.orderItems) {
      shopItem.orderItems.forEach((item: any) => {
        item.sku = parseJSON(item.sku);
        item.message = parseJSON(item.message);
      });
    }
    return data;
  }

  // 格式化地址入参数据
  private formatDeliveryParams(params: any) {
    const { delivery } = params;
    if (delivery && delivery.address) {
      delivery.address.recipients = delivery.address.userName;
    }
    return params;
  }

  private formatPlatformParams(params: any) {
    params.source = {
      ...(params.source || {}),
      platform: getDiscountPlatform(this.ctx),
    }
    // 私域直播订单，强制修改platform，用来过滤私域直播优惠券
    const { source = {} } = params;
    const { orderMark = '' } = source;
    if (orderMark === 'private_live') {
      params.source.platform = 'pl_live';
    }
    return params;
  }

  public async codPay(orderNo: string): Promise<any> {
    const params = {
      orderNoList: [orderNo],
      payType: 'CASH_ON_DELIVERY',
    };
    return this.invoke(this.MALL_TRADE_PAY_OPEN_SERVICE, 'decidePayType', [params]);
  }

  public queryPlusBuyExchangeInfo(params: QueryExchangeGoodsParamsDTO): Promise<IPlusBuyExchangeVO> {
    return this.invoke(this.MALL_TRADE_SERVICE, 'queryPlusBuyExchangeInfo', [params]);
  }

  // 获取酒店订单额外参数
  public async getHotelBooking(params: any): Promise<INewHotelExtensions> {
    return this.invoke(this.MALL_RIGEL_HOTEL, 'getHotelBooking', [params]);
  }

  /**
   *  修改订单缓存信息
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1173857
   *
   *  @param {Object} orderModifyCacheDTO - {@link OrderModifyCacheDTO}
   *  @param {string} orderModifyCacheDTO.bookKey - 需要修改的bookKey
   *  @param {number} orderModifyCacheDTO.buyerId - 越权校验
   *  @param {Array.<Object>} orderModifyCacheDTO.modifyCacheItems[] - 新增的商品信息
   *  @return {Promise}
   */
  async modifyCacheOrder(orderModifyCacheDTO: any) {
    return this.invoke(this.MALL_TRADE_SERVICE, 'modifyCacheOrder', [orderModifyCacheDTO]);
  }

  /**
   *  查询缓存里的订单信息，商品列表
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/516118
   *
   *  @param {string} bookKey -
   *  @return {Promise}
   */
  async queryMultiOrderPreparationV2(bookKey: string) {
    return this.invoke(this.MALL_TRADE_SERVICE, 'queryMultiOrderPreparationV2', [bookKey]);
  }
}

export = TradeService;
