import { Dialog } from 'vant'
import args from '@youzan/utils/url/args';
import * as log from './log';
import OrderActionComponent from 'common/order-action';
import makeRandomString from '@youzan/utils/string/makeRandomString';

const OrderAction = new OrderActionComponent();
const isXhsLocalLife = args.get('isXhsLocalLife');

export class ButtonHandler {
  constructor({ orderNo, orderItem, button, kdtId, extraData, directBuyAgainBtnConfig, repurchaseCoupon, orderPermission}) {
    const { orderExtra, buyWay, status } = orderItem
    this.orderItem = orderItem || {}
    this.button = button
    this.orderExtra = orderExtra
    this.directBuyAgainBtnConfig = directBuyAgainBtnConfig
    this.extraData = extraData
    this.repurchaseCoupon = repurchaseCoupon
    this.orderPermission = orderPermission;
    this.prettyFormData = {
      order_no: orderNo,
      kdt_id: kdtId,
      orderExtra,
      buyWay,
      status
    }
    this.buyerFormData = {
      order_no: orderNo,
      kdt_id: kdtId,
      buyer_id:Number(orderItem.buyerId)
    }

    this.bigFormData = {
      ...this.prettyFormData,
      goodsList: this.orderItem.items.map((goodsItem) => ({
        title: goodsItem.title,
        sku_id: goodsItem.skuId,
        sku: goodsItem.sku,
        num: goodsItem.num,
        goods_id: goodsItem.goodsId,
        url: goodsItem.url,
      }))
    }
  }

  // 获取跳转链接
  getDirectUrl() {
    const getUrl = () => {
      const { orderItem, button } = this
      if (button && button.url) {
        return button.url
      }

      return orderItem.detailUrl
    }

    const url = getUrl();

    if (isXhsLocalLife) {
      return args.add(url, { isXhsLocalLife });
    }

    return url;
  }

  // 取消订单
  cancelPayOnDelivery() {
    Dialog.alert({
      message: '商家可能已经接单配货，你需联系客服申请取消订单哟',
      confirmButtonText: '我知道了',
    });
  }

  // 延长收货
  laterReceive() {
    const { prettyFormData } = this
    log.LOG_DELAY_GOODS()
    return OrderAction.extend({
      checkUrl: '/wsctrade/order/checkOrderDelayReceive.json',
      url: '/wsctrade/order/delayReceive.json',
      data: prettyFormData,
      method: 'POST',
    })
  }

  // 确认收货
  confirmReceive() {
    const { btnType, prettyFormData, orderExtra, orderItem, orderPermission } = this
    if (btnType === 'confirm-receive') {
      log.LOG_CONFIRM_GOODS(orderItem.orderNo);
    }
    let message = orderItem.buyWay === 1 ? 
      '请确保已收到商品并检查无误。该订单货款已直接给商家，售后问题需要联系商家解决。' :
      '请确保已收到商品并检查无误。确认收货后有赞将结算货款给商家，商家可立刻提现，售后问题需要联系商家解决。'
    if (orderExtra.isHotel) {
      message = orderItem.buyWay === 1 ? 
      '请确保已入住，确认入住后有赞将结算货款给商家，商家可立刻提现，售后问题需要联系商家解决。' :
      '请确保已入住，该订单货款已直接给商家，售后问题需要联系商家解决。'
    }
    return OrderAction.confirm({
      url: '/wsctrade/order/confirmReceiveV2.json',
      data: prettyFormData,
      message,
      title: orderExtra.isHotel ? '确认入住' : '',
      confirmButtonText: orderExtra.isHotel ? '确认入住' : '',
      toastMessage: orderExtra.isHotel ? '入住成功' : '',
    }).then(() => {
      // 酒店确认入住埋点
      if (btnType === 'confirm-receive-hotel') {
        log.LOG_CONFIRM_HOTEL_GOODS()
      }
    })
  }

  _cancel({ callback }) {
    const { bigFormData } = this;
    log.LOG_CANCEL_ORDER();
    return OrderAction.cancel({
      url: '/wsctrade/order/cancelOrder.json',
      data: bigFormData,
      callback,
    });
  }

  // 取消订单
  cancel(params) {
    // 是否是充值优惠订单
    const isRechargeOrderFree = this.orderItem.orderExtra && this.orderItem.orderExtra.isRechargeOrderFree;
    if (isRechargeOrderFree) {
      Dialog.confirm({
        title: '取消订单将放弃充值优惠',
        message: '充值优惠仅针对本订单，取消订单后将无法享受本次充值优惠',
        confirmButtonText: '使用优惠',
        cancelButtonText: '放弃优惠',
      }).catch(() => {
        this._cancel(params);
      });
    } else {
      this._cancel(params);
    }
  }

  // 再来一单
  buyAgain() {
    const { bigFormData, extraData, directBuyAgainBtnConfig } = this;
    const goodsIds = [];
    bigFormData.goodsList.forEach(item => {
      goodsIds.push(item.goods_id)
    })
    let params = {
      orderNo: bigFormData?.order_no,
      goods_id: goodsIds,
      is_new: false,
    }
    // 新再来一单按钮的AB结果false时
    if (directBuyAgainBtnConfig.type === 'directBuyAgain' && directBuyAgainBtnConfig.abTraceId && !directBuyAgainBtnConfig.show) {
      params.abTraceId = directBuyAgainBtnConfig.abTraceId;
    }
    log.LOG_DIRECT_BUY_AGAIN(params);
    console.log('bigFormData', bigFormData);
    return OrderAction.buyAgain({
      url: '/wsctrade/order/buyAgain.json',
      cartUrl: '/wsctrade/cart',
      data: bigFormData,
      extraData,
    });
  }

  getBannerId(index = 0) {
    const pageRandomNumber = makeRandomString(8);
    const loggerSpm = window?.Logger?.getSpm() || '';
    return `${loggerSpm}~new_buy_again_click~${index}~${pageRandomNumber}`;
  }

  // 再来一单，点击直接进下单页
  directBuyAgain() {
    const { bigFormData, buyerFormData, directBuyAgainBtnConfig = {}, extraData = {} } = this;
    const goodsIds = [];
    bigFormData.goodsList.forEach(item => {
      goodsIds.push(item.goods_id)
    })
    const bannerId = this.getBannerId();
    let params = {
      banner_id: bannerId,
      buyer_id: buyerFormData.buyer_id,
      goods_id: goodsIds,
      order_no: bigFormData.order_no,
      abTraceId: directBuyAgainBtnConfig.abTraceId,
      is_new: true,
      has_buyagain_coupon: !!this.repurchaseCoupon,
    }
    log.LOG_DIRECT_BUY_AGAIN(params);
    return OrderAction.directBuyAgain({
      url: '/wsctrade/order/directBuyAgain.json',
      bannerId,
      data: bigFormData,
      extraData,
    });
  }

  // 提醒发货
  expressReminder({ message = '' }) {
    const { prettyFormData } = this
    log.LOG_EXPRESS_REMINDER()
    return OrderAction.remindSend({
      remindSendUrl: '/wsctrade/order/remindExpress.json',
      data: prettyFormData,
      message
    })
  }

  // 取消订单
  delete({ callback }) {
    const { buyerFormData } = this
    log.LOG_ORDER_DELETE(buyerFormData)
    return OrderAction.delete({
      url: '/wsctrade/order/deleteOrder.json',
      data: buyerFormData,
      callback,
    });
  }
}