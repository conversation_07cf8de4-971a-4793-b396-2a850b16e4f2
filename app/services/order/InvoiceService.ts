import { IApplyInvoiceRequest, IPreInvoiceRequest } from 'definitions/order/Invoice';
import BaseService from '../base/BaseService';

class InvoiceService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.retail.trade.core.api.service.invoice.InvoiceService';
  }

  /**
    *  初始化开发票
    *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/131248 
    *
    *  @param {Object} invoiceRequest - 
    *  @param {string} invoiceRequest.bankAccount - 用户银行账号
    *  @param {string} invoiceRequest.openingBankName - 开户行
    *  @param {string} invoiceRequest.orderNo - 订单号
    *  @param {string} invoiceRequest.shop - shopEntity 的json
    *  @param {string} invoiceRequest.address - 用户地址
    *  @param {number} invoiceRequest.kdtId - 店铺Id
    *  @param {string} invoiceRequest.source - 
    *  @param {number} invoiceRequest.type - 发票类型 个人 企业
    *  @param {number} invoiceRequest.isAuthException - 查询当前订单能够开发票按钮展示时，异常场景是否需要抛异常，还是直接返回reason,默认不抛异常 1为抛异常
    *  @param {string} invoiceRequest.phone - 用户电话
    *  @param {number} invoiceRequest.invoiceType - 1普通发票
    2增值税专用发票
    InvoiceFinanceTypeEnum
    *  @param {number} invoiceRequest.invoiceStatus - 发票开票状态
    *  @param {number} invoiceRequest.operatorId - 操作人Id
    *  @param {number} invoiceRequest.contentType - 发票内容类型
    *  @param {string} invoiceRequest.email - 
    *  @return {Promise}
    */
  async preInvoice(invoiceRequest: IPreInvoiceRequest) {
    return this.invoke('preInvoice', [invoiceRequest]);
  }

  /**
    *  申请开发票接口
    *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/131249 
    *
    *  @param {Object} invoiceRequest - 
    *  @param {string} invoiceRequest.bankAccount - 用户银行账号
    *  @param {string} invoiceRequest.openingBankName - 开户行
    *  @param {string} invoiceRequest.orderNo - 订单号
    *  @param {string} invoiceRequest.shop - shopEntity 的json
    *  @param {string} invoiceRequest.address - 用户地址
    *  @param {number} invoiceRequest.kdtId - 店铺Id
    *  @param {string} invoiceRequest.source - 
    *  @param {number} invoiceRequest.type - 发票类型 个人 企业
    *  @param {number} invoiceRequest.isAuthException - 查询当前订单能够开发票按钮展示时，异常场景是否需要抛异常，还是直接返回reason,默认不抛异常 1为抛异常
    *  @param {string} invoiceRequest.phone - 用户电话
    *  @param {number} invoiceRequest.invoiceType - 1普通发票
    2增值税专用发票
    InvoiceFinanceTypeEnum
    *  @param {number} invoiceRequest.invoiceStatus - 发票开票状态
    *  @param {number} invoiceRequest.operatorId - 操作人Id
    *  @param {number} invoiceRequest.contentType - 发票内容类型
    *  @param {string} invoiceRequest.email - 
    *  @return {Promise}
    */
  async applyInvoice(invoiceRequest: IApplyInvoiceRequest) {
    return this.invoke('applyInvoice', [invoiceRequest]);
  }
}

export default InvoiceService;
