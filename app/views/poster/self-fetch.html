<style>
#container {
  width: 375px;
  height: 522px;
  line-height: 1;
}

.dash-border {
  position: absolute;
  width: 327px;
  left: 24px;
  bottom: 0;
  border-bottom: 1px dashed #e5e5e5;
}

#container header {
  padding: 24px 24px 7px;
}

.self-fetch__title {
  color: #333333;
  font-size: 16px;
  font-weight: 500;
}

.self-fetch__address {
  position: relative;
  padding: 0 24px 6px 24px;
  color: #323233;
  font-size: 14px;
  line-height: 20px;
}

.self-fetch__address > p:first-child {
  margin-bottom: 12px;
}

.self-fetch__goods {
  position: relative;
  padding: 16px 24px;
}

.self-fetch__only .self-fetch__img {
  display: inline-block;
  width: 60px;
  height: 60px;
  background-size: 100% 100%;
  background-position: center center;
  margin-right: 6px;
}

.self-fetch__only .self-fetch__only-info {
  position: relative;
  display: inline-block;
  width: 255px;
  height: 60px;
}

.self-fetch__only .self-fetch__only-info-title {
  text-align: left;
  color: #323233;
  font-size: 14px;
  position: absolute;
  top: -14px;
  left: 0;
  line-height: 20px;
  height: auto;
  overflow: hidden;
  display: -webkit-box;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.self-fetch__only .self-fetch__only-info-sku {
  position: absolute;
  bottom: 0;
  left: 0;
  top: 36px;
  color: #969799;
  font-size: 14px;
}

.self-fetch__double {
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  align-items: center;
  color: #7d7e80;
  font-size: 14px;
}

.self-fetch__double .self-fetch__double-imgs {
  display: flex;
  flex-flow: row nowrap;
  justify-content: flex-start;
  align-items: center;
}

.self-fetch__double .self-fetch__img {
  width: 60px;
  height: 60px;
  background-size: 100% 100%;
  background-position: center center;
  margin-right: 6px;
}

.self-fetch__double .self-fetch__double-eclipse {
  margin-right: 24px;
}

.self-fetch__qrcode {
  margin: 24px auto 16px;
  width: 178px;
  height: 178px;
  background-size: 100% 100%;
  border: 1px solid #dcdee0;
}

.self-fetch__qrnum {
  text-align: center;
  margin: 0 auto;
  color: #333;
}

.self-fetch__qrnum span:first-child {
  font-size: 14px;
  vertical-align: middle;
  font-weight: 500;
}

.self-fetch__qrnum span:last-child {
  font-size: 24px;
  font-weight: 800;
  vertical-align: middle;
}
</style>

<article id="container">
  <header>
    <i class="self-fetch__icon"></i>
    <span class="self-fetch__title">自提订单提货凭证</span>
  </header>
  <div class="self-fetch__address">
    <p>提货地址：{{ fetchAddress }}</p>
    <p>提货时间：{{ fetchTime }}</p> 
    <div class="dash-border"></div>
  </div>
  <div class="self-fetch__goods">
    {% if items.length === 1 %}
    <div class="self-fetch__only">
      <div class="self-fetch__img" style="background-image: url('{{ items[0].imgUrl }}')"></div>
      <div class="self-fetch__only-info">
        <p class="self-fetch__only-info-title">{{ items[0].title }}</p>
        <p class="self-fetch__only-info-sku">{{ items[0].sku }}</p>
      </div>
    </div>
    {% else %}
    <div class="self-fetch__double">
      <div class="self-fetch__double-imgs">
        {% for item in items %}
          <div class="self-fetch__img" style="background-image: url('{{ item.imgUrl }}')"></div>
        {% endfor %}
        {% if itemsLength > 3 %}
          <span class="self-fetch__double-eclipse">...</span>
        {% endif %}
      </div>
      <span>共{{ itemsLength }}件</span>
    </div>
    {% endif %}
    <div class="dash-border"></div>
  </div>
  <div class="self-fetch__qrcode" style="background-image: url('{{ qrcode }}')"></div>
  <div class="self-fetch__qrnum">
    <span>提货码：</span>
    <span>{{ number }}</span>
  </div>
</article>


