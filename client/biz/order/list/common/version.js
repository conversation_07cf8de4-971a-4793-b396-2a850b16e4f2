export function compareVersion(semVerA, semVerB) {
  try {
    if (semVerA === semVerB || !semVerA || !semVerB) {
      return 0;
    }

    const arrA = semVerA.split('.');
    const arrB = semVerB.split('.');
    const lenA = arrA.length;
    const lenB = arrB.length;

    for (let i = 0, len = Math.min(lenA, lenB); i < len; i++) {
      let numA = parseInt(arrA[i], 10);
      let numB = parseInt(arrB[i], 10);
      isNaN(numA) && (numA = 0);
      isNaN(numB) && (numB = 0);
      if (numA !== numB) {
        return numA > numB ? 1 : -1;
      }
    }

    // eslint-disable-next-line no-nested-ternary
    return lenA > lenB ? 1 : lenA < lenB ? -1 : 0;
  } catch (err) {
    return -1;
  }
}