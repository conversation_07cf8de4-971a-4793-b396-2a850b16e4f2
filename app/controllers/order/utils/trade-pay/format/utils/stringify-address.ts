/**
 * 拼接完整的地址
 */

const ADDRESS_KEYS = [
  'country',
  'province',
  'city',
  'county',
  'community',
  'addressDetail',
  'address_detail', // 兼容旧版数据
  'houseNumber'
];

export function stringifyAddress(address: Record<string, any>, start = 0, end = ADDRESS_KEYS.length, separator = '') {
  const keys = ADDRESS_KEYS.slice(start, end);

  // 判断省市是否相同
  if (address[keys[1]] && address[keys[1]] === address[keys[2]]) {
    keys.splice(1, 1);
  }

  // 中国不需要展示
  if (address[keys[0]] === '中国') {
    keys.shift();
  }

  return keys
    .filter(key => address[key])
    .map(key => address[key])
    .join(separator);
}

