import BaseService from '../base/BaseService';

class OrderHelpService extends BaseService {
  async getHelpList(): Promise<any> {
    const codes = 'buyer_faq.order_detail';
    const all = await this.apiCall({
      url: '/cms/subject/getAllBySectionCodes',
      data: {
        codes,
      },
    });

    return all[codes];
  }

  async getHelpDetail(id: string): Promise<any> {
    return await this.apiCall({
      url: '/cms/subject/getById',
      data: {
        id,
      },
    });
  }
}

export default OrderHelpService;
