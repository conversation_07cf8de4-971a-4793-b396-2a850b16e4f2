import { IShopAbilityInfo } from 'definitions/shop/shopConfig';
import ShopAbilityInfoService from '../../services/shop/ShopAbilityInfoService';
import BaseController from '../base/BaseController';
import { Context } from 'astroboy';
import DeliverySettingService from '../../services/delivery/DeliverySettingService';

class ShopConfigController extends BaseController {
  async init() {
    await super.init();
  }

  // 检查店铺能力有效性
  async checkAbilityValid(ctx: Context) {
    const { kdtId, query } = ctx;
    const abilityInfo: IShopAbilityInfo = await new ShopAbilityInfoService(ctx).queryShopAbilityInfo([
      kdtId,
      query.code,
    ]);
    ctx.json(0, '0k', abilityInfo.valid);
  }

  // 获取店铺物流设置
  async getShopDeliverySettings(ctx: Context) {
    const { key } = ctx.query;
    const settings = await new DeliverySettingService(ctx).getSettings({
      kdtIds: [ctx.kdtId],
      fromApp: 'wsc-h5-trade',
    });
    const currentConfig = settings[ctx.kdtId];
    if (key) {
      return ctx.json(0, 'ok', currentConfig[key]);
    }
    return ctx.json(0, 'ok', currentConfig);
  }
}

export = ShopConfigController;
