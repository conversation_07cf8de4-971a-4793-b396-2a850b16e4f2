<template>
  <van-popup class="separate-buy-popup" position="bottom" :value="separateBuy.show" @input="popupValueChange">
    <beauty
      v-if="showLogistics"
      :separate-buy="separateBuy"
      :buy-text="buyText"
      @buy="buy"
      @setSeparateBuy="setSeparateBuy"
    />
  </van-popup>
</template>

<script>
import Beauty from './Beauty';
import { Popup } from 'vant';

export default {
  name: 'separate-buy',

  components: {
    Beauty,
    [Popup.name]: Popup,
  },

  props: {
    separateBuy: {
      type: Object,
      default: () => ({}),
    },
    buyText: {
      type: String,
      default: '去结算',
    },
  },

  computed: {
    showLogistics() {
      return ['courseMixType', 'mixType', 'logistics'].indexOf(this.separateBuy.type) !== -1;
    },
  },

  methods: {
    buy(params) {
      this.$emit('buy', params);
    },

    setSeparateBuy(params) {
      this.$emit('setSeparateBuy', params);
    },

    popupValueChange(params) {
      this.$emit('setSeparateBuy', { show: params });
    },
  },
};
</script>
<style lang="scss" scoped>
.separate-buy-popup {
  border-radius: 20px 20px 0 0;
}
</style>
