const URL = require('@youzan/iron-base/app/lib/URL');
const BaseController = require('../base/BaseController');
const HeaderOrderService = require('../../services/groupbuying/HeaderOrderService');

class HeaderOrderController extends BaseController {
  async init() {
    await super.init();
  }

  async pageAcl() {
    await this.acl({
      allowNotLogin: false,
      useAjaxLogin: false,
      forceOauthLogin: false,
      kdtId: this.ctx.kdtId,
      mpId: this.ctx.getLocalSession('mp_id')
    });
  }

  async getIndexHtml(ctx) {
    if (ctx.query.kdtId) {
      ctx.kdtId = ctx.query.kdtId;
    }
    return ctx.redirect(URL.site(`/wscump/communitybuy/order-list/header?kdtId=${ctx.kdtId}`, 'h5'));
  }

  // 查询团长订单
  async getHeaderOrderJson(ctx) {
    const { mobile, page, pageSize, state, verifyState, states, activityId, startTime, endTime } = ctx.getQueryData();
    const finalState = this.handleState(+state, +states);
    const params = {
      ...finalState,
      customerMobile: mobile || '',
      headerBuyerId: this.buyer.buyerId,
      withItemInfo: true,
      withBuyerInfo: true,
      sortBy: 'created_at',
      page: page || 1,
      pageSize: pageSize || 10,
      verifyState: verifyState !== undefined ? verifyState : null,
      kdtId: ctx.kdtId,
      activityId: +activityId,
      startTime: startTime || null,
      endTime: endTime || null
    };
    const headerOrderService = new HeaderOrderService(ctx);
    const headerOrder = await headerOrderService.orderQuery(params);
    ctx.r(0, '', headerOrder);
  }

  // 查询买家订单
  /* eslint-disable-next-line */
  async getBuyerOrderJson(ctx) {
    const { page, pageSize, state, mobile, states, verifyState } = ctx.getQueryData();
    const { customerId = 0, customerType = 0, buyerId } = this.buyer;
    const finalState = this.handleState(+state, +states);
    const params = {
      ...finalState,
      withItemInfo: true,
      withBuyerInfo: true,
      sortBy: 'created_at',
      page: page || 1,
      pageSize: pageSize || 10,
      verifyState: verifyState !== undefined ? verifyState : null,
      headerMobile: mobile,
      customerId,
      customerType,
      customerBuyerId: buyerId,
      kdtId: ctx.kdtId
    };
    const headerOrderService = new HeaderOrderService(ctx);
    const headerOrder = await headerOrderService.orderQuery(params);
    ctx.r(0, '', headerOrder);
  }

  // 验证订单
  /* eslint-disable-next-line */
  async getVerifyOrderByCodeJson(ctx) {
    const { verifyCode } = ctx.getQueryData();
    const { buyerId } = this;
    const kdtId = +ctx.kdtId;
    const params = {
      kdtId,
      verifyCode,
      headerBuyerId: buyerId,
      withItemInfo: true,
      withBuyerInfo: true
    };
    const headerOrderService = new HeaderOrderService(ctx);
    const orders = await headerOrderService.orderQuery(params);
    ctx.r(0, '', orders);
  }

  // 验证订单
  /* eslint-disable-next-line */
  async getVerifyOrderByMobileJson(ctx) {
    const { mobile } = ctx.getQueryData();
    const kdtId = +ctx.kdtId;
    const { buyerId } = this;
    const params = {
      kdtId,
      customerMobile: mobile,
      headerBuyerId: buyerId,
      // 固定查处已经支付和已完成的
      states: [5, 60, 100],
      // 未提货的
      verifyState: 0,
      withItemInfo: true,
      withBuyerInfo: true,
      sortBy: 'created_at'
    };
    const headerOrderService = new HeaderOrderService(ctx);
    const orders = await headerOrderService.orderQuery(params);
    ctx.r(0, '', orders);
  }

  // 确认提货
  /* eslint-disable-next-line */
  async confirmVerify(ctx) {
    const { headerBuyerId, orderNo } = ctx.getPostData();
    const params = {
      headerBuyerId,
      orderNo,
      kdtId: ctx.kdtId
    };
    const headerOrderService = new HeaderOrderService(ctx);
    const result = await headerOrderService.confirmVerify(params);
    ctx.json(0, '', result);
  }

  // 汇总
  async queryOrdersSummary(ctx) {
    const { startTime, endTime } = ctx.getQueryData();
    const params = {
      headerBuyerId: this.buyer.buyerId,
      startTime: startTime || null,
      endTime: endTime || null,
      kdtId: ctx.kdtId
    };
    const headerOrderService = new HeaderOrderService(ctx);
    const result = await headerOrderService.queryOrdersSummary(params);
    ctx.json(0, '', result);
  }

  handleState(state, states) {
    if (state === 100 || states === 60 || states === 100) {
      return {
        state: null,
        states: [60, 100]
      };
    }
    return {
      state: state || null,
      states: states ? [states] : []
    };
  }
}

module.exports = HeaderOrderController;
