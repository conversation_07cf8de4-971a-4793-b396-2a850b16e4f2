import BaseService from '../base/BaseService';

type OrderPayPromptParams = {
  kdtId: number;
  buyerId: number;
  source?: string;
}
class OrderPayPromptService extends BaseService {

  get ORDER_PAY_PROMPT_SERVICE() {
    return 'com.youzan.ebiz.mall.trade.buyer.api.service.OrderPromptService';
  }

  /**
   * zan-api地址：http://zanapi.qima-inc.com/site/service/view/1085665
   * 待付款订单催付弹窗内容（店铺首页、商品详情页、微页面、物流详情页）
   * @param params
   */
  async getOrderPayPrompt(params: OrderPayPromptParams) {
    const result = await this.invoke(this.ORDER_PAY_PROMPT_SERVICE, 'promptOrderPayCommon', [params]);

    return result;
  }
}

export = OrderPayPromptService;