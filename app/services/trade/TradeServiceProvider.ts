import BaseService from '../base/BaseService';

class TradeServiceProvider extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.qtt.order.api.service.TradeServiceProvider';
  }

  /**
   *  缓存需要下单的信息
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1333645
   */
  async cacheOrderCreationJson(params: any) {
    return this.invoke(this.SERVICE_NAME, 'cacheOrderCreationJson', [params]);
  }

  /**
   *  单店-准备下单 新版
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1333646
   */
  async prepareV2(params: any) {
    return this.invoke(this.SERVICE_NAME, 'prepareV2', [params]);
  }

  /**
   *  单店-确认下单 新版
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1333647
   */
  async confirmV2(params: any) {
    return this.invoke(this.SERVICE_NAME, 'confirmV2', [params]);
  }

  /**
   *  单店-下单 新版
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1333648
   */
  async create(params: any) {
    return this.invoke(this.SERVICE_NAME, 'create', [params]);
  }

  /**
   *  准备支付（去支付）
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1333649
   */
  async preparePaymentV2(params: any) {
    return this.invoke(this.SERVICE_NAME, 'preparePaymentV2', [params]);
  }
}

export = TradeServiceProvider;
