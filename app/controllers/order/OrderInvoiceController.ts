import { PageException } from '@youzan/iron-base';
import BusinessServiceError from '@youzan/iron-base/app/services/base/BusinessServiceError';
import BaseController from '../base/BaseController';
import OrderInvoiceService from '../../services/order/OrderInvoiceService';
import TradeSettingService from '../../services/trade/TradeSettingService';
import { getTradeApolloConfig } from '../../lib/Apollo';
import { IInvoicePageApolloConfig } from 'definitions/order/buy/Invoice';
import InvoiceService from '../../services/order/InvoiceService';

// 零售开票页面 和 微商城开票页面
enum FROM_TYPE {
  online_shop = 0,
  sweep_receipt = 1,
}

enum INVOICE_STATUS {
  invoiceNotYet = 'INVOICE_NOT_YET', // 1 未开票
  invoiceIng = 'INVOICE_ING', // 10 开票中
  invoiceFailed = 'INVOICE_FAILED', // 20 开票失败
  invoiceSuccess = 'INVOICE_SUCCESS', // 30 开票成功
  invoiceRed = 'INVOICE_RED' // 40 已冲红
}

// 零售扫小票接口定义的status invoicePreInfo.status 开票状态 0 未开票 1 开票中 2 已开票 3开票失败 4 已冲红
const getInvoiceStatus = (status: number) => {
  switch (status) {
    case 0:
      return INVOICE_STATUS.invoiceNotYet;
    case 1:
      return INVOICE_STATUS.invoiceIng;
    case 2:
      return INVOICE_STATUS.invoiceSuccess;
    case 3:
      return INVOICE_STATUS.invoiceFailed;
    case 4:
      return INVOICE_STATUS.invoiceRed;
    default:
      return INVOICE_STATUS.invoiceFailed;
  }
}

enum InvoiceStatusEnum {
  /** 未开票 */
  INVOICE_NOT_YET = 1,
  /** 开票中 */
  INVOICE_ING = 10,
  /** 开票失败 */
  INVOICE_FAILED = 20,
  /** 开票成功 */
  INVOICE_SUCCESS = 30,
  /** 已冲红 */
  INVOICE_RED = 40,
}

// 开票状态：有赞云对外导出的枚举值，进行统一
const getExportInvoiceStatus = (invoiceStatus: INVOICE_STATUS) => {
  return InvoiceStatusEnum[invoiceStatus];
}

enum INVOICE_DETAIL_TYPE {
  itemDetail = 'itemDetail',
  itemCategory = 'itemCategory',
}

// 零售扫小票场景 invoiceDetailType 开票内容 1 商品明细 2 商品内容
const getContentType = (invoiceDetailType: INVOICE_DETAIL_TYPE) => {
  switch (invoiceDetailType) {
    case INVOICE_DETAIL_TYPE.itemDetail:
      return 1;
    case INVOICE_DETAIL_TYPE.itemCategory:
      return 2;
  }
}

enum RAISE_TYPE {
  enterprise = 'enterprise',
  personal = 'personal'
}

// 零售扫小票场景 raiseType 抬头类型 1 企业 2 个人
const getType = (raiseType: RAISE_TYPE) => {
  switch (raiseType) {
    case RAISE_TYPE.enterprise:
      return 1;
    case RAISE_TYPE.personal:
      return 2;
  }
}

class OrderInvoiceController extends BaseController {
  public async init() {
    await super.init({
      validKdtId: true,
      initMpData: false,
      initMpAccount: false,
    });
  }

  public async orderInvoiceAcl() {
    await this.acl({
      allowNotLogin: false,
      useAjaxLogin: false,
      forceOauthLogin: false,
      kdtId: this.ctx.kdtId,
    });
  }

  // 新版开票页面
  public async getInvoiceCreateIndexHtml() {
    const { ctx } = this;

    await this.orderInvoiceAcl();
    await this.rantaAcl({ onlySetGlobal: true });

    if (ctx.status === 302) {
      return;
    }

    const { query: { order_no: orderNo, kdt_id: kdtId, from = FROM_TYPE.online_shop } } = ctx;


    // 区分微商城（零售线上）和零售开票场景
    if (+from === FROM_TYPE.sweep_receipt) {
      let res = [];
      try {
        res = await Promise.all([
          new InvoiceService(ctx).preInvoice({
            orderNo,
            kdtId,
            source: 'wsc-h5-trade'
          }),
          Promise.resolve({ invoiceServiceIsOpen: true, orderInvoiceHeadSettingIsOpen: true }), // 零售线下没有这个开关，直接模拟成功数据
          new TradeSettingService(ctx).get({ kdtId }),
        ]);

        const [invoicePreInfo = {}, switchInfo = {}, tradeSetting = {}] = res;
        const shop = JSON.parse(invoicePreInfo.shop || '{}');
        const invoiceStatus = getInvoiceStatus(invoicePreInfo.status)

        const invoiceInfo = {
          amount: invoicePreInfo.amount ? `￥${(invoicePreInfo.amount / 100).toFixed(2)}` : '',
          shopName: shop.shopName,
          editable: [INVOICE_STATUS.invoiceNotYet, INVOICE_STATUS.invoiceRed].includes(invoiceStatus),
          invoicePrompt: false,
          invoiceStatus,
          status: getExportInvoiceStatus(invoiceStatus),
          items: [],
          unavailableItems: []
        };

        // 不能开票就转成状态开票失败（即loading状态）
        if (invoicePreInfo.canInvoice === 0) {
          invoiceInfo.invoiceStatus = INVOICE_STATUS.invoiceFailed;
        }

        // 不能开发票的商品（零售没有支持开票的列表，不传入items是兼容的无影响）
        // 必须字段 title
        if (invoicePreInfo.illegalProducts !== undefined) {
          invoiceInfo.unavailableItems = JSON.parse(invoicePreInfo.illegalProducts).map((goods: { name: string }) => ({
            ...goods,
            title: goods.name,
          }));
        }

        // 开票内容 10：开启商品明细开票；01：开启商品类目开票；11：两种方式均开启。
        // 发票类型：invoiceType 10：普票；01：专票；11：普票和专票
        const { invoiceContent, invoiceType } = tradeSetting;

        const clientUrl = {
          order_detail_url: `/wsctrade/order/detail?order_no=${orderNo}&kdt_id=${kdtId}`,
          // 零售扫码开票成功页面
          invoice_success_url: `https://store.youzan.com/v2/h5/invoice?orderNo=${orderNo}&kdtId=${kdtId}&redirect=1`,
        };

        // 已开票成功
        switch (invoiceInfo.invoiceStatus) {
          case 'INVOICE_SUCCESS':
          case 'INVOICE_RED':
            ctx.redirect(clientUrl.invoice_success_url);
            break;
        }

        ctx.setGlobal({
          invoiceInfo,
          invoiceContent,
          invoiceType,
          switchInfo,
          clientUrl,
          // 零售扫小票场景特殊给一个标记
          sweepReceipt: 1,
        })
      } catch (err) {
        const content = err.errorContent || {};
        throw new PageException(content.code || 10500, content.msg || '发票数据异常');
      }

    } else {
      let res = [];
      try {
        res = await Promise.all([
          new OrderInvoiceService(ctx).getInvoiceDetail({ orderNo, kdtId, buyerId: this.buyerId }),
          new OrderInvoiceService(ctx).getInvoiceSwitchInfo({ kdtId }),
          new TradeSettingService(ctx).get({ kdtId }),
        ]);

        const [invoiceInfo = {}, switchInfo = {}, tradeSetting = {}] = res;

        // 店铺名称不是开票的实际店铺，后面字段补齐之后再展示
        // const shopMetaInfo = await this.callService('iron-base/shop.ShopMetaReadService', 'getShopMetaInfo', kdtId);
        invoiceInfo.amount = invoiceInfo.amount ? `￥${(invoiceInfo.amount / 100).toFixed(2)}` : '';
        // invoiceInfo.shopName = shopMetaInfo.shopName;
        invoiceInfo.status = getExportInvoiceStatus(invoiceInfo.invoiceStatus);

        // 开票内容 10：开启商品明细开票；01：开启商品类目开票；11：两种方式均开启。
        // 发票类型：invoiceType 10：普票；01：专票；11：普票和专票
        const { invoiceContent, invoiceType } = tradeSetting;

        const clientUrl = {
          order_detail_url: `/wsctrade/order/detail?order_no=${orderNo}&kdt_id=${kdtId}`,
          invoice_success_url: `/wsctrade/order/invoice/success?order_no=${orderNo}`,
        };

        // 已开票成功
        switch (invoiceInfo.invoiceStatus) {
          case 'INVOICE_SUCCESS':
          case 'INVOICE_RED':
            ctx.redirect(clientUrl.invoice_success_url);
            break;
        }

        ctx.setGlobal({
          invoiceInfo,
          invoiceContent,
          invoiceType,
          switchInfo,
          clientUrl,
        })
      } catch (err) {
        const content = err.errorContent || {};
        throw new PageException(content.code || 10500, content.msg || '发票数据异常');
      }
    }

    await this.setRantaPageInitData(ctx, {
      framework: 'vue',
      bizName: '@wsc-h5-trade/invoice-detail-v2',
      ecloud: {
        setExtensionParams: [
          {
            kdtId,
            // 2.0 页面标，没接2.0 可以不填
            pageNameV2: 'invoice-detail',
            conditionContext: {
              platform: ctx.platform,
              pageType: 'h5',
            },
          },
        ],
      },
    })

    await ctx.render('order/invoice-v2.html');
  }

  /**
    * 是否使用新版开票页面
    * 预计下线时间，2025年12月31日，如果还有黑名单店铺，需要考虑这部分的商家，延后下线
    * @returns {Promise<boolean>}
    */
  async useUnifyInvoicePage() {
    try {
      const invoicePageApolloConfig: string = getTradeApolloConfig('wsc-h5-trade.application', 'useUnifyInvoicePage');

      const {
        blackList = [],
        whiteList = [],
        percent = 0,
      } = JSON.parse(invoicePageApolloConfig || '{}') as IInvoicePageApolloConfig;

      const { kdtId } = this.ctx;

      const shopMetaInfo = await this.callService('iron-base/shop.ShopMetaReadService', 'getShopMetaInfo', kdtId);
      const { rootKdtId = kdtId } = shopMetaInfo;

      /**
       * 命中黑名单
       */
      if (blackList.includes(rootKdtId)) {
        return false;
      }
      /**
       * 命中白名单
       */
      if (whiteList.includes(rootKdtId)) {
        return true;
      }
      /**
       * 命中百分比
       * 按照kdtId最后两位进行切流，此处不考虑流量的不均匀分布。实际访问流量与峰值的百分比会有一定出入。
       */
      if (
        typeof percent === 'number' &&
        percent > 0 &&
        rootKdtId % 100 <= percent
      ) {
        return true;
      }
      /**
       * 默认
       */
      return false;
    } catch (error) {
      return false;
    }
  }

  // 旧版开票页面
  public async getIndexHtml() {
    if (await this.useUnifyInvoicePage()) {
      this.ctx.redirect(`/wsctrade/order/invoice-v2?order_no=${this.ctx.query.order_no}&kdt_id=${this.ctx.query.kdt_id}`);
      return;
    }

    const { ctx } = this;

    await this.orderInvoiceAcl();
    if (ctx.status === 302) {
      return;
    }

    const { query } = ctx;
    const orderNo = query.order_no;
    const kdtId = query.kdt_id;

    let res = [];
    try {
      res = await Promise.all([
        new OrderInvoiceService(ctx).getInvoiceDetail({ orderNo, kdtId, buyerId: this.buyerId }),
        new OrderInvoiceService(ctx).getInvoiceSwitchInfo({ kdtId }),
        new TradeSettingService(ctx).get({ kdtId }),
      ]);
    } catch (err) {
      const content = err.errorContent || {};
      throw new PageException(content.code || 10500, content.msg || '发票数据异常');
    }

    const [invoiceInfo = {}, switchInfo = {}, tradeSetting = {}] = res;

    // 开票内容 10：开启商品明细开票；01：开启商品类目开票；11：两种方式均开启。
    // 发票类型：invoiceType 10：普票；01：专票；11：普票和专票
    const { invoiceContent, invoiceType } = tradeSetting;

    const clientUrl = {
      order_detail_url: `/wsctrade/order/detail?order_no=${orderNo}&kdt_id=${kdtId}`,
      invoice_detail_url: `/wsctrade/order/invoice?order_no=${orderNo}&kdt_id=${kdtId}`,
      invoice_success_url: `/wsctrade/order/invoice/success?order_no=${orderNo}`,
    };

    // 已开票成功
    switch (invoiceInfo.invoiceStatus) {
      case 'INVOICE_SUCCESS':
      case 'INVOICE_RED':
        ctx.redirect(clientUrl.invoice_success_url);
        break;
      default:
        break;
    }

    ctx.setGlobal('invoice_info', invoiceInfo);
    ctx.setGlobal('invoice_content', invoiceContent);
    ctx.setGlobal('invoice_type', invoiceType);
    ctx.setGlobal('switch_info', switchInfo);
    ctx.setGlobal('client_url', clientUrl);
    await ctx.render('order/invoice.html');
  }

  public async getInvoiceSuccessHtml() {
    const { ctx } = this;
    await this.orderInvoiceAcl();
    if (ctx.status === 302) {
      return;
    }

    let res = null;
    try {
      res = await new OrderInvoiceService(ctx).queryInvoiceByOrderNo({ orderNo: ctx.query.order_no });
    } catch (err) {
      const content = err.errorContent || {};
      throw new PageException(content.code || 10500, content.msg || '发票数据异常');
    }

    if (!res || !res.length || !Object.keys(res[0]).length) {
      throw new PageException(10500, '发票数据不存在');
    }

    ctx.setGlobal('invoice_info', res[0]);
    await ctx.render('order/invoice-success.html');
  }

  public async postGetInvoiceDetail() {
    const { ctx } = this;
    const postData = ctx.getPostData();
    const data = await new OrderInvoiceService(ctx).getInvoiceDetail(
      { ...postData, buyerId: this.buyerId }
    );

    ctx.r(0, '', data);
  }

  public async postApplyInvoice() {
    const { ctx, buyerId } = this;

    if (!buyerId) {
      throw new BusinessServiceError(9999, '无权限请求');
    }

    const postData = {
      ...ctx.getPostData(),
      buyerId,
    };

    let data = null;
    if (+postData.from === FROM_TYPE.sweep_receipt) {
      // 零售扫小票场景 数据转换
      const {
        userName,
        invoiceDetailType,
        emailList,
        raiseType,
        invoiceType,
        kdtId,
        taxpayerId,
        orderNo,
        bankAccount = '',
        openingBankName = '',
        phone = '',
        address = '',
      } = postData;

      const shop = JSON.stringify({
        shopName: userName,
        taxCode: taxpayerId || '',
      });

      const newPostData = {
        bankAccount,
        openingBankName,
        address,
        orderNo,
        shop,
        kdtId,
        source: this.ctx.isWeapp ? 'WX' : '',
        type: getType(raiseType),
        phone,
        invoiceType,
        contentType: getContentType(invoiceDetailType),
        email: emailList[0]
      }
      await new InvoiceService(ctx).applyInvoice(newPostData);
      data = true;
    } else {
      data = await new OrderInvoiceService(ctx).applyInvoice(postData);
    }

    ctx.r(0, '', data);
  }


  public async postQueryTaxInfoListByCorpName() {
    const { ctx } = this;
    const postData = ctx.getPostData();
    const data = await new OrderInvoiceService(ctx).queryTaxInfoListByCorpName(postData);

    ctx.r(0, '', data);
  }

  public async postQueryCompanyDetailTaxInfo() {
    const { ctx } = this;
    const postData = ctx.getPostData();
    const data = await new OrderInvoiceService(ctx).queryCompanyDetailTaxInfo(postData);

    ctx.r(0, '', data);
  }

  public async postGetAuthorUrl() {
    const { ctx } = this;
    const postData = ctx.getPostData();
    const data = await new OrderInvoiceService(ctx).getAuthorUrl(postData);

    ctx.r(0, '', data);
  }

  public async postInvoiceEmail() {
    const { ctx } = this;
    const postData = ctx.getPostData();
    const data = await new OrderInvoiceService(ctx).sendInvoiceEmail(postData);

    if (Object.keys(data).length) {
      ctx.r(data.code || 10000, data.msg || '发送失败', data);
    } else {
      ctx.r(0, '电子发票PDF原件已发送至邮箱', data);
    }
  }
}

export = OrderInvoiceController;
