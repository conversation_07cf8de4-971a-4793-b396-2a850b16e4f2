export type OrderListType =
  | 'all'
  | 'topay'
  | 'tosend'
  | 'send'
  | 'sign'
  | 'confirm'
  | 'totuan'
  | 'togroup'
  | 'safe'
  | 'rights'
  | 'toevaluate'
  | 'going'
  | 'current'
  | 'history';

export interface IListHtmlQuery {
  type: OrderListType;
  kdt_id: number;
  isWeapp: boolean;
  pageType: string;
  orderMark: string;
}

export interface IOrderListReqExtra {
  searchtag: string;
  toevaluate?: number;
  knowledgeGoods?: number;
  receivertel?: string;
  receivername?: string;
  goodstitle?: string;
  haveOfflineOrder?: string;
  showFulfillDetail?: number;
}

export interface IOrderListReq {
  buyerId: number;
  offset: number;
  pageId: string;
  page: number;
  pageSize: number;
  status: OrderListType;
  extra: IOrderListReqExtra;
  platform: string;
  caller?: string;
  orderMark?: string;
  kdtId?: number;
  receiverTel?: string;
  source?: string;
  keyword?: string;
  isDrugList?: string;
  marketingMark?: any;
  bizNo?: string;
  isHideXhsMiniAppLocalLifeOrder?: boolean;
}

export interface IOrderListPermission {
  /**
   * 显示去支付
   */
  isShowTopay?: boolean;
  /**
   * 显示取消订单
   */
  isShowCancelOrder?: boolean;
  /**
   * 待付定金：取消 + 支付定金
   */
  isShowTopayPresaleDownpayment?: boolean;
  /**
   *待付尾款 && 已开始：支付尾款
   */
  isShowTopayPresaleFinalpayment?: boolean;
  /**
   * 待付尾款 && 未开始：支付尾款
   */
  isShowBeforePresaleFinalpayment?: boolean;
  /**
   * 评价
   */
  isShowEvaluate: boolean;
  /**
   * 是否可以：追加评价
   */
  isShowAddEvaluate: boolean;
  /**
   * 是否可以：查看评价
   */
  isShowViewEvaluate: boolean;
  /**
   * 是否可以：延长收货
   */
  isAllowLaterReceive: boolean;
  /**
   * 是否可以：确认收货
   */
  isAllowConfirmReceive: boolean;
  /**
   * 是否可以：提醒发货
   */
  isShowReminder: boolean;
  /**
   * 是否可以：再来一单
   */
  isAllowBuyAgain: boolean;
  /**
   * 是否可以：查看卡券
   */
  allowViewCardDetail: boolean;
  /**
   * 是否可以：查看拼团详情
   */
  allowPintuanDetail: boolean;
  /**
   * 是否可以：查看课程商品详情
   */
  allowCourseDetail: boolean;
  /**
   * 是否可以：查看抽奖结果
   */
  allowViewLotteryResult: boolean;
  /**
   * 是否可以：查看物流
   */
  allowViewLogistics: boolean;
  /**
   * 是否可以：删除订单
   */
  allowShowDeleteOrder: boolean;
  /**
   * 是否可以：apollo展示删除订单按钮
   */
  isApolloShowDeteleOrder: boolean;
  /**
   * 是否可以：领优惠券
   */
  isAllowFissionCoupon: boolean;
}

export interface IOrderPayInfo {
  amountDesc: string;
  payAmount: number;
  last?: string;
}

export interface IOrderListItem {
  id: string;
  kdtId: number;
  orderNo: string;
  shopName: string;
  statusCode: string;
  orderStateStr: string;
  orderPermission: IOrderListPermission;
  orderItems: any[];
  status: number;
  presaleVoucherStatus: number;
}

export interface IOrderListResp {
  hasNext: boolean;
  list: Array<IOrderListItem>;
  page: number;
  pageId: string;
  pageSize: number;
}

export interface IOneOrderReq {
  buyerId: number;
  bizNo: string;
  paltform: string;
  caller?: string;
  isDrugList?: string;
  extra?: object;
}

export type IOrderListReqUnion = IOrderListReq | IOneOrderReq;

export interface IOrderLevel {
  appName: string;
  goodsId: number;
  kdtId: number;
  levelAlias: string;
  levelId: number;
  originKdtI: number;
  requestId: string;
  traceId: string;
  tenant: string;
}
export interface IOrderLevelReq {
  autoRenew: boolean;
}

export interface IItemProperty {
  propValueList: Array<{
    propValueName: string;
  }>;
  propName: string;
}

export interface IItemSku {
  k: string;
  v?: string;
  value?: string;
}
