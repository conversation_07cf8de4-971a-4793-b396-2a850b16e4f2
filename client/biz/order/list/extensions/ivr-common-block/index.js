import args from '@youzan/utils/url/args';

export default class IvrCommonBlock {
  constructor(options) {
    this.ctx = options.ctx;

    this.ctx.process.define('getIvrData', () => {
      return {
        from: 'ivr',
      };
    });
  }

  static lambdas = {
    detailUrlFormatter(url) {
      // IVR订单处理跳转URL参数
      if (args.get('from', url) !== 'ivr') {
        url = args.add(url, {
          from: 'ivr',
        });
      }

      return url;
    },

    shopHomeUrlFormatter() {
      return '';
    },
  };
}
