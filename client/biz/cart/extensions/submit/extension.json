{"extensionId": "@wsc-h5-trade/submit", "name": "@wsc-h5-trade/submit", "version": "1.7.6", "pathInBundle": "submit-block", "bundle": "https://b.yzcdn.cn/wsc-h5-trade/ext/ranta-extension_f58c0119.js", "hasWidget": true, "widget": {"consume": ["UpperBar"]}, "data": {"consume": {"shopCart": ["r"], "editMode": ["r"], "isCheckedAll": ["r"], "checkedGoodsList": ["r"], "presentData": ["r"]}}, "event": {"emit": ["toggleCheckedAll", "deleteCartItems"]}, "sandbox": {"id": "", "level": "Unsafe"}}