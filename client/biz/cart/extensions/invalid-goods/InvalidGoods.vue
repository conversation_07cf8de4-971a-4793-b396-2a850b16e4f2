<template>
  <div
    v-if="showUnavailableItems"
    class="invalid-goods"
    :class="{ 'border-radius-hack': borderRadiusHack }"
  >
    <div class="title">
      <span>失效商品</span>
      <div class="clear-btn" @click="clearInvalid">
        清空失效商品
      </div>
    </div>
    <goods
      v-for="(invalidGoods, index) in unavailableItems"
      :key="index"
      :is-invalid="true"
      :goods="invalidGoods"
      :kdt-id="-1"
      @deleteCartItem="deleteCartItem"
    />
  </div>
</template>

<script>
import { Toast } from 'vant';
import Goods from './Goods';
import each from 'utils/each';
import { getMultiRecommendGoods, batchDeleteGoods, deleteGoods } from '@biz/cart/api';

export default {
  name: 'invalid-goods',

  components: {
    Goods,
  },

  props: {
    ctx: Object,
  },

  data() {
    return {
      unavailableItems: [],
      reGoodsLists: [],
    };
  },

  computed: {
    borderRadiusHack() {
      const ua = window.navigator.userAgent.toLowerCase();
      const isIphone = /iphone/gi.test(ua);
      return isIphone;
    },
    showUnavailableItems() {
      return this.unavailableItems.length > 0;
    },
  },

  created() {
    this.watchUnavailableItems();
  },

  destroyed() {
    this.unwatchUnavailableItems && this.unwatchUnavailableItems();
  },

  methods: {
    watchUnavailableItems() {
      this.unwatchUnavailableItems = this.ctx.watch('unavailableItems', (val = []) => {
        this.unavailableItems = val;
        this.getRecommendList();
      });
    },

    getRecommendList() {
      let goodsIds = [];
      each(this.unavailableItems, (item) => {
        goodsIds.push(item.goodsId)
      });
      getMultiRecommendGoods(goodsIds)
        .then((res) => {
          if (res.code === 0) {
            const reGoodsLists = res.data || [];
            let temp = JSON.parse(JSON.stringify(this.unavailableItems));
            temp.forEach((item) => {
              item.recommendListObj = reGoodsLists.find((listObj) => item.goodsId === listObj.goodsId) || {};
            });
            this.unavailableItems = temp;
          }
        })
        .catch();
    },

    deleteCartItem({ goods }) {
      deleteGoods(goods)
        .then((resp = []) => {
          if (resp.code !== 0) {
            Toast(resp.msg);
          } else {
            this.ctx.event.emit('updateCartGoodsList');
          }
        })
        .catch(() => {
          Toast('出错了');
        });
    },

    clearInvalid() {
      batchDeleteGoods(this.unavailableItems).then((resp) => {
        Toast('成功清除失效商品');
        this.ctx.event.emit('updateCartGoodsList');
      }).catch((e) => {
        Toast(e.msg);
      });
    },
  },
};
</script>

<style scoped lang="scss">
.invalid-goods {
  background: #fff;
  position: relative;
  margin: 0 12px;
  border-radius: 8px;
  overflow: hidden;
  margin-top: 12px;

  &.border-radius-hack {
    // 解决 iphone border-radios + voerflow:hidden + css transform 导致圆角失效bug
    -webkit-mask-image: -webkit-radial-gradient(white, black);
  }

  .title {
    height: 44px;
    background: #fff;
    font-size: 14px;
    color: #323233;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 12px;
  }

  .goods {
    .goods-card {
      margin-left: 10px;
    }
  }
}

.clear-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 88px;
  height: 26px;
  line-height: 24px;
  text-align: center;
  border-radius: 13px;
  font-size: 12px;
  box-sizing: border-box;
  border: 1px solid #dcdee0;
}
</style>
