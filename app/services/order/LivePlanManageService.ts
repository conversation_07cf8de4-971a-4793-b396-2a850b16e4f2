import BaseService from '../base/BaseService';

interface ILiveHelpActivityQuery {
  /** 直播来源场景 */
  liveScene?: string;
  /** 店铺ID */
  shopId?: number;
  /** 商品ID列表 */
  goodsIds?: number[];
}

interface ILiveHelpActivity {
  /** 直播计划活动alias */
  alias?: string;
  /** 直播计划活动开始时间 */
  startTime?: number;
  /** 直播计划活动ID */
  id?: number;
  /** 店铺ID */
  shopId?: number;
  /** 直播计划活动结束时间 */
  endTime?: number;
  /** 直播计划活动名称 */
  title?: string;
}

/* com.youzan.ebiz.video.channels.flow.api.service.LivePlanManageService -  */
class LivePlanManageService extends BaseService {
  get LIVE_PLAN_SERVICE(): string {
    return 'com.youzan.ebiz.video.channels.flow.api.service.LivePlanManageService';
  }

  /**
  *  C端接口
 查询当前在直播中的并且商品匹配的直播计划
  *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1241371 
  *
  *  @param {Object} param - 
  *  @param {string} param.liveScene - 直播来源场景
  *  @param {number} param.shopId - 店铺ID
  *  @param {Array.<Array>} param.goodsIds[] - 商品ID列表
  *  @param {Array} param.goodsIds[] - 
  *  @return {Promise}
  */
  async getActivityForOrderMark(param: ILiveHelpActivityQuery): Promise<ILiveHelpActivity> {
    return this.invoke(this.LIVE_PLAN_SERVICE, 'getActivityForOrderMark', [param]);
  }
}

export default LivePlanManageService;
