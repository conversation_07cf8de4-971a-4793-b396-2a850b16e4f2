import {
  IBehalfOrderAddressForCustRequest,
  IDeliveryDTO,
} from 'definitions/api/retail/sales/BehalfOrderWapReadService/getBehalfOrderCustomerAddress';
import BaseService from '../../../base/BaseService';

/** com.youzan.retail.sales.api.service.behalf.BehalfOrderWapReadService */
class BehalfOrderWapReadService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.retail.sales.api.service.behalf.BehalfOrderWapReadService';
  }

  /**
   * 查询导购代客下单 导购设置的联系地址
   * @link http://zanapi.qima-inc.com/site/service/view/1168984
   * @param {Object} request - 导购代客下单 列表请求类
   * @param {string} request.retailSource - 请求来源,系统名称或前端终端(替代source)
   * @param {number} request.kdtId -
   * @param {string} request.behalfBizNo - 代客单业务单号
   * @param {number} request.adminId - 操作人id
   * @param {string} request.source - 请求来源,系统名称或前端终端。
   * @return {Promise}
   */
  async getBehalfOrderCustomerAddress(request: IBehalfOrderAddressForCustRequest): Promise<IDeliveryDTO> {
    return this.invoke('getBehalfOrderCustomerAddress', [request]);
  }
}

export = BehalfOrderWapReadService;
