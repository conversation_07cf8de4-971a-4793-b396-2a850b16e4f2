import { PageException } from '@youzan/iron-base';
import { Context } from 'astroboy';

// 配置名单内的打warn，未预期的error
/* const SERVICE_ERROR_MAP: ServiceErrorType = {
  'com.youzan.ebiz.mall.trade.buyer.api.service.TradeService.prepareV2': [429],
  'com.youzan.ebiz.mall.trade.buyer.api.service.TradeService.create': [429],
  'com.youzan.ebiz.trade.api.manage.detail.DetailService.getOrderInfoFormat': [167000101]
}; */

/**
 * 自定义错误日志级别
 * @param error
 */
export default function handle(error: any, ctx: Context) {
  let level = 'ERROR';
  // 错误类型
  if (
    error instanceof PageException ||
    error.errorType === 'FlowLimitError' ||
    error.errorType === 'ParamsError' ||
    error.errorType === 'BusinessServiceError' ||
    process.env.NODE_ENV === 'pre'
  ) {
    level = 'WARN';
  }

  /**
   * TODO
   * 业务错误过滤掉打warn，其他打error
   */
  /* if (error.errorType === 'BusinessServiceError') {
    const { errorContent = {} } = error;
    if (errorContent.extra && errorContent.extra.serviceName) {
      const matched = SERVICE_ERROR_MAP[`${errorContent.extra.serviceName}.${errorContent.extra.methodName}`];
      if (matched && matched.indexOf(error.code) >= 0) {
        level = 'WARN';
      } else if (matched) {
        level = 'ERROR';
      }
    }
  } */

  if (error.errorType === 'CsrfError') {
    level = 'WARN';

    const csrfSecret = ctx.cookies.get('csrf-secret');
    const csrfToken = ctx.header['csrf-token'];
    // 额外打印日志
    ctx.logger.warn('CsrfError', null, {
      csrfToken,
      csrfSecret,
    });
  }

  return level;
}
