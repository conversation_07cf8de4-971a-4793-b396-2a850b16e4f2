import BaseService from '../base/BaseService';
import {
  IDiseaseQueryRequest,
  IPrescriptionGetRequest,
  IPrescriptionCreateRequest,
  IPrescriptionCreateResponse,
  IPrescriptionGetResponse,
  IPatientCreateRequest,
  IPatientQueryRequest,
  IPatientDeleteRequest,
  IHospitalListGetRequest,
  IHospital
} from 'definitions/order/OrderDrug';

class OrderDrugService extends BaseService {
  // 用药人相关
  get PATIENT_MANAGEMENT_SERVICE(): string {
    return 'com.youzan.retail.trade.misc.api.service.medicine.PatientManagementApi';
  }

  // 处方相关
  get PRESCRIPTION_MANAGEMENT_SERVICE(): string {
    return 'com.youzan.retail.trade.misc.api.service.medicine.PrescriptionManagementApi';
  }

  get DISEASE_MANAGEMENT_SERVICE(): string {
    return 'com.youzan.retail.trade.misc.api.service.medicine.DiseaseManagementApi';
  }

  // 店铺医院相关
  get HOSPITAL_MANAGEMENT_SERVICE() {
    return 'com.youzan.retail.trade.misc.api.service.medicine.HospitalManagementApi';
  }
 
// 获取疾病信息
  public async searchDiseaseInfo(params: any){
    return this.invoke(this.DISEASE_MANAGEMENT_SERVICE, 'searchDiseaseInfo', [
      params,
    ]);
  }

  public async getDefaultDisease( // 获取默认的健康数据
  ): Promise<any> {
    return this.invoke(this.DISEASE_MANAGEMENT_SERVICE, 'getDefaultDisease', []);
  }

  // 根据用药人和处方药 查询对应的确诊疾病列表
  public async queryDiagnoseDisease( 
    params: any,
  ): Promise<any> {
    return this.invoke(this.DISEASE_MANAGEMENT_SERVICE, 'listDiagnoseDiseases', [
      params,
    ]);
  }

  // 处方单创建，H5 开单时，先创建处方单
  public async create(params: IPrescriptionCreateRequest): Promise<IPrescriptionCreateResponse>{ 
    return this.invoke(this.PRESCRIPTION_MANAGEMENT_SERVICE, 'create', [
      params,
    ]);
  }

  // C端处方信息查询
  public async get(params:IPrescriptionGetRequest) : Promise<IPrescriptionGetResponse>{
    return this.invoke(this.PRESCRIPTION_MANAGEMENT_SERVICE, 'get', [
      params,
    ]); 
  }

  // C端医药订单查询，用于订单列表
  public async orderSearch(params: any) : Promise<IPrescriptionGetResponse> {
    return this.invoke(this.PRESCRIPTION_MANAGEMENT_SERVICE, 'orderSearch', [
      params,
    ]); 
  }

  //创建用药人信息
  public async creatUser(params:IPatientCreateRequest) {
    return this.invoke(this.PATIENT_MANAGEMENT_SERVICE, 'create', [
      params,
    ]);
  }

  //查询用药人信息
  public async queryUser(params:IPatientQueryRequest) {
    return this.invoke(this.PATIENT_MANAGEMENT_SERVICE, 'query', [
      params,
    ]);
  }

 //删除用药人信息
  public async deleteUser(params:IPatientDeleteRequest) {
    return this.invoke(this.PATIENT_MANAGEMENT_SERVICE, 'delete', [
      params,
    ]);
  }
  
  // 更新用药人
  public async updateUser(params:IPatientDeleteRequest) {
    return this.invoke(this.PATIENT_MANAGEMENT_SERVICE, 'update', [
      params,
    ]);
  }

  /**
   *  获取医院列表
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1163602
   */
  public async getHospitalList(params: IHospitalListGetRequest): Promise<{allHospitals: IHospital[]}> {
    return this.invoke(this.HOSPITAL_MANAGEMENT_SERVICE, 'getHospitalList', [params]);
  }
}

export = OrderDrugService