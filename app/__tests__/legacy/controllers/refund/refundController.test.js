import { app, mock } from '@youzan/astroboy-mock/bootstrap';
import BaseController from 'app/controllers/base/BaseController';
import RefundService from 'app/services/refund/RefundService';

jest.mock('app/controllers/base/BaseController', () =>
  jest.requireActual('app/__tests__/__mocks__/BaseController')
);

jest.mock('app/services/refund/RefundService');

afterEach(() => {
  BaseController.mockClear();
  RefundService.mockClear();
});

test('getIndexHtml', async () => {
  app.mockContext({
    buyerId: 8106728201,
    kdtId: 491391,
  });

  RefundService.mockImplementation(() => {
    return {
      getSellerPhone: jest.fn().mockResolvedValue(''),
    };
  });

  const res = await app.httpRequest().get('/wsctrade/refund/index');
  const global = mock.getViewGlobal(res.text);
  expect(BaseController.mockNeedPlatformAcl.mock.calls.length).toBe(1);
  expect(global.refundConcatPhone).toBe('');
});

test('getRefundStateJson', async () => {
  app.mockContext({
    buyerId: 8106728201,
    kdtId: 491391,
    isWeapp: true,
    request: {
      itemId: 2776883852032081932,
      orderNo: 'E20201213171857098804131',
    },
  });

  RefundService.mockImplementation(() => {
    return {
      getRefundState: jest.fn().mockResolvedValue({ redirectTarget: 2 }),
    };
  });

  const res = await app
    .httpRequest()
    .get(
      '/wsctrade/refund/getRefundState.json?itemId=2776883852032081932&orderNo=E20201213171857098804131'
    );

  expect(res.body).toEqual({
    code: 0,
    data: {
      redirectTarget: 2,
      redirectUrl:
        'https://h5.youzan.com/wsctrade/refund/v2/index?kdtId=491391&itemId=2776883852032081932&orderNo=E20201213171857098804131#/fill_out_form/',
    },
    msg: 'ok',
  });
});