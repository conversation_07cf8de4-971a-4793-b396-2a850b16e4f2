import BaseService from '../base/BaseService';

class SnapshotService extends BaseService {
  async postSnapshot(data: any) {
    const result = await this.ajax({
      method: 'POST',
      url: `${this.getConfig('CARD_SNAP_URL')}/snapshot/snapshot.json`,
      data,
      timeout: 50000,
    });
    return result;
  }

  async getSnapshot(redisKey: any) {
    const result = await this.ajax({
      method: 'GET',
      url: `${this.getConfig('CARD_SNAP_URL')}/snapshot/cache.json`,
      data: { redisKey },
      timeout: 500,
    });
    return result;
  }
}

export = SnapshotService;
