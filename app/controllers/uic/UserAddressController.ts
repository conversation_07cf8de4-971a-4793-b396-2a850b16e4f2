import BaseController from '../base/BaseController';
import RegionService from '../../services/delivery/RegionService';
import UserAddressService from '../../services/uic/UserAddressService';
import { Context } from 'astroboy';
const setupCdn = require('@youzan/iron-base/app/lib/set-up-cdn');

// 资产也维护了一份代码，如果有改动，麻烦通知一下资产， @小濛
class UserAddressController extends BaseController {
  // 获取收货地址列表
  async postAddressListJson(ctx: Context) {
    if (this.buyerId === 0) {
      const sessionInfo = ctx.getLocalSession();
      ctx.logger.warn(
        `buyerId为0:postAddressListJson
        ${(ctx.traceCtx as any).rootId}
        sessionId: ${ctx.sessionId}
        session内容: ${sessionInfo ? JSON.stringify(ctx.getLocalSession()) : ''}
      `
      );
      return ctx.json(0, '', []);
    }

    const addressList = await new UserAddressService(ctx).getAddressList({
      userId: this.buyerId,
    });
    ctx.json(0, '', addressList);
  }

  // 新增收货地址
  async postAddAddressJson(ctx: Context) {
    const postData = ctx.getPostData() || {};
    postData.userId = this.buyerId;
    postData.userAgent = ctx.userAgent;
    postData.ipAddress = ctx.firstXff;
    if (this.buyerId === 0) {
      const sessionInfo = ctx.getLocalSession();
      ctx.logger.warn(
        `buyerId为0:postAddAddressJson
        ${(ctx.traceCtx as any).rootId}
        sessionId: ${ctx.sessionId}
        session内容: ${sessionInfo ? JSON.stringify(ctx.getLocalSession()) : ''}
      `
      );
      return ctx.json(10000, '登录态失效，请刷新重试', {});
    }
    const data = await new UserAddressService(ctx).addAddress(postData);
    ctx.json(0, '地址添加成功', data);
  }

  // 更新收货地址
  async postUpdateAddressJson(ctx: Context) {
    const postData = ctx.getPostData() || {};
    postData.userId = this.buyerId;
    postData.userAgent = ctx.userAgent;
    postData.ipAddress = ctx.firstXff;
    const data = await new UserAddressService(ctx).updateAddress(postData);
    ctx.json(0, '地址编辑成功', data);
  }

  // 删除收货地址
  async postDeleteAddressJson(ctx: Context) {
    const postData = ctx.getPostData() || {};
    postData.userId = this.buyerId;
    const data = await new UserAddressService(ctx).deleteAddress(postData);
    ctx.json(0, '地址删除成功', data);
  }

  // 地址解析
  async getParseAddressJson(ctx: Context) {
    const postData = ctx.getPostData() || {};
    const data = await new UserAddressService(ctx).parseAddress(postData);

    ctx.json(0, '', data);
  }

  private randomName(len: number, id: number): string {
    id = id || 0;
    len = len || 24;
    const chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678';
    const maxPos = chars.length;
    let str = '';
    for (let i = 0; i < len; i++) {
      str += chars.charAt(Math.floor(Math.random() * maxPos));
    }
    return `${id}${new Date().getTime()}${str}`.substr(0, len);
  }

  // 获取支付宝收获地址areaCode为空，新增接口 解析省市区 返回 areaCode
  async getParseAliAddressJson(ctx: Context) {
    const postData = ctx.getPostData() || {};
    postData.fromApp = 'alipayApp';
    const { buyer } = this;
    postData.requestId = this.randomName(24, buyer.buyerId);
    const data = await new RegionService(ctx).getRegionIdByName(postData);

    ctx.json(0, '', data);
  }

  // 获取省市区列表
  async getAllRegionJson(ctx: Context) {
    const queryData = ctx.getQueryData() || {};
    queryData.fromApp = 'wsc-h5-trade';

    const data = await new RegionService(ctx).getAllRegion(queryData);

    setupCdn(ctx, 3600);
    ctx.json(0, '', data);
  }

  async getRegionByLevelJson(ctx: Context) {
    const queryData = ctx.getQueryData() || {};
    queryData.fromApp = 'wsc-h5-trade';
    const data = await new RegionService(ctx).getRegionByLevel(queryData);
    ctx.json(0, '', data);
  }
}

export = UserAddressController;
