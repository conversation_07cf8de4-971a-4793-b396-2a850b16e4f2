import {
  BenefitTypeEnum,
  SelectedBenefitTypeEnum,
  CarrierTypeEnum,
  IBillingResponseItem,
  ICustomerDiscountCarrierBenefitDTO,
  ICustomerDiscountCarrierFeature,
  IPointDeductionInfoDTO,
  IWeShopProductInfoDTO,
  ISelectedItem,
} from './types';

/**
 * * 将对象中所有_拼接的字段名都转化成小驼峰命名
 * * 如果里面有嵌套，则循环调用，确保全都转成小驼峰
 */
export function transformToUpperKeyName(originData: any[] | Record<string, any>): any {
  if (Array.isArray(originData)) {
    return originData.map((item) => transformToUpperKeyName(item));
  }
  if (typeof originData === 'object' && originData !== null) {
    return Object.keys(originData).reduce((acc, key) => {
      const newKey = key.replace(/_(\w)/g, (_, letter) => letter.toUpperCase());
      acc[newKey] = transformToUpperKeyName(originData[key]);
      return acc;
    }, {} as Record<string, any>);
  }
  return originData;
}

/**
 * * 将对象中所有小驼峰命名的字段名都转化成下划线格式
 * * 如果里面有嵌套，则循环调用，确保全都转成下划线格式
 */
export function transformToUnderScoreKeyName(originData: any[] | Record<string, any>): any {
  if (Array.isArray(originData)) {
    return originData.map((item) => transformToUnderScoreKeyName(item));
  }
  if (typeof originData === 'object' && originData !== null) {
    return Object.keys(originData).reduce((acc, key) => {
      const newKey = key.replace(/[A-Z]/g, (letter) => `_${letter.toLowerCase()}`);
      acc[newKey] = transformToUnderScoreKeyName(originData[key]);
      return acc;
    }, {} as Record<string, any>);
  }
  return originData;
}

export function transformSelectedBenefitList(
  selectedBenefitList: ISelectedItem[]
): IWeShopProductInfoDTO {
  if (!selectedBenefitList || !selectedBenefitList.length) return {} as IWeShopProductInfoDTO;
  const pointsBenefit = selectedBenefitList.find(
    (item) => item.type === SelectedBenefitTypeEnum.POINTS
  );
  const memberBenefit = selectedBenefitList.find(
    (item) => item.type === SelectedBenefitTypeEnum.MEMBER
  );
  const couponBenefit = selectedBenefitList.find(
    (item) => item.type === SelectedBenefitTypeEnum.COUPON
  );

  let carrierId = '';

  if (memberBenefit?.chosenList?.[0]?.id) {
    carrierId = memberBenefit?.chosenList?.[0]?.id as string;
  } else if (memberBenefit?.list?.length) {
    // * 未选中但是有可选的会员权益，则传递 "0" 代表手动取消选择
    carrierId = '0';
  }

  let appointCouponInfos;
  if (couponBenefit?.chosenList?.length) {
    appointCouponInfos = couponBenefit?.chosenList?.map((item) => ({
      couponId: item.id as number,
      couponType: item.type,
      couponOrder: item.couponOrder,
    }));
  } else if (couponBenefit?.list?.length) {
    // * 未选中但是有可选的优惠券，则传空数组代表手动取消选择
    appointCouponInfos = [];
  }

  return {
    isUsePointDeduction: pointsBenefit && !!pointsBenefit?.chosenList?.length,
    costPoints: (pointsBenefit?.chosenList?.[0]?.valueDesc as number) || 0,
    carrierId,
    appointCouponInfos,
  };
}

// * 获取会员权益说明文案
function getBenefitDesc(
  feature: ICustomerDiscountCarrierFeature,
  carrierBenefitDTOList: ICustomerDiscountCarrierBenefitDTO[]
) {
  return carrierBenefitDTOList
    .map((item) => {
      const { benefitType, benefitValue } = item;
      if (!benefitValue) return '';
      if (benefitType === BenefitTypeEnum.Discount) {
        return '部分商品会员价';
      }
      if (benefitType === BenefitTypeEnum.FreeShipping) {
        return '订单包邮';
      }
      if (benefitType === BenefitTypeEnum.PointsMultiplier) {
        const pointsTimes = Math.round(benefitValue / 10);
        return `${pointsTimes}倍积分`;
      }
      return '';
    })
    .filter(Boolean)
    .join('，');
}
function transformCustomerDiscountCarrierDTOListToMemberBenefits(list: any[]): any[] {
  return list.map((item) => {
    const { carrierId, feature, carrierName, carrierBenefitDTOList } = item;
    const { carrierType } = feature;
    const isBenefitCard = carrierType === CarrierTypeEnum.CARD;
    const validPeriodCopywriting = getBenefitDesc(feature, carrierBenefitDTOList);
    return {
      id: carrierId,
      available: true,
      valueDesc: isBenefitCard ? '权益卡' : '会员等级',
      name: carrierName,
      extraInfo: {
        validPeriodCopywriting,
      },
      chosen: item.chosen,
      singleTitle: !validPeriodCopywriting,
    };
  });
}

/**
 * * 积分抵现计算
 * 根据 canCostPoints/rate 算出可抵扣金额，返回的数组只有一项内容，valueDesc 为可抵现金额，unitDesc 为 '积分', name 为 `抵现${canCostPoints}元`
 * @param pointDeductionInfo
 */
function getPointsBenefits(pointDeductionInfo: IPointDeductionInfoDTO, pointsName: string) {
  if (!pointDeductionInfo) return [];
  const { canCostPoints, rate } = pointDeductionInfo;
  if (!canCostPoints || !rate) return [];
  const amount = parseFloat((canCostPoints / rate).toFixed(2));
  const pointsDesc = `${pointsName}抵现${amount}元`;

  return [
    {
      available: true,
      id: 21964401837,
      name: pointsDesc,
      unitDesc: pointsName,
      valueDesc: String(canCostPoints),
      singleTitle: true,
    },
  ];
}

/**
 * * 数据转换成 3 个数组，分别是：会员权益，优惠券，积分
 * @param result
 * @returns
 */
export function parseBenefitList(pointsName: string, result?: IBillingResponseItem) {
  if (!result) return [];
  const { customerDiscountCarrierDTOList = [], coupons = [], pointDeductionInfo = {} } = result;
  // * 会员优惠计算开始
  const memberBenefits = transformCustomerDiscountCarrierDTOListToMemberBenefits(
    customerDiscountCarrierDTOList
  );
  // * 会员优惠计算结束

  // * 优惠券计算开始
  const couponList = coupons
    .filter((item) => item.available)
    .map((item) => ({
      ...item,
      extraInfo: item.extra,
      chosen: item.isUse,
      canOverlying: item.isCanOverlying,
    }));
  // * 优惠券计算结束

  // * 积分抵现计算开始
  const pointsBenefits = getPointsBenefits(
    pointDeductionInfo as IPointDeductionInfoDTO,
    pointsName
  );
  const { orderPreference } = result;
  const pointsOrderPreference = orderPreference?.find((item) => item.promotionTypeId === 256);
  let chosenPointsBenefits: any = [];
  try {
    if (pointsOrderPreference?.extra && JSON.parse(pointsOrderPreference.extra).costPoints) {
      chosenPointsBenefits = pointsBenefits;
    }
  } catch (error) {}
  // * 积分抵现计算结束

  return [
    {
      title: '会员优惠',
      list: memberBenefits,
      chosenList: memberBenefits.filter((item) => item.chosen),
      type: SelectedBenefitTypeEnum.MEMBER,
    },
    {
      title: '优惠券',
      list: couponList,
      chosenList: couponList.filter((item) => item.chosen),
      type: SelectedBenefitTypeEnum.COUPON,
    },
    {
      title: '积分抵现',
      list: pointsBenefits,
      chosenList: chosenPointsBenefits,
      type: SelectedBenefitTypeEnum.POINTS,
    },
  ].filter((item) => item.list.length > 0);
}
