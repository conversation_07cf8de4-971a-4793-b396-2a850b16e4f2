<template>
  <div>
    <back-button v-if="platformType === 'kuaishou'"></back-button>
  </div>
</template>

<script>
import './fresh-food-shop-nav/FreshFoodShopNav';
import { WebLogger } from '@youzan/client-log-sdk';

const { platform = '', customCartName } = window._global || {};

export default {
  name: 'special-customization',

  components: {
    BackButton: () => import('./kuaishou-back/BackButton'),
  },

  props: {
    ctx: Object,
  },

  data() {
    return {
      platformType: platform,
    };
  },

  computed: {
    getCustomCartName() {
      let parse = {};
      try {
        parse = JSON.parse(customCartName.value);
      } catch (error) {
        parse = {};
      }
      return parse.label || '购物车';
    },
  },

  created() {
    // 同步独立域名 cookies 至 youzan.com
    WebLogger.syncTrackerCookies();
  },

  mounted() {
    // 自定义购物车名称，document.title 在 ios 微信下不生效
    document.querySelector('title').innerText = this.getCustomCartName;
  },
};
</script>
<style lang="scss">
/* 生鲜果蔬店铺导航 */
.has-local-nav {
  padding-bottom: 100px !important;

  .cart-bottom {
    bottom: 50px;
  }
}
</style>
