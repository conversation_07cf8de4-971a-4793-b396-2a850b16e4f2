import { getTradeApolloConfig } from '../../../lib/Apollo';

import { MAP_DEFAULT_KEY, MAP_VERSION } from '../../../constants/map';
import { Context } from 'astroboy';
import forIn from 'lodash/forIn';

// 获取对应地图服务的key
export const getMapKey = (v: MAP_VERSION, biz?: string) => {
  const mapKeyJson = getTradeApolloConfig('wsc-h5-trade.application', 'address-map-key');
  try {
    const mapKey = JSON.parse(mapKeyJson);
    const mapConfig = mapKey[v];
    // 如果有biz，则取serviceBizConfig下的key
    if (biz) {
      return mapConfig?.serviceBizConfig?.[biz] || mapConfig.serviceKey;
    }
    return mapConfig.serviceKey;
  } catch (err) {
    return MAP_DEFAULT_KEY[v];
  }
};

// 获取当前使用的地图服务的版本，没有配置则按环境分配
export const getMapVersion = (ctx: Context): MAP_VERSION => {
  const version = getTradeApolloConfig('wsc-h5-trade.application', 'address-map-api-version') || '';
  switch (version) {
    case MAP_VERSION.AMAP:
    case MAP_VERSION.QQMAP:
      return version;
    default:
      return ctx.isWeapp ? MAP_VERSION.QQMAP : MAP_VERSION.AMAP;
  }
};

/**
 * 经纬度字符串反转
 * @description 高德location入参格式为'lng,lat'，腾讯则为'lat,lng'，
 * 本api入参定义为'lng,lat'，请求腾讯地图用此函数进行转换
 * @param location 经纬度字符串 'lng,lat'
 * @return 经纬度字符串 'lat,lng'
 */
export const reverseLocationString = (location: string) => {
  const [lng, lat] = location.split(',');
  return [lat, lng].join(',');
};

// 获取高德地图字段的值，高德地图如果没有值返回为空数组，因此需要做判断处理
export const getAMapFieldValue = (data: { [t: string]: any }, key: string, defaultValue = '') => {
  const value = data[key];
  return Array.isArray(value) ? defaultValue : value;
};

/**
 * 高德地图返回数据存在脏数据，在没有数据时会返回空数组，比如city: []
 * 该方法进行数据清晰，将空数组赋值为''，方便后续数据处理
 */
export const getCleanData = (data: { [t: string]: any }) => {
  const cleanData: any = {};
  forIn(data, (value, key) => {
    cleanData[key] = Array.isArray(value) && !value.length ? '' : value;
  });
  return cleanData;
};

// 格式化地址，传入数组： [省，市，区，详细地址]，返回格式化后的详细地址字符串
// 目的是过滤“北京市北京市”的重复内容
export function stringifyAddress(addressList: string[]): string {
  return [...new Set(addressList)].join('');
}

// service方法返回泛形格式
export function invokeResponse<T>(status: number, msg: string, data: T): { status: number; message: string; data: T } {
  return {
    status,
    message: msg,
    data,
  };
}
