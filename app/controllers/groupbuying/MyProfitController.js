/* eslint-disable */
const PageException = require('@youzan/iron-base/app/exceptions/PageException');
const BaseController = require('../base/BaseController');
const MyProfitService = require('../../services/groupbuying/MyProfitService');
const StatisticsService = require('../../services/groupbuying/StatisticsService');

class MyProfitController extends BaseController {
  async init() {
    await super.init({
      validKdtId: true,
      initMpData: true,
      initMpAccount: true,
      initPlatform: true,
    });
  }

  async getIndexHtml(ctx) {
    const { kdtId, kdt_id } = ctx.query;
    if (kdtId || kdt_id) {
      ctx.kdtId = kdtId || kdt_id;
    }
    const { headerBuyerId } = ctx.getQueryData();
    const params = {
      kdtId: ctx.kdtId,
      headerBuyerId: headerBuyerId || this.buyer.buyerId,
    };
    const myProfitService = new MyProfitService(ctx);
    let response;
    try {
      response = await Promise.all([
        myProfitService.headerInfo(params),
        myProfitService.headerSettle(params),
      ]);
    } catch (error) {
      const content = error.errorContent;
      if (content) {
        throw new PageException(content.code, content.msg);
      } else {
        throw error;
      }
    }
    const [headerInfo, headerSettle] = response;
    ctx.setGlobal({
      headerInfo,
      headerSettle,
      kdt_id: ctx.kdtId,
    });
    await ctx.render('groupbuying/my-profit.html');
  }

  // 获取团长信息
  async getHeaderInfoJson(ctx) {
    const { headerBuyerId } = ctx.getQueryData();
    const params = {
      kdtId: ctx.kdtId,
      headerBuyerId: headerBuyerId || this.buyer.buyerId,
    };
    const myProfitService = new MyProfitService(ctx);
    const headerInfo = await myProfitService.headerInfo(params);
    ctx.r(0, '', headerInfo);
  }

  // 获取团长利润
  async getHeaderSettleJson(ctx) {
    const { headerBuyerId } = ctx.getQueryData();
    const params = {
      kdtId: ctx.kdtId,
      headerBuyerId: headerBuyerId || this.buyer.buyerId,
    };
    const myProfitService = new MyProfitService(ctx);
    const headerSettle = await myProfitService.headerSettle(params);
    ctx.r(0, '', headerSettle);
  }

  /**
   * 获取团长明细列表
   * @deprecated
   */
  async getProfitListJson(ctx) {
    const { page, pageSize, headerBuyerId } = ctx.getQueryData();
    const params = {
      headerBuyerId: headerBuyerId || this.buyer.buyerId,
      withItemInfo: false,
      sortBy: 'created_at',
      page: page || 1,
      pageSize: pageSize || 20,
      kdtId: ctx.kdtId,
      state: 5,
      source: 'PROFIT',
    };
    const myProfitService = new MyProfitService(ctx);
    const profitList = await myProfitService.getProfitList(params);
    ctx.r(0, '', profitList);
  }

  // 获取明细收益列表
  async getMyProfitListJson(ctx) {
    const { page, pageSize, activityId = '' } = ctx.getQueryData();
    if (activityId) {
      this.validator.isNumeric(activityId, '参数activityId不合法');
    }
    const params = {
      headerBuyerId: this.buyerId,
      kdtId: ctx.kdtId,
      page: +page || 1,
      pageSize: +pageSize || 10,
    };
    if (activityId) {
      params.activityId = activityId;
    }
    const profitList = await new StatisticsService(ctx).queryProfitList(params);
    ctx.json(0, '', profitList);
  }

  // 获取团长提现记录列表
  async getRecordListJson(ctx) {
    const { page, pageSize } = ctx.getQueryData();
    const params = {
      page: page || 1,
      pageSize: pageSize || 20,
      buyerId: this.buyer.buyerId,
    };
    const myProfitService = new MyProfitService(ctx);
    const recordList = await myProfitService.getRecordList(params);
    ctx.r(0, '', recordList);
  }

  // 获取团长提现详情
  async getWithdrawDetailJson(ctx) {
    const { waterNo, dataType } = ctx.getQueryData();
    const params = {
      waterNo,
      buyerId: this.buyer.buyerId,
      dataType,
    };
    const myProfitService = new MyProfitService(ctx);
    const withDrawDetail = await myProfitService.getWithdrawDetial(params);
    ctx.r(0, '', withDrawDetail);
  }

  // 获取微信授权
  async getWxBindWalletJson(ctx) {
    const params = {
      kdtId: ctx.kdtId,
      buyerId: this.buyer.buyerId,
    };
    const myProfitService = new MyProfitService(ctx);
    const wxBindWallet = await myProfitService.getWxBindWallet(params);
    ctx.r(0, '', wxBindWallet);
  }

  // 微信授权
  async postBindWxWalletJson(ctx) {
    const { nickName } = ctx.getQueryData();
    const { nick_name } = this.ctx.getLocalSession().buyer;
    const params = {
      kdtId: ctx.kdtId,
      nickName: nickName || nick_name,
      buyerId: this.buyer.buyerId,
    };
    const myProfitService = new MyProfitService(ctx);
    const flag = await myProfitService.bindWeChatWallet(params);
    ctx.r(0, '', flag);
  }

  // 获取团长可提现余额
  async getLeaderForwardJson(ctx) {
    const buyerId = ctx.getQueryData('buyerId');
    const params = {
      buyerId: buyerId || this.buyer.buyerId,
    };
    const myProfitService = new MyProfitService(ctx);
    const leaderForward = await myProfitService.getLeaderForward(params);
    ctx.r(0, '', leaderForward);
  }

  // 提现
  async postPutForwardJson(ctx) {
    const { money, acctType, bindCardId, buyerId } = ctx.getPostData();
    const params = {
      buyerId: buyerId || this.buyer.buyerId,
      money: +money,
      acctType: acctType || 'WEIXIN',
      bindCardId,
    };
    const myProfitService = new MyProfitService(ctx);
    const putForward = await myProfitService.putForward(params);
    ctx.r(0, '', putForward);
  }

  async querySalemanUpgradeResult(ctx) {
    const params = {
      userId: `${ctx.userId}`,
    };
    const myProfitService = new MyProfitService(ctx);
    const data = await myProfitService.querySalemanUpgradeResult(params);
    ctx.json(0, '', data);
  }
}

module.exports = MyProfitController;
