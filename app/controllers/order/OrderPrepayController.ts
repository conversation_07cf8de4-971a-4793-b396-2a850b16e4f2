import { Context } from 'astroboy';
import BaseController from '../base/BaseController';
import TradeService from '../../services/trade/TradeService';
import OrderContext from '../../lib/OrderContext';
import { OrderPaymentPreparationDTO } from 'definitions/order/buy/OrderPaymentPreparationDTO';

/**
 * nina待支付页面
 */
class OrderPrepayController extends BaseController {
  public async init(): Promise<void> {
    // 初始化订单上下文
    OrderContext.init(this.ctx);

    await super.init({
      validKdtId: true,
      initMpAccount: false,
      initGlobalTheme: true,
      initShopSettings: true,
      initShopMetaInfo: true
    });
  }

  private async callAcl(): Promise<void> {
    await this.needPlatformAcl();
  }

  /**
   * 入口页面
   */
  public async getIndexHtml(ctx: Context): Promise<void> {
    this.callAcl();
    if (ctx.status === 302) {
      return;
    }

    const { order_no: orderNo, kdt_id: queryKdtId, pay_way: payWay = '' } = ctx.query;
    const kdtId = queryKdtId || ctx.kdtId;
    const orderNos = Array.isArray(orderNo) ? orderNo : [orderNo];

    const preparePaymentParams: OrderPaymentPreparationDTO = {
      kdtId,
      orderNos,
      userAgent: ctx.userAgent,
      buyerId: this.buyerId,
      orderMark: '',
      forbidWxpay: 0
    };

    const result = await new TradeService(ctx).preparePaymentV2(preparePaymentParams);

    const redirect = result.redirectConfig || {};
    if (redirect.timeout || redirect.orderCanceled) {
      ctx.redirect(OrderContext.buildUrl(`/wsctrade/order/detail?order_no=${orderNo}&kdt_id=${kdtId}`, 'h5', kdtId));
    } else if (redirect.orderPaid) {
      ctx.redirect(OrderContext.buildUrl(`/wsctrade/order/payresult?request_no=${orderNo}&kdt_id=${kdtId}`, 'h5', kdtId));
    }

    let payWayDesc = '未付款';
    if (payWay.startsWith('WX')) {
      payWayDesc = '微信未付款';
    }

    const {
      proxy_weixin_openid: wxSubOpenId = '', // 大账号 openId
      verify_weixin_openid: wxSelfOpenId = '' // 自有粉丝 openId
    } = ctx.getLocalSession();

    ctx.setGlobal({
      payWayDesc,
      prepare: result,
      // 微信支付参数
      weixinPayParams: {
        wxSubOpenId,
        wxSelfOpenId
      }
    });
    await ctx.render('order/prepay.html');
  }
}

export = OrderPrepayController;
