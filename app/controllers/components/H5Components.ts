import { Context } from 'astroboy';
import BaseController from '../base/BaseController';
import { injectComponents } from '@youzan/h5-cpns-injector-plugin/async-inject';

class H5Components extends BaseController {
  constructor(ctx: Context) {
    super(ctx);
  }

  async getH5ComponentJson(ctx: Context) {
    const { components, extraData } = ctx.getQueryData();
    const result = await injectComponents(ctx, { components, extraData });
    ctx.json(0, 'ok', result);
  }
}

export = H5Components;
