import { escape as lodashEscape } from 'lodash';
import BaseController from '../base/BaseController';
import OrderDetailService from '../../services/order/OrderDetailService';
import OrderShareService from '../../services/order/OrderShareService';
import ShopConfigReadService from '../../services/shop/ShopConfigReadService';
import { getTradeApolloConfig } from '../../lib/Apollo';
import { PageException } from '@youzan/iron-base';
import mapKeysToCamelCase from '@youzan/utils/string/mapKeysToCamelCase';
import { IItemInfoDto } from 'definitions/order/OrderShare';
import { checkRetailHqStore } from '@youzan/utils-shop';

class OrderShareController extends BaseController {
  async init() {
    await super.init({
      validKdtId: true,
      initCopyrightFooter: true,
    });
  }

  /**
   * 使用白名单的方式配置部分商家在分享的订单的显示
   * 零售价格而非优惠后的价格
   */
  public async showOriginPrice() {
    const { ctx } = this;
    const { kdt_id: kdtId } = ctx.query;
    const orderShareShowOriginPrice =
      getTradeApolloConfig('wsc-h5-trade.application', 'orderShareShowOriginPrice') || '';
    const orderSharePageHideAvatar = getTradeApolloConfig('wsc-h5-trade.application', 'orderSharePageHideAvatar') || '';
    const isShowOriginPrice = orderShareShowOriginPrice.split(/[\t\s,]+/).indexOf(kdtId.toString()) > -1;
    const hideAvatar = orderSharePageHideAvatar.split(/[\t\s,]+/).indexOf(kdtId.toString()) > -1;
    ctx.json(0, 'success', { showOriginPrice: isShowOriginPrice, hideAvatar });
  }

  /**
   * 订单没有优惠价格的时候展示划线价格
   * @param itemInfo {IItemInfoDto} 订单商品信息
   */
  public async handleCrossLinePrice(itemInfo: IItemInfoDto[]): Promise<IItemInfoDto[]> {
    const { ctx } = this;
    try {
      // 订单没有优惠价格的时候展示划线价格
      const noDiscountItems = itemInfo
        .filter((item: IItemInfoDto) => {
          const { originUnitPrice = 0, unitPrice = 0 } = item;
          return originUnitPrice === unitPrice;
        })
        .map((item: IItemInfoDto) => item.goodsId);

      if (noDiscountItems.length > 0) {
        // 批量获取划线价格
        const goodsParams = {
          containItemMark: true,
          containsContent: false,
          itemIds: noDiscountItems,
          kdtId: ctx.kdtId,
          withDelete: false,
        };
        const crossLinePrice = await new OrderShareService(ctx).getGoodsInfo(goodsParams);

        // 更新itemInfo
        if (crossLinePrice instanceof Array && crossLinePrice.length > 0) {
          itemInfo = itemInfo.map((item: IItemInfoDto) => {
            if (noDiscountItems.indexOf(item.goodsId) > -1) {
              for (let i = 0; i < crossLinePrice.length; i++) {
                if (+crossLinePrice[i].id === +item.goodsId && crossLinePrice[i].origin) {
                  item.showCrossLinePrice = true;
                  item.crossLinePrice = crossLinePrice[i].origin;
                  break;
                }
              }
            }
            return item;
          });
        }
      }
      return itemInfo;
    } catch (e) {
      return itemInfo;
    }
  }

  /**
   * 小程序调用接口
   * 解耦获取订单信息接口，采用lightDetailByOrderNo仅获取需要展示的信息
   */
  public async getOrderInfo() {
    const { ctx } = this;
    const reqBody = ctx.getPostData();
    const { orderNo = '' } = reqBody;
    const kdtId = reqBody.kdtId || ctx.kdtId || 0;
    const params = {
      orderNo,
      kdtId,
      buyerId: 0,
      customerId: 0,
      customerType: 0,
      sourceName: this.sourceName,
    };
    let data = null;
    try {
      data = await new OrderDetailService(ctx).lightDetailByOrderNo(params);
    } catch (e) {
      const content = e.errorContent || {};
      throw new PageException(content.code || 10500, content.msg || '订单数据异常');
    }

    if (!data || Object.keys(data).length === 0) {
      // 无此订单
      throw new PageException(10500, '无此订单');
    }

    // 根据buyerId查询买家信息
    const { buyerInfo = {} } = data;
    let buyerDetail = { avatar: '', nickName: '无名大侠' };
    try {
      buyerDetail =
        (await new OrderShareService(ctx).getBuyerInfo({
          appName: 'wsc-h5-trade',
          businessId: kdtId,
          businessType: 1,
          userId: buyerInfo.buyerId,
        })) || buyerDetail;
    } catch (err) {}

    ctx.json(
      0,
      'success',
      mapKeysToCamelCase({
        buyerInfo: {
          avatar: buyerDetail.avatar,
          customerName: buyerDetail.nickName,
        },
        itemInfo: data.itemInfo.map((item: { goodsInfo: any; unitPrice: number; originUnitPrice: number }) => {
          return {
            ...item.goodsInfo,
            unitPrice: item.unitPrice,
            originUnitPrice: item.originUnitPrice,
          };
        }),
      })
    );
  }

  async getIndexHtml() {
    this.initNewShopSettings();

    const { ctx } = this;
    const { order_no: orderNo = 0, kdt_id: kdtId } = ctx.query;

    const { isWeappWebview } = ctx;

    const orderParams = {
      orderNo,
      kdtId,
      buyerId: 0,
      customerId: 0,
      customerType: 0,
      sourceName: this.sourceName,
    };

    let data = null;
    try {
      data = await new OrderDetailService(ctx).lightDetailByOrderNo(orderParams);
    } catch (e) {
      const content = e.errorContent || {};
      throw new PageException(content.code || 10500, content.msg || '订单数据异常');
    }
    if (!data || Object.keys(data).length === 0) {
      // 无此订单
      throw new PageException(10500, '无此订单');
    }

    const { mainOrderInfo = {} as Record<string, any>, itemInfo = [], buyerInfo = {} } = data;
    const { shopName, orderType } = mainOrderInfo;

    // 根据buyerId查询买家信息
    let buyerDetail = { avatar: '', nickName: '无名大侠' };
    try {
      buyerDetail =
        (await new OrderShareService(ctx).getBuyerInfo({
          appName: 'wsc-h5-trade',
          businessId: kdtId,
          businessType: 1,
          userId: buyerInfo.buyerId,
        })) || buyerDetail;
    } catch (err) {}

    // xss防御
    this.escapeGoods(itemInfo);

    // 获取历史订单的进店配置
    const orderEnterShopPolicy = await new ShopConfigReadService(ctx).getOrderEnterShopPolicy();

    // 判断是否为连锁总部
    const shopMetaInfo = await this.callService('iron-base/shop.ShopMetaReadService', 'getShopMetaInfo', kdtId);
    const isRetailHqShop = checkRetailHqStore(shopMetaInfo);

    ctx.setGlobal({
      shopName,
      itemInfo,
      orderNo,
      orderType,
      mainOrderInfo: {
        buyWay: mainOrderInfo.buyWay,
        isVirtual: mainOrderInfo?.isVirtual || false,
      },
      isWeappWebview,
      isWeapp: isWeappWebview,
      orderBuyerInfo: {
        avatar: buyerDetail.avatar,
        nickName: buyerDetail.nickName,
      },
      orderEnterShopPolicy,
      isRetailHqShop,
    });

    // 日志埋点模型
    this.setSpm('shareorder', kdtId);

    await ctx.render('order/share.html');
  }

  private escapeGoods(itemGoods: any = []) {
    itemGoods.forEach((item: any) => {
      const { goodsInfo } = item;
      if (goodsInfo) {
        goodsInfo.title && (goodsInfo.title = lodashEscape(goodsInfo.title));
        goodsInfo.shortTitle && (goodsInfo.shortTitle = lodashEscape(goodsInfo.shortTitle));
      }
      item.buyerMemo && (item.buyerMemo = lodashEscape(item.buyerMemo));
    });
  }
}

export = OrderShareController;
