import BaseController from '../base/BaseController';
import WholesalePaymentService from '../../services/wholesale/WholesalePaymentService';

class WholesalePaymentController extends BaseController {
  async queryPaymentVoucher() {
    const { ctx, buyer = {} } = this;
    const { orderNo } = ctx.getQueryData();

    const result = await new WholesalePaymentService(ctx).queryPaymentVoucher({
      orderNo,
      kdtId: ctx.kdtId,
      operatorId: buyer.buyerId,
      operatorName: buyer.fansNickname,
    });

    ctx.json(0, 'success', result);
  }
}

export = WholesalePaymentController;
