<template>
  <van-popup
    v-model="show"
    closeable
    position="bottom"
    safe-area-inset-bottom
    class="coupon-popup"
    @close="handleClose"
  >
    <h3 class="coupon-popup__title">优惠券</h3>
    <div class="coupon-popup__content">
      <cap-coupon
        v-for="coupon in coupons"
        v-bind:key="coupon.id"
        class="coupon-card"
        :coupon="coupon"
        :btn-text="!coupon.isAvailable ? '立即领取' : ''"
        btnIcon="false"
        :display-type="displayType"
        :theme-type="themeType"
        :bottomRightIcon="coupon.isAvailable"
        @click-btn="handleSendCoupon(coupon)"
      />
    </div>
  </van-popup>
</template>

<script>
import { Popup } from 'vant';
import { Coupon } from '@youzan/captain';
import { Logger } from 'common/log/logger';
import { BUYER_ID } from '@biz/cart/constant';

const { Description, ExtraInfo } = Coupon;
const { themeColors } = _global;

export default {
  name: 'CouponPopup',

  data() {
    this.firstPopup = true;

    return {
      show: this.showPopup,
      displayType: 'flat',
      themeType: {
        bgColor: themeColors['ump-coupon-bg'],
        mainColor: themeColors['ump-icon'],
        borderColor: themeColors['ump-border'],
        stampBorderColor: themeColors['ump-icon'],
        stampBgColor: themeColors['ump-tag-bg'],
        stampTextColor: themeColors['ump-icon'],
      },
    };
  },

  components: {
    [Popup.name]: Popup,
    [Coupon.name]: Coupon,
    [Description.name]: Description,
    [ExtraInfo.name]: ExtraInfo,
  },

  props: {
    couponList: Array,
    showPopup: Boolean,
  },

  watch: {
    showPopup(val) {
      this.show = val;

      if (val && this.firstPopup) {
        this.firstPopup = false;

        this.couponList.forEach((coupon) => {
          Logger.log({
            et: 'view', // 事件类型
            ei: 'view', // 事件标识
            en: '领券列表的券曝光', // 事件名称
            params: {
              item_type: 'coupon',
              item_id: coupon.id,
              component: 'takeCoupon',
              kdt_id: coupon.kdtId,
              buyer_id: BUYER_ID,
            }, // 事件参数
          });
        });
      }
    },
  },

  computed: {
    coupons() {
      return this.couponList.map((coupon) => ({
        id: coupon.id,
        value: coupon.value_copywriting,
        unit: coupon.unit_copywriting,
        name: coupon.title,
        validTime: coupon.valid_time_copywriting,
        threshold: coupon.use_threshold_copywriting,
        isAvailable: coupon.isAvailable,
        kdtId: coupon.kdt_id,
      }));
    },
  },

  methods: {
    handleClose() {
      this.$emit('close');
    },
    handleSendCoupon(coupon) {
      this.$emit('sendCoupon', coupon);

      Logger.log({
        et: 'click', // 事件类型
        ei: 'take_coupon_click_take', // 事件标识
        en: '领券列表的券点击领取', // 事件名称
        params: {
          item_type: 'coupon',
          item_id: coupon.id,
          component: 'takeCoupon',
          kdt_id: coupon.kdtId,
          buyer_id: BUYER_ID,
        }, // 事件参数
      });
    },
  },
};
</script>

<style lang="scss">
.coupon-popup {
  width: 100%;
  min-height: 50vh;
  max-height: 80vh;
  border-radius: 20px 20px 0 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  &__title {
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 22px;
    text-align: center;
    color: #323233;
    margin-top: 12px;
  }

  &__content {
    padding: 16px 16px 32px;
    overflow: auto;
    font-size: 13px;
    box-sizing: border-box;
    -webkit-overflow-scrolling: touch;
    z-index: 1;
    position: relative;
    flex: 1;

    > p {
      margin-bottom: 15px;
    }
  }
  .coupon-card {
    .cap-couponbox__card {
      border: 1px solid;
      background-color: #fff8f8 !important;
      border-color: #ffd4d4 !important;
      box-shadow: none;
    }
    .cap-coupon-description__content {
      color: #969799;
    }
  }
}
</style>