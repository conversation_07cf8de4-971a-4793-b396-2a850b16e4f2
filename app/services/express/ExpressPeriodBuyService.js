const BaseService = require('../base/BaseService');

class ExpressPeriodBuyService extends BaseService {
  get DELIVERY_SERVICE() {
    return 'com.youzan.ebiz.mall.trade.buyer.api.service.DeliveryService';
  }

  // 获取周期购订单的配送日历
  async getDeliveryCalendar(params) {
    return this.invoke(this.DELIVERY_SERVICE, 'getDeliveryCalendar', [params]);
  }

  // 获取期次的配送详情
  async getPeriodDeliveryDetail(params) {
    return this.invoke(this.DELIVERY_SERVICE, 'getPeriodDeliveryDetail', [params]);
  }

  // 顺延配送期次
  async postponeIssue(params) {
    return this.invoke(this.DELIVERY_SERVICE, 'postpone', [params]);
  }

  // 取消顺延配送期次
  async cancelPostponeIssue(params) {
    return this.invoke(this.DELIVERY_SERVICE, 'cancelPostpone', [params]);
  }
}

module.exports = ExpressPeriodBuyService;
