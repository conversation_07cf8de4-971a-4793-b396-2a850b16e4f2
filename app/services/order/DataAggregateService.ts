import BaseService from '../base/BaseService';

interface IRequestParam {
  kdtId: number;
  itemIds: number[];
}

interface IItemCompositeId {
  itemId: number;
  skuId: number;
}

interface IItemsEvaluationLabelRequest {
  kdtId: number;
  items: IItemCompositeId[];
}

interface IResponseData {
  [itemId: number]: number;
}

interface IEvaluationEnrichmentRequest {
  itemId: number;
  skuId: number;
  orderNo: string;
  kdtId: number;
  buyerId: number;
  content: string;
}

class DataAggregateService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.social.ebiz.content.api.evaluation.service.DataAggregateService';
  }

  get SHOP_EVALUATION_SERVICE_NAME() {
    return 'com.youzan.social.ebiz.content.api.evaluation.service.ShopEvaluationService';
  }

  /**
   *  获取发布评价页猜你想说标签
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1268032
   */
  async getItemsEvaluateLabel(params: IRequestParam): Promise<IResponseData> {
    return this.invoke('getItemsEvaluateLabel', [params]);
  }

  /**
   *  评价推荐标签
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1509570
   *
   *  @param {Object} request -
   *  @param {number} request.kdtId -
   *  @param {Array.<Object>} request.items[] -
   *  @param {integer} request.items[].itemId - spu id
   *  @param {integer} request.items[].skuId - sku id
   *  @return {Promise}
   */
  async getItemsEvaluationLabels(request: IItemsEvaluationLabelRequest) {
    return this.invoke(this.SHOP_EVALUATION_SERVICE_NAME, 'getItemsEvaluationLabels', [request], {
      timeout: 10000, // ajax超时
      headers: {
        'X-Timeout': 10000, // tether超时
      },
    });
  }

  /**
   *  扩写评价
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1509776
   *
   *  @param {Object} request -
   *  @param {number} request.itemId -
   *  @param {string} request.orderNo -
   *  @param {number} request.kdtId -
   *  @param {number} request.buyerId -
   *  @param {string} request.content -
   *  @return {Promise}
   */
  async enrichEvaluationContent(request: IEvaluationEnrichmentRequest) {
    return this.invoke(this.SHOP_EVALUATION_SERVICE_NAME, 'enrichEvaluationContent', [request], {
      timeout: 10000, // ajax超时
      headers: {
        'X-Timeout': 10000, // tether超时
      },
    });
  }
}
export default DataAggregateService;
