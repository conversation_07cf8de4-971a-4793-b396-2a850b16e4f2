import { getTradeApolloConfig, getH5GoodsApolloConfig, getH5FenxiaoApolloConfig } from './Apollo';

/**
 * 解析apollo配置的切流决策
 * apollo的文本配置以半角逗号分隔，百分比值写在第一项，其次白名单，最后黑名单
 * 遇到0或者0%会判为不匹配
 * '%1,491391,603367,-160,-11998'
 * const useNew = isInGrayReleaseByKdtId(ctx, { namespace: '', key: '' }, kdtId);
 */
export function matchGrayConfig(config: string, kdtId: JSNumber): boolean {
  const kdtIdList = config.split(',');
  // 先判断0或者黑名单的情况
  if (kdtIdList.includes('0') || kdtIdList.includes('0%') || kdtIdList.includes('-' + kdtId)) {
    return false;
  } else if (kdtIdList.includes(String(kdtId))) {
    // kdtId全匹配
    return true;
  } else if (config.indexOf('%') > 0) {
    // 百分比判断
    const percentArr = kdtIdList.filter((singleConfig: string) => {
      return singleConfig.endsWith('%');
    }).map((singleConfig: string) => {
      return singleConfig.slice(0, singleConfig.length - 1);
    });
    if (percentArr && percentArr.length) {
      // 只取第一个百分比配置
      const onlyPercent = Number(percentArr[0]);
      return !!(onlyPercent >= 0 && onlyPercent <= 100 && Number(kdtId) % 100 <= onlyPercent);
    } else {
      return false;
    }
  } else {
    return false;
  }
}


const checkGaryReleaseHandler = (getConfig: (namespace: string, key: string) => string) => {
  return async function isInGrayReleaseByKdtId(ctx: any, { namespace, key }: { namespace: string, key: string }, kdtId: JSNumber) {
    let matched = false;
    try {
      const configList = getConfig(
        namespace,
        key
      ) || '';
  
      matched = matchGrayConfig(configList, kdtId);
    } catch (e) {
      // console.log('get apollo config error:', e);
      matched = false;
    }
    // console.log('matched:', matched);
  
    return matched;
  }
}

/**
 * 解析apollo配置的切流决策
 * @param ctx 
 * @param { namespace, key: '只支持单key' }
 * @param kdtId 
 */
export const isInGrayReleaseByKdtId = checkGaryReleaseHandler(getTradeApolloConfig);

// 与上面效果一致，不同的是根据h5-goods下的Apollo配置去计算
export const isInGrayReleaseByKdtIdForGoodsApollo = checkGaryReleaseHandler(getH5GoodsApolloConfig);

// 与上面效果一致，不同的是根据h5-fenxiao下的Apollo配置去计算
export const isInGrayReleaseByKdtIdForQttApollo = checkGaryReleaseHandler(getH5FenxiaoApolloConfig);