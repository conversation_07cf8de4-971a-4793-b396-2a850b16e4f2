{% extends '../common/base.html' %}

{% block page_title %} 微信好物圈 {% endblock %}

{% block page_style %}
  {{ loadCss('weapp/shopping-list') | safe }}
{% endblock %}

{% block page_content %}
<div class="component">
  <div class="icon"></div>
  <div class="title">微信好物圈</div>
  <div class="desc">微信好物圈是由微信提供的订单和商品管理工具。你可以通过购物单，统一查看并管理所有小程序的已购订单和购物车商品。</div>
  <img class="image" src="https://img01.yzcdn.cn/public_files/2018/09/05/342b3cfdedc9eabbfe7b5b029bd504f4.png">

  <div class="card">
    <div class="word">1. 打开 “发现” - “小程序”</div>
    <div class="word">2. 点击搜索框，即可看到购物单入口</div>
  </div>
</div>
{% endblock %}
