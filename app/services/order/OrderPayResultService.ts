import BaseService from '../base/BaseService';
import buildUrlWithCtx from '@youzan/utils/url/buildUrlWithCtx';
import { PayResultParam, IWxPayResultRequestDTO, GetCouponMessageParam } from 'definitions/pay/PayResult';

class OrderPayResultService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.trade.product.api.payresult.PayResultService';
  }

  /**
   * 修改支付有礼信息
   * @param paidPromotion 支付有礼信息
   * @param orderNo 订单ID
   */
  patchPaidPromotion(paidPromotion: any, orderNo: string) {
    const { detailUrl, promotionType } = paidPromotion;
    const buildUrl = buildUrlWithCtx(this.ctx);
    if (detailUrl && ['tradeincard', 'promocode', 'couponpackage'].indexOf(promotionType) !== -1) {
      paidPromotion.detailUrl = buildUrl(
        `https://h5.youzan.com/wscump/marketing/paidpromotion/take?order_no=${orderNo}`,
        '',
        this.ctx.ktdId
      );
    }
  }

  /**
   *  支付结果页
   *
   *  ZanAPI: https://zanapi.qima-inc.com/site/service/view/194158
   *  天网: https://ops.qima-inc.com/v3/skynet/#/main/prod/log/search/all-log?appName=wsc-h5-trade&order=DESC&queryString=&tags=methodName%3DgetPayResult&tags=serviceName%3DPayResultService
   *
   *  @param {object} params
   *  @returns {object}
   */
  async getPayResult(params: PayResultParam) {
    return this.invoke('getPayResult', [params]);
  }

  /**
   *  商家小票支付结果页接口
   *  @param {object} params
   *  @returns {object}
   */
  async getWxPayResult(params: IWxPayResultRequestDTO) {
    return this.invoke('getWxPayResult', [params]);
  }

  /**
   *  获取支付结果接口（支付结果页轮询获取支付结果）
   *  @param {object} params
   *  @returns {object}
   */
  async checkPay(params: PayResultParam) {
    return this.invoke('checkPay', [params]);
  }

  /**
   *  支付结果页异步获取优惠券信息
   *  @param {object} params
   *  @returns {object}
   */
  async getCouponMessage(params: GetCouponMessageParam) {
    return this.invoke('getCouponMessage', [params]);
  }

  /**
   *  异步查询自提信息接口
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/263241
   *
   *  @param {object} params
   *  @param {number} params.phase - 阶段 无阶段概念用0，有阶段概念从1开始计数
   *  @param {Object} params.buyerDTO - 买家信息
   *  @param {integer} params.buyerDTO.fansId - 分析信息
   *  @param {integer} params.buyerDTO.buyerId - 用户Id(必填)
   *  @param {integer} params.buyerDTO.fansType - 粉丝类型
   *  @param {string} params.requestNo - 订单号order_no或批量支付单
   *  @return {object}
   */
  async getSelfFetchMessage(params: PayResultParam) {
    return this.invoke('getSelfFetchMessage', [params]);
  }

  /**
  *  自提页查询优惠券信息；
  查询用户未领取到店自提权益或门店专享券，优先展示未领取到店自提权益；
  *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1112744 
  *
  *  @param {Object} request - 
  *  @param {number} request.kdtId - 店铺id
  *  @param {number} request.userId - 当前用户ID
  *  @return {Promise}
  */
  async getSelfFetchCoupons(request: { kdtId: number; userId: number }) {
    return this.invoke('getSelfFetchCoupons', [request]);
  }

  /**
   *  异步查询自提取货信息接口
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1304848
   *
   *  @param {Object} getSelfFetchRequestDTO -
   *  @param {number} getSelfFetchRequestDTO.phase - 阶段 无阶段概念用0，有阶段概念从1开始计数
   *  @param {Object} getSelfFetchRequestDTO.buyerDTO - 买家信息
   *  @param {integer} getSelfFetchRequestDTO.buyerDTO.fansId - 粉丝信息
   *  @param {integer} getSelfFetchRequestDTO.buyerDTO.buyerId - 用户Id(必填)
   *  @param {integer} getSelfFetchRequestDTO.buyerDTO.fansType - 粉丝类型
   *  @param {string} getSelfFetchRequestDTO.requestNo - 订单号order_no或批量支付单
   *  @return {Promise}
   */
  async getSelfFetchTakeGoodsMessage(request: {
    requestNo: string;
    buyerDTO: { buyerId: number; fansId?: string; fansType?: string };
  }) {
    return this.invoke('getSelfFetchTakeGoodsMessage', [request]);
  }
}

export default OrderPayResultService;
