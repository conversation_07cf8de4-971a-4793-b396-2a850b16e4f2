import BaseService from '../base/BaseService';
import { CommonReCommendDTO, ChainAndRetailReCommendDTO, IRecommendParam } from 'definitions/other/RecommendGoods';

class GoodsRecommendService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.bigdata.datacenter.consumer.api.service.GoodsService';
  }

  /**
   *  普通微商城商品推荐接口
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/108985
   */
  async getCommonRecommend(params: CommonReCommendDTO) {
    return this.invoke('listRecommendGoods', [params]);
  }

  /**
   *  零售和连锁店铺商品推荐接口
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/501916
   */
  async getChainAndRetailRecommend(params: ChainAndRetailReCommendDTO) {
    return this.invoke('getRecommendResult', [params]);
  }

  async getRecommandGoodsOrderDetailAddToCart(params: IRecommendParam) {
    return this.invoke('getRecommendResultData', [params]);
  }
}

export = GoodsRecommendService;
