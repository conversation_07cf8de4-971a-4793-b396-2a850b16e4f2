import formatDate from '@youzan/utils/date/moment';
import formatMoney from '@youzan/utils/money/format';
import { activityMap, getMonthDay, getGoodDesc } from '../utils/utils';

// 预售文案提示
const expressTipsMap = {
  0: ['发货', '后发货', '开始发货'],
  1: ['可自提', '后可自提', '开始可自提'],
  2: ['配送', '后配送', '开始配送'],
};
function getPresaleTips(item, pay, expressType) {
  const startTime = item.presaleStartTime;
  const endTime = item.presaleEndTime;
  const [expressName, laterExpressName, startExpressName] = expressTipsMap[expressType];

  if (!item.presale) {
    return '';
  }

  if (item.presaleTimeType === 0) {
    const now = new Date();
    const presaleDate = new Date(startTime);
    const dateFormat =
      (now.getFullYear() === presaleDate.getFullYear() ? '' : 'YYYY年') + 'MM月DD号';
    return `${formatDate(presaleDate, dateFormat)} ${startExpressName}`;
  }
  if (item.presaleTimeType === 1) {
    const text = pay.multiPhase ? '尾款支付' : '付款';
    return `${text} ${item.presaleStartTimeAfterPay} 天${laterExpressName}`;
  }
  const startTimeStr = formatDate(startTime, 'MM月DD号');
  const endTimeStr = formatDate(endTime, 'MM月DD号');

  if (startTime && endTime) {
    return (
      '预计 ' + (startTimeStr === endTimeStr ? startTimeStr : startTimeStr + ' ~ ' + endTimeStr)
    );
  }
  if (startTime) {
    return startTimeStr + startExpressName;
  }
  if (endTime) {
    return '最晚' + endTimeStr + expressName;
  }

  return '';
}

function getFormattedGoods({ state, getters }) {
  const { goods, memberCard, shop } = state;
  return goods.list
    .filter((item) => !item.fromTmpAdded)
    .map((item) => {
      // 预售商品
      // 全款预售自提和配送都需要展示配送提示，定金预售不变
      if (item.presale && (getters.expressType === 'express' || item.presaleType === 0)) {
        // 定金预售的文案都是发货
        const expressType = item.presaleType === 1 ? 0 : getters.postage.currentExpressType;
        item.showDeliveryTime = true;
        item.deliveryTime = getPresaleTips(item, state.pay, expressType);
      }

      // 会员卡续费商品
      if (memberCard.renewal && memberCard.memberCardStartTime && memberCard.memberCardEndTime) {
        item.memberCardEndTime = formatDate(memberCard.memberCardEndTime, 'YYYY.MM.DD');
        item.memberCardStartTime = formatDate(memberCard.memberCardStartTime, 'YYYY.MM.DD');
      }

      if (item.present) {
        item.acvitityTag = '赠品';
      } else if (+item.haitao === 1) {
        item.acvitityTag = '海淘';
      } else if (item.isUseGoodsExchangeCoupon) {
        item.acvitityTag = '兑换券';
      } else if ('activityType' in item) {
        item.acvitityTag = activityMap[item.activityType];
      }

      // 电子卡券
      item.isECard = item.virtualType === 3;
      if (item.isECard) {
        const { quotaNum, stockNum, buyedNum = 0 } = item;

        // 没有库存时视为异常状态，不可修改商品数量
        // 商品中有兑换券商品时，所有电子卡券商品都不能修改数量
        item.canChangeNum =
          stockNum != null &&
          stockNum !== '' &&
          goods.list.every((item) => !item.isUseGoodsExchangeCoupon);

        if (item.canChangeNum) {
          item.maxNum = quotaNum > 0 ? Math.min(quotaNum - buyedNum, stockNum) : stockNum;
        } else {
          // 不可修改数量时给 maxNum 设置一个数字，避免 van-stepper 内部报错
          item.maxNum = 2147483647;
        }
      }

      item.tags = [];
      item.presale && item.tags.push('预售');
      item.isFission && item.tags.push('内购价');
      item.quickRefund && item.tags.push('自动退款');
      getters.isPeriodBuy && item.tags.push('周期购');
      shop.activityType === 20 && item.tags.push('F码专享');
      shop.activityType === 21 && item.tags.push('砍价');

      item.url = item.imgUrl;
      // 实付价
      item.payPriceStr = item.payPrice ? formatMoney(item.payPrice) : 0;

      item.desc = getGoodDesc(item, state.memberCard);

      // 显示原价的逻辑
      if (item.price > item.payPrice && item.payPrice) {
        item.PriceStr = formatMoney(item.price);
      }

      // 酒店商品
      if (state.tradeTag.hasHotelGoods) {
        item.goodsDate = getMonthDay(new Date(item.hotelSkuDate));
      }

      // item.message 为空对象时，将 item.message 置为null
      if (JSON.stringify(item.message) === '{}') {
        item.message = null;
      }

      return item;
    });
}

export { getFormattedGoods };