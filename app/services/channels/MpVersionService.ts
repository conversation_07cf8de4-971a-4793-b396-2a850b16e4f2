import BaseService from '../base/BaseService';

interface IGetMpVersionDto {
  accountType: number;
  bundleId?: string;
  businessType: number;
  kdtId: number;
  mpId?: number;
}
class MpVersionService extends BaseService {
  get MP_VERSION_SERVICE() {
    return 'com.youzan.channels.apps.service.MpVersionService';
  }

  /**
   * 通过kdtID查店铺线上小程序版本
   * http://zanapi.qima-inc.com/site/service/view/475884
   * @param params
   */
  public async getMpVersion(params: IGetMpVersionDto): Promise<any> {
    return this.invoke(this.MP_VERSION_SERVICE, 'getMpVersion', [params]);
  }
}

export default MpVersionService;
