import { app, mock } from '@youzan/astroboy-mock/bootstrap';
import BaseController from 'app/controllers/base/BaseController';
import OrderDetailService from 'app/services/order/OrderDetailService';

jest.mock('app/controllers/base/BaseController', () =>
  jest.requireActual('app/__tests__/__mocks__/BaseController')
);

jest.mock('app/services/order/OrderDetailService');

afterEach(() => {
  BaseController.mockClear();
  OrderDetailService.mockClear();
});

test('getOrderInfo', async () => {
  app.mockContext({
    buyerId: 8000035538,
    kdtId: 491391,
  });

  OrderDetailService.mockImplementation(() => {
    return {
      detailByOrderNo: jest.fn().mockResolvedValue([]),
    };
  });

  const res = await app
    .httpRequest()
    .post('/wsctrade/order/detail/getOrderInfo.json');

  expect(res.body).toEqual({
    code: 0,
    data: [],
    msg: '',
  });
});
