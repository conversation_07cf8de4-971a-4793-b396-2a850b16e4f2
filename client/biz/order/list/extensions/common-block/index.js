import { api as http } from 'common/api';
import get from 'utils/get';
import { camelize } from 'utils/camelize';
import { Toast } from 'vant';
import { formatOrderList } from '../../common/format';
import * as SafeLink from '@youzan/safe-link';
import { ButtonHandler } from '../../common/buttonHandler';
import args from '@youzan/utils/url/args';
import chinaMobile from '@youzan/utils/validate/chinaMobile';
import { TITLE_CONF } from '../../common/constant';
import queryString from 'query-string';
import { getLevel } from '../../scrm-level'
import { isWeixin } from '@youzan/utils/browser/ua_browser';

const global = window._global;
const PAGE_SIZE = 10;

export default class PageCommonBlock {
  constructor(options) {
    this.ctx = options.ctx;

    this.ctx.data.kdtId = global.kdtId || 0;
    this.ctx.data.searchCondition = { value: '' };
    this.ctx.data.bizName = 'order_list';
    
    this.openAppConfig = camelize(get(global, 'open_app_config'));
    this.orderMark = global.orderMark || '';
    this.pageType = global.pageType;
    this.isYZEdu = get(_global, 'shopMetaInfo.shopTopic', 0) === 1;

    this.ctx.data.activityTab = {
      hasNext: true,
      list: [],
      page: 0,
    };

    this.getUseUmpGray();
    this.initEventListen();
    this.initProcessDefine();

    window.addEventListener('pageshow', function(e) {
      if (e.persisted) {
        window.location.reload();
      }
    });
  }

  // 重置tab信息
  resetTab() {
    this.ctx.data.activityTab = {
      ...this.ctx.data.activityTab,
      hasNext: true,
      list: [],
      page: 0,
    };
  }

  refreshList() {
    this.resetTab();
  }

  isDrugList() {
    const search = window.location.search
    const { type } = queryString.parse(search)
    return type === 'drug'
  }

  initProcessDefine() {
    this.ctx.process.define('fetchMoreOrder', () => {
      const { activityTab, searchCondition } = this.ctx.data;
      const { pageType, orderMark, isYZEdu } = this;
      const currentType = activityTab.type;

      let paramsFormater = {
        page_id: activityTab.pageId || 'wsc',
        page: activityTab.page + 1,
        page_type: pageType,
        page_size: activityTab.pageSize || 10,
        type: activityTab.type || 'all',
        orderMark,
        from: '',
        isDrugList: this.isDrugList()
      };

      // 爱逛默认查询所有店铺订单数据
      if (!paramsFormater.page_type && orderMark === 'weapp_guang') {
        const kdtId = args.get('kdt_id', window.location.href);
        if (kdtId) {
          paramsFormater.page_type = 'single';
        } else {
          paramsFormater.page_type = 'all';
        }
      }

      const searchValue = get(searchCondition, 'value', '');

      if (chinaMobile(searchValue)) {
        paramsFormater.receivertel = searchValue;
      } else if (searchValue) {
        paramsFormater.keyword = searchValue;
      }

      if (
        isYZEdu &&
        (paramsFormater.type === 'going' || paramsFormater.type === 'send')
      ) {
        // 1 表示查询知识付费订单 0 表示排除知识付费订单
        paramsFormater.knowledgeGoods = paramsFormater.type === 'going' ? 1 : 0;
      }

      // 定制扩展点
      const extraData = this._getExtraData();

      extraData.forEach((data) => {
        paramsFormater = {
          ...paramsFormater,
          ...data
        };
      });
      // 记住scrollY
      const scrollY = window.scrollY;
      return http
        .get({
          url: '/wsctrade/order/list.json',
          data: paramsFormater,
        })
        .then(({ data = {} }) => {
          const newData = camelize(data);
          if (newData.list) {
            newData.list = formatOrderList(this.ctx, newData.list, {
              openAppConfig: this.openAppConfig,
            });
          }
          // 如果请求刚返回时，tab已被切走，则返回
          if (this.ctx.data.activityTab.type !== currentType) {
            return;
          }

          this.ctx.data.activityTab = {
            ...activityTab,
            hasNext: newData.hasNext,
            list: [...activityTab.list, ...newData.list],
            page: newData.page,
            pageId: newData.pageId,
            pageSize: newData.pageSize,
          };
          // 数据更新后，滚动到scrollY
          window.scrollTo(0, scrollY);

          return {
            loading: false,
          };
        })
        .catch((error) => {
          console.log(error);
          this.ctx.data.activityTab = {
            ...this.ctx.data.activityTab,
            hasNext: false,
          };
          throw new Error(error.msg || '服务器开小差了');
        });
    });
  }

  initEventListen() {
    // 监听 tab 切换
    this.ctx.event.listen('activityTabChange', (tab = {}) => {
      document.querySelector('title').innerText = TITLE_CONF[tab.type] || '订单列表';
      const { activityTab } = this.ctx.data;
      if (activityTab.type === tab.type) return;

      this.ctx.data.activityTab = {
        ...tab,
        hasNext: true,
        list: [],
        page: 0,
        pageId: ['all', 'topay'].indexOf(tab.type) >= 0 ? 'wsc' : '1',
        pageSize: PAGE_SIZE,
      };
    });

    // 监听搜索
    this.ctx.event.listen('searchChange', (val) => {
      console.log('searchChange', val);
      this.ctx.data.searchCondition = val;

      // 重置当前 tab 数据，触发list watch，获取列表
      this.ctx.data.activityTab = {
        ...this.ctx.data.activityTab,
        hasNext: true,
        list: [],
        page: 0,
      };
    });

    // 底部按钮处理
    this.ctx.event.listen(
      'doOrderFooterAction',
      ({ btn = {}, listIndex, listItem, extraData = {} }) => {
        const { type } = btn;
        const directBuyAgainBtnConfig = get(window, '_global.directBuyAgainBtnConfig', {});
        // 非微信H5环境，找人代付页面无法支付
        if (type === 'peerpay' && !isWeixin()) {
           Toast('此渠道暂不支持代付');
           return;
        }
        const orderItem = get(listItem, 'orderItems', [])[0] || {};
        const { kdtId, orderNo, repurchaseCoupon, orderPermission } = listItem;
        const buttonHandler = new ButtonHandler({
          orderNo,
          kdtId,
          orderItem,
          button: btn,
          directBuyAgainBtnConfig,
          extraData,
          repurchaseCoupon,
          orderPermission,
        });

        const directUrl = buttonHandler.getDirectUrl();
        const payGradeCard = get(orderItem, 'items[0].payGradeCard', false);

        switch (type) {
          case 'shared':
            this.ctx.event.emit('handleShare', {
              listItem,
            });
            break;
          case 'cancelPayOnDelivery':
            buttonHandler.cancelPayOnDelivery();
            break;
          case 'laterReceive':
            buttonHandler.laterReceive();
            break;
          case 'confirmReceive':
          case 'confirmReceiveHotel':
            buttonHandler.confirmReceive().then(() => {
              this.refreshOrderInfo({
                listIndex,
                orderNo,
              });
            });
            break;
          case 'cancel':
            buttonHandler.cancel({
              callback: () => {
                this.refreshList();
              },
            });
            break;
          case 'buyAgain':
            buttonHandler.buyAgain();
            break;
          case 'directBuyAgain':
            buttonHandler.directBuyAgain();
            break;
          // 删除订单
          case 'deleteOrder':
            buttonHandler.delete({
              callback: () => {
                this.deleteOrderInfo({
                  listIndex,
                  orderNo,
                });
              },
            });
            break;
          case 'expressReminder':
          case 'expressReminderHotel':
            buttonHandler
              .expressReminder({
                message:
                  type === 'expressReminderHotel'
                    ? '提醒卖家接单消息发送成功'
                    : '',
              })
              .then(() => {
                if (listItem.order_permission) {
                  listItem.order_permission.is_show_reminder = false;
                  this.$commit('UPDATE_LIST_ITEM', {
                    listIndex,
                    orderList: [listItem],
                  });
                }
              });
            break;
          default:
            if (payGradeCard) {
              // 判断是否属于续费商品
              const goodsId = get(orderItem, 'items[0].goodsId', '');
              const skuId = get(orderItem, 'items[0].sku[0].vId');
              getLevel({
                kdtId,
                goodsId,
                skuId,
              }).then((h5Url) => {
                if (h5Url) {
                  SafeLink.redirect({
                    url: h5Url,
                  });
                } else {
                  SafeLink.redirect({
                    url: directUrl,
                    kdtId,
                  });
                }
              });
            } else {
              SafeLink.redirect({
                url: directUrl,
                kdtId,
              });              
            }
            break;
        }
      }
    );
  }

  updateOrderItem(listIndex, newData) {
    try {
      const oldList = JSON.parse(JSON.stringify(this.ctx.data.activityTab.list));
      if (newData) {
        oldList.splice(listIndex, 1, ...newData);
      } else {
        oldList.splice(listIndex, 1);
      }
      this.ctx.data.activityTab = {
        ...this.ctx.data.activityTab,
        list: oldList
      };
    } catch (e) {
      console.error(e);
    }
  }

  // 更新一条订单数据
  refreshOrderInfo({ listIndex, orderNo }) {
    // 待收货列表收货后直接不展示该订单
    if (this.ctx.data.activityTab.type !== 'all') {
      this.updateOrderItem(listIndex);
      return;
    }
    return http
      .get({
        url: '/wsctrade/order/oneOrder.json',
        data: {
          order_no: orderNo,
        },
      })
      .then(({ data = {} }) => {
        data = camelize(data);
        data.list = formatOrderList(this.ctx, data.list || [], {
          openAppConfig: this.openAppConfig,
        });
        if (data.list.length) {
          this.updateOrderItem(listIndex, data.list);
        }
      })
      .catch((error) => {
        console.log(error);
        throw new Error(error.msg || '服务器开小差了');
      });
  }
  // 在当前tab直接隐藏掉这个订单
  deleteOrderInfo({ listIndex, orderNo }){
    this.ctx.data.activityTab.list.splice(listIndex, 1)
    try {
      this.ctx.data.activityTab=JSON.parse(JSON.stringify(this.ctx.data.activityTab))
    } catch (e) {
      console.error(e);
    }
  }

  cacelRefreshOrderInfo({ listIndex, orderNo }) {
    return http
      .get({
        url: '/wsctrade/order/oneOrder.json',
        data: {
          order_no: orderNo,
        },
      })
      .then(({ data = {} }) => {
        data = camelize(data);
        data.list = formatOrderList(this.ctx, data.list || [], {
          openAppConfig: this.openAppConfig,
        });
        
        if (data.list.length) {
          const {
            hasNext,
            page,
            pageId,
            pageSize,
            text,
            type,
          } = this.ctx.data.activityTab
          let deepCloneList = []; 
          try {
            deepCloneList = JSON.parse(JSON.stringify(this.ctx.data.activityTab.list));
            if (this.ctx.data.activityTab.type === 'all') {// 全部就更新状态
              deepCloneList.splice(listIndex, 1,data.list[0]);
            } else if(this.ctx.data.activityTab.type === 'topay') { // 代付款状态下就干掉
              deepCloneList.splice(listIndex, 1);
            }
          } catch(e) {
            if (this.ctx.data.activityTab.type === 'all') {// 全部就更新状态
              this.ctx.data.activityTab.list.splice(listIndex, 1,data.list[0]);
            } else if(this.ctx.data.activityTab.type === 'topay') { // 代付款状态下就干掉
              this.ctx.data.activityTab.list.splice(listIndex, 1);
            }
            deepCloneList = this.ctx.data.activityTab.list;
          }
          
          this.ctx.data.activityTab = {
            hasNext,
            page,
            pageId,
            pageSize,
            text,
            type,
            list: deepCloneList,
          }
      }
    })
    .catch((error) => {
      console.log(error);
      throw new Error(error.msg || '服务器开小差了');
    });
  }

  _getExtraData() {
    let extraData = [];
    try {
      extraData = this.ctx.process.invoke('getFetchListExtraData') || [];
    } catch (error) {
      extraData = [];
    }
    return extraData;
  }

  getUseUmpGray() {
    return http
      .get({
        url: '/wscump/trade/use-trade-ump-v1.json',
      })
      .then(res => {
        this.ctx.data.canUseTradeUmpV1 = res.data;
      })
      .catch((error) => {
        console.log(error);
        this.ctx.data.canUseTradeUmpV1 = false;
        throw new Error(error.msg || '服务器开小差了');
      });
  }
}
