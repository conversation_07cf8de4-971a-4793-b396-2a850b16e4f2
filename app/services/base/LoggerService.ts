import { IronBaseService } from '@youzan/iron-base';

/**
 * 注入日志服务
 */
class LoggerService extends IronBaseService {
  private startTime: number = 0;

  /**
   * 获取日志前缀
   * @param checkCloudApp 是否检测 app 开店
   */
  private getLogPrefix(checkCloudApp?: boolean) {
    const { ctx } = this;

    // 生成prefix
    let { platform, platformVersion } = ctx;
    if (ctx.isWeapp) {
      platform = 'weapp';
      platformVersion = ctx.xExtraData && ctx.xExtraData.version;
    }

    // app 开店来源
    if (checkCloudApp && /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{10,}$/.test(platform)) {
      platform = 'yzcloudapp';
      platformVersion = platform;
    }

    // @ts-ignore
    return `[${ctx.traceCtx && ctx.traceCtx.rootId}] [${platform} ${platformVersion}]`;
  }

  /**
   * 上报服务请求日志
   * @param serviceName 服务名
   * @param methodName 方法
   * @param args 请求参数
   */
  public reportDubboStart(serviceName: string, methodName: string, keyArgs = {}, args = []) {
    const { ctx } = this;
    const extra: any = {
      serviceName: serviceName.substr(serviceName.lastIndexOf('.') + 1),
      methodName,
      ...keyArgs,
    };

    this.startTime = Date.now();
    const tag = `${this.getLogPrefix()} - actionStart=${serviceName}.${methodName}, args=${JSON.stringify(args)}`;
    ctx.logger.info(tag, null, extra);
  }

  /**
   * 上报服务响应日志
   * @param serviceName 服务名
   * @param methodName 方法
   * @param args 参数
   * @param result 响应数据
   */
  public reportDubboEnd(serviceName: string, methodName: string, keyArgs = {}, result: any) {
    const { ctx } = this;

    const extra: any = {
      serviceName: serviceName.substr(serviceName.lastIndexOf('.') + 1),
      methodName,
      ...keyArgs,
    };
    // @ts-ignore
    const res = typeof result === 'string' ? result : JSON.stringify(result || {});
    const tag = `
      ${this.getLogPrefix()} - actionEnd=${serviceName}.${methodName}, 
      costTime=${Date.now() - this.startTime}, 
      result=${res}
    `;

    ctx.logger.info(tag, null, extra);
  }

  /**
   * 支付服务请求日志
   * @param serviceName 服务名
   * @param methodName 方法
   * @param args 参数
   */
  public reportPayServiceStart(serviceName: string, methodName: string, args = {}) {
    const { ctx } = this;
    const extra: any = {
      serviceName,
      methodName,
      headers: ctx.headers,
      request: { ...args },
    };

    this.startTime = Date.now();
    // @ts-ignore
    const tag = `
      ${this.getLogPrefix(true)} - payGatewayStart=${serviceName}.${methodName}, 
      args=${JSON.stringify(args)}
    `;
    ctx.logger.info(tag, null, extra);
  }

  /**
   * 上报支付服务响应日志
   * @param serviceName 服务名
   * @param methodName 方法
   * @param args 参数
   * @param result 响应数据
   */
  public reportPayServiceEnd(serviceName: string, methodName: string, args = {}, result: any = {}) {
    const { ctx } = this;
    const extra: any = {
      serviceName,
      methodName,
      headers: ctx.headers,
      response: { ...args },
    };
    // @ts-ignore
    const tag = `
      ${this.getLogPrefix(true)} - payGatewayEnd=${serviceName}.${methodName}, 
      costTime=${Date.now() - this.startTime}, 
      result=${JSON.stringify(result)}
    `;
    ctx.logger.info(tag, null, extra);
  }
}

export default LoggerService;
