import { SecurityLevel } from '@youzan/cloud-guard';

const origin = 'https://b.yzcdn.cn/wsc-h5-trade';

export default (config) => {
  // 增加自定义extension
  config.extensions.unshift({
    extensionId: 'list-header',
    name: 'list-header',
    version: '1.0.0',
    extensionPathInBundle: 'list-header-block',
    bundle: origin + '/order/list-v2/custom-demo/list-header.js',
    hasWidget: false,
    sandbox: {
      id: 'list-header',
      level: SecurityLevel.Unsafe,
    },
    component: {
      provide: ['ListItemHeader'],
    },
  })

  // 定义module
  config.modules.unshift({
    moduleId: 'list-header-block@random-string',
    extensionId: 'list-header',
  })

  // 改写list-block
  const listBlockIndex = config.modules.findIndex(module => module.moduleId === 'list-block@random-string')

  config.modules[listBlockIndex].bindings.component['list-item-header'] = {
    extensionId: 'list-header',
    name: 'ListItemHeader',
  }

  // app 增加 header module
  config.app.pages[0].modules.push('list-header-block@random-string')

  return config
}