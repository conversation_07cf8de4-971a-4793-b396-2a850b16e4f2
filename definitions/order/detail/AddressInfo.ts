interface IHotelRecipients {
  checkInTime: string;
  checkOutTime: string;
  recipients: Array<string>;

  lon: string;
  lat: string;
  areaCode: string;
}

interface ISelfFetchInfoDo {
  province: string;
  city: string;
  county: string;
  addressDetail: string;
  lng: string;
  lon: string;
  lat: string;
  userName: string;
  userTel: string;
  userTime: string;
}

interface ISelfFetchInfoDto {
  province: string;
  city: string;
  county: string;
  addressDetail: string;
  lon: string;
  lat: string;
  userName: string;
  userTel: string;
  userTime: string;
}

export interface IAddressInfoDto {
  receiverName: string;
  receiverTel: string;
  deliveryCountry: string;
  deliveryProvince: string;
  deliveryCity: string;
  deliveryDistrict: string;
  deliveryStreet: string;
  deliveryPostalCode: string;
  /**
   * 酒店订单入住信息
   */
  addressExtra?: IHotelRecipients;
  /**
   * 自提信息
   */
  selfFetchInfo?: ISelfFetchInfoDto;
  /**
   * 自提码信息
   */
  selfFetchResult?: any;
  /**
   * 同城配送范围
   */
  localDeliveryScope?: any;
  /**
   * 定时达：送达时间
   */
  deliveryTimeDisplay?: string;
  /**
   * 定时达：送达时间短格式显示
   */
  deliveryTimeShortDisplay?: string;
  /**
   * 定时达：配送开始时间戳（毫秒）
   */
  deliveryStartTime?: number;
  /**
   * 定时达：配送结束时间戳（毫秒）
   */
  deliveryEndTime?: number;

  // 服务端返回的数据对象
  serviceData?: any;
}
