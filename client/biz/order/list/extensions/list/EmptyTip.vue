<template>
  <div
    v-show="showEmptyTip"
    class="empty-tip"
    :class="{
      'empty-tip_small': small,
      'empty-tip_transparent': transparent,
    }"
  >
    <div class="empty-tip__img">
      <img
        src="https://b.yzcdn.cn/public_files/7b9b24e6591cdd94a0c8e98ad72bf6d3.png"
      />
    </div>

    <div class="empty-tip__text">
      暂无订单
    </div>
  </div>
</template>

<script>
import { Button } from 'vant';

export default {
  name: 'empty-tip',

  components: {
    [Button.name]: Button,
  },

  props: {
    showEmptyTip: Boolean,
    small: Boolean,
    transparent: Boolean,
  },

  data() {
    return {};
  },
};
</script>

<style lang="scss">
.empty-tip {
  text-align: center;
  padding-top: 120px;
  height: 100vh;
  box-sizing: border-box;
  background: #fff;

  &_small {
    padding: 32px 0;
    height: auto;
  }

  &_transparent {
    background: transparent;
  }

  &__img {
    margin-bottom: 16px;

    img {
      width: 160px;
    }
  }

  &__text {
    color: #969799;
    line-height: 20px;
    font-size: 14px;
  }
}
</style>
