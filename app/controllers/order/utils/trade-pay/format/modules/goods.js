import { formatTotalPrice } from '../utils/utils';


export default function (args) {
  // store 有这几个属性可以取
  const { state, getters } = args;

  return {
    // 总价格
    goodsTotalPrice: formatTotalPrice(
      state.pay.itemPay,
      state.points.totalPoints,
      state.pointsName
    ),

    // 商品列表
    formattedGoods: getters.formattedGoods,

    hasGoods: !!state.goods.list.length,

    shopName: state.shop.shopName,
    pointsName: state.pointsName,
  };
}
