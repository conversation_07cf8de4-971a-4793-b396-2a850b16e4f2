<template>
  <div class="goods-card">
    <div class="goods-card__thumb">
      <van-image :src="thumb" width="72px" height="72px" fit="contain" />
    </div>

    <div class="goods-card__content">
      <div class="goods-card__content-left">
        <div class="goods-card__title van-multi-ellipsis--l2">
          <img
            v-if="isHaiTao"
            class="goods-card__title-tag"
            :src="haiTao"
            alt="海淘商品"
          />

          <img
            v-if="isPeriod"
            class="goods-card__title-tag"
            :src="period"
            alt="周期购商品"
          />

          <img
            v-if="isPrescriptionDrugGoods"
            class="goods-card__title-tag"
            :src="drug"
            alt="处方药"
          />

          <span class="goods-card__title-text">{{ title }}</span>
        </div>

        <div v-if="subTitle" class="goods-card__sub-title">
          {{ subTitle }}
        </div>

        <div v-if="goodsSku" class="goods-card__sku">
          {{ goodsSku }}
        </div>

        <div v-if="hotelDesc" class="goods-card__hotel-desc">
          {{ hotelDesc }}
        </div>

        <template v-if="orderExplanation.length">
          <div
            v-for="(item, index) in orderExplanation"
            :key="index"
            class="goods-card__explanation"
          >
            {{ item }}
          </div>
        </template>

        <template v-for="(tags, index) in tagList">
          <div v-if="tags.length" class="goods-card__tags" :key="index + 1">
            <van-tag
              v-for="(tag, subIndex) in tags"
              :key="subIndex + 1"
              class="goods-card__tag"
              :plain="tag.plain"
              round
            >
              {{ tag.text }}
            </van-tag>
          </div>
        </template>
      </div>

      <div class="goods-card__content-right">
        <div class="goods-card__price">
          {{ goodsPrice }}
        </div>

        <div v-if="!orderExtra.isHotel || isHotelPresale" class="goods-card__num">
          x{{ goods.num }}
        </div>

        <div v-if="status" class="goods-card__status">
          {{ status }}
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { Card, Tag, Image } from 'vant';
import fullfillImage from '@youzan/utils/fullfillImage';
import get from 'utils/get';
import formatDate from '@youzan/utils/date/formatDate';

export default {
  name: 'goods-card',

  components: {
    [Tag.name]: Tag,
    [Card.name]: Card,
    [Image.name]: Image,
  },

  props: {
    goods: {
      type: Object,
      default: () => ({}),
    },
    hotelDetail: {
      type: Object,
      default: () => ({}),
    },
    orderItem: {
      type: Object,
      default: () => ({}),
    },
    isPrescriptionDrugGoods: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      haiTao: '//b.yzcdn.cn/public_files/3a774609c08dc284f27ba5a64be85fa6.png',
      period: '//b.yzcdn.cn/public_files/61954b0fdd8319a9c5722f16ca2e31de.png',
      drug: 'https://b.yzcdn.cn/path/to/cdn/dir/isDrugTag_3x.png',
      pointsName: get(window, '_global.pointsName', '积分'),
    };
  },

  computed: {
    status() {
      if (this.goods.isShipped) {
        return '已发货';
      }

      if (this.goods.feedback === 201) {
        return '退款中';
      }

      return '';
    },

    tagList() {
      const mainTagList = (this.goods.tagList || []).filter(
        (tag) => !tag.isSub
      );
      const subTagList = (this.goods.tagList || []).filter((tag) => tag.isSub);

      return [mainTagList, subTagList];
    },

    orderExplanation() {
      // 定金预售发货时间
      const preSaleExpressTimeDesc = get(this.goods, 'preSaleExpressTimeDesc', '');

      // 定金预售尾款
      const preSaleFinalPaymentDesc = get(
        this.orderItem,
        'orderDesc.preSaleDesc.preSaleFinalPaymentDesc',
        ''
      );

      // 电子卡券失效时间
      const effectiveTimeDesc = get(
        this.orderItem,
        'orderDesc.effectiveTimeDesc',
        ''
      );

      // 拼团描述
      const groupAgencyReceiveDesc = get(
        this.orderItem,
        'orderDesc.groupAgencyReceiveDesc',
        ''
      );

      // 7天无理由
      const sevenDayUnconditionalReturnDesc = this.goods
        .isSevenDayUnconditionalReturn
        ? '7天无理由退货'
        : '';

      return [
        preSaleFinalPaymentDesc,
        preSaleExpressTimeDesc,
        effectiveTimeDesc,
        groupAgencyReceiveDesc,
        sevenDayUnconditionalReturnDesc,
      ].filter((item) => !!item);
    },

    orderExtra() {
      return this.orderItem?.orderExtra || {};
    },

    isHaiTao() {
      return !!this.orderExtra?.isCrossBorder;
    },

    isPeriod() {
      return !!this.orderExtra?.isPeriod;
    },

    isHotelPresale() {
      return +this.orderExtra?.hotelOrderType === 3
    },

    hotelDesc() {
      if(this.isHotelPresale) {
        const {
          // hotelPackageOrderBeginTime: startTime, 
          hotelPackageOrderEndTime: endTime
        } = this.orderExtra;
        // 新酒店订单：预售、预约
        return endTime ? `预约有效期至${formatDate(endTime, 'YYYY年MM月DD日')}` : '';
        // return startTime && endTime ? `${formatDate(startTime, 'YYYY年MM月DD日')} - ${formatDate(endTime, 'YYYY年MM月DD日')}` : '';
      }
      if (this.orderExtra?.isHotel) {
        return `${this.hotelDetail.checkInTime} - ${this.hotelDetail.checkOutTime} 共${this.orderItem.items.length}晚/${this.goods.num}间`;
      }

      return '';
    },

    isNewHotelOrder() {
      return this.goods.newHotelGoods === '1';
    },

    title() {
      return this.isNewHotelOrder
        ? `${this.hotelDetail.hotelName}`
        : this.goods.title;
    },

    subTitle() {
      if (this.isNewHotelOrder) {
        return `${this.hotelDetail.roomTypeName} ${this.hotelDetail.saleProjectName}`;
      }
    },

    thumb() {
      return fullfillImage(this.goods.image, '!200x0.jpg');
    },

    goodsSku() {
      // 如果直接有描述语，就不需要再计算
      if (this.goods.skuStr) {
        return this.goods.skuStr;
      }

      const sku = this.goods.sku || [];
      const skuArr = [];

      sku.forEach((item) => {
        skuArr.push(item.v || item.value);
      });

      return [skuArr.join('；'), this.goodsPropertiesStr]
        .filter((item) => !!item)
        .join('；');
    },

    goodsPropertiesStr() {
      const properties = this.goods.properties || [];
      const propertiesArr = [];

      properties.forEach((currentProperty = {}) => {
        const propValueList = currentProperty.propValueList || [];
        propValueList.forEach((currentValue) => {
          propertiesArr.push(currentValue.propValueName);
        });
      });

      return propertiesArr.join('；');
    },

    goodsPrice() {
      if(this.orderItem?.orderExtra.isHotel && this.orderExtra?.hotelOrderType !== 3){
        return this.getTotalPrice(this.goods.price*this.goods.num || 0, this.goods.pointsPrice*this.goods.num);
      }
      return this.getTotalPrice(this.goods.price|| 0, this.goods.pointsPrice);
    },
  },

  methods: {
    getTotalPrice(price, points) {
      const arr = [];
      if (points) {
        arr.push(points + this.pointsName);
      }
      if (price || !points) {
        arr.push('¥' + (price / 100).toFixed(2));
      }
      return arr.join(' + ');
    },
  },
};
</script>
<style scoped lang="scss">
.goods-card {
  padding: 12px 12px 0;
  display: flex;
  align-items: flex-start;

  &__thumb {
    border-radius: 8px;
    overflow: hidden;
    line-height: 0;
    margin-right: 8px;
  }

  &__content {
    flex: 1;
    display: flex;
    align-items: flex-start;
  }

  &__content-left {
    flex: 1;
    margin-right: 12px;
    width: 0;
  }

  &__content-right {
    text-align: right;
  }

  &__title {
    color: #323233;
    line-height: 20px;
    font-size: 14px;
  }

  &__title-text {
    // vertical-align: middle;
    line-height: 16px;
  }

  &__title-tag {
    vertical-align: middle;
    height: 16px;
    margin-right: 4px;
  }

  &__sub-title {
    font-size: 14px;
    color: #969799;
  }

  &__sku,
  &__hotel-desc {
    color: #969799;
    line-height: 16px;
    margin-top: 8px;
    font-size: 12px;
  }

  &__explanation {
    color: var(--notice, #ed6a0c);
    line-height: 16px;
    margin-top: 8px;
    font-size: 12px;
  }

  &__price {
    color: #323233;
    letter-spacing: 0;
    font-size: 14px;
  }

  &__num {
    color: #969799;
    line-height: 16px;
    margin-top: 8px;
    font-size: 12px;
  }

  &__status {
    font-size: 12px;
    color: var(--highlight, #ee0a24);
    margin-top: 8px;
  }

  &__tags {
    margin-top: 8px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
  }

  &__tag {
    margin-right: 4px;
    padding: 0 4px;
    flex-shrink: 0;
    line-height: 16px;
    font-size: 12px;
    background-color: var(--ump-tag-bg, #f2f2ff);
    color: var(--ump-tag-text, #323233) !important;
  }
}
</style>
