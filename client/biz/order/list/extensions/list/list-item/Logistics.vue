<template>
  <div v-if="lastExpressTrace" class="list-item-logistics">
    <div class="list-item-logistics__title">
      <i class="list-item-logistics__icon"></i>
    </div>

    <div class="list-item-logistics__value">
      {{ lastExpressTrace }}
    </div>
  </div>
</template>
<script>
import get from 'utils/get';

export default {
  name: 'list-item-logistics',

  props: {
    listIndex: {
      type: Number,
      default: 0,
    },
    listItem: Object,
  },

  data() {
    return {};
  },

  computed: {
    orderItem() {
      return this.listItem.orderItems[0] || {};
    },

    lastExpressTrace() {
      return get(this.orderItem, 'lastExpressTrace', '');
    },
  },
};
</script>
<style scoped lang="scss">
.list-item-logistics {
  height: 36px;
  padding: 0 8px;
  margin: 12px 12px 0;
  background: #f7f8fa;
  border-radius: 4px;
  display: flex;
  align-items: center;
  font-size: 14px;

  &__title {
    flex-grow: 0;
    white-space: nowrap;
    color: #2da641;
    display: flex;
    align-items: center;
    margin-right: 8px;
  }

  &__icon {
    display: block;
    width: 14px;
    height: 14px;
    background: url('https://b.yzcdn.cn/public_files/fe42ba229e45d702aff92df787af233b.png')
      no-repeat center;
    background-size: 100% 100%;
  }

  &__value {
    flex-grow: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
