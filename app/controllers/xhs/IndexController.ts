import { Context } from 'astroboy';
import BaseController from '../base/BaseController';
import OrderSyncService from '../../services/xhs/OrderSyncService';

class IndexController extends BaseController {
  async getOrderPayToken(ctx: Context) {
    const { kdtId, orderNo } = ctx.getPostData();
    const result = await new OrderSyncService(ctx).syncOrderAndGetPayToken({ kdtId, orderNo });
    return ctx.json(0, 'ok', result);
  }
}

export = IndexController;
