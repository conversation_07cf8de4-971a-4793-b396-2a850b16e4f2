<template>
  <cap-sku
    v-model="showSku"
    class="cart-goods__sku-selector"
    :alias="goods.alias"
    :goods="skuGoods"
    :async-prop-price="isAsyncPropPrice"
    :kdt-id="kdtId"
    :show-add-cart-btn="false"
    :get-container="getContainer"
    :message-config="getMessageConfig"
    :close-on-click-overlay="true"
    buy-text="确认"
    @buy-clicked="onConfirmClicked"
    @sku-selected="skuSelected"
    @fetch-sku-data="fetchSkuData"
  >
    <!-- 组合商品， extra-sku-group -->
    <template slot="extra-sku-group" v-if="isComboGoods">
      <combine-goods-block :chosed-combo-detail="chosedComboDetail" :selected-sku-comb="selectedSkuComb"/>
    </template>

    <div slot="sku-stepper" />
  </cap-sku>
</template>

<script>
import { Sku } from '@youzan/captain';
import mapKeysToCamelCase from '@youzan/utils/string/mapKeysToCamelCase';
import urlHelper from '@youzan/utils/url/helper';
import image from '@youzan/utils/browser/image';
import { getGoodsPropertiesIds, parseGoodsProperties } from '@biz/cart/utils';
import CombineGoodsBlock from './CombineGoodsBlock.vue';

export default {
  name: 'goods-sku',

  components: {
    [Sku.name]: Sku,
    [CombineGoodsBlock.name]: CombineGoodsBlock,
  },

  props: {
    ctx: Object,
  },

  data() {
    return {
      goods: { sku: '[]' },
      showSku: false,
      originSkuData: null,
      selectedSkuComb: null,
    };
  },

  mounted() {
    this.ctx.event.listen('changeSku', (e) => {
      this.showSku = true;
      this.goods = e;
    });
  },

  computed: {
    imgUrl() {
      return image.toWebp(
        urlHelper.getCdnImageUrl(this.goods.attachmentUrl, '!200x200.jpg')
      );
    },
    skuGoods() {
      let sku = {};
      try {
        sku = JSON.parse(this.goods.sku);
      } catch (error) {
        sku = {};
      }

      return {
        sku: mapKeysToCamelCase(sku),
        title: this.goods.title,
        goodsId: this.goods.goodsId,
        picture: this.imgUrl,
        storeId: this.goods.storeId,
        activityType: this.goods.activityTypeStr || '',
        properties: mapKeysToCamelCase(this.goods.properties || []),
      };
    },
    kdtId() {
      return this.goods.kdtId;
    },
    isAsyncPropPrice() {
      return !!this.goods.activityTag && Array.isArray(this.goods.properties);
    },
    getMessageConfig() {
      let initialMessages = {};
      try {
        initialMessages = JSON.parse(this.goods.messages);
      } catch (error) {
        initialMessages = {};
      }
      return {
        initialMessages,
      };
    },
    isComboGoods(){
      const { isCombo = false } = this.originSkuData?.itemDataModel?.comboMark || {}
      return isCombo
    },
    chosedComboDetail() {
      if (!this.selectedSkuComb) return null;
      const { comboGroupModels = [] } = this.originSkuData?.itemDataModel?.comboDetailModel || {};
      if(!comboGroupModels || !comboGroupModels.length) return null
      if (comboGroupModels.length === 1) return comboGroupModels[0];
      const { id: skuId } = this.selectedSkuComb;
      return comboGroupModels.find((item) => item.skuId === skuId) || null;
    },
  },

  methods: {
    fetchSkuData(skuData) {
      this.originSkuData = skuData;
    },

    skuSelected(selectData) {
      this.selectedSkuComb = selectData;
    },

    getContainer() {
      return document.querySelector('body');
    },

    onConfirmClicked(skuData = {}, allSkuData = {}) {
      const { id: skuId, properties = [] } = skuData.selectedSkuComb;

      const { messages } = skuData;
      const formatMessages = Object.keys(messages).map((key) => messages[key]);
      const formatMessagesStr = formatMessages.length
        ? JSON.stringify(formatMessages)
        : '';
      // 获取属性ID
      const propertyIds = getGoodsPropertiesIds(
        parseGoodsProperties(properties)
      );

      if (
        formatMessagesStr === this.getGoodsMessageValueArrStr() && // 留言
        skuId === this.goods.skuId && // sku
        propertyIds.join(',') === (this.goods.property_ids || []).join(',') // 属性
      ) {
        return;
      }

      const { itemDataModel = {} } = allSkuData;
      const { comboMark = {}, comboDetailModel = {} } = itemDataModel;
      // 支持组合商品
      let combo = null;
      if (comboMark.isCombo) {
        const { comboGroupModels = [] } = comboDetailModel;
        const currentCombo = comboGroupModels.filter((comboGroup) => comboGroup.skuId === skuId);
        if (currentCombo?.length) {
          const { comboSubItemModels = [], goodsComboGroupId } = currentCombo[0];
          const subComboList = [];
          comboSubItemModels.map((comboSubItem) => {
            const { skuRelatedModels, propModels = [] } = comboSubItem;
            const propertyIds = propModels.reduce((propertyIds, propModel) => {
              const { textModels = [] } = propModel;
              textModels.forEach((item) => {
                propertyIds.push(item.id);
              });
              return propertyIds;
            }, []);
            skuRelatedModels.forEach(({ combineNum, itemId, price, skuId: subSkuId }) => {
              subComboList.push({
                goodsId: itemId,
                num: combineNum,
                price,
                propertyIds,
                skuId: subSkuId,
              });
            });
          });
          combo = {
            comboType: comboMark.comboType,
            groupList: [
              {
                id: goodsComboGroupId,
                subComboList,
              },
            ],
          };
        }
      }
      const params = {
        goods: {
          ...this.goods,
          skuId,
          propertyIds,
          messages: formatMessagesStr,
          combo
        },
        loading: true,
      };
      this.ctx.event.emit('skuChanged', params);
    },
    getGoodsMessageValueArrStr() {
      let messages = {};
      try {
        messages = JSON.parse(this.goods.messages);
      } catch (error) {
        messages = {};
      }
      const messageArr = Object.keys(messages).map((key) => messages[key]);
      return messageArr.length ? JSON.stringify(messageArr) : '';
    },
  },
};
</script>

<style lang="scss" scoped>
.cart-goods {
  position: relative;
  background: #fff;

  /* 移除计步器上面 sku的下划线 */
  &__sku-selector {
    ::v-deep.van-sku-group-container {
      .van-sku-row:last-child {
        &::after {
          display: none;
        }
      }
    }
  }

  ::v-deep.van-stepper__plus,
  ::v-deep.van-stepper__minus {
    width: 24px;
    height: 24px;
  }

  ::v-deep.van-stepper__input {
    width: 34px;
    height: 24px;
    line-height: 24px;
  }
}
</style>
