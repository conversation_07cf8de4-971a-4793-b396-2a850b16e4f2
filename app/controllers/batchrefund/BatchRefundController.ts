import get from 'lodash/get';
import URL from '@youzan/iron-base/app/lib/URL';
import BaseController from '../base/BaseController';
import BatchRefundService from '../../services/batchrefund/BatchRefundService';
import OrderDetailService from '../../services/order/OrderDetailService';
import { PageException } from '@youzan/iron-base';
import { Context } from 'astroboy';

class BatchRefundController extends BaseController {
  async batchRefundAcl() {
    await this.acl({
      allowNotLogin: false,
      useAjaxLogin: false,
      forceOauthLogin: false,
      kdtId: this.ctx.kdtId,
    });
  }

  async getIndexHtml(ctx: Context) {
    await this.batchRefundAcl();

    if (ctx.status === 302) {
      return;
    }

    const { order_no: orderNo, kdt_id: kdtId } = ctx.query;

    const clientUrl = {
      orderDetailUrl: URL.site(`/wsctrade/order/detail?order_no=${orderNo}&kdt_id=${kdtId}`, 'h5')
    };

    // 查询订单信息来决定显示哪些退款方式
    const params = {
      orderNo,
      kdtId,
      buyerId: 0,
      customerId: 0,
      customerType: 0,
      sourceName: this.sourceName,
    };
    let data = null;
    try {
      data = await new OrderDetailService(ctx).lightDetailByOrderNo(params);
    } catch (e) {
      ctx.logger.warn('获取订单详情失败, ' + e.toString());
      const content = e.errorContent || {};
      throw new PageException(content.code || 10500, content.msg || '订单数据异常');
    }
    if (!data || Object.keys(data).length === 0) {
      // 无此订单
      throw new PageException(10500, '无此订单');
    }
    // state < 60 表示存在商品未发货，退款方式隐藏“退货退款”
    const showGoodAndMoneyRefund = data.mainOrderInfo.state >= 60;

    // 批量退款单页结构，selectGoodsDesign是选择商品路由下的组件列表
    this.setExtensionParams(ctx, 'batch-refund-select-goods', {}, true)

    ctx.setGlobal({
      clientUrl,
      showGoodAndMoneyRefund,
      query: ctx.query
    });
    await ctx.render('batch-refund/index.html');
  }

  async postGetSelectGoodsList(ctx: Context) {
    const { orderNo, kdtId, demand } = ctx.getPostData();

    const params = {
      buyerRefundInitDTOList: [
        {
          orderNo,
          kdtId,
          buyerId: String(this.buyerId)
        }
      ],
      demand
    };

    const res = await new BatchRefundService(ctx).getSelectGoodsList(params);

    ctx.r(0, '', res);
  }

  async postGetApplyGoodsList(ctx: Context) {
    const { orderNo, kdtId, demand, itemIdList } = ctx.getPostData();

    const params = {
      buyerRefundInitDTOList: [
        {
          orderNo,
          kdtId,
          itemIdList,
          buyerId: String(this.buyerId)
        }
      ],
      demand
    };

    const res = await new BatchRefundService(ctx).getApplyGoodsList(params);

    ctx.r(0, '', res);
  }

  async postApplyBatchRefund(ctx: Context) {
    const source = {
      clientIp: ctx.firstXff,
      from: 'wsc-wap'
    };

    const params = Object.assign({}, ctx.getPostData(), {
      source,
      operatorId: String(this.buyerId)
    });

    const res = await new BatchRefundService(ctx).applyBatchRefund(params);

    ctx.r(0, '', res);
  }

  async postGetApplyResult(ctx: Context) {
    const { refundOperateStr = '', itemIdList = [], orderNo = '' } = ctx.getPostData();

    const params = {
      refundOperateStr,
      refundOperatorReqDTOList: [
        {
          orderNo,
          itemIdList
        }
      ]
    };

    const res = await new BatchRefundService(ctx).getApplyResult(params);

    ctx.r(0, '', res);
  }

  async postGetRefundReason(ctx: Context) {
    const { demand, deliveryed } = ctx.getPostData();

    const params = [deliveryed, demand];
    const res = await new BatchRefundService(ctx).getRefundReason(params);

    ctx.r(0, '', res);
  }
}

module.exports = BatchRefundController;
