import formatDate from '@youzan/utils/date/formatDate';
import format from '@youzan/utils/date/format';

import { calculateDistance } from '@youzan/utils/number/distance';

/**
 * 本地配送状态枚举
  订单提交 1
  订单支付 2
  商家已接单 3
  呼叫骑手 4
  骑手赶往商家 5
  骑手已到店 6
  骑手配送中 7
  已送达 8
  交易关闭 9
  已完成 10
 */
enum FulfillmentDetailStatusEnum {
  ORDER_SUBMITTED = 1, // 订单提交
  ORDER_PAID = 2, // 订单支付
  MERCHANT_ACCEPTED = 3, // 商家已接单
  CALL_RIDER = 4, // 呼叫骑手
  RIDER_TO_MERCHANT = 5, // 骑手赶往商家
  RIDER_ARRIVED = 6, // 骑手已到店
  RIDER_DELIVERING = 7, // 骑手配送中
  DELIVERED = 8, // 已送达
  TRANSACTION_CLOSED = 9, // 交易关闭
  COMPLETED = 10, // 已完成
}

interface IFulfillmentDetailLine {
  fulfillStageNodeDesc: string;
  executeTime: number;
  fulfillStageNode: FulfillmentDetailStatusEnum;
}

const EXPRESS_REMINDER_BTN = {
  type: 'expressReminder',
  text: '催一下',
};
const CONTACT_RIDER_BTN = {
  type: 'contactRider',
  text: '联系骑手',
};
const CONTACT_SELLER_BTN = {
  type: 'contactSeller',
  text: '联系商家',
};

function getLocalDeliveryActionsByStauts(status: FulfillmentDetailStatusEnum) {
  const actions = [];
  // 用户已支付并且骑手配送中之前，都显示催一下按钮，区间：[订单支付, 骑手配送中)
  if (
    status >= FulfillmentDetailStatusEnum.ORDER_PAID &&
    status < FulfillmentDetailStatusEnum.RIDER_DELIVERING
  ) {
    actions.push(EXPRESS_REMINDER_BTN);
  }
  // 骑手已到店后到已完成都展示联系骑手按钮，区间：[骑手已到店, 已完成]
  if (
    status >= FulfillmentDetailStatusEnum.RIDER_ARRIVED &&
    status <= FulfillmentDetailStatusEnum.DELIVERED
  ) {
    actions.push(CONTACT_RIDER_BTN);
  }
  // 商家已接单之后，展示联系商家按钮，区间：[商家已接单, 已完成]
  if (
    status >= FulfillmentDetailStatusEnum.MERCHANT_ACCEPTED &&
    status <= FulfillmentDetailStatusEnum.DELIVERED
  ) {
    actions.push(CONTACT_SELLER_BTN);
  }

  return actions;
}

export function formatLocalDeliveryExpressDetail(localDeliveryDetail: any) {
  if (!localDeliveryDetail) {
    return localDeliveryDetail;
  }

  const {
    currentFulfillDetailStatus,
    fulfillStageNodeLines,
    estimateArriveTime,
    riderLat,
    riderLng,
    buyerLat,
    buyerLng,
    deliveryShopLat,
    deliveryShopLng,
  } = localDeliveryDetail;

  if (!currentFulfillDetailStatus) {
    return localDeliveryDetail;
  }

  // 订单轨迹处理
  if (fulfillStageNodeLines) {
    localDeliveryDetail.localDeliveryLines = fulfillStageNodeLines.map(
      (item: IFulfillmentDetailLine) => {
        const {
          fulfillStageNodeDesc,
          executeTime,
          fulfillStageNode,
        } = item;
        return {
          ...item,
          status: fulfillStageNode,
          statusDesc: fulfillStageNodeDesc,
          timeStr: executeTime ? formatDate(executeTime, 'MM-DD HH:mm:ss') : '',
        };
      }
    );
  }

  // 按钮处理
  localDeliveryDetail.localDeliveryActions = getLocalDeliveryActionsByStauts(
    currentFulfillDetailStatus
  );

  // 距离计算
  // 存在骑手经纬度，则根据当前状态计算经纬度
  if (riderLat && riderLng) {
    // 状态在配送中之前，都是距商家，否则就是距用户
    if (
      currentFulfillDetailStatus < FulfillmentDetailStatusEnum.RIDER_DELIVERING &&
      deliveryShopLat &&
      deliveryShopLng
    ) {
      localDeliveryDetail.distance = calculateDistance(riderLat, riderLng, deliveryShopLat, deliveryShopLng);
    } else if (buyerLat && buyerLng) {
      localDeliveryDetail.distance = calculateDistance(riderLat, riderLng, buyerLat, buyerLng);
    }
  }

  // 约x分钟时间格式化
  if (estimateArriveTime) {
    const timeMilliseconds = new Date(estimateArriveTime).getTime() - Date.now();
    // 格式不确定
    const timeObj = format(timeMilliseconds);
    localDeliveryDetail.estimateArriveTimeData = {
      hour: timeObj.data.hour,
      minute: timeObj.data.second ? timeObj.data.minute + 1 : timeObj.data.minute,
    };
  }

  return localDeliveryDetail;
}
