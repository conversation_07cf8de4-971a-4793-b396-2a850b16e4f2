const formatDate = require('@youzan/utils/date/formatDate');
const BaseService = require('../base/BaseService');

class BuyerTradeService extends BaseService {
  /* eslint-disable-next-line */
  get ACTIVITY_SERVICE() {
    return 'com.youzan.ebiz.mall.rigel.api.groupbuy.service.activity.ActivityService';
  }

  /* eslint-disable-next-line */
  get TRADE_SERVICE() {
    return 'com.youzan.ebiz.mall.rigel.api.groupbuy.service.order.TradeService';
  }

  /* eslint-disable-next-line */
  get ORDER_SERVICE() {
    return 'com.youzan.ebiz.mall.rigel.api.groupbuy.service.order.OrderService';
  }

  /* eslint-disable-next-line */
  get BUYER_SERVICE() {
    return 'com.youzan.ebiz.mall.trade.buyer.api.service.TradeService';
  }

  /* eslint-disable-next-line */
  get ITEM_SERVICE() {
    return 'com.youzan.ebiz.mall.rigel.api.groupbuy.service.goods.ItemService';
  }

  /* eslint-disable-next-line */
  get ITEM_DETAIL_SERVICE() {
    return 'com.youzan.item.detail.api.ItemService';
  }

  get HEADER_SERVICE() {
    return 'com.youzan.ebiz.mall.rigel.api.groupbuy.service.header.GroupHeaderService';
  }

  async getActivityDetail(params) {
    const dubboData = await this.invoke(
      this.ACTIVITY_SERVICE,
      'queryHeaderActSet',
      [params]
    );
    return dubboData;
  }

  
  async preTrade(params) {
    const dubboData = await this.invoke(this.TRADE_SERVICE, 'confirm', [
      params,
    ]);
    return dubboData;
  }

  async createTrade(params) {
    const dubboData = await this.invoke(this.TRADE_SERVICE, 'create', [params]);
    return dubboData;
  }

  async orderDetail(params) {
    const dubboData = await this.invoke(
      this.ORDER_SERVICE,
      'queryOrderDetail',
      [params.kdtId, params.orderNo]
    );
    return dubboData;
  }

  async queryOrderDetailV2(params) {
    const dubboData = await this.invoke(
      this.ORDER_SERVICE,
      'queryOrderDetailV2',
      [params]
    );
    return dubboData;
  }

  async orderPayment(params) {
    const dubboData = await this.invoke(this.BUYER_SERVICE, 'preparePayment', [
      params,
    ]);
    return dubboData;
  }

  async queryItemDetailV2(params) {
    const dubboData = await this.invoke(
      this.ITEM_SERVICE,
      'queryItemDetailV2',
      [params]
    );
    return dubboData;
  }

  async getById(params) {
    const dubboData = await this.invoke(this.ITEM_DETAIL_SERVICE, 'getById', [
      params,
    ]);
    return dubboData;
  }

  async headerInfo(params) {
    const dubboData = await this.invoke(this.HEADER_SERVICE, 'getById', [
      params.kdtId,
      params.headerBuyerId,
    ]);

    if (dubboData && dubboData.joinTime) {
      dubboData.joinTime = formatDate(dubboData.joinTime, 'YYYY-MM-DD');
    }

    return dubboData;
  }

  /**
   *  查询活动最新
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/268285
   *
   *  @param {Object} params -
   *  @param {number} params.activityId -
   *  @param {number} params.offset -
   *  @param {number} params.kdtId -
   *  @param {string} params.sortType -
   *  @param {number} params.pageSize - 每页显示大小
   *  @param {string} params.sortBy -
   *  @param {number} params.page - 当前页
   *  @param {string} params.pageId -
   *  @return {Promise}
   */
  async queryRollOrders(params) {
    const dubboData = await this.invoke(this.ORDER_SERVICE, 'queryRollOrders', [
      params,
    ]);
    return dubboData;
  }

  /**
   *  缓存下单信息
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/380233
   *
   *  @param {Object} orderCreateDTO -
   *  @param {Object} orderCreateDTO.seller - 卖家 （包括团长信息）
   *  @param {Object} orderCreateDTO.groupBuy - 群团购信息
   *  @param {Object} orderCreateDTO.source - 来源
   *  @param {Array.<Object>} orderCreateDTO.items[] - 商品
   *  @param {Object} orderCreateDTO.config - 设置
   *  @param {Object} orderCreateDTO.buyer - 买家
   *  @return {Promise}
   */
  async cache(orderCreateDTO) {
    return this.invoke(this.TRADE_SERVICE, 'cache', [orderCreateDTO]);
  }

  /**
   *  准备下单
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/380234
   *
   *  @param {Object} orderPrepareDTO -
   *  @param {string} orderPrepareDTO.bookKey -
   *  @return {Promise}
   */
  async prepare(orderPrepareDTO) {
    return this.invoke(this.TRADE_SERVICE, 'prepare', [orderPrepareDTO], {
      timeout: 5000, // ajax超时
      headers: {
        'X-Timeout': 5000, // tether超时
      },
    });
  }
}

module.exports = BuyerTradeService;
