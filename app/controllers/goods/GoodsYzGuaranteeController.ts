import BaseController from '../base/BaseController';

import GoodsYzGuaranteeService from '../../services/goods/GoodsYzGuaranteeService';

import { Context } from 'astroboy';
import { IGuaranteeParams } from 'definitions/other/GoodsYzGuarantee';

class GoodsYzGuaranteeController extends BaseController {
  async getYzGuaranteeByAliases(ctx: Context) {
    const { aliases } = ctx.getPostData();
    const service = new GoodsYzGuaranteeService(ctx);
    const params = {
      aliases,
    } as IGuaranteeParams;
    
    const result = await service.getYzGuarantee(params);

    // 快手小程序强制不展示放心购
    if (this.ctx?.isKsApp) {
      result.securedItems = [];
    }

    // 小红书本地生活场景强制不展示放心购
    if (this.ctx?.xExtraData?.bizEnv === 'xhsLocalLife') {
      result.securedItems = [];
    }
  
    ctx.json(0, '', result);
  }
}

module.exports = GoodsYzGuaranteeController;
