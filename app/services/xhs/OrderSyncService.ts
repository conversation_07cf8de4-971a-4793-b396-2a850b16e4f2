import BaseService from '../base/BaseService';

interface IXiaohongshuSyncOrderRequest {
  /** 订单号 */
  orderNo: string;
  /** 店铺ID */
  kdtId: number;
}

class OrderSyncService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.ebiz.video.channels.trade.api.service.xiaohongshu.XiaohongshuOrderService';
  }

  /**
   *  将订单信息同步给小红书并获取paytoken见小红书开放平台文档：
   *  https://miniapp-open.xiaohongshu.com/docs/api-3rd/post-api-rmp-component-deal-order-upsert
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1204139
   *
   *  @param {Object} request -
   *  @param {string} request.orderNo - 订单号
   *  @param {number} request.kdtId - 店铺ID
   *  @return {Promise}
   */
  async syncOrderAndGetPayToken(request: IXiaohongshuSyncOrderRequest) {
    return this.invoke('syncOrderAndGetPayToken', [request]);
  }
}

export default OrderSyncService;
