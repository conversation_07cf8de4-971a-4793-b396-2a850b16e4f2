// CRM 线下订单展示枚举
const CRM_OFFLINE_TYPE = {
  NONE: 0, // 不展示
  ONLINE: 1, // 线上订单
  OFFLINE: 2, // 线下订单
};

const DEFAULT_POINTS_NAME = '积分';
// 标签配置

interface GetTagConfigOptions {
  pointsName: string;
}

const getTagConfig = (options: GetTagConfigOptions) => {
  const { pointsName = DEFAULT_POINTS_NAME } = options;
  return {
    presale: { text: '预售' },
    fcode: { text: 'F码专享' },
    enjoyBuy: { text: '随心订' },
    knowledgeGift: { text: '知识付费送礼' },
    group: { text: '拼团' },
    lotteryGroup: { text: '抽奖团' },
    selfFetch: { text: '自提', type: 'danger' },
    peerpay: { text: '代付' },
    points: { text: `${pointsName}订单` },
    present: { text: '赠品', plain: true, isSub: true },
    plusBuy: { text: '换购', plain: true, isSub: true },
    goodsExchange: { text: '兑换商品', plain: true, isSub: true },
    giftOrder: { text: '送礼' },
    recommendGift: { text: '新客立减' }, // 教育活动
    period: { text: '周期购', type: 'danger' },
    limitSeckill: { text: '限时秒杀', type: 'danger' },
  };
};

export {
  CRM_OFFLINE_TYPE,
  DEFAULT_POINTS_NAME,
  getTagConfig,
}
