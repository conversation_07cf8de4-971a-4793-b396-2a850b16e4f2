import { Context } from 'astroboy';
import BaseController from '../base/BaseController';
import OrderContext from '../../lib/OrderContext';
import FrequentlyPurchaseGoodsService from '../../services/order/FrequentlyPurchaseGoodsService';
import { ShopCorePlugin } from '@youzan/plugin-h5-shop';
import { initRanta } from '@youzan/plugin-h5-ranta-config';

/**
 * 订单搜索页面
 */
class OrderSearchController extends BaseController {
  public async init(): Promise<void> {
    // 初始化订单上下文
    OrderContext.init(this.ctx);

    await super.init({});
  }

  /**
   * 入口页面
   */
  public async getIndexHtml(ctx: Context): Promise<void> {
    await this.teeRantaAcl();
    await this.loadRantaConfig('@wsc-tee-h5-trade/order-search', 'wsc-tee-h5');
    await new ShopCorePlugin(ctx as any).initRantaShopInfo();

    let frequentlyPurchaseGoods = {};

    try {
      frequentlyPurchaseGoods = await this.getFrequentlyPurchaseGoods(ctx);
    } catch (e) {
      this.ctx.logger.warn('【订单搜索页】获取已购商品失败', e);
    }

    ctx.setGlobal({
      frequentlyPurchaseGoods,
    });
    this.setSpm('orderSearchPage', ctx.kdtId);
    await initRanta(ctx as any, {
      framework: 'tee',
      bizName: '@wsc-tee-h5-trade/order-search',
      appName: 'wsc-tee-h5'
    });
    await ctx.render(
      'order/search.html',
      {},
      {
        appName: 'wsc-tee-h5',
        skipLoadOpsWebConfig: true,
      }
    );
  }

  async getFrequentlyPurchaseGoods(ctx: Context) {
    const { kdtId, offlineId, buyerId } = ctx;
    const freqPurchaseGoods = await new FrequentlyPurchaseGoodsService(ctx).getFrequentlyPurchaseGoods({
      kdtId,
      offlineId,
      buyerId,
    });
    return freqPurchaseGoods;
  }

  public async getFrequentlyPurchaseGoodsJson(ctx: Context) {
    const frequentlyPurchaseGoods = await this.getFrequentlyPurchaseGoods(ctx);
    ctx.json(0, 'ok', frequentlyPurchaseGoods);
  }
}

export = OrderSearchController;
