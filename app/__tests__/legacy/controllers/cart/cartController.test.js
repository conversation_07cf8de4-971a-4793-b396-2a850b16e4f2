import { app, mock } from '@youzan/astroboy-mock/bootstrap';
import BaseController from '../../../controllers/base/BaseController';
import CartService from '../../../services/cart/CartService';
import mapKeysToSnakeCase from '@youzan/utils/string/mapKeysToSnakeCase';
import CartController from '../../../controllers/cart/CartController';

jest.mock('../../../controllers/base/BaseController', () =>
  jest.requireActual('../../__mocks__/BaseController')
);

jest.mock('../../../services/cart/CartService');

afterEach(() => {
  BaseController.mockClear();
  CartService.mockClear();
});

test('getIndexHtml', async () => {
  app.mockContext({
    buyerId: 8000035538,
    kdtId: 491391,
  });

  mock(app.diyCheck, 'whiteListCheck', () => true);

  app.mockService('iron-base', 'common.GrayReleaseService', {
    isInGrayReleaseByKdtId: jest.fn().mockResolvedValue(true),
  });
  app.mockService('iron-base', 'common.WhitelistService', {
    exists: jest.fn().mockResolvedValue(false),
  });

  const data = [
    {
      kdtId: 43776978,
      latestAddCartTime: 1596367930,
      shopName: 'WE MET',
    },
  ];

  CartService.mockImplementation(() => {
    return {
      customCartName: jest
        .fn()
        .mockResolvedValue({ customCartName: '自定义购物车名' }),
      mergeCartGoodsService: jest.fn().mockResolvedValue(),
      getCartList: jest.fn().mockResolvedValue(data),
    };
  });

  const res = await app.httpRequest().get('/wsctrade/cart');

  const global = mock.getViewGlobal(res.text);
  expect(global.cart_list).toEqual(mapKeysToSnakeCase(data));
  expect(global.hiddenPowerBy).toBeFalsy();
  expect(global.isUseNewBoookJson).toBeTruthy();
});

test('getGoodsListJson', async () => {
  app.mockContext({
    buyerId: 8000035538,
    kdtId: 491391,
  });

  CartService.mockImplementation(() => {
    return {
      getCartList: jest.fn().mockResolvedValue([]),
    };
  });

  const res = await app.httpRequest().get('/wsctrade/cart/goodsList.json');

  expect(res.body).toEqual({
    code: 0,
    data: [],
    msg: 'success',
  });
});

test('getCartGoodsListJson', async () => {
  CartService.mockImplementation(() => {
    return {
      getCartList: jest.fn().mockResolvedValue([]),
    };
  });

  const res = await app.httpRequest().get('/wsctrade/cartGoodstList.json');
  expect(res.body).toEqual({
    code: 0,
    data: [],
    msg: 'success',
  });
});

test('getCartGoodsListJson', async () => {
  app.mockContext({
    buyerId: 8000035538,
    kdtId: 491391,
  });

  CartService.mockImplementation(() => {
    return {
      getCartList: jest.fn().mockResolvedValue([{ goods: 1 }]),
    };
  });

  const res = await app.httpRequest().get('/wsctrade/cartGoodstList.json');
  expect(res.body.data).toEqual([
    {
      goods: 1,
      is_new_hope_shop: false,
    },
  ]);
});

test('postDeleteBatchListJson', async () => {
  app.mockContext({
    buyerId: 8000035538,
    kdtId: 491391,
    getPostData: () => [],
  });

  CartService.mockImplementation(() => {
    return {
      deleteGoodsBatchOrSingle: jest.fn().mockResolvedValue(true),
    };
  });

  const res = await app
    .httpRequest()
    .post('/wsctrade/cart/deleteBatchList.json');

  expect(res.body).toEqual({
    code: 0,
    data: true,
    msg: '删除成功',
  });
});

test('postDeleteBatchListJson', async () => {
  app.mockContext({
    buyerId: 8000035538,
    kdtId: 491391,
    getPostData: () => [],
  });

  CartService.mockImplementation(() => {
    return {
      deleteGoodsBatchOrSingle: jest.fn().mockResolvedValue(false),
    };
  });

  const res = await app
    .httpRequest()
    .post('/wsctrade/cart/deleteBatchList.json');

  expect(res.body).toEqual({
    code: 500,
    data: false,
    msg: '删除失败',
  });
});

test('postDeleteGoodsJson', async () => {
  app.mockContext({
    getPostData: () => {
      return { goodsId: 394201701 };
    },
  });

  CartService.mockImplementation(() => {
    return {
      deleteGoodsBatchOrSingle: jest.fn().mockResolvedValue(true),
    };
  });

  const res = await app
    .httpRequest()
    .post('/wsctrade/cart/deleteBatchList.json');

  expect(res.body).toEqual({
    code: 0,
    data: true,
    msg: '删除成功',
  });
});

test('postDeleteGoodsJson', async () => {
  app.mockContext({
    getPostData: () => {
      return { goodsId: 394201701 };
    },
  });

  CartService.mockImplementation(() => {
    return {
      deleteGoodsBatchOrSingle: jest.fn().mockResolvedValue(false),
    };
  });

  const res = await app
    .httpRequest()
    .post('/wsctrade/cart/deleteBatchList.json');

  expect(res.body).toEqual({
    code: 500,
    data: false,
    msg: '删除失败',
  });
});

test('postUpdateCartGoodsNumJson', async () => {
  CartService.mockImplementation(() => {
    return {
      updateCartGoodsNum: jest.fn().mockResolvedValue(true),
    };
  });

  const res = await app
    .httpRequest()
    .post('/wsctrade/cart/updateCartGoodsNum.json');

  expect(res.body).toEqual({
    code: 0,
    data: true,
    msg: '操作成功',
  });
});

test('postUpdateCartGoodsNumJson', async () => {
  CartService.mockImplementation(() => {
    return {
      updateCartGoodsNum: jest.fn().mockResolvedValue(false),
    };
  });

  const res = await app
    .httpRequest()
    .post('/wsctrade/cart/updateCartGoodsNum.json');

  expect(res.body).toEqual({
    code: 500,
    data: {},
    msg: '操作失败',
  });
});

test('postSelectGoodsJson', async () => {
  CartService.mockImplementation(() => {
    return {
      selectGoodsService: jest.fn().mockResolvedValue(true),
    };
  });

  const res = await app.httpRequest().post('/wsctrade/cart/selectGoods.json');

  expect(res.body).toEqual({
    code: 0,
    data: true,
    msg: '操作成功',
  });
});

test('postSelectGoodsJson', async () => {
  CartService.mockImplementation(() => {
    return {
      selectGoodsService: jest.fn().mockResolvedValue(false),
    };
  });

  const res = await app.httpRequest().post('/wsctrade/cart/selectGoods.json');

  expect(res.body).toEqual({
    code: 500,
    data: false,
    msg: '操作失败',
  });
});

test('postUnselectGoodsJson', async () => {
  CartService.mockImplementation(() => {
    return {
      unselectGoodsService: jest.fn().mockResolvedValue(true),
    };
  });

  const res = await app.httpRequest().post('/wsctrade/cart/unselectGoods.json');

  expect(res.body).toEqual({
    code: 0,
    data: true,
    msg: '操作成功',
  });
});

test('postUnselectGoodsJson', async () => {
  CartService.mockImplementation(() => {
    return {
      unselectGoodsService: jest.fn().mockResolvedValue(false),
    };
  });

  const res = await app.httpRequest().post('/wsctrade/cart/unselectGoods.json');

  expect(res.body).toEqual({
    code: 500,
    data: false,
    msg: '操作失败',
  });
});

test('postBatchSelectGoodsJson', async () => {
  CartService.mockImplementation(() => {
    return {
      selectGoodsService: jest.fn().mockResolvedValue(true),
    };
  });

  const res = await app
    .httpRequest()
    .post('/wsctrade/cart/batchSelectGoods.json');

  expect(res.body).toEqual({
    code: 0,
    data: true,
    msg: '操作成功',
  });
});

test('postBatchSelectGoodsJson', async () => {
  CartService.mockImplementation(() => {
    return {
      selectGoodsService: jest.fn().mockResolvedValue(false),
    };
  });

  const res = await app
    .httpRequest()
    .post('/wsctrade/cart/batchSelectGoods.json');

  expect(res.body).toEqual({
    code: 500,
    data: false,
    msg: '操作失败',
  });
});

test('postBatchUnselectGoodsJson', async () => {
  CartService.mockImplementation(() => {
    return {
      unselectGoodsService: jest.fn().mockResolvedValue(true),
    };
  });

  const res = await app
    .httpRequest()
    .post('/wsctrade/cart/batchUnselectGoods.json');

  expect(res.body).toEqual({
    code: 0,
    data: true,
    msg: '操作成功',
  });
});

test('postBatchUnselectGoodsJson', async () => {
  CartService.mockImplementation(() => {
    return {
      unselectGoodsService: jest.fn().mockResolvedValue(false),
    };
  });

  const res = await app
    .httpRequest()
    .post('/wsctrade/cart/batchUnselectGoods.json');

  expect(res.body).toEqual({
    code: 500,
    data: false,
    msg: '操作失败',
  });
});

test('postSelectAllGoodsJson', async () => {
  CartService.mockImplementation(() => {
    return {
      selectAllGoodsService: jest.fn().mockResolvedValue(true),
    };
  });

  const res = await app
    .httpRequest()
    .post('/wsctrade/cart/selectAllGoods.json');

  expect(res.body).toEqual({
    code: 0,
    data: true,
    msg: '操作成功',
  });
});

test('postUnselectAllGoodsJson', async () => {
  CartService.mockImplementation(() => {
    return {
      unselectAllGoodsService: jest.fn().mockResolvedValue(true),
    };
  });

  const res = await app
    .httpRequest()
    .post('/wsctrade/cart/unselectAllGoods.json');

  expect(res.body).toEqual({
    code: 0,
    data: true,
    msg: '操作成功',
  });
});

test('reselectGoods', async () => {
  CartService.mockImplementation(() => {
    return {
      reselectGoods: jest.fn().mockResolvedValue(true),
    };
  });

  const res = await app
    .httpRequest()
    .post('/wsctrade/cart/reselect-goods.json');

  expect(res.body).toEqual({
    code: 0,
    data: true,
    msg: 'ok',
  });
});

test('postBatchAddGoodsJson', async () => {
  CartService.mockImplementation(() => {
    return {
      batchAddGoodsService: jest.fn().mockResolvedValue(true),
    };
  });

  const res = await app.httpRequest().post('/wsctrade/cart/batchAddGoods.json');

  expect(res.body).toEqual({
    code: 0,
    data: true,
    msg: 'ok',
  });
});

test('getCountGoodsNums', async () => {
  app.mockContext({
    buyerId: 8000035538,
    kdtId: 491391,
  });

  CartService.getCartIdRequestData = jest.fn().mockReturnValue({
    buyerId: 8000035538,
    platform: 'wsc',
  });

  CartService.mockImplementation(() => {
    return {
      countGoodsNums: jest.fn().mockResolvedValue(1),
    };
  });

  const res = await app.httpRequest().get('/wsctrade/cart/countGoodsNums.json');
  expect(res.body).toEqual({
    code: 0,
    data: { count: 1 },
    msg: 'ok',
  });
});

test('getSelectedGoodsJson', async () => {
  CartService.mockImplementation(() => {
    return {
      getCartList: jest.fn().mockResolvedValue([]),
    };
  });

  const res = await app.httpRequest().get('/wsctrade/cart/selectedGoods.json');

  expect(res.body).toEqual({
    code: 0,
    data: [],
    msg: '',
  });
});

test('findExchangeGoods', async () => {
  CartService.mockImplementation(() => {
    return {
      findAggregatedExchangeSkus: jest.fn().mockResolvedValue([]),
    };
  });

  const res = await app
    .httpRequest()
    .get('/wsctrade/cart/find-exchange-goods.json');

  expect(res.body).toEqual({
    code: 0,
    data: [],
    msg: 'ok',
  });
});

test('getMultiRecommendGoods', async () => {
  const data = { goodsId: '123456', recommendList: [] };

  CartService.mockImplementation(() => {
    return {
      getMultiRecommendGoods: jest.fn().mockResolvedValue(data),
    };
  });

  const res = await app
    .httpRequest()
    .post('/wsctrade/cart/getMultiRecommendGoods.json');
  expect(res.body).toEqual({
    code: 0,
    data,
    msg: '',
  });
});

test('function', () => {
  const cartList = [
    {
      goodsGroupList: [
        {
          groupActivityInfo: {
            activityType: 101,
          },
        },
      ],
    },
  ];

  const ctx = app.mockContext({
    isQQApp: false,
    isAlipayApp: true,
    query: {
      mpVersion: '0.0.51',
    },
  });

  const res = new CartController(ctx).modifyGoodsGroupList(cartList);
  expect(res).toEqual([
    {
      groupActivityInfo: undefined,
    },
  ]);
});
