import { api } from 'common/api';

const TEST_CASE = [
  'noTest',
  'hasStockActivityCountdown',
  'hasTakeCoupon',
  'hasInvalidGoodsRecommend',
];
// /wsctrade/abtest/test

/* const abTestResult = {};
abTestGroup.forEach((item) => {
  const { name } = item.configurations || {};
  if (TEST_CASE.indexOf(name) >= 0) {
    abTestResult[name] = {
      ...item.configurations,
      abTraceId: item.abTraceId,
    };
  }
}); */

export const queryAbTest = function (pageScene) {
  return api
    .ajax({
      url: '/wsctrade/abtest/test',
      data: {
        pageScene,
      },
    })
    .then((resp) => {
      if (resp.code !== 0) {
        return;
      }

      return resp.data;
    })
    .catch(() => {
      return;
    });
};
