import _ from 'lodash';
import BaseService from '../base/BaseService';
import {  
  IOrderLevelReq,
} from 'definitions/scrm/level';

class ScrmLevelService extends BaseService {    
  get LEVEL_SERVICE():string {
    return 'com.youzan.scrm.api.level.service.LevelV2Service'
  } 
  
  // 商家查询单个等级接口
  public async getLevelJson(
    params: any
  ):Promise<IOrderLevelReq> {    
       const result = await this.invoke(this.LEVEL_SERVICE, 'get', [
      params,
    ]);        
    const autoRenew = _.get(result, 'autoRenew', false)
    const levelAlias = _.get(result, 'levelAlias', '')
    const levelGoods = _.get(result, 'levelGoods', [])    
    return { autoRenew, levelAlias, levelGoods}
  }
}

export default ScrmLevelService;
