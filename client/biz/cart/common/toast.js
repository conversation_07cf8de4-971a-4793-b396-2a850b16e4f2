import { Toast } from 'vant';

let timeoutToastClear;

function push() {
  if (timeoutToastClear) {
    clearTimeout(timeoutToastClear);
    timeoutToastClear = null;
  }
}

function pop(fn, timeout) {
  timeoutToastClear = setTimeout(fn, timeout);
}

class DToast extends Toast {
  loading(...params) {
    super.loading(...params);
    push();
  }

  clear() {
    pop(super.clear, 200);
  }
}

export default DToast;
