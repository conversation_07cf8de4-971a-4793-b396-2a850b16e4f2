
const BaseService = require('../../../base/BaseService');


/** com.youzan.ump.voucher.front.api.cobuild.service.activity.ActivityQueryCobuildService */
class ActivityQueryCobuildService extends BaseService {

    get SERVICE_NAME() {
        return 'com.youzan.ump.voucher.front.api.cobuild.service.activity.ActivityQueryCobuildService';
    }

    /**
     * 批量查询用户可参与的活动信息
     * @link http://zanapi.qima-inc.com/site/service/view/1147934
     * @param {Object} request - 活动查询参数
     * @param {number} request.kdtId - 店铺id
     * @param {string} request.bizName - 业务名称
     * @param {Array} request.orderList - 订单信息
     * @param {string} request.source - 发券来源
     * @param {number} request.yzUid - 用户ID
     * @return {Promise}
     */
    async batchQueryUserCanParticipateActivity(request) {
        return this.invoke('batchQueryUserCanParticipateActivity', [request]);
    }
}


module.exports = ActivityQueryCobuildService;

