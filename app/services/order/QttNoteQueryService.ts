import BaseService from '../base/BaseService';

interface INoteBaseQuery {
  /** 操作人用户ID */
  operatorUserId?: number;
  withRealNoteStatus?: boolean;
  withImg?: boolean;
  noteId?: number;
  /** 操作人ID */
  operatorId?: number;
  /** 操作人名称 */
  operatorName?: string;
  withCache?: boolean;
}

class QttNoteQueryService extends BaseService {
  // @ts-ignore
  get SERVICE_NAME() {
    return 'com.youzan.qtt.note.api.note.INoteQueryProvider';
  }

  /**
   *  获取群团团笔记信息
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1333239
   */
  async queryNoteBaseById(query: INoteBaseQuery) {
    return this.invoke('queryNoteBaseById', [query]);
  }
}

export = QttNoteQueryService;
