import BaseService from '../base/BaseService';

interface SupportSecuredParam {
  aliases: string[];
}

class FinComponentQueryService extends BaseService {
 
  get CHAIN_SERVICE() {
    return 'com.youzan.pay.yz.fin.portal.api.portal.FinComponentQueryService';
}

  public async batchQuerySupportSecured(params: SupportSecuredParam) {
     return this.invoke(this.CHAIN_SERVICE,'batchQuerySupportSecured', [params]);
  } 

}

export = FinComponentQueryService;
