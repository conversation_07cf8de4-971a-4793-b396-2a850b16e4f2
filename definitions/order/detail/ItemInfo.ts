export interface IItemInfoDto {
  itemId: number;
  skuId: number;
  goodsId: number;
  goodsType: number;
  extra: any;
  num: number;
  unitPrice: number;
  originUnitPrice: number;
  snapShot: string;
  goodsInfo: any;
  sku: any;
  safeRefund?: any;
  controlButton: any;
  controlExtra: any;
  buyerMemo: any;
  orderItemId: number;
  /**
   * 是否包税
   * 1.非海淘商品
   * 2.包税
   * 3.不包税
   */
  tariffTag?: number;
  /**
   * 税费
   */
  tariffPay?: number;
  postponeDeliveryUrl?: string;
  cancelPostponeDeliveryUrl?: string;
  /**
   * 退差价信息
   */
  refundOrderItem?: any;
  /**
   * 商品制作进度
   */
  processInfo: IProcessInfo;
  /**
   * 进口税费
   */
  formattedTaxTips?: string;
  /**
   * 商品标签
   */
  tags?: any[];
  /**
   * 是否使用内购
   */
  isUseFissionUmp?: boolean;
  /**
   * 格式化后的商品标签
   */
  formattedTags?: any[];
  /**
   * 套餐商品信息
   */
  comboDetail?: any;
  /**
   * 格式化后的套餐商品信息
   */
  formattedComboDetail?: string[];
  /**
   * 动态计算提示信息
   */
  formattedGoodsTips?: string[];
  /**
   * 是否为失效赠品
   */
  isInvalidPresent?: boolean;
  /**
   * 商品售后状态
   */
  formattedFeedbackTips?: string;
  /**
   * 划线价
   */
  formattedOriginPrice?: string;
}

export interface IProcessInfo {
  [key: string]: IProcessInfoItem
}

export interface IProcessInfoItem {
  bomProcessList: IProcessItem[],
  orderItemId: number,
  orderNo: number,
  status: string
}

export interface IProcessItem {
  name: string,
  order: number,
  current: number,
  time: number
}