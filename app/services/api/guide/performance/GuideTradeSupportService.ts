
import { IGuideFromSlRequest } from 'definitions/api/guide/performance/GuideTradeSupportService/getGuideFromSl';

import BaseService from '../../../base/BaseService';


/** com.youzan.guide.performance.achievement.api.service.GuideTradeSupportService */
class GuideTradeSupportService extends BaseService {

    get SERVICE_NAME() {
        return 'com.youzan.guide.performance.achievement.api.service.GuideTradeSupportService';
    }

    /**
     * 根据sl标返回导购信息
     * @link http://zanapi.qima-inc.com/site/service/view/1316957
     * @param {Object} request -
     * @param {number} request.kdtId - 店铺ID
     * @param {string} request.sl - sl标
     * @return {Promise}
     */
    async getGuideFromSl(request: IGuideFromSlRequest) {
        return this.invoke('getGuideFromSl', [request]);
    }
}


export = GuideTradeSupportService;

