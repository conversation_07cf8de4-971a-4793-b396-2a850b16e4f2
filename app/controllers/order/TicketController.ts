import { PageException } from '@youzan/iron-base';
import { Context } from 'astroboy';
import BaseController from '../base/BaseController';
import TicketService from '../../services/order/TicketService';
import SecureService from '../../services/order/SecureService';

import {
  IVirtualTicketDTO,
} from 'definitions/order/OrderTicket';

class TicketController extends BaseController {
  async getDetailHtml(ctx: Context) {
    const kosTokenVerified = await new SecureService(ctx).validKosToken();

    // 非收货人模式需要走 acl
    if (!kosTokenVerified) {
      await this.needPlatformAcl();
    }

    const { kdtId } = ctx;
    const { orderNo, order_no: orderNoOther } = ctx.query;
    const finalOrderNo = orderNo || orderNoOther;
    // eslint-disable-next-line youzan/youzan-standard-words
    this.validator.required(finalOrderNo, '订单号不能为空');
    try {
      const result = await this.checkCloud(ctx, kdtId, finalOrderNo);
      if (result) return;
    } catch (e) {
      const content = e.errorContent;
      if (content) {
        throw new PageException(content.code, content.msg);
      } else {
        throw e;
      }
    }
    const ticketService = new TicketService(ctx);
    const params: IVirtualTicketDTO = {
      orderNo: finalOrderNo,
      kdtId,
      from: 'h5',
      buyerId: 0,
    };

    if (kosTokenVerified) {
      Object.assign(params, {
        extension: {
          IS_RECEIVER_TEL_QUERY: '1',
        },
      });
    } else {
      params.buyerId = this.buyerId;
    }
    const data = await ticketService.queryByOrderNo(params);

    ctx.setGlobal({ ticketInfo: escape(JSON.stringify(data)) });
    await ctx.render('order/ticket-detail.html');
  }

  async checkCloud(ctx: Context, kdtId: number, orderNo: string) {
    const params = {
      kdtId,
      pageName: 'TicketDetails',
      optionalRuleContext: {
        orderNo,
      },
    };
    const ticketService = new TicketService(ctx);
    const data = await ticketService.queryFrontPathRedirectPath(params);
    if (data && data.needRedirect) {
      const url = data.redirectPath;
      ctx.redirect(url);
      return true;
    }
    return false;
  }

  /**
   * 获取电子卡券详情
   */
  async getDetailJson(ctx: Context) {
    const { kdtId } = ctx;
    const { orderNo, order_no: orderNoOther } = ctx.query;
    const finalOrderNo = orderNo || orderNoOther;
    // eslint-disable-next-line youzan/youzan-standard-words
    this.validator.required(finalOrderNo, '订单号不能为空');
    const ticketService = new TicketService(ctx);
    const params = {
      orderNo: finalOrderNo,
      kdtId,
      buyerId: this.buyerId,
      from: 'weapp',
    };
    const data = await ticketService.queryByOrderNo(params);
    ctx.r(0, '', data);
  }

  async getSyncWxJson(ctx: Context) {
    const { kdtId } = ctx;
    const { cardNo, orderNo } = ctx.getPostData();
    const ticketService = new TicketService(ctx);
    const data = await ticketService.getSyncWx(kdtId, orderNo, cardNo);
    ctx.json(0, '', data);
  }
}

module.exports = TicketController;
