import BaseService from '../base/BaseService';

interface IClientDetail {
  kdtId: number;
  itemId: number;
} 

/** com.youzan.ebiz.mall.rigel.api.hotel.service.RoomVoucherPackageQueryService -  */
class RoomVoucherPackageQueryService extends BaseService {
  get SERVER_NAME() {
    return 'com.youzan.ebiz.mall.rigel.api.hotel.service.RoomVoucherPackageQueryService';
  }

  /**
   *  C端-查询房券套餐聚合信息
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1172934
   *
   *  @param {Object} query -
   *  @param {number} query.id - 房券套餐id
   *  @return {Promise}
   */
  async getCclientDetail(query: IClientDetail) {
    return await this.invoke(this.SERVER_NAME, 'getCclientDetail', [query]);
  }
}

export = RoomVoucherPackageQueryService;
