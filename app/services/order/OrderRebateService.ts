import BaseService from '../base/BaseService';

class OrderRebateService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.ebiz.mall.trade.buyer.api.service.ShopService';
  }
  /**
   *  获取返储值金的说明文案
   *  @param {object} params
   *  @returns {object}
   */
  async queryShopUserCenterName(params: any) {
    return this.invoke('queryCashBackDetail', [params]);
  }
}

export = OrderRebateService;
