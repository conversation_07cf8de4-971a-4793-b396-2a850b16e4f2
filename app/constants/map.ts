// 当前map接口支持的方法，新增方法需要在此添加方法名枚举，routes生成依赖MAP_METHODS
export enum MAP_METHODS {
  geocoder = 'geocoder',
  reverseGeocoder = 'reverseGeocoder',
  searchNearBy = 'searchNearBy',
  getSuggestion = 'getSuggestion',
  getLocationByIp = 'getLocationByIp',
}

// 地图版本：amap/qqmap
export enum MAP_VERSION {
  /* 高德 */
  AMAP = 'amap',
  /* 腾讯 */
  QQMAP = 'qqmap',
}
// 地图api类型：map/amap/qqmap
export enum MAP_TYPE {
  /* 当前封装的地图服务 */
  MAP = 'map',
  /* 高德 */
  AMAP = 'amap',
  /* 腾讯 */
  QQMAP = 'qqmap',
}

// 地图域名
export const MAP_HOSTS = {
  [MAP_VERSION.AMAP]: 'restapi.amap.com',
  [MAP_VERSION.QQMAP]: 'apis.map.qq.com',
};

const AMAP_REQUEST_URLS = {
  [MAP_METHODS.geocoder]: 'http://restapi.amap.com/v3/geocode/geo',
  [MAP_METHODS.reverseGeocoder]: 'http://restapi.amap.com/v3/geocode/regeo',
  [MAP_METHODS.searchNearBy]: 'http://restapi.amap.com/v3/place/around',
  [MAP_METHODS.getSuggestion]: 'http://restapi.amap.com/v3/assistant/inputtips',
  [MAP_METHODS.getLocationByIp]: 'http://restapi.amap.com/v3/ip',
};

// 腾讯地图api，geocoder为通用api
const QQMAP_REQUEST_URLS = {
  [MAP_METHODS.geocoder]: 'http://apis.map.qq.com/ws/geocoder/v1',
  [MAP_METHODS.reverseGeocoder]: 'http://apis.map.qq.com/ws/geocoder/v1',
  [MAP_METHODS.searchNearBy]: 'http://apis.map.qq.com/ws/geocoder/v1',
  [MAP_METHODS.getSuggestion]: 'http://apis.map.qq.com/ws/place/v1/suggestion',
  [MAP_METHODS.getLocationByIp]: 'http://apis.map.qq.com/ws/location/v1/ip',
};

// 第三方地图服务请求链接
export const REQUEST_URLS = {
  [MAP_VERSION.AMAP]: AMAP_REQUEST_URLS,
  [MAP_VERSION.QQMAP]: QQMAP_REQUEST_URLS,
};

// poi类型对应的值
export const POI_TYPE = {
  [MAP_VERSION.QQMAP]: 1,
  [MAP_VERSION.AMAP]: 3,
};

// 是否未开发地图api模式
export const MAP_DEVELOP = process.env.LOCAL_MAP === '1';

// 地图默认key
export const MAP_DEFAULT_KEY = {
  [MAP_VERSION.AMAP]: 'eb682361988aec8112ed91e5c28705b8',
  [MAP_VERSION.QQMAP]: 'DE4BZ-X6DCP-WUNDV-L4DDA-6NWA7-3CBSO',
}