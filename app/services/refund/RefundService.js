const _ = require('lodash');
const BaseService = require('../base/BaseService');

class RefundService extends BaseService {
  get REFUND_SERVICE() {
    return 'com.youzan.ebiz.mall.trade.buyer.api.service.RefundService';
  }

  get EXCHANGE_GOODS_SERVICE() {
    return 'com.youzan.ebiz.mall.trade.buyer.api.service.ExchangeGoodsService';
  }

  get POINTS_READ_REFUND_SERVICE() {
    return 'com.youzan.scrm.api.credit.points.PointsReadService';
  }

  async buyerGetRefundInfo(params) {
    const response = await this.invoke(this.REFUND_SERVICE, 'buyerGetRefundInfo', [params]);

    return {
      ...response,
      // 是否支持重新发起退款:退款次数超过三次
      supportRefund: response.refundCount < 3,
      refundFundProcessDetailList: _.map(response.refundFundProcessDetailList, ({ done, desc, processType }) => ({
        isDone: done,
        desc,
        processType,
      })),
    };
  }

  async buyerRefundConsultMessage(params) {
    return await this.invoke(this.REFUND_SERVICE, 'buyerRefundConsultMessage', [params]);
  }

  async buyerCloseRefund(params) {
    return await this.invoke(this.REFUND_SERVICE, 'buyerCloseRefund', [params]);
  }

  async buyerExchangeClose(params) {
    return await this.invoke(this.EXCHANGE_GOODS_SERVICE, 'buyerExchangeClose', [params]);
  }

  async buyerGetExpress(params) {
    return await this.invoke(this.REFUND_SERVICE, 'buyerGetExpress', [params]);
  }

  async buyerRefundInit(params) {
    const response = await this.invoke(this.REFUND_SERVICE, 'buyerRefundInit', [params]);

    return {
      ...response,
      isFullRefund: response.fullRefund,
      buyerPhoneByDefault: response.buyerPhone,
      isShipped: response.shipped,
    };
  }

  async buyerAddMessage(params) {
    return await this.invoke(this.REFUND_SERVICE, 'buyerAddMessage', [params]);
  }

  async buyerInvolve(params) {
    return await this.invoke(this.REFUND_SERVICE, 'buyerInvolve', [params]);
  }

  async buyerReturnGoods(params) {
    return await this.invoke(this.REFUND_SERVICE, 'buyerReturnGoods', [params]);
  }

  async buyerRefundUpdate(params) {
    return await this.invoke(this.REFUND_SERVICE, 'buyerRefundUpdate', [params]);
  }

  async buyerExchangeUpdate(params) {
    return await this.invoke(this.EXCHANGE_GOODS_SERVICE, 'buyerExchangeUpdate', [params]);
  }

  async buyerExchangeReceive(params) {
    return await this.invoke(this.EXCHANGE_GOODS_SERVICE, 'buyerExchangeReceive', [params]);
  }

  async buyerRefundCreate(params) {
    return await this.invoke(this.REFUND_SERVICE, 'buyerRefundCreate', [params]);
  }

  async buyerExchangeCreate(params) {
    return await this.invoke(this.EXCHANGE_GOODS_SERVICE, 'buyerExchangeCreate', [params]);
  }

  async getSellerPhone(params) {
    return await this.invoke(this.REFUND_SERVICE, 'getSellerPhone', [params]);
  }

  async getRefundState(params) {
    return await this.invoke(this.REFUND_SERVICE, 'getRefundState', [params]);
  }

  /**
   *  查询积分消耗退款明细
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/227923
   *
   *  @param {Object} params -
   *  @param {string} params.orderNo - 订单号
   *  @param {number} params.kdtId - 店铺ID
   *  @param {Object} params.sourceDTO - 客户来源描述
   *  @param {Array.<Array>} params.refundNoList[] - 退款流水号
   *  @param {number} params.consumeType - 消费类型
   *  @return {Promise}
   */
  async getConsumeRefundDetail(params) {
    return this.invoke(this.POINTS_READ_REFUND_SERVICE, 'getConsumeRefundDetail', [params]);
  }

  /**
   * 维权单评价弹框已弹标记
   */
  async addRefundTag(params) {
    return this.invoke(this.REFUND_SERVICE, 'addRefundTag', [params]);
  }
}

module.exports = RefundService;
