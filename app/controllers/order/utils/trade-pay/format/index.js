/**
 * 原trade-pay-page-setup中的format逻辑迁移
 * 剔除了针对不同端的数据处理逻辑，只保留了通用的format逻辑
 * 不同端的处理逻辑仍旧放在客户端运行时处理
 */

import getState from './state';
import getGetters from './getters';

import getAddressData from './modules/address';
import getPriceData from './modules/price';

import { getFormattedData } from './dataFormat';

// 暴露给trade-pay-ecloud使用的数据
export function getFormatState(prepare = {}) {
  const state = getState({ prepare });
  const getters = getGetters({ prepare, state });

  return {
    ...state,
    ...getters,
    ...getAddressData({ prepare, state, getters }),
    ...getPriceData({ prepare, state, getters }),
  };
}

/**
 * 暴露给待支付页的数据
 */
export function getTradePayClientData(prepare = {}) {
  const state = getState({ prepare });
  const getters = getGetters({ prepare, state });


  const clientState =  {
    ...state,
    ...getters,
    ...getAddressData({ prepare, state, getters }),
    ...getPriceData({ prepare, state, getters }),
  }
  const clientCtxData = getFormattedData({ prepare, state, getters });
  return {
    clientState,
    clientCtxData,
  }
}