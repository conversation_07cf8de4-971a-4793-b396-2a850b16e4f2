import { Context } from 'astroboy';
import BaseController from '../base/BaseController';
import KfcTicketService from '../../services/kfc/KfcTicketService';

class KfcTicketController extends BaseController {
  async getKfcTicketExpandInfoJson(ctx: Context) {
    const queryData = ctx.getQueryData();
    try {
      const res = await new KfcTicketService(ctx).getKfcTicketExpandInfo(queryData);
      ctx.r(0, 'success', res.data);
    } catch (e) {
      const errorContent = e.errorContent;
      if (!errorContent) {
        // eslint-disable-next-line no-console
        console.error('getKfcTicketExpandInfo unexpected error:', e);
        throw e;
      } else {
        // eslint-disable-next-line no-console
        console.error(e.message, e);
        ctx.r(errorContent.code, errorContent.msg);
      }
    }
  }
}

export = KfcTicketController;
