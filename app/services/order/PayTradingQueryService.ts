import BaseService from '../base/BaseService';

class PayTradingQueryService extends BaseService {
  async querySalemanUpgradeResult(params: any) {
    const options = {
      service: 'youzan.pay.trading.query.api.tradeinf.PayTradingQueryService',
      method: 'queryPaymentDetail',
      version: '1.0.2',
      partnerId: '',
    };
    const dubboData = await this.invokePay(options, params);
    return dubboData;
  }
}

export = PayTradingQueryService;
