import BaseService from '../base/BaseService';
import { IGuaranteeParams } from 'definitions/other/GoodsYzGuarantee';

class GoodsYzGuaranteeService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.pay.yz.fin.portal.api.portal.FinComponentQueryService';
  }

  /**
   *  获取商品sku
   *  zanAPI文档地址: com.youzan.item.detail.api.ItemSkuService.getSkuData
   */
  async getYzGuarantee(params: IGuaranteeParams) {
    return this.invoke('batchQuerySupportSecured', [params]);
  }
}

export = GoodsYzGuaranteeService;
