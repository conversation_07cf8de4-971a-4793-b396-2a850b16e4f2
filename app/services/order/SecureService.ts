import BusinessServiceError from '@youzan/iron-base/app/services/base/BusinessServiceError';
import BaseService from '../base/BaseService';
import UicEncryptService from '../uic/UicEncryptService';
import buildUrlWithCtx from '@youzan/utils/url/buildUrlWithCtx';
import { IVerifyRecord } from 'definitions/order/OrderSecure';
import { isInGrayReleaseByKdtId } from '../../lib/WhiteListUtils';
import mapKeysToCamelCase from '@youzan/utils/string/mapKeysToCamelCase';

class SecureService extends BaseService {
  private get ORDER_VERIFY_SERVICE_NAME() {
    return 'com.youzan.ebiz.trade.api.manage.detail.OrderVerifyService';
  }

  /*  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1011567
   *
   *  @param {Object} verifyParam - the {@link OrderReceiverVerifyParam}
   *  @param {string} verifyParam.orderNo - 订单号
   *  @param {string} verifyParam.telephone - 收货人电话号码
   *  @return {Promise}
   */
  public async verifyOrderReceiverMatch(params: {
    orderNo: string;
    telephone: string;
  }): Promise<boolean> {
    const result: boolean = await this.invoke(
      this.ORDER_VERIFY_SERVICE_NAME,
      'verifyOrderReceiverMatch',
      [params]
    );
    return result;
  }

  async validKosToken() {
    const { ctx } = this;
    const { orderNo, kosToken: kosTokenStr, verifymode } = mapKeysToCamelCase(ctx.query);
    // 首先识别kos_token鉴权模式（新方案放在后端鉴权；老的参数格式也统一走）
    if (this.checkKosToken(kosTokenStr) || verifymode === 'phone') {
      // 本来想按照订单存储key，session接口不支持超长（超过14位长度）,于是简化逻辑；用户打开多个订单时前面的校验记录会被最后一次覆盖掉
      const verifyRecord = <IVerifyRecord>(ctx.getLocalSession('verified_order'));
      if (
        verifyRecord &&
        verifyRecord.orderNo === orderNo &&
        verifyRecord.expireAt > Date.now()
      ) {
        // 使用缓存校验成功
        return true;
      } else {
        this.redirectToVerifyPage();
        return true;
      }
    }
    return false;
  }

  validKosPhone(kosToken: any): boolean {
    const { ctx } = this;

    const { phone = '', orderNo, kosToken: token = '', kdtId = 0 } = mapKeysToCamelCase(ctx.query);
    // 订单号不匹配，返回失败
    if (kosToken.order_no !== orderNo) {
      return false;
    } else if (!phone || phone !== kosToken.phone) {
      // 手机号不匹配，跳转验证页面
      const currentUri = encodeURIComponent(
        'https://' + this.ctx.host + this.ctx.originalUrl
      );
      const buildUrl = buildUrlWithCtx(ctx);
      let redirectUri = buildUrl(
        'https://h5.youzan.com/wsctrade/order/secure/verifyphone?redirect_uri=' +
          currentUri +
          '&kos_token=' +
          encodeURIComponent(token),
        '',
        kdtId
      );
      if (
        (kdtId && kdtId.length > 10) ||
        (kosToken.kdt_id && kosToken.kdt_id.length > 10)
      ) {
        try {
          // 埋点记录kdtId
          ctx.logger.info(
            `kdtIdCrash${JSON.stringify(ctx.query)} split ${JSON.stringify(
              kosToken
            )}`
          );
        } catch (e) {
          // crash
          ctx.logger.error('error kdtIdCrash:' + JSON.stringify(e));
        }
      }

      if (kdtId) {
        redirectUri += '&kdt_id=' + kdtId;
      }
      if (orderNo) {
        redirectUri += '&order_no=' + orderNo;
      }

      ctx.redirect(redirectUri);
      return false;
    }

    // 验证通过
    return true;
  }

  redirectToVerifyPage() {
    const { ctx } = this;
    const { kdtId = 0, orderNo = '' } = mapKeysToCamelCase(ctx.query);

    // 手机号不匹配，跳转验证页面
    const currentUri = encodeURIComponent(
      'https://' + ctx.host + ctx.originalUrl
    );
    let redirectUri = buildUrlWithCtx(ctx)(
      'https://h5.youzan.com/wsctrade/order/secure/verifyphone?redirect_uri=' +
        currentUri,
      '',
      kdtId
    );

    if (kdtId) {
      redirectUri += '&kdt_id=' + kdtId;
    }
    if (orderNo) {
      redirectUri += '&order_no=' + orderNo;
    }

    ctx.redirect(redirectUri);
  }

  checkKosToken(kosToken: any) {
    return kosToken && kosToken.length > 10;
  }

  // 从URL.kos_token解析成对象
  async parseUrlKosToken(kosTokenStr: string) {
    const { ctx } = this;
    let kosToken;
    const decryptObj = await new UicEncryptService(ctx).aesDecrypt(kosTokenStr);
    if (decryptObj && Object.keys(decryptObj).length > 0) {
      try {
        kosToken =
          decryptObj.decryptValue && JSON.parse(decryptObj.decryptValue);
      } catch (e) {
        console.error(e);
      }
      if (!kosToken || Object.keys(kosToken).length <= 0) {
        throw new BusinessServiceError(40204, '你没有权限操作');
      }
    } else {
      throw new BusinessServiceError(40204, '你没有权限操作');
    }
    return kosToken;
  }
}

export = SecureService;