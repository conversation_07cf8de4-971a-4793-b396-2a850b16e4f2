import BaseService from './BaseService';

class WeappQRCodeService extends BaseService {
  get SERVER_NAME() {
    return 'com.youzan.channels.apps.service.WeappQRCodeService';
  }

  /**
   * 获取无限小程序码
   * @param {*} kdtId
   * @param {*} page
   * @param {Object} query query 的每个值都必须是字符串
   * http://zanapi.qima-inc.com/site/service/view/266047
   */
  async getCodeUltra(kdtId: number | string, page: string, query: string, options?: any) {
    const params = JSON.parse(query);
    Object.keys(params).forEach(key => {
      params[key] = String(params[key]);
    });
    const result = await this.invoke(this.SERVER_NAME, 'wxaGetCodeUltra', [{ kdtId, page, params, ...options }]);
    return result;
  }
}

export = WeappQRCodeService;
