import BaseService from '../base/BaseService';

interface IWholesalerCheckRequest {
  kdtId?: number;
  pageSize?: number;
  originKdtId?: number;
  userId?: number;
  pageNum?: number;
}

/** com.youzan.ebiz.social.wholesale.consumer.api.wholesasler.WholesalerQueryService */
class WholesalerReadService extends BaseService {
  get WHOLESALE_SERVICE_NAME() {
    return 'com.youzan.ebiz.social.wholesale.consumer.api.wholesasler.WholesalerQueryService';
  }

  /**
   * 是否是批发商
   * @link http://zanapi.qima-inc.com/site/service/view/1252000
   * @param {Object} request -
   * @param {number} request.kdtId -
   * @param {number} request.pageSize -
   * @param {number} [request.originKdtId] -
   * @param {number} request.userId -
   * @param {number} request.pageNum -
   * @return {Promise}
   */
  async isWholesaler(wholesalerCheckRequest: IWholesalerCheckRequest) {
    return this.invoke(this.WHOLESALE_SERVICE_NAME, 'isWholesaler', [wholesalerCheckRequest]);
  }
}

export = WholesalerReadService;
