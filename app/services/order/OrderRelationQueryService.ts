import BaseService from '../base/BaseService';

class OrderRelationQueryService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.trade.core.service.query.OrderRelationQueryService';
  }

  /**
   * @description 依据关联关系类型以及订单号查询一条关联关系记录
   * @param {Number} relationType
   * @param {String} orderNo
   */
  public queryOrderRelationByOrderNoAndType(relationType: number, orderNo: string) {
    return this.invoke(this.SERVICE_NAME, 'queryOrderRelationByOrderNoAndType', [relationType, orderNo]);
  }
}

export default OrderRelationQueryService;
