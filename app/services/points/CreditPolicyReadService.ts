import BaseService from '../base/BaseService';

class CreditPolicyReadService extends BaseService {
  public get SERVICE_NAME() {
    return 'com.youzan.scrm.api.credit.policy.CreditPolicyReadService';
  }

  /**
   *  获取商家自定义名称
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/279989
   *
   *  @param {Object} queryDTO -
   *  @param {number} queryDTO.creditDefinitionId - 积分定义 | 为了以后扩展加的字段，先传0
   *  @param {number} queryDTO.kdtId - 店铺ID
   *  @return {Promise}
   */
  public async getName() {
    const { kdtId } = this.ctx;
    return this.invoke('getName', [{ kdtId, creditDefinitionId: 0 }]);
  }

  /**
   *  获取商家自定义名称
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/279989
   *
   *  @param {Object} queryDTO -
   *  @param {number} queryDTO.creditDefinitionId - 积分定义 | 为了以后扩展加的字段，先传0
   *  @param {number} queryDTO.kdtId - 店铺ID
   *  @return {Promise}
   */
  public async getNameFast() {
    const { kdtId } = this.ctx;
    return this.invokeFast('getName', [{ kdtId, creditDefinitionId: 0 }], {
      responseType: 'text'
    });
  }
}

export = CreditPolicyReadService;
