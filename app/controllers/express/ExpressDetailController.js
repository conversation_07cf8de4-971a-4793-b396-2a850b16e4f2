import { createWscIsolateCheck, checkRetailShop } from '@youzan/utils-shop';
import CompareVersion from '../../lib/CompareVersion';

const BaseController = require('../base/BaseController');
const ExpressDetailService = require('../../services/express/ExpressDetailService');

class ExpressDetailController extends BaseController {
  async init() {
    await super.init({
      initCopyrightFooter: !this.ctx.acceptJSON,
      initGlobalTheme: true,
    });
  }

  // H5 - 物流配送详情页
  async getIndexHtml(ctx) {
    const { order_no: orderNo = '', express_type: expressType = '', item_id: itemId = '' } = ctx.getQueryData();
    // 安全漏洞 添加鉴权
    const { kdtId = 0, buyerId = 0 } = ctx;
    const fromApp = 'wsc-h5-trade';
    let exchange = false;

    // 感觉未来会拓展, 前端先处理成 switch case
    switch (expressType) {
      case 'exchange':
        // 售后换货物流
        exchange = true;
        break;
      default:
        // 常规发货物流
        exchange = false;
        break;
    }

    const queryParams = {
      kdtId,
      orderNo,
      exchange,
      buyerId,
      fromApp,
    };

    if (itemId) {
      queryParams.itemId = itemId;
    }

    try {
      this.setExtensionParams(ctx, 'express-detail');

      const deliveryOrder = await new ExpressDetailService(ctx).getExpressDetail(queryParams);

      ctx.logger.info(`【物流详情页流程】getExpressDetail: ${JSON.stringify(deliveryOrder || {})}`);

      const shopMetaInfo = await this.callService('iron-base/shop.ShopMetaReadService', 'getShopMetaInfo', kdtId);

      ctx.logger.info(`【物流详情页流程】getShopMetaInfo: ${JSON.stringify(shopMetaInfo || {})}`);

      // 微商城单店
      const { isPureWscSingleStore } = createWscIsolateCheck(shopMetaInfo);
      // 零售店铺
      const isRetailShop = checkRetailShop(shopMetaInfo);

      const showSubscription =
        !(ctx.isWeapp && ctx.xExtraData.version && CompareVersion.isLt(ctx.xExtraData.version, '2.87.3'));
      ctx.setGlobal({
        kdtId,
        orderNo,
        deliveryOrder,
        isPureWscSingleStore,
        showSubscription,
        isRetailShop,
      });

      ctx.logger.info(
        `【物流详情页流程】setGlobal: ${JSON.stringify({
          kdtId,
          orderNo,
          deliveryOrder,
          isPureWscSingleStore,
          showSubscription,
          isRetailShop,
        } || {})}`
      );

      // 日志埋点模型
      this.setSpm('traceDetail', kdtId);

      ctx.logger.info(`【物流详情页流程】node开始渲染`);
    } catch (err) {
      ctx.logger.info(`【物流详情页流程】node渲染error`, err);
    }

    await ctx.render('express/detail.html');
  }

  /**
   * 同城配送信息
   */
  async getCityDetail(ctx) {
    const { order_no: orderNo = '', pack_id: packId = '' } = ctx.query;
    const queryData = {
      kdtId: Number(ctx.kdtId),
      buyerId: ctx.buyerId,
      orderNo,
      packId: String(packId),
    };
    const result = await new ExpressDetailService(ctx).getCityDetail(queryData);
    ctx.json(0, '', result);
  }
}

module.exports = ExpressDetailController;
