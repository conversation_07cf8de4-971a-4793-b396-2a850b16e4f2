<template>
  <recommend-goods
    biz-name="cart"
    ref="cartRecommendGoodsRef"
    @recommnd-goods-mounted="onComponentMounted"
  />
</template>

<script>
import Delegate from 'dom-delegate';
import { jumpLink } from '@shared/common/helper';
import { closestDom } from '@/shared/common/dom';
import RecommendGoods from 'components/recommend-goods';

export default {
  name: 'recommend-block',

  components: {
    RecommendGoods,
  },

  props: {
    ctx: Object,
  },

  methods: {
    onComponentMounted() {
      this.$nextTick(() => {
        const recommendGoodsDel = Delegate(
          this.$refs.cartRecommendGoodsRef &&
            this.$refs.cartRecommendGoodsRef.$el
        );
        recommendGoodsDel &&
          recommendGoodsDel.on('click', 'a', function(e) {
            let { target } = e;
            if (target.tagName.toLowerCase() !== 'a') {
              target = closestDom(target, 'a');
            }
            if (target.href) {
              jumpLink(target.href);
              return false;
            }
            return true;
          });
      });
    },
  },
};
</script>
