import List from './Main';
import ListItemTag from './list-item/Tag';
import ListItemHeader from './list-item/Header';
import ListItemTimeTips from './list-item/TimeTips';
import ListItemBody from './list-item/Body';
import ListItemLogistics from './list-item/Logistics';
import EmptyTip from './EmptyTip';
import NoMoreTip from './NoMoreTip';

export default class ListBlock {
  static widgets = { Main: List };

  static components = {
    EmptyTip,
    NoMoreTip,
    ListItemTag,
    ListItemBody,
    ListItemHeader,
    ListItemTimeTips,
    ListItemLogistics,
  };

  constructor(options) {
    this.ctx = options.ctx;
  }
}
