import BaseService from '../../../base/BaseService';

interface IExchangeCodeParams {
  kdtId: number;
  userId: number;
  couponCode: string;
  platform: number;
}

interface IThirdCouponOrderExchangeRequest {
  /** 来源 如Android iOS web */
  retailSource?: string;
  /** 三方订单id */
  orderId?: string;
  /** 店铺id */
  kdtId?: number;
  /** 请求唯一标识 */
  requestId?: string;
  /** 卡门内部使用（操作人id） */
  adminId?: number;
  /** 请求方ip */
  requestIp?: string;
  /** 用户id */
  userId?: number;
  /** 操作人ID */
  operatorId?: number;
  /** 支持套餐商品 */
  supportCombo: boolean;
}

export default class CouponService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.retail.trade.misc.api.service.CouponService';
  }

  /**
   * 兑换三方优惠券
   * http://zanapi.qima-inc.com/site/service/view/1350068
   *
   * @param {IExchangeCodeParams} params
   * @returns {object}
   */
  async exchangeByThirdCodeValue(params: IExchangeCodeParams) {
    return this.invoke('exchangeByThirdCodeValue', [params]);
  }

  /**
   *  三方券兑换
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1393959
   *
   *  @param {Object} request -
   *  @param {string} request.retailSource - 来源 如Android iOS web
   *  @param {string} request.orderId - 三方订单id
   *  @param {number} request.kdtId - 店铺id
   *  @param {string} request.requestId - 请求唯一标识
   *  @param {number} request.adminId - 卡门内部使用（操作人id）
   *  @param {string} request.requestIp - 请求方ip
   *  @param {number} request.userId - 用户id
   *  @param {number} request.operatorId - 操作人ID
   *  @return {Promise}
   */
  async exchangeByThirdOrderId(request: IThirdCouponOrderExchangeRequest) {
    return this.invoke('exchangeByThirdOrderId', [request]);
  }
}
