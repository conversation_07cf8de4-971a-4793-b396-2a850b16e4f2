const BaseService = require('../base/BaseService');
const formatDate = require('@youzan/utils/date/formatDate');

class HeaderOrderService extends BaseService {
  get HEADER_ORDER_SERVICE() {
    return 'com.youzan.ebiz.mall.rigel.api.groupbuy.service.order.OrderService';
  }

  async orderQuery(params) {
    const dubboData = await this.invoke(this.HEADER_ORDER_SERVICE, 'queryOrders', [params]);
    dubboData.list = dubboData.list.map(item => {
      item.bookTime = formatDate(item.bookTime, 'YYYY-MM-DD HH:mm:ss');
      // 订单状态处理，兼容老版本小程序
      // 100 === 60
      item.state = item.state === 60 ? 100 : item.state;
      return item;
    });
    return dubboData;
  }

  /**
   * 确认提货（核销）
   * http://zanapi.qima-inc.com/site/service/view/264778
   */
  async confirmVerify(params) {
    const dubboData = await this.invoke(this.HEADER_ORDER_SERVICE, 'confirmVerify', [params]);
    return dubboData;
  }

  /**
   *  分页查询汇总信息
*  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/111436 
*
    *  @param {Object} orderQueryDTO - 
    *  @param {string} orderQueryDTO.customerNickName - 买家昵称
    *  @param {boolean} orderQueryDTO.withBuyerInfo - 是否包含买家信息
    *  @param {number} orderQueryDTO.pageSize - 每页显示大小
    *  @param {string} orderQueryDTO.source - 查询来源
收益：PROFIT
    *  @param {string} orderQueryDTO.headerMobile - 团长手机号
    *  @param {string} orderQueryDTO.actName - 活动名称
    *  @param {Array.<Array>} orderQueryDTO.states[] - 订单状态集合
    *  @param {number} orderQueryDTO.customerBuyerId - 买家uid
    *  @param {number} orderQueryDTO.activityId - 活动id
    *  @param {number} orderQueryDTO.customerType - 买家粉丝类型
    *  @param {number} orderQueryDTO.customerId - 买家粉丝id
    *  @param {number} orderQueryDTO.startTime - 开始时间
    *  @param {string} orderQueryDTO.sortBy - 排序字段
    *  @param {number} orderQueryDTO.state - 订单状态 3:待支付, 5:已付款, 100:交易完成, 99:已关闭
    *  @param {string} orderQueryDTO.verifyCode - 提货核销码
    *  @param {string} orderQueryDTO.orderNo - 订单编号
    *  @param {number} orderQueryDTO.offset - 偏移量
    *  @param {number} orderQueryDTO.kdtId - 
    *  @param {number} orderQueryDTO.headerBuyerId - 团长uid
    *  @param {string} orderQueryDTO.pageId - 页面ID
    *  @param {number} orderQueryDTO.verifyState - 自提状态
    *  @param {string} orderQueryDTO.sortType - 排序方式： asc：正序    desc：倒序
    *  @param {number} orderQueryDTO.endTime - 结束时间
    *  @param {number} orderQueryDTO.page - 当前页
    *  @param {string} orderQueryDTO.customerMobile - 买家手机号
    *  @param {boolean} orderQueryDTO.withItemInfo - 是否包含商品信息
    *  @return {Promise}
    */
  async queryOrdersSummary(orderQueryDTO) {
    return this.invoke(this.HEADER_ORDER_SERVICE, 'queryOrdersSummary', [orderQueryDTO]);
  }
}

module.exports = HeaderOrderService;
