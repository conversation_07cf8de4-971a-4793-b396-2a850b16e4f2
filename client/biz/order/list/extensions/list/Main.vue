<template>
  <div>
    <theme />

    <van-list
      v-model="loading"
      class="list"
      :finished="!activityTab.hasNext"
      :offset="300"
      @load="loadOrder"
    >
      <template v-for="(item, index) in activityTab.list">
        <div :key="index" class="list-item" :list-index="index">
          <!-- 通过框架注入的，需要消费的组件 -->
          <list-item-tag :list-item="item" />
          <list-item-header :list-index="index" :list-item="item" />
          <list-item-time-tips :list-index="index" :list-item="item" />
          <list-item-body :list-index="index" :list-item="item" />

          <!-- footer 需要emit doOrderFooterAction 方法，组件内无法获取 ctx，所以将footer 放到当前 widget -->
          <!-- 中台化方案组件内不能使用 vue $emit -->
          <div
            v-if="!hideFooter && actionBtns(item).length > 0 && !isDrugList && !checkIsVideoThirdComponent(item)"
            class="list-item-footer"
          >
            <div class="list-item-footer__extend">
              <i
                v-if="hiddenBtns(item).length"
                class="list-item-footer__extend-icon"
                @click="toggleShowPopupExtendBtn(item)"
              ></i>

              <div
                v-if="showPopupExtendBtn[item.id]"
                class="list-item-footer__extend-popup"
                :class="{
                  'list-item-footer__extend-popup_last':
                    index === activityTab.list.length - 1,
                }"
              >
                <div
                  class="list-item-footer__extend-btn"
                  v-for="btn in hiddenBtns(item).slice().reverse()"
                  :key="btn.type"
                  @click.prevent.stop="
                    onFooterBtnClick({ btn, listIndex: index, listItem: item })
                  "
                >{{ btn.text }}</div>
              </div>
            </div>

            <div class="list-item-footer__btns">
              <div
                class="list-item-footer__block"
                v-for="(btn, i) in displayBtns(item)"
                :key="btn.type"
              >
                <van-button
                  round
                  plain
                  size="small"
                  class="list-item-footer__btn"
                  :type="btn.styleType || 'default'"
                  :disabled="btn.disabled"
                  :icon="btn.icon"
                  @click.prevent.stop="
                    onFooterBtnClick({ btn, listIndex: index, listItem: item })
                  "
                >{{ btn.text }}</van-button>
                <div
                  v-if="((btn.type === 'buyAgain' || btn.type === 'directBuyAgain') && item.repurchaseCoupon && btnList[index][i].showCoupon)"
                  class="list-item-footer__tip"
                >
                  <van-icon
                    name="https://b.yzcdn.cn/public_files/e49f5b413529e624ab6a538642bf4eeb.png"
                    size="16"
                  />
                  <span
                    @click="onFooterTipClick({ btn, listIndex: index, listItem: item })"
                  >领取 {{ item.repurchaseCoupon.valueCopywriting }}{{ item.repurchaseCoupon.unitCopywriting }}复购券 ，下单立享优惠</span>
                  <van-icon
                    name="cross"
                    color="#fff"
                    size="12"
                    @click="closeRepurchaseCoupon({ index, i, item })"
                  />
                </div>
              </div>
            </div>
          </div>

          <van-notice-bar
            v-if="checkIsVideoThirdComponent(item)"
            class="wxvideo-notice-swipe"
            mode="link"
            @click="handleShowWxVideoGuide"
          >视频号订单，请前往视频号订单中心操作</van-notice-bar>

          <list-item-logistics
            :list-index="index"
            :list-item="item"
            @click.native.stop="
              onFooterBtnClick({
                btn: transportBtn(item),
                listIndex: index,
                listItem: item,
              })
            "
          />
        </div>

        <template v-if="!isXhsApp && !isKsApp">
          <welike-entry
            v-if="index === 0"
            :key="index + 0.5"
            class="welike-entry__override"
            sourcePage="order_list"
          />
        </template>
      </template>

      <no-more-tip
        :show-recommend="showRecommend"
        v-if="activityTab.list.length && !activityTab.hasNext"
      />

      <template v-if="showEmpty">
        <template v-if="!isXhsApp && !isKsApp">
          <welike-entry
            v-show="welikeEntryShow"
            class="welike-entry__override"
            @show="welikeEntryShow = true"
            sourcePage="order_list"
          />
        </template>
        <empty-tip
          :small="showRecommend || isTTApp"
          :transparent="isTTApp"
          :show-empty-tip="showEmpty && !welikeEntryShow"
        />
      </template>
    </van-list>

    <!-- 临时改动，只影响抖音小程序，后续会下掉 -->
    <!-- 抖音小程序IOS发布受限，店铺资质信息在抖音验收范围内，需要临时增加在webview订单列表页 -->
    <div v-if="isTTApp" class="shop-cert-info" :class="{ 'shop-cert-info__fixed': fixedTTCert }">
      <div class="shop-cert-info-title">
        <div class="shop-cert-info__left"></div>
        <div @click="handCertUrl" class="shop-cert-info__text">店铺信息</div>
        <div class="shop-cert-info__right"></div>
      </div>
      <div class="shop-cert-info-support">
        <img
          class="shop-cert-info__img"
          src="https://img01.yzcdn.cn/upload_files/2023/06/28/Fr9QT6IwqfL5uXfrAQ0IncD2UsoD.png"
          alt
        />
      </div>
    </div>

    <!-- 交易组件3.0订单引导弹窗,后面会下掉 -->
    <van-popup v-model="showWxVideoGuide" round position="bottom" @close="onWxVideoGuideClose">
      <div class="wxvideo-guide-pop">
        <div class="pop-header">查看/处理视频号订单</div>
        <van-image
          src="https://b.yzcdn.cn/public_files/b4bd57493510a13fe591b1c6e8bd65f0.png"
          width="318px"
          class="wxvideo-guide-img"
          alt
        />
        <div class="wxvideo-guide-btn" @click="onWxVideoGuideClose">我知道了</div>
      </div>
    </van-popup>
  </div>
</template>


<script>
import { api } from 'common/api';
import { List, Button, Toast, Popover, Icon, Popup, NoticeBar, Image } from 'vant';
import Theme from 'components/Theme';
import WelikeEntry from '@/components/WelikeEntry';
import get from 'utils/get';
import args from '@youzan/utils/url/args';
import { Logger } from 'common/log/logger';
import { isDrugList } from '../../common/url';
import { compareVersion } from '../../common/version';
import ZNB from '@youzan/znb';

export default {
  name: 'list',

  components: {
    [List.name]: List,
    [Theme.name]: Theme,
    [Button.name]: Button,
    [WelikeEntry.name]: WelikeEntry,
    [Popover.name]: Popover,
    [NoticeBar.name]: NoticeBar,
    [Popup.name]: Popup,
    [Image.name]: Image,
    [Icon.name]: Icon,
  },

  props: {
    ctx: Object,
    hideFooter: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    const { activityTab } = this.ctx.data;

    return {
      showPopupExtendBtn: {},
      listStatus: {
        loading: false,
      },
      showWxVideoGuide: false,
      activityTab,
      orderMark: get(_global, 'orderMark', ''),
      showRecommend: false,
      isDrugList: isDrugList(),
      btnList: [],
      welikeEntryShow: false,
    };
  },

  computed: {
    loading: {
      get() {
        return this.listStatus.loading;
      },
      set(val) {
        this.listStatus.loading = val;
      },
    },
    isTabAll() {
      return this.activityTab.type === 'all';
    },
    showEmpty() {
      return !this.activityTab.list.length && !this.loading;
    },
    isXhsApp() {
      return window._global.miniprogram.isXhsApp;
    },
    isTTApp() {
      return window._global.miniprogram.isTTApp;
    },
    fixedTTCert() {
      // 订单数 <= 1 个，资质需要常驻底部
      return this.activityTab.list.length <= 1;
    },
    isKsApp() {
      return window._global.miniprogram.isKsApp;
    },
  },

  created() {
    this.watchActivityTab();

    this.ctx.watch('showRecommend', (val) => {
      this.showRecommend = val;
    });
  },

  methods: {
    async handCertUrl() {
      if (!this.isTTApp) return '';
      const kdtId = get(_global, 'kdtId', 0);
      const buyerId = get(_global, 'buyer_id', 0);
      const mpVersion = args.get('mpVersion', window.location.href);
      let redirectUrl = `https://h5.youzan.com/wscassets/shopinfo?shopId=${kdtId}&no_btn=1`;
      // 兼容老版小程序
      if (buyerId > 0 || compareVersion(mpVersion, '1.1.9') < 1) {
        window.location.href = redirectUrl;
      } else {
        if (!window.$$sdkTT) {
          await ZNB.init({ kdtId });
        }
        // znb sdk抖音环境多版本存在问题,直接使用原生方法
        $$sdkTT.miniProgram.navigateTo({
          url: `/pages/shop/auth-phone/index?redirectUrl=${encodeURIComponent(redirectUrl)}`,
        });
      }
    },
    onFooterBtnClick(params) {
      if (
        (params?.btn?.type === 'buyAgain' || params?.btn?.type === 'directBuyAgain') &&
        params.listItem.repurchaseCoupon
      ) {
        return this.onFooterTipClick(params);
      }
      // 查看物流埋点
      if (params?.btn?.type == 'transport') this.clickTransportBtn();
      if (params?.btn?.type === 'fissionCoupon') {
        Logger.log({
          et: 'click', // 事件类型
          ei: 'click_divide_coupon', // 事件标识
          en: '点击领裂变券按钮', // 事件名称
          pt: 'ol', // 页面类型
          params: {}, // 事件参数
        });
      }
      const paidPromotionBtn = ['paidPromotion', 'liveQrCode', 'coupon'];
      if (paidPromotionBtn.indexOf(params?.btn?.type) > -1) {
        Logger.log({
          et: 'click', // 事件类型
          ei: 'click_marketing_paidpro', // 事件标识
          en: '支付有礼按钮点击', // 事件名称
          pt: 'ol', // 页面类型
          params: {
            type: params?.btn?.type,
            orderNo: params.listItem.orderNo,
          }, // 事件参数
        });
      }

      this.showPopupExtendBtn = {};

      if (this.isTTApp) {
        params.extraData = {
          ...params.extraData,
          isTTApp: this.isTTApp,
        };
      }

      this.ctx.event.emit('doOrderFooterAction', params);
    },

    async onFooterTipClick(params) {
      const repurchaseCouponInfo = params.listItem.repurchaseCoupon;
      try {
        const res = await api.ajax({
          url: '/wsctrade/order/detail/getVoucher.json',
          type: 'POST',
          contentType: 'application/json',
          errorMsg: '领取失败',
          data: {
            activityId: repurchaseCouponInfo.id,
            /** 业务名称 */
            bizName: 'order_list',
            /** 来源, 可以与业务名称一致 */
            source: 'order_list',
            /** 无约定，保证唯一即可, 每次请求不同 */
            // requestId: 'order_detail_fission_coupon',
          },
        });
        this.showPopupExtendBtn = {};
        params.extraData = {
          repurchaseCoupon: {
            ...res,
            valueCopywriting: repurchaseCouponInfo.valueCopywriting || '',
            unitCopywriting: repurchaseCouponInfo.unitCopywriting || '',
          },
        };
        if (this.isTTApp) {
          params.extraData.isTTApp = this.isTTApp;
        }
        this.ctx.event.emit('doOrderFooterAction', params);
      } catch (e) {
        this.showPopupExtendBtn = {};
        params.extraData = {
          repurchaseCoupon: e,
        };
        if (this.isTTApp) {
          params.extraData.isTTApp = this.isTTApp;
        }
        this.ctx.event.emit('doOrderFooterAction', params);
      }
    },

    handleShowWxVideoGuide() {
      this.showWxVideoGuide = true;
    },

    onWxVideoGuideClose() {
      this.showWxVideoGuide = false;
    },

    clickTransportBtn() {
      Logger.log({
        et: 'click',
        ei: 'click_transport',
        en: '点击查看物流',
        params: {
          component: 'list_index',
        },
      });
    },

    closeRepurchaseCoupon(params) {
      const { index, i, item } = params;
      Logger.log({
        et: 'click',
        ei: 'close_buyagain_coupon',
        en: '复购券浮层关闭',
        params: {
          orderNo: item.orderNo,
        },
      });
      this.btnList[index][i].showCoupon = false;
    },

    watchActivityTab() {
      this.ctx.watch('activityTab', (val = {}, oldVal = {}) => {
        this.activityTab = {
          ...val,
          list: val.list.filter((item) => {
            return item.orderItems.length > 0;
          }),
        };

        // 为每个按钮的展示状态做保留data
        this.btnList = this.activityTab.list.map((item) => this.displayBtns(item));

        // 切换 tab 滚动到顶部
        document.documentElement.scrollTop = 0;
        // 兼容安卓手机，安卓机 document.documentElement.scrollTop 无效
        document.body.scrollTop = 0;

        // 切换tab后发起一次请求
        if (val.list.length === 0 && val.hasNext === true) {
          this.loadOrder();
        }
      });
    },

    loadOrder() {
      // 首次初始化避免 van-list 和 watch activityTab 触发两次请求
      if (!this.activityTab.type) return;

      this.loading = true;

      const res = this.ctx.process.invoke('fetchMoreOrder');

      Promise.all(res)
        .then((res = []) => {
          const { loading = true } = res[0] || {};
          // 防止加载过程中 tab 被切走，导致loading 状态设置错误。需要在接口调用之后返回 loading状态
          // watch 有延迟，需要使用 settimeout 避免两次请求
          setTimeout(() => {
            this.loading = loading;
          }, 50);
        })
        .catch((error) => {
          Toast('服务器开小差了，请稍后再试');
          setTimeout(() => {
            this.loading = false;
          }, 50);
        });
    },

    actionBtns(listItem) {
      let buttonList = [];
      try {
        buttonList = JSON.parse(JSON.stringify(listItem.btnList));
      } catch (e) {
        console.error(e);
      }
      if (this.orderMark === 'weapp_guang') {
        buttonList = buttonList.filter((button) => ['cancelPayOnDelivery', 'courseDetail'].includes(button.type));
      }

      return buttonList;
    },

    displayBtns(listItem) {
      let btns = this.actionBtns(listItem);
      if (btns.length > 3) {
        btns = btns.slice(-3);
      }
      const paidPromotionBtn = ['paidPromotion', 'liveQrCode', 'coupon'];
      btns.forEach((item) => {
        if (paidPromotionBtn.indexOf(item.type) > -1) {
          Logger.log({
            et: 'view', // 事件类型
            ei: 'view_marketing_paidpro', // 事件标识
            en: '支付有礼按钮曝光', // 事件名称
            params: {
              type: item.type,
              orderNo: listItem.orderNo,
            },
          });
        }
        if (item.type === 'buyAgain' && listItem.repurchaseCoupon) {
          Logger.log({
            et: 'view', // 事件类型
            ei: 'view_buyagain_coupon', // 事件标识
            en: '复购券浮层曝光', // 事件名称
            params: {
              type: item.type,
              orderNo: listItem.orderNo,
            },
          });
        }
      });
      return btns;
    },

    hiddenBtns(listItem) {
      const btns = this.actionBtns(listItem);
      if (btns.length <= 3) return [];

      return btns.slice(0, btns.length - 3);
    },

    // 物流按钮
    transportBtn(listItem) {
      const btns = this.actionBtns(listItem);

      return btns.find((btn) => btn.type === 'transport');
    },

    toggleShowPopupExtendBtn(item) {
      const current = this.showPopupExtendBtn[item.id];
      this.$set(this.showPopupExtendBtn, item.id, !current);
    },

    checkIsVideoThirdComponent(item) {
      return item.orderItems[0]?.orderExtra?.isVideoThirdComponent;
    },
  },
};
</script>

<style lang="scss" scoped>
@import 'media.scss';

.wxvideo-notice-swipe {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  margin-bottom: -16px;
}

.welike-entry__override {
  width: auto;
  padding: 8px 12px;
  margin: 12px;
  border-radius: 8px;
}

.list {
  overflow: hidden;

  .van-loading {
    margin: 30px auto;
  }

  .list-item {
    margin: 12px;
    padding-bottom: 16px;
    border-radius: 8px;
    background: #fff;
  }
}

.list-item-footer {
  padding: 4px 12px 0;
  text-align: right;
  box-sizing: border-box;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;

  &__extend {
    position: relative;
    font-size: 0;
    margin-top: 3px;

    &-icon {
      display: inline-block;
      width: 24px;
      height: 24px;
      background: url('https://b.yzcdn.cn/public_files/f9ccfc06dfc71e20a4e327b475af7173.png') no-repeat center;
      background-size: 20px auto;
    }

    &-popup {
      width: 128px;
      position: absolute;
      left: 0;
      top: 30px;
      background: #fff;
      box-sizing: border-box;
      z-index: 999;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(50, 50, 51, 0.12);

      &::before {
        content: '';
        display: block;
        position: absolute;
        top: -14px;
        left: 8px;
        width: 0;
        height: 0;
        border: 8px solid transparent;
        border-bottom-color: #fff;
      }
    }

    &-popup_last {
      top: auto;
      bottom: 30px;

      &::before {
        top: auto;
        bottom: -14px;
        transform: rotate(180deg);
      }
    }

    &-btn {
      height: 43px;
      font-size: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      &:not(:first-child)::after {
        position: absolute;
        box-sizing: border-box;
        content: ' ';
        pointer-events: none;
        top: 0;
        left: 16px;
        right: 16px;
        border-bottom: 1px solid #ebedf0;
        transform: scaleY(0.5);
      }
    }
  }

  &__block {
    position: relative;
    &:not(:last-child) {
      margin-right: 12px;
    }

    @include small-screen {
      &:not(:last-child) {
        margin-right: 8px;
      }
    }

    &:first-child {
      .list-item-footer__tip {
        left: -30px;
        right: auto;
        transform: translate(0, 0);
      }
      .list-item-footer__tip::after {
        left: 70px;
        transform: translate(-6px, 0);
      }
    }

    &:last-child {
      .list-item-footer__tip {
        left: auto;
        right: 0;
        transform: translate(0, 0);
      }
      .list-item-footer__tip::after {
        left: auto;
        right: 40px;
        transform: translate(6px, 0);
      }
    }
  }

  &__btns {
    display: flex;
  }

  &__tip {
    position: absolute;
    height: 32px;
    top: -40px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    border-radius: 8px;
    padding: 0 8px;
    background-color: rgba($color: #000, $alpha: 0.7);
    color: #fff;
    font-size: 13px;
    white-space: nowrap;

    > span {
      margin: 0 8px 0 4px;
      flex: 1;
    }
  }

  &__tip::after {
    position: absolute;
    content: '';
    top: 32px;
    left: 50%;
    width: 0;
    height: 0;
    border: 6px solid transparent;
    border-top: 6px solid rgba($color: #000, $alpha: 0.7);
    transform: translate(-6px, 0);
  }

  &__btn {
    min-width: 80px;
    font-size: 14px;

    @include small-screen {
      min-width: 70px;
      width: 70px;
      padding: 0;
    }
  }
}

.wxvideo-guide-pop {
  display: flex;
  flex-direction: column;
  align-items: center;

  .pop-header {
    font-weight: 500;
    font-size: 16px;
    line-height: 44px;
    margin-bottom: 8px;
  }

  .wxvideo-guide-btn {
    width: 91.4%;
    margin-bottom: 5px;
    height: 40px;
    margin-top: 16px;
    background: linear-gradient(270deg, #ff6034 0%, #ee0a24 100%);
    border-radius: 20px;
    line-height: 40px;
    font-size: 14px;
    text-align: center;
    color: #ffffff;
  }
}

.shop-cert-info {
  margin-top: 40px;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);

  &__fixed {
    position: fixed;
    width: 100%;
    bottom: 0;
  }

  &-title {
    color: #666;
    font-size: 14px;
    line-height: 14px;
    display: flex;
    align-items: center;
    padding: 12px 12px 0;
    width: 100%;
    box-sizing: border-box;
  }

  &__left,
  &__right {
    background: #e0e0e0;
    height: 1px;
    flex: 1;
    transform: scaleY(0.5);
  }

  &__text {
    margin: 0 4px;
  }

  &-support {
    padding: 16px 0 12px;
    text-align: center;
  }

  &__img {
    width: 96px;
    height: 52px;
  }
}
</style>
