import BaseService from '../base/BaseService';
import { IFrequentlyPurchaseGoodsReqDTO, IFrequentlyPurchaseGoodsVO } from 'definitions/order/FrequentlyPurchaseGoods';

class FrequentlyPurchaseGoodsService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.ebiz.mall.trade.buyer.api.service.RecommendService';
  }

  /**
   * 获取推送链接
   * http://zanapi.qima-inc.com/site/service/view/1131156
   */
  getFrequentlyPurchaseGoods(params: IFrequentlyPurchaseGoodsReqDTO): Promise<IFrequentlyPurchaseGoodsVO> {
    return this.invoke('getFrequentlyPurchaseGoods', [params]);
  }
}

export default FrequentlyPurchaseGoodsService;
