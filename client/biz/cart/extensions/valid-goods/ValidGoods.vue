<template>
  <div v-if="hasGoods">
    <theme :template="themeTemplate" />

    <div
      class="goods-group"
      :class="{
        'cross-shop': IS_CROSS_SHOP_CART,
        'border-radio-hack': borderRadioHack,
      }"
      v-for="(goodsGroup, goodsGroupIndex) in shopGoods.goodsGroupList"
      :key="goodsGroupIndex"
      :goods-group="goodsGroup"
    >
      <!-- extension provide component -->
      <goods-group-ump
        v-if="goodsGroup.groupActivityInfo"
        :kdt-id="kdtId"
        :is-editing="isEditing"
        :goods-list="goodsGroup.goodsList"
        :checked-goods-list="checkedGoodsList"
        :activity-info="goodsGroup.groupActivityInfo"
        @change-item-checked="
          handleItemChecked({
            ...$event,
            isActivity: !!goodsGroup.groupActivityInfo,
            goodsGroupIndex,
          })
        "
        @refresh-cart-goods-list="refreshCartGoodsList"
      />

      <!-- 商品 -->
      <template v-for="(goods, goodsIndex) in goodsGroup.goodsList">
        <goods-item-ump
          :key="goodsIndex"
          :goods-item="goods"
        />
        <goods
          :key="goods.cartId"
          class="goods-group__goods-item"
          :is-invalid="false"
          :is-activity="!!goodsGroup.groupActivityInfo"
          :goods="goods"
          :is-choose="
            !!(
              (isEditing && editCheckedGoods[goods.cartId + '']) ||
              (!isEditing && goods.selectState)
            )
          "
          :is-can-choose="
            !(
              !isEditing &&
              isIOS &&
              goods.goodsType === 31 &&
              goods.bizExtension &&
              goods.bizExtension.cartBizMark &&
              goods.bizExtension.cartBizMark.isOnlineCourse === '1'
            )
          "
          :kdt-id="kdtId"
          :is-editing="isEditing"
          :show-estimated-price="showEstimatedPrice"
          @num-change="
            handleItemNumChange({
              num: $event,
              goods,
              isActivity: !!goodsGroup.groupActivityInfo,
              goodsIndex,
              goodsGroupIndex,
            })
          "
          @delete-goods="
            handleDeleteGoods({
              goods,
              isActivity: !!goodsGroup.groupActivityInfo,
              goodsIndex,
              goodsGroupIndex,
            })
          "
          @change-goods-sku="
            handleChangeGoodsSku({ goods, goodsIndex, goodsGroupIndex })
          "
          @change-item-checked="
            handleItemChecked({ ...$event, goodsIndex, goodsGroupIndex })
          "
          @hide-sale-countdown="
            hideSaleCountdown({ goodsIndex, goodsGroupIndex })
          "
          @open-combo-popup="
            openComboPopup({ goods })
          "
        />
      </template>
      <!-- 叠加活动赠品 -->
      <div v-if="goodsGroup.groupActivityInfoList && goodsGroup.groupActivityInfoList.length > 0">
          <present
            v-for="(groupActivityInfo, index) in goodsGroup.groupActivityInfoList"
            :key="index"
            :kdt-id="kdtId"
            :activity-id="groupActivityInfo.activityId"
            :group-activity-info="groupActivityInfo || {}"
            :present-list="presentMap[groupActivityInfo.activityId]"
            :navigate-to-goods="navigateToGoods"
            @on-show="handleChangePresentPopup"
            @change-sku="handleChangePresentSku"
        />
      </div>
       <!-- 非叠加活动赠品 -->
      <present
        v-else
        :kdt-id="kdtId"
        :activity-id="goodsGroup.groupActivityInfo && goodsGroup.groupActivityInfo.activityId"
        :group-activity-info="goodsGroup.groupActivityInfo || {}"
        :present-list="presentMap[goodsGroup.groupActivityInfo && goodsGroup.groupActivityInfo.activityId]"
        :navigate-to-goods="navigateToGoods"
        @on-show="handleChangePresentPopup"
        @change-sku="handleChangePresentSku"
      />
    </div>
    <!-- 挑选赠品弹窗 -->
    <present-popup
      :goods-list="presentInfo.goodsList"
      :show="presentInfo.show"
      :activity-id="presentInfo.activityId"
      :pick-goods-list="presentInfo.pickGoodsList"
      :selectable-present-num="presentInfo.selectablePresentNum"
      :kdt-id="kdtId"
      :navigate-to-goods="navigateToGoods"
      @on-close="handleChangePresentPopup"
      @on-confirm="handlePickPresent"
      @change-sku="handleChangePresentSku"
    />
    <!-- 组合商品弹框 -->
    <combo-detail-popup ref="combo-detail-popup" />
  </div>
</template>

<script>
import Vue from 'vue';
import get from 'utils/get';
import { Dialog, Toast } from 'vant';
import Goods from './components/goods';
import ComboDetailPopup from './components/goods/ComboDetailPopup.vue';
import Present from './components/present';
import PresentPopup from 'components/present-popup';
import {
  setGoodsNum,
  reselectGoods,
  batchDeleteGoods,
  updateSelectGoods,
  batchUpdateSelectGoods,
} from '@biz/cart/api';
import {
  isGoodsCheckboxEnable,
  whereCurrentGoodsInShop,
  isEduIosOnlineGoods,
} from '@biz/cart/utils';
import { deleteGoods, updateSelectAllGoods } from '@biz/cart/service';
import { IS_CROSS_SHOP_CART, IS_IOS_WEAPP } from '@biz/cart/constant';
import Theme from 'components/Theme';
import cloneDeep from '@youzan/utils/object/clone-deep';
import cookie from '@youzan/utils/browser/cookie';
import { queryAbTest } from '@biz/cart/common/ab-test';
import args from 'utils/browser/args';
import { navigateToGoods } from './utils';
import { Logger } from 'common/log/logger';
import { NumberLoggerUtils, SelectedLoggerUtils, sendGoodsNumChangeLog } from './loggerUtils';

// 赠品数据缓存key
const presentCookieKey = 'pick_present_activity_id_map';

export default {
  components: {
    Goods,
    [PresentPopup.name]: PresentPopup,
    [Theme.name]: Theme,
    [Present.name]: Present,
    ComboDetailPopup
  },

  props: {
    ctx: Object,
  },

  data() {
    const shopGoods = cloneDeep(this.ctx.data.shopCart);
    const hasGoods = (shopGoods.goodsGroupList || []).some(
      (goodsGroup) => !!goodsGroup.goodsList.length
    );

    // safari浏览器 圆角 解决方案
    // https://gist.github.com/ayamflow/b602ab436ac9f05660d9c15190f4fd7b
    const ua = window.navigator.userAgent.toLowerCase();
    const borderRadioHack = /iphone/gi.test(ua);

    /* if (hasPromition) {
      queryAbTest('cart-promotion-text').then((result) => {
        if (!result || !result.isValid) return;

        // console.log('queryAbTest', result);
        // 保存数据
        this.abTestInfo = result;

        this.abTestHasPromotion = !!result.configurations?.hasPromotion;

        Logger.log({
          et: 'view', // 事件类型
          ei: 'component_view', // 事件标识
          en: '购物车活动文案', // 事件名称
          params: {
            component: 'cartPromotionText',
            abTraceId: result.abTraceId,
          },
        });
      });
    } */

    return {
      shopGoods,
      hasGoods,
      kdtId: shopGoods.kdtId,
      // 编辑模式启用
      currentEditKdtId: 0,
      // 编辑模式选中商品列表 { cartId: selectState }
      editCheckedGoods: {},
      IS_CROSS_SHOP_CART,
      borderRadioHack,
      checkedGoodsList: [],
      // 赠品信息
      presentInfo: {
        // 可选赠品列表
        goodsList: [],
        // 是否展示赠品挑选弹框
        show: false,
        // 活动id
        activityId: 0,
        // 挑选的赠品商品列表
        pickGoodsList: [],
        // 可选数量
        selectablePresentNum: 0,
      },
      // 缓存挑选赠信息
      pickPresentCookieData: {},
      selectablePresentNumMap: {},
      selectedPresentListMap: {},
      // abTest实验下线 2021-05-11留此作为参考目的
      // abTestHasPromotion: false,
      // 预估到手价abTest配置参数
      abTestConfig: {},
      isShowEstimatedPrice: true, // 店铺配置显示预估价
      onlineCourseUnselectFlag: false,
      isIOS: IS_IOS_WEAPP,
      navigateToGoods,
    };
  },

  created() {
    this.numLogger = new NumberLoggerUtils();
    this.selectedLogger = new SelectedLoggerUtils();
    this.onlineCourseUnselectFlag = false;
    this.unwatchShopCart = this.ctx.watch('shopCart', (newVal) => {
      this.reloadShopCart(newVal);
    });

    this.unwatchEditMode = this.ctx.watch('editMode', (newVal) => {
      if (newVal === 'submit') {
        this.currentEditKdtId = 0;
      } else if (newVal === 'edit') {
        this.currentEditKdtId = this.shopGoods.kdtId;
      }

      // 重置编辑模式选中状态
      this.editCheckedGoods = {};

      // 编辑模式改变，重新计算checkedGoodsList
      this.computeCheckedGoods();
      // 由于是否处于编辑模式下 checkgoodList是维护两份 所以需要手动更新下
      // app开店ios端出现无法排查到结论的bug，是由于页面初始化时直接请求了一次list接口kdtId异常导致，因此加confirmLoaded变量去掉这次请求
      // https://jira.qima-inc.com/browse/ONLINE-426231
      if (newVal === 'submit' && this.confirmLoaded) {
        this.ctx.event.emit('updateCartGoodsList');
      }
    });

    this.ctx.event.listen('skuChanged', (e) => {
      this.handleChangeGoodsSkuCallback(e);
    });

    this.ctx.event.listen('presentSkuChanged', (e) => {
      this.handleChangePresentSkuCallback(e);
    });

    this.ctx.event.listen('toggleCheckedAll', () => {
      const shopGoods = this.ctx.data.shopCart;
      if (this.ctx.data.editMode === 'edit') {
        // 如果已全选，则取消选中
        if (this.ctx.data.isCheckedAll) {
          this.editCheckedGoods = {};
        } else {
          // 如果未全选，则全部选中
          (shopGoods.goodsGroupList || []).forEach((goodsGroup) => {
            (goodsGroup.goodsList || []).forEach((goods) => {
              Vue.set(this.editCheckedGoods, goods.cartId + '', 1);
            });
          });
        }
      } else {
        this.onlineCourseUnselectFlag = false;
        // 结算模式: 批量选中或取消选中
        const params = {
          rangeType: 'shop',
          type: this.ctx.data.isCheckedAll ? 'remove' : 'add',
        };
        this.handleBatchUpdateSelectGoods(params);
      }
    });

    // 删除商品
    this.ctx.event.listen('deleteCartItems', () => {
      // 批量删除商品
      const toDeleteGoodsList = this.ctx.data.checkedGoodsList;
      let message = '';
      if (toDeleteGoodsList.length === 1) {
        const isCourse = get(toDeleteGoodsList, '[0].goodsType') === 31;
        message = `确定删除该${isCourse ? '课程' : '商品'}吗？`;
      } else {
        const isAllCourse = toDeleteGoodsList.every((item) => item.goodsType === 31);
        message = `确定删除所选店铺的${toDeleteGoodsList.length}个${isAllCourse ? '课程' : '商品'}？`;
      }
      return Dialog.confirm({
        message,
      })
        .then((resp) => {
          if (resp === 'confirm') {
            this.handleBatchDeleteGoods({ goodsList: toDeleteGoodsList });
          }
        })
        .catch(() => {
          Dialog.close();
        });
    });

    this.ctx.event.listen('cartGoodsListDidUpdate', ({scene} = {}) => {
      if (this.showEstimatedPrice) {
        // 数量改变埋点
        if (scene === 'numChange') {
          const isShowEstimatedPrice = this.isHaveEstimatedPrice();
          this.numLogger.setShow(Number(isShowEstimatedPrice)).log().reset();
        }
        // 选中商品后埋点
        if (scene === 'selectedChange' && this.selectedLogger.getSkuId()) {
          const goods = this.getGoodsBySkuId(this.selectedLogger.getSkuId());
          if (typeof goods?.estimatedPrice === 'number') {
            this.selectedLogger.setNum(goods.num).log().reset();
          }
        }
      }
    });

    this.unwatchCheckedGoodsList = this.ctx.watch('checkedGoodsList', (val) => {
      this.checkedGoodsList = val;
    });

    this.getPresentListCookie();

    this.getAbtestConfig();

    this.getPresentMap();
  },
  mounted() {
    this.confirmLoaded = true;
  },

  computed: {
    isEditing() {
      return !!this.currentEditKdtId;
    },
    // 显示预估价： 店铺配置展示 && abTest展示
    showEstimatedPrice() {
      return this.isShowEstimatedPrice && !!this.abTestConfig.isShow;
    }
  },

  destroyed() {
    this.unwatchShopCart && this.unwatchShopCart();
    this.unwatchEditMode && this.unwatchEditMode();
    this.unwatchCheckedGoodsList && this.unwatchCheckedGoodsList();
  },

  methods: {
    getPresentMap() {
      const result = {};
      (this.shopGoods?.goodsGroupList || []).forEach((goodsGroup) => {
        const { groupActivityInfoList = [], groupActivityInfo } = goodsGroup || {};

        let groupList = [];

        if (groupActivityInfoList?.length > 0) {
          groupList = groupActivityInfoList;
        } else if (groupActivityInfoList?.length === 0 && groupActivityInfo) {
          groupList = [groupActivityInfo];
        }
        groupList.forEach((groupActivityInfo) => {
          const { activityId, activityType, selectablePresents = [], selectablePresentNum } = groupActivityInfo;
          // 赠品缓存信息
          const presentCookieInfo = this.pickPresentCookieData[activityId];
          const { skuIdList, selectablePresentNum: preSelectablePresentNum } = presentCookieInfo || {};
          let presentGoodsList = selectablePresents.filter((item) => item.isSelected);

          // 记录每个活动的可选赠品数量
          this.selectablePresentNumMap[activityId] = selectablePresentNum;
          this.selectedPresentListMap[activityId] = presentGoodsList;

          function getGoods(goods) {
            // 处理商品数据
            return {
              ...goods,
              activityId,
              activityType,
              presentId: goods.id,
              goodsSkuInfoList: goods.goodsSkuInfoList.map((item) => ({
                ...item,
                isSelected: goods.skuId === item.skuId,
              })),
            };
          }

          // 预选择的数量等于赠品可选数量
          if (skuIdList && preSelectablePresentNum === selectablePresentNum) {
            // 获取所有可选的sku
            const presentsMap = selectablePresents.reduce((map, goods) => {
              // 所有可选赠品键值对
              const { skuId, goodsSkuInfoList = [] } = goods;
              // 如果是多sku的赠品，需要遍历goodsSkuInfoList
              if (goodsSkuInfoList.length) {
                goodsSkuInfoList.forEach((item) => {
                  map[item.skuId] = {
                    ...goods,
                    sku: item.sku,
                    skuId: item.skuId,
                  };
                });
              } else {
                map[skuId] = goods;
              }

              return map;
            }, {});

            // 缓存挑选商品是否还都在活动内
            const inPresents = skuIdList.every((skuId) => {
              return presentsMap[skuId];
            });

            if (inPresents) {
              result.IS_SELECT_PRESENT = true;
              presentGoodsList = skuIdList.map((skuId) => presentsMap[skuId]);
            }
          }
          result[activityId] = presentGoodsList.map(getGoods);

          // 没有缓存时 & 可选数量等于赠品列表数量,直接返回所有赠品列表
          if (!skuIdList && selectablePresents.length === selectablePresentNum) {
            result[activityId] = selectablePresents.map(getGoods);
          }

        });
      });
      
      this.presentMap = result;

      const SELECTED_PRESENTS = Object.keys(this.presentMap).reduce(
        (total, item) => {
          if (item !== 'IS_SELECT_PRESENT') {
            total = total.concat(this.presentMap[item]);
          }
          return total;
        },
        []
      );

      const IS_SELECT_PRESENT = result['IS_SELECT_PRESENT'] ? 1 : 0;

      this.ctx.data.presentData = {
        IS_SELECT_PRESENT,
        SELECTED_PRESENTS: IS_SELECT_PRESENT ? SELECTED_PRESENTS: [],
      };

    },

    // 需要刷新购物车数据情况处理
    handleItemNumChange({
      num,
      goods,
      isActivity,
      goodsIndex,
      goodsGroupIndex,
    }) {
      sendGoodsNumChangeLog(goods.num, num, goods)

      this.numLogger.setGoodsId(goods.goodsId).setSkuId(goods.skuId).setPreNum(goods.num).setNextNum(num);
      // 1. 先更新本地data，显示操作结果
      this.updateGoodsList('local', {
        type: 'UPDATE_GOODS_NUM',
        cartId: '',
        goodsGroupIndex,
        goodsIndex,
        val: num,
      });
      const params = {...goods, num}
      if (goods.comboDetail) {
        params.combo = {
          comboType: goods.comboDetail.comboType,
          groupList: goods.comboDetail.groupList
        }
      }
      // 2. 请求接口
      setGoodsNum(params)
        .then(() => {
          // 3.1 更新成功，同步商品数量到ctx
          this.updateGoodsList('context', {
            type: 'UPDATE_GOODS_NUM',
            cartId: '',
            goodsGroupIndex,
            goodsIndex,
            val: num,
          });

          this.ctx.event.emit('updateCartGoodsList', {scene: 'numChange'});
          
          window.cartEventBus.$emit('cartNumUpdated');
        })
        .catch((err) => {
          Toast(err.msg || '操作失败，请重试');
          // 3.2 更新失败，把ctx的数据重新同步到本地data
          this.reloadShopCart();
        });
    },

    reloadShopCart(shopCart) {
      const shopGoods = cloneDeep(shopCart || this.ctx.data.shopCart);
      const hasGoods = (shopGoods.goodsGroupList || []).some(
        (goodsGroup) => !!goodsGroup.goodsList.length
      );

      const handleGoodsSku = (group) => {
        // 做个前置安全判断
        if (!group?.groupActivityInfo?.selectablePresents) {
          return group;
        }

        // 改写skuId，老赠品没有goodsSkuInfoList，所以用原本的skuId和sku信息兜底
        group.groupActivityInfo.selectablePresents = group.groupActivityInfo.selectablePresents.map((present) => {
          const selectedSku = present.goodsSkuInfoList?.find((i) => i.isSelected) ?? present;
          return {
            ...present,
            sku: selectedSku.sku,
            skuId: selectedSku.skuId,
          };
        });
        return group;
      };

      /**
       * 考虑到新老赠品的兼容逻辑，对于老赠品的skuId不进行修改，
       * 只在goodsSkuInfoList里面选中，所以需要前端再新版本中对skuId做一个覆盖操作
       * 前端的变更都根据skuId来处理
       */
      shopGoods.goodsGroupList = shopGoods.goodsGroupList?.map((group) => {

        if (group?.groupActivityInfoList?.length > 0) {
          group.groupActivityInfoList = group.groupActivityInfoList.map(item => handleGoodsSku(item));
        } else {
          group = handleGoodsSku(group);
        }
        
        return group;
      });

      this.shopGoods = shopGoods;
      this.hasGoods = hasGoods;
      this.isShowEstimatedPrice = shopCart.isShowEstimatedPrice;

      this.computeCheckedGoods();
      this.batchUnSelectEduOnlineGoods();
    },

    // 选中/取消选中单个商品
    handleItemChecked(payload) {
      const {
        rangeType,
        type,
        goods,
        goodsList = [],
        isActivity,
        goodsIndex,
        goodsGroupIndex,
      } = payload;
      // 编辑模式
      if (this.ctx.data.editMode === 'edit') {
        if (rangeType === 'batch') {
          // Vue.set本地调试无效，先改为editCheckedGoods赋值
          goodsList.forEach((goods) => {
            // Vue.set(
            //   this.editCheckedGoods,
            //   goods.cartId + '',
            //   type === 'add' ? 1 : 0
            // );
            this.editCheckedGoods = {
              ...this.editCheckedGoods,
              [goods.cartId + '']: type === 'add' ? 1 : 0
            }
          });
        } else {
          // Vue.set(
          //   this.editCheckedGoods,
          //   goods.cartId + '',
          //   type === 'add' ? 1 : 0
          // );
          this.editCheckedGoods = {
              ...this.editCheckedGoods,
              [goods.cartId + '']: type === 'add' ? 1 : 0
            }
        }

        // 编辑 选项改变后
        this.computeCheckedGoods();
        window.cartEventBus.$emit('cartNumUpdated');
      } else {
        if (rangeType === 'batch') {
          const filterGoodsList = goodsList.filter((goods) => !isEduIosOnlineGoods(goods));
          if (!filterGoodsList.length) {
            Toast(`购物车里面尚未选中商品，请重新返回活动页/商品页加购`);
            return;
          }
          // 批量操作
          const params = {
            kdtId: this.kdtId,
            goodsList: filterGoodsList,
            type,
            isActivity,
          };

          batchUpdateSelectGoods(params)
            .then(() => {
              goodsList.forEach((goods) => {
                const commitPayload = {
                  kdtId: this.kdtId,
                  goods,
                  type: payload.type,
                };

                const {
                  goodsGroupIndex = -1,
                  goodsIndex = -1,
                } = whereCurrentGoodsInShop(this.shopGoods, goods);

                if (goodsGroupIndex >= 0 && goodsIndex >= 0) {
                  this.updateGoodsList('context', {
                    type: 'UPDATE_GOODS_SELECT_STATE',
                    goodsGroupIndex,
                    goodsIndex,
                    val: type === 'add' ? 1 : 0,
                  });
                }
              });
              this.ctx.event.emit('updateCartGoodsList');
              window.cartEventBus.$emit('cartNumUpdated');
            })
            .catch((e) => {
              Toast(`${e && e.msg}, 请刷新或者重试`);
            });
        } else {
          const params = {
            kdtId: this.kdtId,
            goods,
            type,
            isActivity,
          };
          // 向后端发送勾选/取消勾选请求
          updateSelectGoods(params)
            .then(() => {
              // 更新当前选中商品列表
              this.updateGoodsList('context', {
                type: 'UPDATE_GOODS_SELECT_STATE',
                goodsGroupIndex,
                goodsIndex,
                val: type === 'add' ? 1 : 0,
              });              
              this.ctx.event.emit('updateCartGoodsList', { scene: 'selectedChange' });
              window.cartEventBus.$emit('cartNumUpdated');
            })
            .catch((e) => {
              Toast(`${e && e.msg}, 请刷新或者重试`);
            });
          if (type === 'add') {
            this.selectedLogger.setSkuId(goods.skuId).setGoodsId(goods.goodsId);
          }
        }
      }
    },

    handleDeleteGoods({ goods, isActivity, goodsIndex, goodsGroupIndex }) {
      const needConfirm = this.ctx.data.editMode === 'edit' ? true : false;

      deleteGoods({ goods, isActivity, needConfirm }).then(() => {
        Logger.log({
          et: 'click', 
          ei: 'remove_from_cart', 
          en: '购物车移除商品', 
          pt: 'cart', 
          params: { 
            goods_id: goods.goodsId,
            goods_name: goods.title,
            sku_id: goods.skuId,
            sku_name: JSON.parse(goods.sku || '[]')
          } 
        });

        this.updateGoodsList('context', {
          type: 'UPDATE_GOODS_AFTER_DELETE',
          goodsGroupIndex,
          goodsIndex,
        });

        this.ctx.event.emit('updateCartGoodsList');
      });
    },

    handleBatchDeleteGoods({ goodsList }) {
      Toast.loading();
      batchDeleteGoods(goodsList)
        .then((resp) => {
          if (resp.code !== 0) {
            Toast(`${resp.msg}, 请刷新或者重试`);
            return;
          }

          // 获取购物车数据
          this.ctx.event.emit('updateCartGoodsList');

          Toast.clear();
          Dialog.close();
          window.cartEventBus.$emit('cartNumUpdated');
        })
        .catch((err) => {
          Toast('批量删除出错，请重试');
        });
    },

    handleChangeGoodsSku({ goods, goodsIndex, goodsGroupIndex }) {
      this.ctx.event.emit('changeSku', goods);
    },

    handleChangeGoodsSkuCallback({ goods }) {
      return reselectGoods(goods)
        .then(() => {
          this.ctx.event.emit('updateCartGoodsList');
        })
        .catch((e) => {
          Toast(`${e && e.msg}, 请刷新或者重试`);
        });
    },

    // 修改赠品sku的回调
    handleChangePresentSkuCallback({ activityId, goodsData, skuData }) {
      const presentCookieData = this.pickPresentCookieData;
      const { skuIdList = [], selectablePresentNum } = presentCookieData[activityId] || {};
      let pickPresentCookieData = {};

      // 有缓存，只替换正在修改的goodsSkuInfoList
      if (Object.keys(this.pickPresentCookieData).length && skuIdList.length) {
        pickPresentCookieData = {
          ...presentCookieData,
          [activityId]: {
            skuIdList: skuIdList.map((skuId) => {
              /**
               * 由于之前的格式只记录了一个skuId，而没有goodsId和skuId的对应关系
               * 所以这里只替换原本的skuId为新选择的skuId
               */
              if (skuId === goodsData.skuId) {
                return skuData.id;
              }
              return skuId;
            }),
            selectablePresentNum,
          },
        };
      } else {
        // 无缓存，设置当前修改的sku
        const presentList = this.presentMap[activityId] || [];

        const selectedSkuList = presentList.map((item) => {
          if (item.skuId === goodsData.skuId) {
            return skuData.id;
          }
          return item.skuId;
        });

        pickPresentCookieData = {
          ...presentCookieData,
          [activityId]: {
            skuIdList: selectedSkuList,
            selectablePresentNum: this.selectablePresentNumMap[activityId],
          },
        };
      }

      // 保存到cookie当中，在presentMap的computed计算中会用到
      this.pickPresentCookieData = pickPresentCookieData;
      cookie(presentCookieKey, {
        value: JSON.stringify(pickPresentCookieData),
        expires: 1,
      });

      // 修改可选赠品列表的skuId & sku
      if (this.presentInfo.goodsList) {
        this.presentInfo.goodsList = this.presentInfo.goodsList.map((item) => {
          if (item.id === goodsData.id) {
            item.sku = skuData.sku;
            item.skuId = skuData.id;
          }
          return item;
        });
      }

      // 触发重新获取赠品对象
      this.getPresentMap();
    },

    handleBatchUpdateSelectGoods(payload, goodsGroupIndex) {
      const { rangeType, goods, type, isActivity, goodsIndex } = payload;

      const params = {
        kdtId: this.kdtId,
        type,
      };

      // 选中或取消选中店铺所有商品
      if (rangeType === 'shop') {
        updateSelectAllGoods(params)
          .then(() => {
            //
            this.ctx.event.emit('updateCartGoodsList');
          })
          .catch((e) => {
            Toast(`${e && e.msg}, 请刷新或者重试`);
          });
      }
    },

    hideSaleCountdown({ goodsIndex, goodsGroupIndex }) {
      this.ctx.event.emit('updateCartGoodsList');
    },

    // 计算已勾选商品列表
    computeCheckedGoods(isModifyPresent = false) {
      const checkedGoodsList = [];
      let isCheckedAll = true;
      const { shopCart, editMode } = this.ctx.data;

      if (editMode === 'edit' && !isModifyPresent) {
        // 在编辑模式且不是修改选择赠品
        (shopCart.goodsGroupList || []).forEach((goodsGroup) => {
          (goodsGroup.goodsList || []).forEach((goods) => {
            // 编辑状态不判断是否可选
            if (this.editCheckedGoods[goods.cartId + '']) {
              checkedGoodsList.push(goods);
            } else {
              isCheckedAll = false;
            }
          });
        });
      } else {
        (shopCart.goodsGroupList || []).forEach((goodsGroup) => {
          const filterGoodsList = (goodsGroup.goodsList || []).filter((goods) => !isEduIosOnlineGoods(goods));
          if (!filterGoodsList.length) {
            return;
          }
          filterGoodsList.forEach((goods) => {
            if (goods.selectState) {
              // 不可选不算在全选范围内
              if (isGoodsCheckboxEnable(goods)) {
                checkedGoodsList.push(goods);
              }
            } else {
              isCheckedAll = false;
            }
          });
        });
      }

      this.getPresentMap();
      this.ctx.data.checkedGoodsList = checkedGoodsList;
      this.ctx.data.isCheckedAll = checkedGoodsList.length ? isCheckedAll : false;
    },

    updateGoodsList(
      optScope,
      { type, cartId, goodsGroupIndex, goodsIndex, val }
    ) {
      if (optScope === 'local') {
        if (type === 'UPDATE_GOODS_NUM') {
          if (goodsGroupIndex >= 0 && goodsIndex >= 0) {
            const shopCart = this.shopGoods;

            shopCart.goodsGroupList[goodsGroupIndex].goodsList[
              goodsIndex
            ].num = val;
            this.shopGoods = shopCart;
          }
        }

        return;
      } else if (optScope === 'context') {
        if (type === 'UPDATE_GOODS_NUM') {
          if (goodsGroupIndex >= 0 && goodsIndex >= 0) {
            const shopCart = this.ctx.data.shopCart;
            // TODO 空值判断
            shopCart.goodsGroupList[goodsGroupIndex].goodsList[
              goodsIndex
            ].num = val;
            this.ctx.data.shopCart = shopCart;
          }
        } else if (type === 'UPDATE_GOODS_SELECT_STATE') {
          if (goodsGroupIndex >= 0 && goodsIndex >= 0) {
            const shopCart = this.ctx.data.shopCart;
            // TODO 空值判断
            shopCart.goodsGroupList[goodsGroupIndex].goodsList[
              goodsIndex
            ].selectState = val;
            this.ctx.data.shopCart = shopCart;
          }
        } else if (type === 'UPDATE_GOODS_AFTER_DELETE') {
          if (goodsGroupIndex >= 0 && goodsIndex >= 0) {
            const shopCart = this.ctx.data.shopCart;

            if (shopCart.goodsGroupList[goodsGroupIndex].goodsList.length) {
              shopCart.goodsGroupList[goodsGroupIndex].goodsList.splice(
                goodsIndex,
                1
              );
              this.ctx.data.shopCart = shopCart;
            }
          }
        } else if (type === 'UPDATE_GOODS_START_SOLD') {
          if (goodsGroupIndex >= 0 && goodsIndex >= 0) {
            const shopCart = this.ctx.data.shopCart;

            if (shopCart.goodsGroupList[goodsGroupIndex].goodsList.length) {
              shopCart.goodsGroupList[goodsGroupIndex].goodsList[
                goodsIndex
              ].startSoldTime = 0;
              this.ctx.data.shopCart = shopCart;
            }
          }
        }
      }
    },

    themeTemplate(mainColor) {
      if (mainColor) {
        return `
          .goods-price,
          .van-sku__goods-price,
          .van-sku-row__item--active {
            color: ${mainColor} !important;
          }
        `;
      }
    },

    // 换购商品加购后刷新列表
    refreshCartGoodsList() {
      this.ctx.event.emit('updateCartGoodsList');
    },

    // 修改是否展示挑选赠品弹窗
    handleChangePresentPopup({ goodsList, ...rest }) {
      let presentInfo = {};

      if (goodsList) {
        presentInfo.goodsList = goodsList;
      }

      presentInfo = {
        ...this.presentInfo,
        ...presentInfo,
        ...rest,
      };
      this.presentInfo = presentInfo;
    },

    // 处理挑选的赠送商品
    handlePickPresent({ goodsList, activityId, selectablePresentNum }) {
      const pickPresentCookieData = {
        ...this.pickPresentCookieData,
        [activityId]: {
          skuIdList: goodsList.map(({ skuId }) => skuId),
          selectablePresentNum,
        },
      };
      this.pickPresentCookieData = pickPresentCookieData;
      cookie(presentCookieKey, {
        value: JSON.stringify(pickPresentCookieData),
        expires: 1,
      });
      // 修改选中，重新计算checkedGoodsList
      this.computeCheckedGoods(true);
    },

    handleChangePresentSku(data) {
      this.ctx.event.emit('changePresentSku', data);
    },

    // 获取缓存中赠品列表
    getPresentListCookie() {
      let pickPresentCookieData = cookie(presentCookieKey) || '{}';
      let presentIds = [];
      const activityId = args.get('activityId');
      const selectablePresentNum = Number(args.get('selectablePresentNum'));
      // 是否在活动页自选
      const isUserSelectPresent = Number(args.get('isUserSelectPresent')) || 0;

      // 活动页面传入
      try {
        presentIds = JSON.parse(args.get('presentIds'));
      } catch(e) {
        presentIds = [];
      }

      // 获取缓存挑选赠品
      try {
        pickPresentCookieData = JSON.parse(pickPresentCookieData);
      } catch (e) {
        pickPresentCookieData = {};
      }

      if (activityId && presentIds.length && isUserSelectPresent === 1) {
        // 活动页传入赠品处理
        pickPresentCookieData[activityId] = {
          skuIdList: presentIds,
          selectablePresentNum,
        };
        cookie(presentCookieKey, {
          value: JSON.stringify(pickPresentCookieData),
          expires: 1,
        });
      }

      this.pickPresentCookieData = pickPresentCookieData;
    },
    getAbtestConfig() {
      queryAbTest('cart_expect_price').then(result => {
        if (!result || !result.isValid) return;
        this.abTestConfig = result.configurations;
        if (this.isShowEstimatedPrice && this.isHaveEstimatedPrice()) {
          Logger.log({
            et: 'view',
            ei: 'component_view',
            en: '购物车预估到手价曝光',
            params: {
              abTraceId: result.abTraceId,
              show: Number(result.configurations.isShow),
              component: 'show_estimated_price',
            },
          });
        }
      });
    },
    // 是否存在预估到手价
    isHaveEstimatedPrice() {
      return this.shopGoods.goodsGroupList.some(goodsGroup => {
        return goodsGroup.goodsList.some(goods => {
          if (typeof goods.estimatedPrice === 'number') {
            return true;
          }
        })
      });
    },
    getGoodsBySkuId(id) {
      let itemGoods = null;
      this.shopGoods.goodsGroupList.some(goodsGroup => {
        goodsGroup.goodsList.some(goods => {
          if (goods.skuId === id) {
            itemGoods = goods;
            return true;
          }
        })
      });
      return itemGoods;
    },

    // 批量把教育线上课商品取消勾选
    batchUnSelectEduOnlineGoods() {
      if (this.onlineCourseUnselectFlag) {
        return;
      }
      const needUnselectGoods = [];
      this.shopGoods.goodsGroupList?.forEach((goods) => {
        goods.goodsList.forEach((item) => {
          if (isEduIosOnlineGoods(item)) {
            needUnselectGoods.push(item);
          }
        });
      });
      // 批量操作
      const params = {
        kdtId: this.kdtId,
        goodsList: needUnselectGoods,
      };

      if (needUnselectGoods.length) {
        batchUpdateSelectGoods(params).then(() => {
          this.onlineCourseUnselectFlag = true;
          this.refreshCartGoodsList();
        });
      }
    },

    // 打开组合商品详情弹框
    openComboPopup({ goods }) {
      const { comboDetail } = goods
      this.$refs['combo-detail-popup'].openPopup(comboDetail);
    },
  },

  watch: {
    editCheckedGoods: function () {
      // 修改选中，重新计算checkedGoodsList
      this.computeCheckedGoods();
    },
  },
};
</script>

<style scoped lang="scss">
.goods-group {
  position: relative;
  margin: 0 12px 12px;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;

  &.border-radio-hack {
    // 解决 iphone border-radios + voerflow:hidden + css transform 导致圆角失效bug
    -webkit-mask-image: -webkit-radial-gradient(white, black);
  }

  &.cross-shop {
    margin: 0 12px;
    border-radius: 0;
  }

  &__goods-item {
    overflow: hidden;
    -webkit-mask-image: -webkit-radial-gradient(white, black);
  }
}
</style>
