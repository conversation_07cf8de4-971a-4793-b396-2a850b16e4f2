import BaseService from '../base/BaseService';

class DeliveryQueryService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.trade.dc.api.service.query.DeliveryQueryService';
  }

  /**
   *  查询微信小程序发货管理订单状态
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1612744
   *
   *  @param {Object} request -
   *  @param {string} request.orderNo - 订单号
   *  @param {string} request.outerTransactionNo - 外部支付流水号
   *  @param {number} request.kdtId - 店铺id
   *  @return {Promise}
   */
  async queryMpShipState(request: any) {
    return this.invoke('queryMpShipState', [request]);
  }
}

export = DeliveryQueryService;
