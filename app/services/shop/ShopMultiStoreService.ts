import BaseService from '../base/BaseService';

interface IDetectStoreIdReq {
  kdtId: JSNumber;
  storeId: JSNumber | null;
  sessionId: string;
  userId: number;
}

class ShopMultiStoreService extends BaseService {
  get SHOP_MULTI_STORE_SERVICE() {
    return 'com.youzan.multistore.center.api.serivce.store.UserStoreContextService';
  }

  /**
   * http://zanapi.qima-inc.com/site/service/view/287905
   * 获取多网点
   */
  async getMultiStore({ kdtId, storeId, sessionId, userId }: IDetectStoreIdReq) {
    const params = {
      kdtId,
      storeId,
      sessionId,
      userId
    };

    return await this.invoke(this.SHOP_MULTI_STORE_SERVICE, 'wapDetectStore', [params]);
  }
}

export = ShopMultiStoreService;
