import BaseController from '../base/BaseController';
import WeappQRCodeService from '../../services/base/WeappQRCodeService';
import SnapshotService from '../../services/poster/SnapshotService';
import crypto from 'crypto';
import { Context } from 'astroboy';

class PosterController extends BaseController {
  // 检查生成二维码所需要的参数
  async checkQueryUrl(ctx: Context) {
    const { url, page, scene } = ctx.query;
    const query = { page: '', scene: '', url: '' };
    if (ctx.isWeappNative) {
      this.validator
        .required(page, '未传递小程序页面')
        .required(scene, '未传递小程序页面参数');
      query.page = page;
      query.scene = decodeURIComponent(scene);
    } else {
      this.validator.required(url, '未传递页面链接');
      query.url = decodeURIComponent(url);
    }
    ctx.setState('query', query);
  }

  // 生成二维码
  async makeQrCode(ctx: Context) {
    // 如果存在缓存结果，则不执行生成二维码
    if (ctx.cachePoster) {
      return;
    }
    const { isWeappNative, qrcodeOpt = {}, kdtId } = ctx;
    const { query = {} } = ctx;
    let qrcode;
    let word = '二维码';
    if (isWeappNative) {
      word = '小程序码';
      // @ts-ignore
      const shopMetaInfo = ctx.getState('shopMetaInfo') || {};
      // @ts-ignore
      const rootKdtId = shopMetaInfo.rootKdtId || kdtId;
      const { page, scene } = query;
      const { imageBase64 } = await new WeappQRCodeService(ctx).getCodeUltra(
        rootKdtId,
        page,
        scene,
        {
          hyaLine: true,
        }
      );
      qrcode = 'data:image/png;base64,' + imageBase64;
    } else {
      const { url } = query;
      let realUrl = url;
      try {
        realUrl = await this.ctx.shortUrl.toShort(realUrl);
      } catch (err) {
      }
      qrcode = await ctx.qrcode.create(
        realUrl,
        {
          case: 1,
          level: 3,
          ...qrcodeOpt,
        },
        'base64'
      );
    }
    ctx.setState('qrcode', qrcode || '');
    ctx.setState('word', word);
    ctx.setState('isWeapp', !!isWeappNative);
  }

  // 生成缓存key，检查是否存在缓存
  async checkCacheInfo(ctx: Context) {
    const hash = crypto.createHash('sha256');
    const data = JSON.stringify(ctx.state);
    hash.update(data);
    ctx.cacheKey = `${ctx.cacheKeyPrefix}${hash.digest('hex')}`;
    // 尝试先获取缓存
    try {
      const result = await new SnapshotService(ctx).getSnapshot(ctx.cacheKey);
      if (result && result.img) {
        ctx.cachePoster = result.img;
      }
    } catch (err) {
      // 未获取到缓存
      ctx.cachePoster = null;
    }
    const { retry = 0 } = ctx.query;
    ctx.logExtra = JSON.parse(data);
    ctx.logExtra.retry = retry;
  }

  // 绘制海报并返回海报cdn地址
  async getPosterJson(ctx: Context) {
    // 如果存在缓存结果，直接返回
    if (ctx.cachePoster) {
      return ctx.json(0, 'ok', ctx.cachePoster);
    }
    const { posterOpt, kdtId = 0 } = ctx;
    const session = ctx.getLocalSession();
    const { buyer } = session || {}
    if (buyer) {
      const { avatar, nick_name} = buyer;
      let nickName = nick_name;
      if (nick_name && nick_name.length > 8) {
        nickName = nickName.slice(0,4)+'...'+ nickName.slice(-3);
      }
      if (nickName) {
        ctx.setState('nickName',nickName);
        ctx.setState('avatar',avatar);
      }
    }

    const rmbUsdConfig = await ctx.getRmbUsdShopConfig({ kdtId });
    const isRmbUsd = rmbUsdConfig.isRmbUsdShop;
    const { type, more = {} } = posterOpt;
    const html = await ctx.renderView(`poster/${type}.html`);
    let result = null;
    if(type === 'order-share') {
      delete more.width;
    }
    if(type === 'card-voucher') {
      delete more.height;
    }
    const params = {
      width: 375,
      html,
      alwaysCdn: 1,
      isNaShop: isRmbUsd ? 1 : 0,
      operatorId: ctx.buyerId || 0,
      redisKey: ctx.cacheKey,
      ...more,
      selector: type === 'order-share' || type === 'card-voucher' ? '#container' : null,
    };
    try {
      result = await new SnapshotService(ctx).postSnapshot(params);
    } catch (err) {
    }
    if (result && result.img) {
      return ctx.json(0, 'ok', result.img);
    }
    return ctx.fail(9999, '生成失败');
  }

  // 预览绘制效果，不会实际去绘制海报
  async getPosterView(ctx: Context) {
    const { posterOpt } = ctx;
    const { type } = posterOpt;
    return ctx.render(`poster/${type}.html`);
  }
}

export = PosterController;
