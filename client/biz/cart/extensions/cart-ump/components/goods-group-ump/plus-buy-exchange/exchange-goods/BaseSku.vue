<template>
  <van-sku
    v-model="showSku"
    :sku="skuData"
    :properties="properties"
    :goods="goods"
    :goods-id="goods.goodsId"
    :quota="quota"
    :show-add-cart-btn="false"
    :message-config="messageConfig"
    :initial-sku="initialSku"
    buy-text="确定"
    :get-container="getContainer"
    @sku-selected="(arg) => { this.$emit('sku-selected', arg) }"
    @sku-prop-selected="(arg) => { this.$emit('sku-prop-selected', arg) }"
    @buy-clicked="onConfirmSku"
  >
    <template v-if="$slots['sku-stepper']" slot="sku-stepper">
      <slot name="sku-stepper" />
    </template>

    <template slot="sku-header-price" slot-scope="props">
      <slot name="sku-header-price" v-bind="props" />
    </template>
  </van-sku>
</template>
<script>

import { Sku } from 'vant';
import upload from 'utils/image/upload';

export default {
  name: 'base-sku',

  components: {
    [Sku.name]: Sku,
  },

  model: {
    prop: 'show',
    event: 'change',
  },

  props: {
    show: Boolean,
    skuData: Object,
    goods: Object,
    quota: Number,
    initialSku: Object,
    properties: Array,
  },

  data() {
    return {};
  },

  computed: {
    showSku: {
      get() {
        return this.show;
      },

      set(val) {
        this.$emit('change', val);
      },
    },

    messageConfig() {
      let initialMessages = {};
      try {
        initialMessages = JSON.parse(this.goods.messages);
      } catch (error) {
        initialMessages = {};
      }

      return {
        uploadImg: this.uploadImg,
        uploadMaxSize: 15,
        initialMessages,
      };
    },
  },

  methods: {
    getContainer() {
      return document.querySelector('body');
    },
    onConfirmSku(skuData) {
      this.showSku = false;
      this.$emit('onConfirmSku', skuData);
    },

     // 上传图片，sku弹层需要
    uploadImg(file) {
      return upload(file)
        .then((resp) => resp.data.attachment_full_url);
    },
  },
};
</script>

<style scoped lang="scss">
/* 移除计步器上面 sku的下划线 */
::v-deep.van-sku-group-container {
  .van-sku-row:last-child {
    &::after {
      display: none;
    }
  }
}
</style>
