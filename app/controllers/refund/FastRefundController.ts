import BaseController from '../base/BaseController';
import FastRefundPayService from '../../services/refund/FastRefundPayService';
import args from '@youzan/utils/url/args';

const partnerReturnUrl = `https://cashier.youzan.com/wscassets/refund/pay-result`;

class FastRefundController extends BaseController {
  async payAdvanceFund() {
    const { kdtId, buyerId } = this.ctx;
    const { amount, refundId, refundDetailUrl } = this.ctx.getRequestData();
    try {
      const result = await new FastRefundPayService(this.ctx).payAdvanceFund({
        kdtId,
        buyerId,
        amount,
        refundId,
        partnerReturnUrl: args.add(partnerReturnUrl, {
          refundDetailUrl: encodeURIComponent(
            await this.ctx.shortUrl.toShort(refundDetailUrl)
          ),
        }),
      });
      this.ctx.json(0, 'ok', result);
    } catch (e) {
      const content = e.errorContent || {};
      this.ctx.json(content.code || 10086, e.msg || '系统开小差，请稍后再试');
    }
  }
}

export = FastRefundController;
