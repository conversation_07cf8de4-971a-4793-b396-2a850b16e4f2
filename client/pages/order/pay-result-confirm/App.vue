<template>
  <div class="pay-result-confirm-container">
    <img class="wait-img" src="https://img01.yzcdn.cn/upload_files/2023/10/17/Ft1w4i9ii03MJeR7Ij5i5D-vuh19.png" />
    <div class="wait-desc">等待支付结果</div>
    <div class="wait-button-group">
      <van-button @click="onConfirm" class="wait-button-item" type="info">已支付完成</van-button>
      <van-button @click="onCancel" class="wait-button-item" type="info" plain>取消支付</van-button>
    </div>
  </div>
</template>

<script>
import Button from 'vant/lib/button';
import queryString from '@youzan/utils/url/queryString';
import { api } from 'common/api';

const query = queryString.parse(window.location.search, {});

let checkCount = 0;
const delayTimes = [...new Array(4).fill(1000), ...new Array(4).fill(2000)];

export default {
  components: {
    'van-button': Button,
  },
  mounted() {
    this.doCheckPay();
  },
  methods: {
    doCheckPay() {
      this.checkPay()
        .then(() => {
          this.onConfirm();
        })
        .catch(() => {
          if (checkCount <= 7) {
            setTimeout(() => {
              this.doCheckPay();
            }, delayTimes[checkCount]);
            checkCount++;
          }
        });
    },
    checkPay() {
      const { phase, request_no: requestNo } = query;
      const requestData = { requestNo };
      if (phase) {
        requestData.phase = phase;
      }
      return new Promise((resolve, reject) => {
        api
          .ajax({
            url: '/wsctrade/order/payresult/checkPay.json',
            data: requestData,
          })
          .then(({ data }) => {
            if (data && data.allOrderPaid) {
              resolve();
            } else {
              reject();
            }
          })
          .catch(reject);
      });
    },
    onConfirm() {
      let url = `${window.location.origin}/wsctrade/order/payresult?request_no=${query.request_no}&userChecked=1`
      if (query.phase) {
        url += `&phase=${query.phase}`;
      }
      if (_global.queryAddKdtId) {
        url += `&kdt_id=${_global.queryAddKdtId}`;
      }
      this.redirectPage(url);
    },
    onCancel() {
      let url = `${window.location.origin}/wsctrade/order/list?type=all`
      if (_global.queryAddKdtId) {
        url += `&kdt_id=${_global.queryAddKdtId}`;
      }
      this.redirectPage(url);
    },
    redirectPage(url) {
      window.history.replaceState({}, '', url);
      window.location.reload();
    },
  },
};
</script>

<style>
.pay-result-confirm-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.wait-img {
  margin-top: 42px;
  width: 226px;
  height: 226px;
}
.wait-desc {
  margin: 12px 0 40px;
}
.wait-button-group {
  display: flex;
  flex-direction: column;
}
.wait-button-item {
  width: 203px;
  margin-bottom: 20px;
}
</style>
