<template>
  <!-- 换购商品弹框组件 -->
  <div class="exchange-modal">
    <van-popup
      v-model="show"
      class="exchange-modal__popup"
      position="bottom"
      get-container="body"
      @closed="closeExchangePop"
    >
      <!-- 标题 -->
      <div class="exchange-modal__header">
        <div class="header-title">
          换购商品
        </div>
        <van-icon class="close-btn" name="cross" @click="closeExchangePop" />
      </div>

      <div
        v-if="!fetchExchangeListIng && goodsList.length"
        class="exchange-modal__prompt"
        v-html="desc"
      />

      <!-- 换购商品列表 -->
      <div class="exchange-modal__body">
        <van-loading
          v-if="fetchExchangeListIng"
          class="exchange-modal__loading"
          type="spinner"
        />

        <exchange-error v-if="getListError" />

        <exchange-list
          v-else
          ref="exchangeList"
          :support-multi="supportMulti"
          :goods-list="goodsList"
          @changeTotalNum="changeTotalNum"
          @updateGoodsListData="updateGoodsListData"
        />
      </div>

      <!-- 底部导航 -->
      <div class="exchange-modal-footer">
        <div class="combined-num">
          <span>已选 {{ TotalNum }} 件</span>
        </div>
        <van-button
          :loading="btnLoading"
          loading-type="spinner"
          type="danger"
          class="confirm-btn theme-background-linear"
          @click="onAddToCart"
        >
          确定
        </van-button>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { Popup, RadioGroup, Radio, Button, Toast, Field, Loading, Icon } from 'vant';
import ExchangeList from './ExchangeList';
import ExchangeError from './ExchangeError';
import mapKeysToCamelCase from '@youzan/utils/string/mapKeysToCamelCase';
import { getGoodsPropertiesIds } from '@biz/cart/utils';
import { deleteBatch, batchAddGoods, findExchangeSkusJson } from '../api';

const { offlineId = 0 } = window._global || {};

export default {
  name: 'exchange-modal',

  components: {
    [Icon.name]: Icon,
    [Popup.name]: Popup,
    [Radio.name]: Radio,
    [Field.name]: Field,
    [Button.name]: Button,
    [Loading.name]: Loading,
    [RadioGroup.name]: RadioGroup,
    [ExchangeList.name]: ExchangeList,
    [ExchangeError.name]: ExchangeError,
  },

  props: {
    activityId: Number,
    activityType: Number,
    isShow: Boolean,
    kdtId: Number,
    activityDesc: String,
    plusBuyGoods: Array,
  },

  data() {
    return {
      show: this.isShow,
      supportMulti: false, // 是否支持多选
      goodsList: [],
      TotalNum: 0,
      btnLoading: false,
      fetchExchangeListIng: false,
    };
  },

  computed: {
    plusBuyGoodsCase() {
      return mapKeysToCamelCase(this.plusBuyGoods);
    },

    desc() {
      let desc = '';
      if (this.supportMulti) {
        desc = '可换购以下任意商品';
      } else {
        desc = `可换购<span class="theme-color">1件</span>商品`;
      }
      return `${this.activityDesc}${desc}`;
    },

    getListError() {
      return !this.fetchExchangeListIng && !this.goodsList.length;
    },
  },

  watch: {
    isShow: {
      immediate: true,
      handler(val) {
        this.supportMulti = false;
        this.fetchExchangeListIng = false;
        this.selectedGoods = [];
        this.goodsList = [];
        this.show = val;

        if (val) {
          this.getExchangeGoods();
        }
      },
    },
  },

  methods: {
    // 获取换购商品
    getExchangeGoods() {
      const { kdtId, activityId } = this;
      this.fetchExchangeListIng = true;
      findExchangeSkusJson({ kdtId, activityId, offlineId })
        .then((exchangeGoods = []) => {
          if (!exchangeGoods.length) {
            return;
          }

          this.goodsList = exchangeGoods.slice(0);
          this.supportMulti = exchangeGoods[0]
            ? exchangeGoods[0].supportMulti
            : false;
          // 获取到换购商品后，查询购物车中的换购商品
          this.searchCartGoods();
        })
        .catch((err) => {
          this.goodsList = [];
        })
        .finally(() => {
          this.fetchExchangeListIng = false;
        });
    },

    // 查看购物车中的换购商品
    searchCartGoods() {
      this.plusBuyGoodsCase.forEach((item) => {
        const {
          goodsId,
          skuData,
          skuId,
          payPrice,
          attachmentUrl,
          messages = '',
          properties = [],
        } = item;
        const goodsListIndex = this.goodsList.findIndex(
          (g) => g.goodsId === goodsId
        );

        if (goodsListIndex > -1) {
          const skuStr = skuData.map((sku) => sku.v).join(';');

          this.goodsList.splice(goodsListIndex, 1, {
            ...this.goodsList[goodsListIndex],
            selected: true,
            skuStr: skuStr || '',
            properties,
            skuData,
            skuId,
            messages,
            exchangePrice: payPrice,
            picture: attachmentUrl,
          });
        }
      });
    },

    // 换购商品添加购物车校验
    onAddToCart() {
      if (this.btnLoading) return;

      if (this.getListError) {
        this.closeExchangePop();
        return;
      }

      this.btnLoading = true;
      let refresh = true;
      const selectedGoods = this.$refs.exchangeList.getSelectedGoods();

      const shouldDeleteGoods = this.getShouldDeleteGoods(selectedGoods);
      const shouldAddGoods = this.getShouldAddGoods(selectedGoods);

      if (!shouldDeleteGoods.length && !shouldAddGoods.length) {
        refresh = false;
        this.btnLoading = false;
      }

      this.deleteCartGoods(shouldDeleteGoods)
        .then(async () => {
          await this.addCart(shouldAddGoods, refresh);
          this.btnLoading = false;
          window.cartEventBus.$emit('cartNumUpdated');
        })
        .catch(() => {
          this.btnLoading = false;
          Toast('添加购物车失败，请重试');
        });
    },

    // 从购物车中删除换购商品
    deleteCartGoods(goods) {
      if (!goods.length) return Promise.resolve();

      const parseGoods = this.parseDeleteGoods(goods);

      return deleteBatch(parseGoods).then((data) => {
        if (!data) {
          throw new Error('添加购物车失败，请重试');
        }
      });
    },

    // 换购商品加入购物车
    addCart(goodsItems, refresh = true) {
      if (!goodsItems.length) {
        this.$emit('exchage:close-pop');
        if (refresh) this.$emit('add-cart');
        return Promise.resolve();
      }

      const parseGoodsItems = this.parseAddGoods(goodsItems);

      return batchAddGoods(parseGoodsItems)
        .then(() => {
          Toast('已成功添加到购物车');
          this.$emit('exchage:close-pop');
          this.$emit('add-cart');
        })
        .catch((err) => {
          const { msg = '添加购物车失败，请重试' } = err || {};
          Toast(msg);
        });
    },

    parseDeleteGoods(goods) {
      const { kdtId, activityId } = this;

      return goods.map((item) => ({
        skuId: item.skuId,
        goodsId: item.goodsId,
        kdtId,
        activityId,
      }));
    },

    parseAddGoods(goods) {
      const { kdtId, activityId, activityType } = this;

      return goods.map((item) => ({
        skuId: item.skuId || item.thinSkus[0].id,
        goodsId: item.goodsId,
        payPrice: item.exchangePrice,
        messages: item.messages || '',
        kdtId,
        activityId,
        propertyIds: getGoodsPropertiesIds(item.properties),
        activityType: activityType.toString(),
        num: 1,
        storeId: offlineId,
      }));
    },

    // 获取应该加购商品
    getShouldAddGoods(selectedGoods) {
      const addGoods = [];

      selectedGoods.forEach((goods) => {
        const goodsInExistGoods = this.plusBuyGoodsCase.find(
          (item) => item.goodsId === goods.goodsId
        );

        if (
          !goodsInExistGoods ||
          !this.compareGoodsIsSame(goods, goodsInExistGoods)
        ) {
          addGoods.push(goods);
        }
      });
      return addGoods;
    },

    // 获取应该删除的商品
    getShouldDeleteGoods(selectedGoods) {
      const deleteGoods = [];
      this.plusBuyGoodsCase.forEach((goods) => {
        const goodsInSelectedGoods = selectedGoods.find(
          (item) => item.goodsId === goods.goodsId
        );

        if (
          !goodsInSelectedGoods ||
          !this.compareGoodsIsSame(goods, goodsInSelectedGoods)
        ) {
          deleteGoods.push(goods);
        }
      });

      return deleteGoods;
    },

    // 更新数据
    updateGoodsListData(currentGoods, options = {}) {
      const currentGoodsIndex = this.goodsList.findIndex(
        (goods) => goods.goodsId === currentGoods.goodsId
      );

      // 更新商品展示信息
      this.goodsList.splice(currentGoodsIndex, 1, {
        ...this.goodsList[currentGoodsIndex],
        ...options,
      });
    },

    // 对比两个换购商品是否一致
    compareGoodsIsSame(goods, newGoods) {
      const KEYS = ['goodsId', 'skuId'];
      let flag = true;

      KEYS.forEach((key) => {
        flag = flag && goods[key] === newGoods[key];
      });

      // 对比商品属性，如果没有属性，给默认值
      goods.properties = goods.properties || [];
      newGoods.properties = newGoods.properties || [];
      flag =
        flag &&
        JSON.stringify(goods.properties) ===
          JSON.stringify(newGoods.properties);

      // 对比商品留言
      flag =
        flag &&
        this.parseMessageToArrString(goods.messages) ===
          this.parseMessageToArrString(newGoods.messages);

      return flag;
    },

    // 把留言格式化成数组字符串
    parseMessageToArrString(messages) {
      let parseMessage = [];
      try {
        parseMessage = JSON.parse(messages);
      } catch (error) {
        parseMessage = [];
      }
      if (Object.prototype.toString.call(parseMessage) === '[object Object]') {
        parseMessage = Object.keys(parseMessage).map(
          (key) => parseMessage[key]
        );
      }

      return JSON.stringify(parseMessage);
    },

    changeTotalNum(num) {
      this.TotalNum = num;
    },

    // 关闭换购商品弹窗
    closeExchangePop() {
      this.$emit('exchage:close-pop');
    },
  },
};
</script>

<style lang="scss">
.exchange-modal {
  &__popup {
    border-radius: 12px 12px 0 0;
    background: #f6f7f9;
  }

  &__header {
    text-align: center;
    position: relative;
    background: #fff;

    .header-title {
      color: #000;
      padding: 11px 0;
      font-size: 16px;
      letter-spacing: 0.3px;
      text-align: center;
    }

    .close-btn {
      position: absolute;
      right: 16px;
      top: 12px;
      color: #dcdde0;
      font-size: 20px;
    }
  }

  &__prompt {
    padding: 12px;
    color: #323233;
    font-size: 14px;
  }

  &__body {
    height: 60vh;
    overflow: scroll;
    font-size: 12px;
    margin: 0 12px;
    position: relative;

    &-item:not(:last-child) {
      .good-item__info {
        border-bottom: 1px solid #f2f2f2;
      }
    }
  }

  &__loading {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &-footer {
    display: flex;
    align-items: center;
    width: 100%;
    height: 50px;
    background: #fff;
    padding: 0 12px;
    box-sizing: border-box;
    height: calc(50px + env(safe-area-inset-bottom));
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);

    .combined-num {
      flex: 1;
      font-size: 14px;
    }

    .confirm-btn {
      flex: 0 0 96px;
      height: 36px;
      border-radius: 18px;
      font-size: 14px;
      line-height: 36px;
      color: #fff;
      text-align: center;
    }
  }
}
</style>
