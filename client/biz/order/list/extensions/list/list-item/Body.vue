<template>
  <div class="list-item-body">
    <order-list-item
      v-for="(item, index) in listItem.orderItems"
      :key="item.orderNo"
      :order-index="index"
      :list-index="listIndex"
      :list-item="listItem"
    />
  </div>
</template>

<script>
import OrderListItem from './components/OrderListItem';

export default {
  name: 'list-item-body',

  components: {
    [OrderListItem.name]: OrderListItem,
  },

  props: {
    listIndex: {
      type: Number,
      default: 0,
    },
    listItem: Object,
  },

  mounted() {
    // console.log('listItem', this.listItem);
  },
};
</script>

<style lang="scss" scoped>
.list-item-body {
  background: #fff;
}
</style>
