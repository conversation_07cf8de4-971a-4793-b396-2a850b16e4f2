/* eslint-disable @typescript-eslint/no-explicit-any */
import BaseService from '../base/BaseService';
import {
  IOrderSnapshotParams,
  IOrderSnapshotResponse,
} from '../../../definitions/order/OrderSnapshot';

class OrderSnapshotService extends BaseService {
  get SERVICE_NAME(): string {
    return 'com.youzan.trade.snapshot.api.main.TradeItemSnapshotService';
  }

  /**
   * 确认收货
   * @param params
   */
  public orderSnapshot(
    params: IOrderSnapshotParams
  ): Promise<IOrderSnapshotResponse> {
    return this.invoke(this.SERVICE_NAME, 'queryOrderSnapshot', [params], {
      allowBigNumberInJSON: true,
    });
  }
}

export = OrderSnapshotService;
