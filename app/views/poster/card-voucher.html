<style>
#container {
  padding: 0 24px;
  width: 375px;
  box-sizing: border-box;
  line-height: 1;
  border-radius: 20px;
  overflow: hidden;
}

.dash-border {
  position: absolute;
  width: 327px;
  left: 0;
  bottom: 0;
  border-bottom: 1px dashed #e5e5e5;
  height: 1px;
}

#container header {
  position: relative;
  padding: 16px 0;
}

.card-voucher__title {
  color: #333333;
  font-size: 16px;
  font-weight: 500;
  line-height: 1;
}

.card-voucher__goods {
  position: relative;
  padding: 16px 0;
}

.card-voucher__goods-item {
  display: flex;
  flex-flow: row nowrap;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
}

.card-voucher__goods .card-voucher__img {
  width: 64px;
  height: 64px;
  margin-right: 8px;
  vertical-align: middle;
  border-radius: 4px;
}

.card-voucher__goods .card-voucher__goods-info {
  position: relative;
  width: 255px;
  height: 56px;
  vertical-align: middle;
}

.card-voucher__goods .card-voucher__goods-info-title {
  display: -webkit-box;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
  color: #323233;
  font-size: 14px;
  position: absolute;
  top: -14px;
  left: 0;
  line-height: 1.4em;
}

.card-voucher__goods .card-voucher__goods-info-validate {
  position: absolute;
  bottom: 0;
  left: 0;
  top: 34px;
  color: #969799;
  font-size: 14px;
  width: 100%;
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  align-items: center;
}

.card-voucher__goods .card-voucher__goods-info-validate > span:last-child {
  color: #323233;
}

.card-voucher__barcode {
  display: block;
  width: 315px;
  height: 72px;
  margin: 16px auto 24px;
  background-position: center;
  background-size: 100% 100%;
}

.card-voucher__qrcode {
  margin: 10px auto;
  width: 180px;
  height: 180px;
  background-size: 100% 100%;
  border: 1px solid #dcdee0;
}

.card-voucher__goods {
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  align-items: center;
}

.card-voucher__goods .card-voucher__goods-imgs {
  display: flex;
  flex-flow: row nowrap;
  justify-content: flex-start;
  align-items: center;
}

.card-voucher__goods .card-voucher__img {
  width: 60px;
  height: 60px;
  margin-right: 6px;
}

.card-voucher__goods .card-voucher__goods-num {
  color: #7d7e80;
  font-size: 14px;
}
.card-voucher_cardcode {
  margin: 12px 0;
  text-align: center;
}

.card-voucher__nums {
  position: relative;
  font-size: 14px;
  line-height: 14px;
  padding-top: 10px;
}

.card-voucher__nums .dash-border {
  top: 0;
  bottom: unset;
}
</style>

<article id="container">
  <header>
    <i class="card-voucher__icon"></i>
    <span class="card-voucher__title">电子卡券</span>
    <div class="dash-border"></div>
  </header>
  <div class="card-voucher__goods">
    <div class="card-voucher__goods-item">
      {% for item in items %}
        <img class="card-voucher__img" src="{{ item.imgUrl }}" alt="商品图片"  />
        <div class="card-voucher__goods-info">
          <p class="card-voucher__goods-info-title">{{ item.title }}</p>
          <p class="card-voucher__goods-info-validate">
            <span>{{ item.validity }}</span>
            <span>x{{ item.num }}</span>
          </p>
        </div>
      {% endfor %}
      <div class="dash-border"></div>
    </div>
  </div>
  <div class="card-voucher__barcode" style="background-image: url('{{ barcode }}')"></div>
  <div class="card-voucher__qrcode" style="background-image: url('{{ qrcode }}')"></div>
  <div class="card-voucher_cardcode">统一核销码： {{ code }} </div>
  <div class="card-voucher__nums">
    <div class="dash-border"></div>
    {% for item in cardnums %}
      <p>券码{{ loop.index }}：{{ item }}</p>
    {% endfor %}
  </div>
</article>
