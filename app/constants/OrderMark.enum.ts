/* stylelint-disable */

export const enum OrderMark {
  WX_APP = 'wx_apps',
  WX_SHOP = 'wx_shop',
  WX_WM = 'wx_wm',
  WAP_WM = 'wap_wm',
  SUPER_STORE = 'super_store',
  WX_SPOTLIGHT = 'weapp_spotlight',
  WX_MEIYE = 'wx_meiye',
  WX_APPS_MAIDAN = 'wx_apps_maidan',
  WX_APPS_DIANCAN = 'wx_apps_diancan',
  WEAPP_YOUZAN = 'weapp_youzan',
  RETAIL_FREE_BUY = 'retail_free_buy',
  WEAPP_OWL = 'weapp_owl',
  APP_SPOTLIGHT = 'app_spotlight',
  RETAIL_SCAN_BUY = 'retail_scan_buy',
  WEAPP_PLUGIN = 'weapp_plugin',
  WEAPP_GUANG = 'weapp_guang'
}

export const OrderMarkTextMap = {
  [OrderMark.WX_APP]: '微信小程序买家版',
  [OrderMark.WX_SHOP]: '微信小程序商家版',
  [OrderMark.WX_WM]: '微信小程序外卖',
  [OrderMark.WAP_WM]: '移动端外卖',
  [OrderMark.SUPER_STORE]: '超级门店',
  [OrderMark.WX_SPOTLIGHT]: '新微信小程序买家版',
  [OrderMark.WX_MEIYE]: '美业小程序',
  [OrderMark.WX_APPS_MAIDAN]: '小程序餐饮买单',
  [OrderMark.WX_APPS_DIANCAN]: '小程序堂食',
  [OrderMark.WEAPP_YOUZAN]: '有赞小程序',
  [OrderMark.RETAIL_FREE_BUY]: '零售自由购',
  [OrderMark.WEAPP_OWL]: '知识付费小程序',
  [OrderMark.APP_SPOTLIGHT]: '有赞精选app',
  [OrderMark.RETAIL_SCAN_BUY]: '零售扫码购',
  [OrderMark.WEAPP_PLUGIN]: '小程序插件',
  [OrderMark.WEAPP_GUANG]: '爱逛街小程序'
};
