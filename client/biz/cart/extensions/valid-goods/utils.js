import get from 'utils/get';
import ZNB from '@youzan/znb';
import { action } from '@youzan/zan-jsbridge';
import { jumpLink, isTTApp, isXhsApp } from '@shared/common/helper';
import { GOODS_LINK, IS_APP_CART, IS_OPEN_NEW } from '@biz/cart/constant';

const isQQApp = get(window, '_global.miniprogram.isQQApp', false);
const isAlipayApp = get(window, '_global.miniprogram.isAlipayApp', false);

export function navigateToGoods(goods) {
  // 点击商品图片时所做的操作
  const goodsLink = `${GOODS_LINK}?alias=${goods.alias}`;
  // 头条小程序内使用jumpLink，内有特殊逻辑
  if (isTTApp) {
    jumpLink(goodsLink);
  }

  if (isAlipayApp || isQQApp || isXhsApp) {
    ZNB.navigate({
      aliappUrl: `/packages/goods/detail/index?alias=${goods.alias}`,
      qqUrl: `/packages/goods/detail/index?alias=${goods.alias}`,
      xhsUrl: `/packages/goods/detail/index?alias=${goods.alias}`,
    });
    return;
  }

  if (!IS_APP_CART) {
    const options = {
      url: goodsLink,
    };
    // 如果页面 url 上有 __openNew__ 参数，说明需要用新页面打开链接
    options.fallback = IS_OPEN_NEW ? 'open' : 'redirect';
    ZNB.navigate(options);
    return;
  }

  action.gotoWebview({
    url: goodsLink,
    page: 'web',
  });
}
