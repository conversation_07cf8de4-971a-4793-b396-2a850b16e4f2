interface IExtraWechatSyncShoppingListOnly {
  WECHAT_SYNC_SHOPPING_LIST: number;
  /**
   * 处方药相关
   */
  rxNo?: number;
  PRIOR_USE_COMPLETED: number;
}

interface FaceToFaceCourseInfo {
  goodsId: number;
  alias: string;
  skuId: number;
  goodsTitle: string;
  /**
   * 教育商品类型
   */
  owlType: number;
  courseType: number;
  courseSellType: number;

  price: number;

  /**
   * 价格信息
   */
  extPriceDTO: {
    originPrice: number;
    currentPrice: number;
    originTotalAmount: number;
    currentTotalAmount: number;
  };

  /**
   * 规格信息
   */
  extItemSkuDTO: object;
}

interface GiveawayInfo {
  productAlias: string; // 商品alias
  skuId: number; // skuid
  giveawayType: number; // 赠品类型 1 资产有效期 2 资产课时
  number: number; // 赠送数量，赠品类型为1的时候number表示赠送天数，赠品类型为2的时候代表课时，220为2.2课时
}

export interface IOrderInfoDto {
  orderNo: string;
  kdtId: number;
  headKdtId: number;
  shopName: string;

  // 有赞担保2.0
  yzGuarantee: boolean;

  bizNo: string;
  /**
   * 订单状态
   */
  state: number;
  stateStr: string;
  /**
   * 订单状态详细文案
   */
  stateDetailStr: string;
  /**
   * 订单类型
   */
  orderType: number;
  /**
   * 订单商品类型
   */
  orderGoodsType: number;
  /**
   * 支付方式
   */
  buyWay: number;
  /**
   * 支付方式描述
   */
  buyWayStr: string;
  /**
   * 支付方式扩展描述
   */
  buyWayExtendDesc: string;
  /**
   * 支付时间
   */
  payTime: number;
  /**
   * 订单创建时间
   */
  createTime: number;
  /**
   * 订单完成时间
   */
  successTime: number;
  /**
   * 订单更新时间
   */
  updateTime: number;
  /**
   * 发货时间
   */
  expressTime: number;
  /**
   * 自动过期时间
   */
  expiredTime: number;
  /**
   * 订单关闭时间
   */
  closeTime: number;
  /**
   * 自动收货时间
   */
  autoReceiveOrderTime: number;
  /**
   * 物流方式
   */
  expressType: number;
  /**
   * 关闭类型
   */
  closeType: number;
  /**
   * 关闭类型文案
   */
  closeTypeStr: string;
  /**
   * 发货方式文案
   */
  expressTypeDesc: string;
  /**
   * 商品数
   */
  num: number;
  /**
   * 订单流转进度
   */
  progressBar: Array<any>;
  /**
   * 是否为虚拟商品
   */
  isVirtual: boolean;
  /**
   * 虚拟商品类型
   */
  virtualType: number;
  /**
   * 是否为批发订单
   */
   isWholesaleOrder: boolean;
  /**
   * 是否为零售订单
   */
  isRetailOrder: boolean;
  /**
   * 是否允许延长收货
   */
  isAllowLaterReceive: boolean;
  /**
   * 是否显示支付方式
   */
  isAllowShowBuyWay: boolean;
  /**
   * 虚拟商品自提凭证码
   */
  isShowSelfetchScancode: boolean;
  /**
   * 是否再来一单
   */
  isAllowBuyAgain: boolean;
  /**
   * 是否再来一单，直接进下单页
   */
  isAllowDirectBuyAgain: boolean;
  /**
   * 是否允许取消订单
   */
  isAllowCancelOrder: boolean;
  /**
   * 是否允许确认收货
   */
  isAllowConfirmReceive: boolean;
  /**
   * 找人代付按钮
   */
  isAllowPeerpay: boolean;
  /**
   * 是否分享
   */
  isAllowShareOrder: boolean;
  /**
   * 查看团详情按钮控制
   * 只要有团编号就显示
   */
  isAllowGroupon: boolean;
  // 拼团类型
  activityType: number;
  /**
   * 是否展示优惠券
   */
  isAllowShareCoupon: boolean;
  /**
   * 是否展示退款信息，知识付费送礼退款信息展示用
   */
  isShowRefundInfo: boolean;

  /**
   * 运费险
   */
  freight: any;
  /**
   * 商家服务
   */
  afterSaleContact: any;

  /**
   * 是否显示海淘身份证
   */
  showIdCard?: boolean;
  /**
   * 海淘身份证
   */
  idCardNumber?: string;
  /**
   * 显示送达时间
   */
  showDeliveryTime?: boolean;

  /**
   * 拼团信息
   */
  groupBuy?: any;
  /**
   * 是否显示拼团代收信息
   */
  showGroupAgencyReceive?: boolean;

  /**
   * 是否显示周期购顺延
   */
  isShowPostponeShip?: boolean;
  /**
   * 周期购信息
   */
  periodDetail?: Array<any>;

  /**
   * 买家留言
   */
  buyerMemo: string;
  isPeriodBuy?: boolean;
  isSelfFetch?: boolean;
  /**
   * 显示订单流转状态
   */
  showSteps: boolean;
  /**
   * 显示收货地址
   */
  showAddress: boolean;
  /**
   * 订单状态：已发货 维权状态：维权已创建
   */
  showOrderStatusSended: boolean;
  /**
   * 订单状态：待成团，其他订单持续时间较短，不包含抽奖拼团订单
   */
  showOrderStatusToGroup: boolean;
  /**
   * 显示配送方式
   */
  showDeliveryType: boolean;
  /**
   * 显示邮费
   */
  showPostage: boolean;
  /**
   * 显示课程查看
   */
  showCourseDetail?: boolean;
  /**
   * 显示售后电话等
   */
  showAfterSaleMobile?: boolean;
  /**
   * 虚拟商品核销网点
   */
  showShopVerifyAddress?: boolean;
  /**
   * 显示课程信息
   */
  showEducation?: boolean;
  /**
   * 课程商品信息
   */
  education?: any;
  /**
   * 自定义资料项
   */
  attributeItems?: any;
  /**
   * 只支持扩展一个字段，兼容小程序版本，以后不许这个对象再暴露新字段
   */
  extra?: IExtraWechatSyncShoppingListOnly;
  /**
   * 显示二维码 - 兼容小程序老版本
   */
  showQrCode?: boolean;
  /**
   * 二维码订单
   */
  virtualResponse?: any;
  /**
   * 二维码订单 - 兼容小程序老版本
   */
  qrCode: any;
  /**
   * 显示卡券信息
   */
  showVirtualTicket?: boolean;
  /**
   * 电子卡券信息
   * deprecated
   */
  virtualTicket?: any;
  /**
   * 电子卡券信息
   */
  virtualTicketResponse?: any;
  /**
   * 物流包裹信息
   */
  goodsPackages?: Array<any>;
  /**
   * 优惠信息
   */
  ump?: any;
  /**
   * 查看同城配送范围
   */
  showCheckDeliveryScope: boolean;
  /**
   * 订单来源
   */
  marketingOrderSource: string;
  /**
   * 是否展示删除订单
   */
  allowShowDeleteOrder: boolean;
  /**
   * 订单使用积分
   */
  pointsPrice: number;

  // 订单维权状态
  feedback: number;

  // 视频号状态标记
  wxChannelsOrder?: boolean;
  // apollo控制是否展示删除订单按钮
  isApolloShowDeteleOrder: boolean;

  /**
   * 线下报名课程信息
   */
  faceToFaceCourseInfo?: {
    /**
     * 课程信息
     */
    courses: FaceToFaceCourseInfo[];
    /**
     * 赠品信息
     */
    giveaways: GiveawayInfo[];
    /**
     * 课程录入人信息
     */
    enrollmentInfoDTO: {
      enrollmentDate: string;
      sellerId: number;
      sellerName: string;
    };
  } | null;
  
  // 领优惠券样式
  shareCouponStyleVersion?: 'v1' | 'v2';

  // 订单渠道
  channelType?: number;
  
  // 是否显示【修改地址】按钮
  allowShowModifyAddress?: boolean;

  // 线下三方门店，外部订单编号
  outBizNo?: number;

  /**
   * 扫码点单取餐号
   */
  pickUpCode?: string;

  // 物流信息
  expressData?: Array<any>;

  // 订单业务类型
  bizType?: number;

  // 是否展示修改地址/时间
  deliveryAllowShow?: any;

  // 同城配送申请退款拦截弹窗配置
  localDeliveryRefundConfig?: any;
  // 同城配送物流详情
  localDeliveryDetail?: any;
}