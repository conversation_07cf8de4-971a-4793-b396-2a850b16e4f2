import <PERSON>an<PERSON>ogger from 'common/log/skynet-report';
import get from 'utils/get';
import { lambdaFormatter } from './lambdaFormatter';
import { BTN_CONF } from './constant';
import * as log from './log';

const isTTApp = get(window, '_global.miniprogram.isTTApp', false);
const isAlipayApp = get(window, '_global.miniprogram.isAlipayApp', false);
const isQQApp = get(window, '_global.miniprogram.isQQApp', false);
const isXhsApp = get(window, '_global.miniprogram.isXhsApp', false);
const isKsApp = get(window, '_global.miniprogram.isKsApp', false);
const directBuyAgainBtnConfig = get(window, '_global.directBuyAgainBtnConfig', {});

export function parseJSON(value) {
  let parseResult = '';

  try {
    parseResult = JSON.parse(value);
  } catch (error) {
    parseResult = '';
  }

  return parseResult;
}

//  订单列表页的底部按钮是在这里控制
function computeActionBtns(listOrder = {}, canUseTradeUmpV1) {
  const orderItem = get(listOrder, 'orderItems', [])[0] || {};
  const { orderPermission } = listOrder;
  const { orderExtra = {}, items = [] } = orderItem;

  const actionBtns = [];
  // 针对美业订单 屏蔽所有按钮
  if (orderExtra.isBeauty) {
    return actionBtns;
  }

  const notALiAndQQ = !isAlipayApp && !isQQApp;

  //= =========== 操作订单顺序不要变

  // 我要晒单
  // 支付宝 或者 QQ 环境下不展示我要晒单 或者 抖音不展示
  if (orderPermission.isAllowShareOrder && notALiAndQQ && !isTTApp) {
    actionBtns.push(BTN_CONF.shared);
  }
  // 删除订单
  if (orderPermission.allowShowDeleteOrder) {
    actionBtns.push(BTN_CONF.delete);
  }

  const goodsIds = [];
  items.forEach((item) => {
    goodsIds.push(item.goodsId);
  });
  let params = {
    goods_id: goodsIds,
    order_no: listOrder.orderNo,
  };
 
  // 小红书、快手暂时不支持再来一单
  if (!isXhsApp && !isKsApp) {
     // 再来一单
    if (orderPermission.isAllowDirectBuyAgain && directBuyAgainBtnConfig.show && notALiAndQQ) {
      // 点击直接进下单页
      actionBtns.push(BTN_CONF.directBuyAgain);
  
      // 曝光埋点
      params.is_new = true;
      params.abTraceId = directBuyAgainBtnConfig?.abTraceId || null;
      log.LOG_NEW_BUY_AGAIN_VIEW(params);
    } else if (orderPermission.isAllowBuyAgain && !orderExtra.isMallGroupBuy) {
      // 再来一单，社区团购订单不展示再来一单
      actionBtns.push(BTN_CONF.buyAgain);
      
      // 曝光埋点
      params.is_new = false;
      // 新再来一单按钮支持，但由于AB结果，走老按钮逻辑，需要添加abTraceId
      if (orderPermission.isAllowDirectBuyAgain) {
        params.abTraceId = directBuyAgainBtnConfig?.abTraceId || null;
      }
      log.LOG_NEW_BUY_AGAIN_VIEW(params);
    }
  }

  // 抽奖结果
  if (orderPermission.allowViewLotteryResult && orderItem.lotteryResultUrl) {
    // lottery-result
    actionBtns.push({
      ...BTN_CONF.lotteryResult,
      url: orderItem.lotteryResultUrl,
    });
  }

  // 查看课程
  if (orderPermission.allowCourseDetail && orderItem.courseDetailUrl) {
    actionBtns.push({
      ...BTN_CONF.courseDetail,
      url: orderItem.courseDetailUrl,
    });
  }

  // 拼团详情
  // 支付宝 不展示拼团详情
  // QQ 环境下 展示
  if (
    orderPermission.allowPintuanDetail &&
    orderItem.groupDetailUrl &&
    !isAlipayApp
  ) {
    // QQ 小程序 抽奖团 不展示拼团详情
    if (isQQApp) {
        const isLotteryGroup = orderExtra.isLotteryGroup; // 抽奖团lotteryGroup
        !isLotteryGroup &&
          actionBtns.push({
            ...BTN_CONF.groupDetail,
            url: orderItem.groupDetailUrl,
          });
      } else {
      // group-detail
      actionBtns.push({
        ...BTN_CONF.groupDetail,
        url: orderItem.groupDetailUrl,
      });
    }
  }

  // 送礼
  if (orderItem.giftUrl) {
    actionBtns.push({
      ...BTN_CONF.gift,
      url: orderItem.giftUrl,
    });
  }

  // 开启催单发货
  if (orderPermission.isShowReminder) {
    if (orderExtra.isHotel) {
      // 酒店商品改为提醒接单
      actionBtns.push(BTN_CONF.expressReminderHotel);
    } else {
      actionBtns.push(BTN_CONF.expressReminder);
    }
  }

  // 延长收货
  if (orderPermission.isAllowLaterReceive) {
    actionBtns.push(BTN_CONF.laterReceive);
  }

  // 货到付款，待发货：显示取消按钮
  if (+orderItem.buyWay === 9 && orderItem.statusCode === 'tosend') {
    actionBtns.push(BTN_CONF.cancelPayOnDelivery);
  }

  // 取消订单
  if (orderPermission.isShowCancelOrder) {
    actionBtns.push(BTN_CONF.cancel);
  }

  // 查看物流
  if (orderPermission.allowViewLogistics && orderItem.expressUrl) {
    // transport
    actionBtns.push({
      ...BTN_CONF.transport,
      url: orderItem.expressUrl,
    });
  }

  // 代付未支付
  // QQ 支付宝小程序 不支持的
  if (+orderItem.buyWay === 7 && +orderItem.status !== 99) {
    actionBtns.push({
      ...BTN_CONF.peerpay,
      url: orderItem.peerpayUrl,
    });
  }

  // 邀请助力
  if (orderPermission.isShowInviteHelp) {
    // invite-help
    actionBtns.push(BTN_CONF.inviteHelp);
  }

  // 支付尾款(不可点击)
  if (orderPermission.isShowBeforePresaleFinalpayment) {
    // before-presale-finalpayment
    actionBtns.push(BTN_CONF.topayRetainageDisabled);
  }

  // 支付尾款(可点击)
  if (orderPermission.isShowTopayPresaleFinalpayment) {
    // topay-presale-finalpayment
    actionBtns.push(BTN_CONF.topayRetainage);
  }

  // 支付定金
  if (orderPermission.isShowTopayPresaleDownpayment) {
    // topay-presale-downpayment
    actionBtns.push(BTN_CONF.topayDeposit);
  }

  // 去支付
  if (orderPermission.isShowTopay && orderItem.confirmUrl) {
    actionBtns.push({
      ...BTN_CONF.topay,
      url: orderItem.confirmUrl,
    });
  }

  // 确认收货
  if (orderPermission.isAllowConfirmReceive) {
    if (orderExtra.isHotel) {
      // 酒店商品改为确认入住
      // confirm-receive-hotel
      actionBtns.push(BTN_CONF.confirmReceiveHotel);
    } else {
      // confirm-receive
      actionBtns.push(BTN_CONF.confirmReceive);
    }
  }

  // 评价
  if (orderPermission.isShowEvaluate && orderItem.evaluateUrl) {
    actionBtns.push({
      ...BTN_CONF.evaluate,
      url: orderItem.evaluateUrl,
    });
  }

  // 评价 / 查看评价 ? 没了
  if (orderPermission.isShowViewEvaluate && orderItem.viewEvaluateUrl) {
    actionBtns.push({
      ...BTN_CONF.viewEvaluate,
      url: orderItem.viewEvaluateUrl,
    });
  }

  // 小红书、快手暂不支持支付有礼
  if (!isXhsApp && !isKsApp) {
    let hasNewPaidPromotion = false;
    const { paidPromotion = {} } = listOrder;
    const { paidPromotionType = '' } = paidPromotion;
    // 支付有礼跳转按钮
    if (canUseTradeUmpV1) {
      hasNewPaidPromotion = !!paidPromotionType;
    }
    if (hasNewPaidPromotion) {
      const { paidPromotionValue } = paidPromotion;
      const { kdtId, orderNo } = listOrder;
      const type = `${paidPromotionType}${paidPromotionType === 'liveQrCode' ? paidPromotionValue.qrCodeType : ''}`;
      actionBtns.push({
        ...BTN_CONF[type],
        url: `/wscump/paid-promotion/fetch?kdtId=${kdtId}&orderNo=${orderNo}&source=order_list`,
      });
    } else {
      const { orderNo, kdtId } = listOrder;
      const { paidPromotion, hasRefund } = orderExtra;
      if (paidPromotion && !hasRefund) {
        actionBtns.push({
          ...BTN_CONF.paidPromotion,
          url: `/wscump/paid-promotion/fetch?kdtId=${kdtId}&orderNo=${orderNo}&source=order_list`,
        });
      }
    }
  }
  

  // 领优惠券
  if (orderPermission.isAllowFissionCoupon) {
    actionBtns.push({
      ...BTN_CONF.fissionCoupon,
      url: orderItem?.fissionCouponUrl,
    });
  }

  // 酒店套餐添加立即使用按钮
  if(orderPermission.hotelPackageIsUseAble) {
    actionBtns.push({
      ...BTN_CONF.hotelComboUse,
    });
  }
  // ============== end

  return actionBtns;
}

// 格式化订单列表
export function formatOrderList(ctx, list, { openAppConfig = {} }) {
  const { canUseTradeUmpV1 } = ctx.data;
  return list.map((listItem) => {
    if (!listItem.orderItems || listItem.orderItems.length <= 0) {
      // 埋点统计order_items为空的情况
      ZanLogger.paasLog({
        name: 'OrderListNoOrderItems',
        message: JSON.stringify({ id: listItem.id }),
      });
    }

    listItem.btnList = computeActionBtns(listItem, canUseTradeUmpV1);

    listItem = lambdaFormatter(ctx, listItem, openAppConfig);

    return listItem;
  });
}
