import BaseController from '../base/BaseController';
import DispatchOrderService from '../../services/dispatch/DispatchOrderService';
import { Context } from 'astroboy';

class OrderChainStoreController extends BaseController {
  async init() {
    await super.init({
      initGlobalTheme: true,
    });
  }

  // 查询页面
  async getIndexHtml(ctx: Context) {
    await ctx.render('order/chainstore-switch-shop.html');
  }

  async getSwitchableShopList(ctx: Context) {
    const reqBody = ctx.getPostData();
    const res = await new DispatchOrderService(ctx).localSwitchShopList(reqBody);

    return ctx.json(0, 'ok', res);
  }

  async isSupportLocalSwitchShop(ctx: Context) {
    const reqBody = ctx.getPostData();
    const res = await new DispatchOrderService(ctx).isSupportLocalSwitchShop(reqBody);

    return ctx.json(0, 'ok', res);
  }

  async localSaleShopAndTime(ctx: Context) {
    const { buyerId } = ctx;
    // @ts-ignore
    const params = { ...ctx.request.body, buyerId };

    const result = await new DispatchOrderService(ctx).localSaleShopAndTime(params).catch((err) => {
      ctx.logger.info(`[抖音先囤后约] err=${err}`);
    });

    ctx.json(0, 'ok', result || {});
  }
}

export = OrderChainStoreController;
