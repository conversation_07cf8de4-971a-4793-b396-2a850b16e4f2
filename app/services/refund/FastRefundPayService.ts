import {
  IFrPayAdvanceFundRequest,
  IFrPayAdvanceFundResult,
} from 'definitions/refund/fast-refund-pay';
import BaseService from '../base/BaseService';

class FastRefundPayService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.secured.fastrefund.api.FastRefundService';
  }

  async payAdvanceFund(
    params: IFrPayAdvanceFundRequest
  ): Promise<IFrPayAdvanceFundResult> {
    return this.invoke(this.SERVICE_NAME, 'payAdvanceFund', [params]);
  }
}

export = FastRefundPayService;
