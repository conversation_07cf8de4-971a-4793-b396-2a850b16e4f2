import _ from 'lodash';
import BaseController from '../base/BaseController';
import ScrmLevelService from '../../services/scrm/ScrmLevelService'

class ScrmLevelController extends BaseController {
  public async init() {
    const hasKdtId = !!this.ctx.kdtId;
    await super.init({
      initMpData: hasKdtId,
      initShopSettings: hasKdtId,
      initOpenAppConfig: false,
      initMpAccount: hasKdtId,
      initGlobalTheme: false,
    });
  }
    
  // 查询店铺等级
  public async getLevelJson() {    
    const { ctx } = this;
    const {
      goodsId,
      kdtId,      
    } = ctx.query;  
    const params = {
      goodsId,      
      kdtId,      
    }    
    const data = await new ScrmLevelService(ctx).getLevelJson(params);
    ctx.json(0, '', data);
  }
}

export = ScrmLevelController;
