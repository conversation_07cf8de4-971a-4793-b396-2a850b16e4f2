import BaseController from '../base/BaseController';
import { Context } from 'astroboy';
import UserAccountService from '../../services/uic/UserAccountService';

class UserAccountController extends BaseController {
  async postUpdateAddressAuthJson(ctx: Context) {
    const queryData = ctx.getPostData() || {};
    const params = {
      ...queryData,
      kdtId: ctx.kdtId,
      sessionId: ctx.sessionId,
      userId: this.buyerId,
    };

    const data = await new UserAccountService(ctx).updateAddressAuth(params);
    ctx.json(0, '', data);
  }
}

export = UserAccountController;
