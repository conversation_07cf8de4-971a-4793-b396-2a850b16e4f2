import BaseService from '../base/BaseService';

class ShopConfigurationService extends BaseService {
  // eslint-disable-next-line constructor-super
  constructor(ctx: any) {
    super(ctx);
    this.ctx = ctx;
  }

  async queryShopConfig(key: string, rootKdtId?: number) {
    const result = (await this.ctx.getShopConfigsWithKdtId(rootKdtId || this.ctx.kdtId, [key]).catch(() => {})) || {};
    return result?.[key];
  }
}

export = ShopConfigurationService;
