import BaseController from '../base/BaseController';
import PackageReservationService from '../../services/order/PackageReservationService';
import { Context } from 'astroboy';

class PackageReservationController extends BaseController {
  async queryPackageReservation(ctx: Context) {
    const { kdtId } = ctx;
    const { orderNos } = ctx.getQueryData();
    const params = { orderNos: JSON.parse(orderNos), buyerId: this.buyerId, kdtId };

    const result = await new PackageReservationService(ctx).queryPackageReservation(params);

    ctx.json(0, 'ok', result || {});
  }
}

export = PackageReservationController;
