const BaseService = require('../base/BaseService');

/**
 * 社区团购团购设置接口
 */
class GroupBuyJoinSettingService extends BaseService {
  /**
   * SERVICE_NAME
   * @return {string}
   * @constructor
   */
  get SERVICE_NAME() {
    return 'com.youzan.ebiz.mall.rigel.api.groupbuy.service.shop.GroupBuyJoinSettingService';
  }

  /**
   * 查询店铺的团长加入设置
   * 文档：http://zanapi.qima-inc.com/site/service/view/104956
   * @param {Number} kdtId 店铺id
   * @return {Promise<Object>}
   */
  async getSettingV2(kdtId, params) {
    const result = await this.invoke('getSettingBOV2', [{ kdtId, ...params }]);
    return result;
  }
}

module.exports = GroupBuyJoinSettingService;
