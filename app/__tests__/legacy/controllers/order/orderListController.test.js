import { app, mock } from '@youzan/astroboy-mock/bootstrap';
import BaseController from '@app/controllers/base/BaseController';
import OrderListService from '@app/services/order/OrderListService';
import MultiStoreSettingService from '@app/services/multistore/MultiStoreSettingService';


jest.mock('@app/controllers/base/BaseController', () =>
  jest.requireActual('@app/__tests__/__mocks__/BaseController')
);

jest.mock('@app/services/order/OrderListService');
jest.mock('@app/services/multistore/MultiStoreSettingService')

afterEach(() => {
  BaseController.mockClear();
  OrderListService.mockClear();
  MultiStoreSettingService.mockClear();
});

test('getSelffetchJson', async () => {
    app.mockContext({
      buyerId: 8000035538,
      kdtId: 491391,
    });
  
    OrderListService.mockImplementation(() => {
      return {
        getSelffetchOrder: jest.fn().mockResolvedValue([]),
      };
    });
  
    const res = await app
      .httpRequest()
      .get('/wsctrade/orderlist/selffetch.json');
  
    expect(res.body).toEqual({
      code: 0,
      data: [],
      msg: '',
    });
  });


test('getWriteOffConfigJson', async () => {
    app.mockContext({
      buyerId: 8000035538,
      kdtId: 491391,
    });
  
    MultiStoreSettingService.mockImplementation(() => {
      return {
        getShopConfig: jest.fn().mockResolvedValue({data:'1'}),
      };
    });
  
    const res = await app
      .httpRequest()
      .get('/wsctrade/order/selffetch/shopConfig.json');
  
    expect(res.body).toEqual({
      code: 0,
      data: {
          data:'1'
      },
      msg: '',
    });
  });

  test('postVerifySelffetchJson', async () => {
    OrderListService.mockImplementation(() => {
      return {
        verifySelffetch: jest.fn().mockResolvedValue(true),
      };
    });
  
    const res = await app
      .httpRequest()
      .post('/wsctrade/order/selffetch/verifySelffetch.json');
  
    expect(res.body).toBeTruthy()
  })
