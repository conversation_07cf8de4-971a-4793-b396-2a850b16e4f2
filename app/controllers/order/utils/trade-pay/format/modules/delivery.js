import { formatDeliveryPostage } from '../utils/postageUtils';

function hasGoods(state) {
  const { list } = state.goods;
  return !!list.length;
}

function expressWayList(state) {
  const { postageItems = [] } = state.postage;

  return postageItems.map((item) => ({
    ...item,
    postage: formatDeliveryPostage(item.postage, item),
  }));
}

// 当前配送方式
function currentPostage(state) {
  const { postage } = state;
  const { postageItems = [] } = postage;
  if (Array.isArray(postageItems)) {
    return postageItems.filter((item) => item.expressType === postage.currentExpressType)[0] || {};
  }
  return {};
}

function showExpressWay(state) {
  return hasGoods(state) && expressWayList(state).length > 0;
}

// 是否显示同城送时间
function showDeliveryTime({ state }) {
  const { showLocalDeliveryTime } = state.display;
  return hasGoods(state) && showLocalDeliveryTime;
}

// 同城送时间
function localDeliveryTime({ state }) {
  const { delivery } = state;
  return delivery.text;
}
export default function ({ state, getters }) {
  const { showLocalDeliveryScope } = state.display;
  const showCheckDeliveryScope = showLocalDeliveryScope && hasGoods(state);
  const curPostage = currentPostage(state);
  return {
    deliveryShowDeliveryType: showExpressWay(state), // 是否显示配送方式
    deliveryShowCheckDeliveryScope: showCheckDeliveryScope, // 是否显示同城配送范围
    deliveryShowDeliveryTime: showDeliveryTime({ state, getters }), // 是否显示定时达
    deliveryService: {
      postage: formatDeliveryPostage(curPostage.postage, curPostage.expressPayMode), // 配送方式
      expressType: curPostage.postageTitle, // 运费
      deliveryTime: localDeliveryTime({ state, getters }), // 定时达时间
      localDeliveryDesc: state.delivery?.desc, // 配送范围介绍
      localDeliveryImg: state.delivery?.attachPic, // 配送范围图片
      buyerMemo: state.order.buyerMsg || '无', // 留言
      deliveryScope: state.delivery,
    },
    deliveryInvoiceStatus: 0, // 发票状态（待支付页暂不支持）
    deliveryShowLookCoupon: false, // 查看优惠券（待支付页暂不支持）
    currentExpressType: curPostage.postage,
  };
}