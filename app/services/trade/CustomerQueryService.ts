import BaseService from '../base/BaseService';

/**
 * com.youzan.scrm.api.customer.customer.request.ReceiverIdentityQueryDTO
 * ReceiverIdentityQueryDTO
 */
interface IReceiverIdentityQueryDTO {
  /** 自定义参数透传  比如标签等shopType的透传 */
  payLoad?: unknown;
  /** 店铺Id,必填 */
  kdtId?: number;
  /** 证件类型,必填（默认为身份证，1:身份证，...） */
  identityType?: number;
  /** 收件人姓名,必填 */
  receiverName?: string;
  /** 调用链 */
  requestId?: string;
  /** 应用名 */
  appName?: string;
  /** 非必填,如有操作员操作需要填写操作人id */
  adminId?: number;
  /** 是否需要校验客户存在,不传默认为false,会根据客户是否存在进行补偿创建客户 */
  checkCustomer?: boolean;
  /** 分配给业务方的source,用来做一些限流等识别业务的标识 */
  source?: string;
  /** originKdtId */
  originKdtId?: number;
  /** 有赞账号的oneId,必填 */
  userId?: number;
}

// IReceiverIdentityQueryDTO

export default class CustomerQueryService extends BaseService {
  get CUSTOMER_QUERY_SERVICE() {
    return 'com.youzan.scrm.api.customer.customer.service.CustomerQueryService';
  }

  // 实名校验
  async getReceiverIdentityList(params: IReceiverIdentityQueryDTO) {
    return this.invoke(this.CUSTOMER_QUERY_SERVICE, 'getReceiverIdentityList', [
      params,
    ]);
  }
}
