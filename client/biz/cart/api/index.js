import mapKeysToCamelCase from '@youzan/utils/string/mapKeysToCamelCase';
import args from 'utils/browser/args';
import { api } from 'common/api';
import {
  SELECT_ALL_GOODS_URL,
  UNSELECT_ALL_GOODS_URL,
  UNSELECT_GOODS_URL,
  SELECT_GOODS_URL,
  DELETE_GOODS_BATCH_URL,
  DELETE_GOODS_SINGLE_URL,
  IS_CROSS_SHOP_CART,
  QUERY_GOODS_URL,
  UPDATE_GOODS_NUM_URL,
  UPDATE_GOODS_SKU_URL,
  BATCH_SELECT_GOODS_URL,
  BATCH_UNSELECT_GOODS_URL,
  MULTI_RECOMMEND_GOODS_URL,
  QUERY_AVL_COUPON_URL,
  SEND_COUPON_URL,
  GET_VOUCHER_URL,
  GET_SHOP_CONFIG_URL,
  GET_WHOLESALE_ISWHOLESALE,
} from '../constant';

const cartGoodsListUrl = IS_CROSS_SHOP_CART ? args.add(QUERY_GOODS_URL, { cart_type: 'all' }) : QUERY_GOODS_URL;
const CHANNEL_ID = +args.get('channel_id', location.href) || 0;

/**
 *
 *
 * @param {*} params
 * @returns
 */
function pickIdFromGoods(params) {
  const goods = mapKeysToCamelCase(params);
  const { kdtId, goodsId, skuId, activityId = 0, cartId = null, activityAlias = '' } = goods;
  const data = {
    kdtId,
    goodsId,
    skuId,
    activityId,
    cartId,
    activityAlias,
  };

  ['storeId', 'channelId', 'canyinId'].forEach((key) => {
    if (goods[key] > 0) {
      data[key] = goods[key];
    }
  });

  return data;
}

// 获取商品列表
function getCartGoodsList() {
  return api.ajax({
    url: cartGoodsListUrl,
    data: {
      // TODO: 新版分流
      supportReviveGroup: true,
      store_id: _global.offlineId || 0,
      // 支持商品套餐
      supportCombo: true,
      excludedComboSubType: JSON.stringify([]),
      // 禁用查询有赞担保
      disableSearchYzGuarantee: true,
    },
  });
}

// 设置商品数量
function setGoodsNum(goods) {
  // 支持套餐商品，要传combo字段
  const { num, combo } = goods;
  return api.ajax({
    url: UPDATE_GOODS_NUM_URL,
    data: {
      ...pickIdFromGoods(goods),
      num,
      combo
    },
    method: 'POST',
  });
}

// 设置商品sku
function reselectGoods(goods) {
  const params = {
    ...pickIdFromGoods(goods),
    messages: goods.messages,
    propertyIds: goods.propertyIds,
    extraAttribute: goods.extraAttribute, // 扩展信息
  };

  // 组合套餐要加上combo数据
  if (goods.combo) {
    params.combo = goods.combo;
  }

  // 周期购
  if (typeof goods.deliverTime === 'number') {
    params.deliverTime = goods.deliverTime;
  }

  return api.ajax({
    url: UPDATE_GOODS_SKU_URL,
    data: params,
    type: 'POST',
  });
}

// 删除商品
function deleteGoods(goods) {
  const { num } = goods;
  return api.ajax({
    url: DELETE_GOODS_SINGLE_URL,
    data: {
      ...pickIdFromGoods(goods),
      num,
    },
    method: 'POST',
  });
}

// 批量删除
function batchDeleteGoods(goodsList) {
  const ids = (goodsList || []).map((goods) => ({
    ...pickIdFromGoods(goods),
    num: goods.num,
  }));
  return api.ajax({
    url: DELETE_GOODS_BATCH_URL,
    method: 'POST',
    data: {
      ids,
    },
  });
}

function kdtUnionUpdate(userInfo) {
  return api.ajax({
    url: '/v2/buyer/kdtunion/index.json',
    data: userInfo,
  });
}

// 批零勾选或取消商品
function batchUpdateSelectGoods(params) {
  const { goodsList, type } = params;
  const list = (goodsList || []).map((good) => ({
    ...pickIdFromGoods(good),
  }));

  return api.ajax({
    url: type === 'add' ? BATCH_SELECT_GOODS_URL : BATCH_UNSELECT_GOODS_URL,
    method: 'POST',
    data: {
      goodsList: list,
    },
  });
}

// 单个勾选或取消商品
function updateSelectGoods(params) {
  const { goods, type } = params;

  return api.ajax({
    url: type === 'add' ? SELECT_GOODS_URL : UNSELECT_GOODS_URL,
    method: 'POST',
    data: pickIdFromGoods(goods),
  });
}

// 店铺全部商品勾选和取消
function updateSelectAllGoods(params) {
  const { kdtId, type } = params;
  const data = {
    isPlatCart: IS_CROSS_SHOP_CART,
    kdtId,
  };

  if (CHANNEL_ID > 0) {
    data.channelId = CHANNEL_ID;
  }

  return api.ajax({
    url: type === 'add' ? SELECT_ALL_GOODS_URL : UNSELECT_ALL_GOODS_URL,
    method: 'POST',
    data,
  });
}

function getMultiRecommendGoods(params) {
  const data = {
    goodsIds: params,
  };
  return api.ajax({
    url: MULTI_RECOMMEND_GOODS_URL,
    method: 'POST',
    data,
  });
}

function getAvlCouponList(data) {
  return api.ajax({
    url: QUERY_AVL_COUPON_URL,
    method: 'GET',
    data,
  });
}

function sendCoupon(data) {
  return api.ajax({
    url: SEND_COUPON_URL,
    method: 'GET',
    data,
  });
}

function getVoucher(data) {
  return api.ajax({
    url: GET_VOUCHER_URL,
    method: 'POST',
    data,
  });
}

// 获取店铺设置
function getShopConfig() {
  return api.ajax({
    url: GET_SHOP_CONFIG_URL,
    data: {
      key: 'subshop_online_shop_switch_show',
    },
  });
}

// 判断用户是否为批发商
function getIsWholesaler() {
  return api.ajax({
    url: GET_WHOLESALE_ISWHOLESALE,
  });
}

// 获取商品的有赞担保信息
function getGoodsGuarantee(aliases) {
  return api.ajax({
    method: 'post',
    url: '/wsctrade/goods-guarantee.json',
    data: {
      aliases
    }
  });
}

export {
  kdtUnionUpdate,
  getCartGoodsList,
  setGoodsNum,
  deleteGoods,
  reselectGoods,
  batchDeleteGoods,
  updateSelectGoods,
  updateSelectAllGoods,
  batchUpdateSelectGoods,
  getMultiRecommendGoods,
  getAvlCouponList,
  sendCoupon,
  getVoucher,
  getShopConfig,
  getIsWholesaler,
  getGoodsGuarantee,
};
