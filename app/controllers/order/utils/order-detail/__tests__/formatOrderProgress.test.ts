import { formatOrderWaitingProgress, WAITING_STATUS } from '../formatOrderProgress';
import { IShelfOrderProgressResponse } from 'definitions/api/retail/misc/ShelfProgressService/queryOrderProgress';

describe('formatOrderWaitingProgress', () => {
  it('应该返回空对象当输入为空时', () => {
    expect(formatOrderWaitingProgress()).toEqual({});
    expect(formatOrderWaitingProgress(undefined)).toEqual({});
  });

  it('应该返回空对象当bizType不为1时', () => {
    const input: IShelfOrderProgressResponse = {
      bizType: 0, // 快递
      isNeedDisplay: true,
    };
    expect(formatOrderWaitingProgress(input)).toEqual({});
  });

  it('应该返回空对象当isNeedDisplay为false时', () => {
    const input: IShelfOrderProgressResponse = {
      bizType: 1, // 自提
      isNeedDisplay: false,
    };
    expect(formatOrderWaitingProgress(input)).toEqual({});
  });

  it('应该正确格式化自提订单进度数据', () => {
    const input: IShelfOrderProgressResponse = {
      orderNo: 'E20241209123456789',
      bizType: 1, // 自提
      isNeedDisplay: true,
      currentGoodsNum: 2,
      orderNum: 5,
      goodsMakingStatus: 2, // 制作中
      productTime: 300, // 5分钟
      goodsNum: 10,
      queueProcessNumSwitch: true,
      queueProcessTimeSwitch: true,
      queueShowUnit: '杯',
    };

    const result = formatOrderWaitingProgress(input);

    expect(result).toMatchObject({
      ...input,
      statusDesc: '制作中',
      progressStr: '5单/10杯',
      waitTime: 5,
    });

    expect(result.orderSteps).toHaveLength(3);
    expect(result.orderSteps?.[1].active).toBe(true); // 制作中状态激活
    expect(result.orderSteps?.[0].active).toBe(false);
    expect(result.orderSteps?.[2].active).toBe(false);
  });

  it('应该正确处理商品数超过最大值的情况', () => {
    const input: IShelfOrderProgressResponse = {
      bizType: 1,
      isNeedDisplay: true,
      orderNum: 10,
      goodsNum: 1500, // 超过999
      queueProcessNumSwitch: true,
      queueShowUnit: '杯',
    };

    const result = formatOrderWaitingProgress(input);
    expect(result.progressStr).toBe('10单/999杯');
  });

  it('应该使用默认单位当queueShowUnit为空时', () => {
    const input: IShelfOrderProgressResponse = {
      bizType: 1,
      isNeedDisplay: true,
      orderNum: 3,
      goodsNum: 5,
      queueProcessNumSwitch: true,
      queueShowUnit: '', // 空单位
    };

    const result = formatOrderWaitingProgress(input);
    expect(result.progressStr).toBe('3单/5杯'); // 使用默认单位"杯"
  });

  it('应该正确向上取整等待时间', () => {
    const input: IShelfOrderProgressResponse = {
      bizType: 1,
      isNeedDisplay: true,
      productTime: 90, // 1.5分钟
      queueProcessTimeSwitch: true,
    };

    const result = formatOrderWaitingProgress(input);
    expect(result.waitTime).toBe(2); // 向上取整为2分钟
  });

  it('应该不返回progressStr当开关关闭时', () => {
    const input: IShelfOrderProgressResponse = {
      bizType: 1,
      isNeedDisplay: true,
      orderNum: 5,
      goodsNum: 10,
      queueProcessNumSwitch: false, // 开关关闭
      queueShowUnit: '杯',
    };

    const result = formatOrderWaitingProgress(input);
    expect(result.progressStr).toBeUndefined();
  });

  it('应该不返回waitTime当开关关闭时', () => {
    const input: IShelfOrderProgressResponse = {
      bizType: 1,
      isNeedDisplay: true,
      productTime: 300,
      queueProcessTimeSwitch: false, // 开关关闭
    };

    const result = formatOrderWaitingProgress(input);
    expect(result.waitTime).toBeUndefined();
  });
});
