/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/camelcase */
import BaseController from '../base/BaseController';
import GoodsSkuService from '../../services/goods/GoodsSkuService';
import { GetSkuParamsDTO } from 'definitions/other/goodsSku';
import { ActivityTypeEnum } from '../../constants/ActivityTypeEnum';

import { Context } from 'astroboy';

class GoodsSkuController extends BaseController {
  async fetchSkuData(ctx: Context) {
    const goodsSkuService = new GoodsSkuService(ctx);
    const { alias, offlineId = 0, activityType } = ctx.getQueryData();

    const params = {
      alias,
      userId: ctx.buyerId,
      businessType: 'wsc',
      platform: ctx.platform,
      client: ctx.client,
      queryComboDetail: true,
    } as GetSkuParamsDTO;
    if (+activityType === ActivityTypeEnum.POINTS_EXCHANGE) {
      params.bizScene = 'goods_sku_component';
      params.activityTypes = ['pointsExchange'];
    }

    if (offlineId) params.offlineId = offlineId;

    params.client = ctx.isWeapp
      ? 'weapp'
      : ctx.isTTApp
        ? 'dy_mini_program'
        : !ctx.isMiniProgram && ctx.pageType === 'h5'
          ? ctx.pageType
          : 'unknown';

    const skuData = await goodsSkuService.getSkuData(params);

    /**
     * 为了修复 https://jira.qima-inc.com/browse/ONLINE-524419
     * 背景：商品后端在itemActivitySpuModels中没有提供itemSalePropList，但是后端又不愿修复。因此前端
     * 这边无差别在activity中带上这个值
     * 优化：如果未来商品后端愿意增加这个key，或者有什么项目需要在后端支持这个key，这坨兼容代码就可以挪掉了
     * 验证：只要后端的skuData.itemActivitySpuModels[].itemSalePropList有值即可移除
     * 影响面：前端购物车重新选择sku
     */
    try {
      if (skuData.itemActivitySpuModels) {
        skuData.itemActivitySpuModels = skuData.itemActivitySpuModels.map((activity: any) => {
          return {
            ...activity,
            itemSalePropList: skuData?.itemSalePropList || []
          }
        })
      }
    } catch (e) {
      ctx.logger.warn(`兼容skuData报错：${JSON.stringify(e)}`);
    }

    ctx.json(0, '', skuData);
  }
}

module.exports = GoodsSkuController;
