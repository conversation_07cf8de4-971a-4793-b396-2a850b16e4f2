import { getCartGoodsList } from '@biz/cart/service';
import mapKeysToCamelCase from '@youzan/utils/string/mapKeysToCamelCase';
import args from '@youzan/utils/url/args';
import ZNB from '@youzan/znb';
import { Logger } from 'common/log/logger';
import { Toast } from 'vant';
import yzGuaranteeHelper from './yzGuaranteeHelper';

const global = window._global;
// eslint-disable-next-line
let { cartList, kdtId } = global;

ZNB.init({ kdtId }).catch(() => {});
// 购物车禁止分享
ZNB.disableShare().catch(() => {});

const couponCodeMap = {
  161201035: '优惠券活动已失效，无法再领取',
  161201033: '优惠券库存不足，无法再领取',
  161201406: '领取规则调整，该券不可再领',
  161201050: '当前领取人数太多，请稍后再试',
};

export default class PageBlock {
  constructor(options) {
    this.ctx = options.ctx;

    this.ctx.data.kdtId = global.kdtId || 0;
    this.ctx.data.bizName = 'cart';

    this.resetCart();

    this.initEvents();

    this.toastStatus();
  }

  initEvents() {
    this.ctx.event.listen('updateCartGoodsList', ({ refresh = true, scene } = {}) => {
      if (refresh) {
        getCartGoodsList().then((result) => {
          if (result.code === 0 && result.data) {
            // 重新获取购物车数据后，重置数据
            cartList = mapKeysToCamelCase(result.data || []);

            this.resetCart();
            this.ctx.event.emit('cartGoodsListDidUpdate', { scene });
          }
        });
        this.ctx.event.emit('updatingCart');
      } else {
        this.resetCart();
      }
    });
  }

  resetCart() {
    // 店铺 + 活动 + 商品列表信息复杂对象
    const currentShopCart = JSON.parse(JSON.stringify(cartList[0] || {}));
    /**
     * 为什么需要包一层yzGuaranteeHelper.useYzGuaranteeCache？
     * 由于有赞担保list接口不再返回，因此第一次请求列表后会请求有赞担保信息并更新列表的有赞担保状态，
     * 购物车状态更新刷新页面需要保持原来有赞担保的状态，否则会出现消失再显示的抖动。
     */
    this.ctx.data.shopCart = yzGuaranteeHelper.useYzGuaranteeCache(currentShopCart);
    this.ctx.data.unavailableItems = currentShopCart.unavailableItems || [];
    this.ctx.data.shopTitle = currentShopCart.shopName || '';
    // 渲染购物车后获取有赞担保信息
    yzGuaranteeHelper.updateYzGuarantee(this.ctx.data.shopCart).then(newCart => {
      this.ctx.data.shopCart = newCart;
    }).catch(() => {});
  }

  toastStatus() {
    setTimeout(() => {
      const repurchaseCouponStatus = args.get('repurchaseCouponStatus');
      if (repurchaseCouponStatus === undefined) return;
      if (repurchaseCouponStatus === '0') {
        const value = args.get('couponValue') || '';
        const unit = args.get('couponUnit') || '';
        return Toast(`已为你领取${value}${unit}优惠券，下单享优惠`);
      }
      couponCodeMap[repurchaseCouponStatus] && Toast(couponCodeMap[repurchaseCouponStatus]);
    }, 3000);
  }
}
