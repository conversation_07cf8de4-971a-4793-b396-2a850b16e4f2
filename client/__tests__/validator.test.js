import { validateIdCard } from '../pages/order/buy/common/validator'; 

describe('Id Card Validator', () => {

  test('没有输入姓名时报错', () => {
    const rs = validateIdCard({
      order: {},
      idcard: {},
      tradeTag: {
        hasOverseaGoods: true
      }
    });
    expect(rs).toBe('请输入真实姓名')
  });

  test('没有输入身份证号码时报错', () => {
    const rs = validateIdCard({
      order: {},
      idcard: {
        name: '张三'
      },
      tradeTag: {
        hasOverseaGoods: true
      }
    });
    expect(rs).toBe('请输入真实身份证号')
  });


  test('没有输入身份证号码时报错', () => {
    const rs = validateIdCard({
      order: {},
      idcard: {
        name: '张三'
      },
      tradeTag: {
        hasOverseaGoods: true
      }
    });
    expect(rs).toBe('请输入真实身份证号')
  });


  test('输入错误身份证号码时报错', () => {
    const rs = validateIdCard({
      order: {},
      idcard: {
        name: '张三',
        number: 'xxxxxxxxx'
      },
      tradeTag: {
        hasOverseaGoods: true
      }
    });
    expect(rs).toBe('请输入真实身份证号')
  });


})

