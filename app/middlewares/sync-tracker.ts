import { Context } from 'astroboy';
import SyncCookieKeyService from '../services/tracker/SyncCookieKeyService';

function getExpires(days: string | number) {
  let expires: any;

  if (days) {
    expires = new Date();
    expires.setTime(expires.getTime() + +days * 24 * 60 * 60 * 1000);
  }

  return expires;
}

export = (options: any, app: any) => {
  return async function cat(ctx: Context, next: any) {
    // 禁止页面BFCache
    const { sck } = ctx.getQueryData();

    if (!sck) {
      await next();
      return;
    }

    const result = await new SyncCookieKeyService(ctx).getSyncData(sck);
    let data = [];

    try {
      data = typeof result === 'string' ? JSON.parse(result || '[]') : result;
    } catch (error) {
      data = [];
      ctx.logger.error(`sck: ${sck}解析异常`, result);
    }

    data.forEach((item: any) => {
      const config: {
        domain: string;
        path: string;
        httpOnly: boolean;
        overwrite: boolean;
        expires?: any;
      } = {
        domain: 'youzan.com',
        path: '/',
        httpOnly: false,
        overwrite: false,
      };

      if (item.days) {
        config.expires = getExpires(item.days);
      }

      ctx.cookies.set(item.key, item.value, config);
    });

    await next();
  };
};
