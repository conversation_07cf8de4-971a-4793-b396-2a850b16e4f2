import { H5BaseController } from '@youzan/iron-base';
import OrderHelpService from '../../services/order/OrderHelpService';
import { Context } from 'astroboy';

class OrderHelpController extends H5BaseController {
  async getIndexHtml(ctx: Context) {
    const data = await new OrderHelpService(ctx).getHelpList();

    ctx.setGlobal('list', data);
    await ctx.render('order/help.html');
  }

  async getHelpDetailJson(ctx: Context) {
    const { id } = ctx.query;
    const data = await new OrderHelpService(ctx).getHelpDetail(id);

    ctx.json(0, '', data);
  }
}

module.exports = OrderHelpController;
