<template>
  <van-popup
    v-model="show"
    closeable
    position="bottom"
    safe-area-inset-bottom
    class="coupon-addon-popup"
    @close="handleClose"
  >
    <div class="coupon-addon-popup__title">优惠券</div>
    <div class="coupon-addon-popup__content">
      <cap-coupon
        v-for="(coupon, index) in couponList"
        :key="coupon.activityId"
        :class="['coupon-card', coupon.ableUse ? '' : 'coupon-addon-popup-stamp-gray']"
        :coupon="coupon"
        :unavailable="!coupon.ableUse"
        :btn-text="!coupon.couponId ? '立即领取' : ''"
        btn-icon="false"
        :display-type="displayType"
        :theme-type="themeType"
        :bottom-right-icon="coupon.couponId"
        @click-btn="handleSendCoupon(coupon)"
        :ref="`coupon_${index}`"
        :data-activity-id="coupon.activityId"
        :data-coupon-index="index"
      >
        <div class="coupon-addon-extra" slot="couponExtra">
          <div :class="['addon-info', !coupon.ableUse ? 'addon-info-unavailable' : '']">
            <div style="padding-bottom: 8px">
              <div v-if="!coupon.ableUse">{{ coupon.unableUseDesc }}</div>
              <div v-else>
                <span
                  :key="item"
                  v-for="item in addOnCopywritingFormats[index]"
                  :class="[item.highlight ? 'theme-color' : '']"
                  >{{ item.text }}</span
                >
              </div>
            </div>
            <div v-if="coupon.ableUse" class="cart-item-list">
              <div
                v-for="item in coupon.allAbleCartGoods"
                :key="item.skuId"
                class="cart-item"
                @click="clickItem(item, coupon)"
              >
                <div class="item-thumb" :style="`background-image: url(${item.attachmentUrl})`" />
                <div class="item-price">￥{{ item.price }}</div>
                <div class="item-num">x{{ item.num }}</div>
                <check-box class="item-check" :value="item.selectState" icon-size="16px" />
              </div>
            </div>
          </div>
          <div
            v-if="!coupon.satisfy && coupon.couponId && coupon.ableUse"
            :class="['extra-action', 'theme-color', coupon.allAbleCartGoods.length > 4 ? 'extra-action-shadow' : '']"
            @click="redirPage(coupon)"
          >
            去凑单
            <van-icon name="arrow" size="16px" />
          </div>
        </div>
      </cap-coupon>
    </div>
  </van-popup>
</template>

<script>
import { Popup, Checkbox, Icon } from 'vant';
import { Coupon } from '@youzan/captain';
import { THEME_TYPE_MAP } from 'components/Theme';
import { EMPTY_CART_LINK as SHOP_INDEX_PAGE } from '@biz/cart/constant';
import * as SafeLink from '@youzan/safe-link';

import args from 'utils/browser/args';
import LOGS from '../log';

const { themeColors } = window._global;

export default {
  name: 'CouponPopup',

  components: {
    [Popup.name]: Popup,
    [Coupon.name]: Coupon,
    'check-box': Checkbox,
    'van-icon': Icon,
  },

  props: {
    couponList: Array,
    showPopup: Boolean,
  },

  data() {
    return {
      show: this.showPopup,
      displayType: 'flat',
      themeType: {
        bgColor: themeColors['ump-coupon-bg'], 
        mainColor: themeColors['ump-icon'],
        borderColor: themeColors['ump-border'],
        stampBorderColor: themeColors['ump-icon'],
        stampBgColor: themeColors['ump-tag-bg'],
        stampTextColor: themeColors['ump-icon'],
      },
      check: 1,
    };
  },

  computed: {
    addOnCopywritingFormats() {
      let copywritingList = this.couponList.map((c) => c.addOnCopywriting);
      copywritingList = copywritingList.map((c) => {
        const list = c.match?.(/(.*)\[(.*)\](.*)/);
        if (list) {
          const rs = [
            {
              text: list[1],
            },
            {
              highlight: true,
              text: list[2],
            },
            {
              text: list[3],
            },
          ];
          return rs;
        }
        return [{ text: c }];
      });
      return copywritingList;
    },
  },

  watch: {
    showPopup(val) {
      this.show = val;
      if (val) {
        LOGS.logGatherPopupExpo();
        this.observeCoupons();
      }
    },
    couponList(val) {
      // 用intersectionObserver做曝光, 需要面板展示时做监听
      if (val && val.length && this.show) {
        this.observeCoupons();
      }
    },
  },

  methods: {
    observeCoupons() {
      // 清除掉之前的observer重新监听
      if (this.couponObserver) this.couponObserver.disconnect();
      console.log('making intersection observer!');
      this.$nextTick(() => {
        this.couponObserver = new IntersectionObserver(
          (entries) => {
            entries.forEach((entry) => {
              if (entry.intersectionRatio > 0.9) {
                const { couponIndex } = entry.target.dataset;
                const coupon = this.couponList[couponIndex];
                if (!coupon.couponId) {
                  LOGS.logCouponGetExpo(coupon.activityId);
                }
                if (!coupon.satisfy && coupon.couponId && coupon.ableUse) {
                  LOGS.logGoAddonBtnExpo(coupon.activityId);
                }
              }
            });
          },
          {
            threshold: 0.9,
          }
        );

        const refs = this.$refs;
        this.couponList.forEach((_, index) => {
          const [couponRef] = refs[`coupon_${index}`];
          this.couponObserver.observe(couponRef.$el);
        });
      });
    },
    handleClose() {
      this.$emit('close');
    },
    handleSendCoupon(coupon) {
      this.$emit('sendCoupon', coupon);
    },
    clickItem(item, coupon) {
      this.$emit('onClickItem', item);
      const { goodsId, selectState } = item;
      const { activityId } = coupon;
      LOGS.logGoodsClick({ goodsId, activityId, type: selectState ? 'unselect' : 'select' });
    },
    redirPage(coupon) {
      /**
       * 网店商品使用范围 applicableOnlineGoodsRangeType
       * 0: 该渠道商品不可用 1: 不限制 2: 部分可用 3: 部分不可用
       *
       */
      LOGS.logGoAddonBtnClick(coupon.activityId);
      const { applicableOnlineGoodsRangeType } = coupon;
      if (applicableOnlineGoodsRangeType === 0 || applicableOnlineGoodsRangeType === 1) {
        this.goToShopIndex();
      } else if (applicableOnlineGoodsRangeType === 2 || applicableOnlineGoodsRangeType === 3) {
        this.goToCouponAddOn(coupon);
      }
    },
    goToShopIndex() {
      SafeLink.redirect({
        url: SHOP_INDEX_PAGE,
      });
    },
    goToCouponAddOn({ couponId, alias }) {
      SafeLink.redirect({
        url: args.add('/wscump/coupon/goodslist', { kdtId: window._global?.kdtId, alias, id: couponId }),
      });
    },
  },
};
</script>

<style lang="scss">
.coupon-addon-popup-stamp-gray {
  .cap-coupon-stamp__bgImg {
    filter: grayscale(100%);
  }
}
.coupon-addon-popup {
  // TO BE REMOVED: icon位置调整
  & .van-popup__close-icon--top-right {
    transform: translateY(-30%);
  }
  // TO BE REMOVED: icon位置调整
  & .van-icon-success::before {
    transform: translateY(-10%);
  }

  width: 100%;
  min-height: 50vh;
  max-height: 80vh;
  border-radius: 20px 20px 0 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  &__title {
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 22px;
    text-align: center;
    color: #323233;
    padding-top: 11px;
    padding-bottom: 11px;
  }

  &__content {
    padding: 16px 16px 32px;
    overflow: auto;
    font-size: 13px;
    box-sizing: border-box;
    -webkit-overflow-scrolling: touch;
    z-index: 1;
    position: relative;
    flex: 1;

    > p {
      margin-bottom: 15px;
    }
  }
  .coupon-card {
    .cap-couponbox__card {
      border: 1px solid;
      background-color: var(--ump-coupon-bg, #f2f2ff) !important;
      border-color: var(--ump-border, #c9c9ff) !important;
      box-shadow: none;
    }
  }
  .cap-couponbox__block-des {
    margin: 0 !important;
    padding: 0 !important;
    padding-left: 8px !important;
    padding-right: 8px !important;
  }

  .coupon-addon-extra {
    width: 100%;
    height: 100%;
    display: flex;
    .addon-info {
      flex-grow: 1;
      padding: 8px 0 8px 0;
      max-width: 100%;
      overflow-x: scroll;
      overflow-y: hidden;
    }
    .addon-info-unavailable {
      color: #323233;
    }
    .van-checkbox__icon .van-icon {
      width: 16px;
      height: 16px;
      background: white;
    }
    .extra-action-shadow {
      box-shadow: -2px 0 6px 0 rgba(100, 101, 102, 0.08);
    }
    .extra-action {
      border-radius: 0 0 4px 0;
      height: 100%;
      display: flex;
      align-items: center;
      font-family: PingFangSC-Regular;
      font-size: 12px;
      color: #ee0a24;
      width: 18.1vw;
      min-width: 18.1vw;
      justify-content: center;
      margin-right: -8px;
      // TO BE REMOVED: icon位置调整
      & .van-icon {
        transform: translateY(-12.5%);
      }
    }
    .cart-item-list {
      display: flex;
      justify-content: flex-start;
      align-items: stretch;
      overflow-x: scroll;
      overflow-y: hidden;
      &::-webkit-scrollbar {
        display: none;
      }
      .cart-item {
        width: 56px;
        height: 84px;
        padding-right: 8px;
        position: relative;
      }
      .item-thumb {
        width: 56px;
        height: 56px;
        border-radius: 4px;
        background-size: cover;
      }
      .item-price {
        font-family: PingFangSC-Medium;
        font-size: 10px;
        color: #323233;
        height: 14px;
        text-align: center;
      }
      .item-num {
        font-family: PingFangSC-Regular;
        font-size: 10px;
        color: #969799;
        height: 14px;
        text-align: center;
      }
      .item-check {
        position: absolute;
        left: 2px;
        top: 2px;
      }
    }
  }
}
</style>
