{% if isRantaPage != true %}
  {% extends '../common/base.html' %}
{% else %}
  {% extends '../common/ranta_base.html' %}
{% endif %}

{% block page_title %} {{ title }} {% endblock %}

{% set pageName = pageName or "order/list" %}

{% block page_style %}
  {% if isRantaPage != true %}
    {{ loadCss(pageName) | safe }}
  {% endif %}
{% endblock %}

{% block page_content %}
  <style>
    body{margin:0}.app-wrapper{position:relative}.buy-skeleton{position:absolute;left:0;top:94px;width:100%;z-index:10}
  </style>
  <div class="app-wrapper">
    <img class="js-skeleton buy-skeleton" src="https://img01.yzcdn.cn/public_files/2021/01/13/39b60bf8027405993d59ce1eabd9b9b0.png" />
    <div id="app"></div>
  </div>
{% endblock %}

{% block page_script %}
  {% include 'ecloud-view/extension.html' %}
  {% set pageJsName = ("version_ext_entry_js." if isRantaPage else "") + pageName %}
  {{ loadJs(pageJsName) | safe }}
{% endblock %}
