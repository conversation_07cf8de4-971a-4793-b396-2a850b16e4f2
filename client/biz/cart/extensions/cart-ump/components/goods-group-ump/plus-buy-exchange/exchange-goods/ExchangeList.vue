<template>
  <van-checkbox-group ref="radio-group" v-model="checkedArr" @change="onChange">
    <van-checkbox
      v-for="(item, index) in goodsList"
      :key="index"
      :name="item.goodsId"
      :data-goods-id="item.goodsId"
      class="exchange-item"
    >
      <div class="exchange-item__content">
        <div class="goods-picture">
          <img :src="item.picture | image" @click.stop="goDetail(item.alias)" />
        </div>
        <div class="goods-info">
          <div class="goods-title">
            {{ item.title }}
          </div>
          <div v-if="getSkuAndPropertiesStr(item)" class="goods-sku">
            <div
              class="sku-info"
              :class="{
                'multi-sku': item.thinSkus.length > 1,
              }"
              @click.stop="handleClickSku(item)"
            >
              <span>{{ getSkuAndPropertiesStr(item) }}</span>
              <van-icon
                v-if="item.thinSkus.length > 1"
                class="multi-sku-icon"
                size="10px"
                name="arrow-down"
              />
            </div>
          </div>

          <div class="goods-price">
            <Price
              :price="item.exchangePrice"
              :origin-price="item.originalPrice"
            />
          </div>
        </div>
      </div>
    </van-checkbox>

    <base-sku
      v-model="isShowSkuPopup"
      :sku-data="skuData"
      :properties="goodsProperties"
      :goods="skuDataGoods"
      :quota="1"
      :safe-area-inset-bottom="true"
      :initial-sku="initialSku"
      @sku-selected="onSkuSelected"
      @sku-prop-selected="onSkuSelected"
      @onConfirmSku="onConfirmSku"
    >
      <div slot="sku-stepper" />

      <template slot="sku-header-price" slot-scope="props">
        <div class="van-sku__goods-price">
          <span class="van-sku__price-symbol">￥</span>
          <span class="van-sku__price-num">{{
            exchangePrice ? format(exchangePrice) : props.price
          }}</span>
        </div>
      </template>
    </base-sku>
  </van-checkbox-group>
</template>

<script>
import urlHelper from '@youzan/utils/url/helper';
import format from '@youzan/utils/money/format';
import get from 'utils/get';
import image from '@youzan/utils/browser/image';
import mapKeysToSnakeCase from '@youzan/utils/string/mapKeysToSnakeCase';
import { Checkbox, CheckboxGroup, Toast, Icon } from 'vant';
import BaseSku from './BaseSku';
import * as SafeLink from '@youzan/safe-link';
import Price from '@biz/cart/components/Price';
import { getGoodSkusJson } from '../api';
import {
  getSelectedSkuFromSkuData,
  parseGoodsProperties,
  getGoodsPropertiesStr,
  getSkuData,
} from '@biz/cart/utils';

export default {
  name: 'exchange-list',

  components: {
    Price,
    [Icon.name]: Icon,
    [BaseSku.name]: BaseSku,
    [Checkbox.name]: Checkbox,
    [CheckboxGroup.name]: CheckboxGroup,
  },

  filters: {
    image(url) {
      return image.toWebp(urlHelper.getCdnImageUrl(url, '!200x200.jpg'));
    },
  },

  props: {
    supportMulti: {
      type: Boolean,
    },
    goodsList: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      format,
      isShowSkuPopup: false,
      checkedArr: [],
      selectedGoods: [],
      skuData: {}, // sku数据
      goodsProperties: [],
      skuDataGoods: {}, // 当前显示sku商品
      exchangePrice: 0, // 换购价
    };
  },

  computed: {
    initialSku() {
      const obj = {};
      const { skuData = [], properties = [] } = this.skuDataGoods;

      skuData.forEach(({ kS, vId }) => {
        if (kS && vId) {
          obj[kS] = vId;
        }
      });

      properties.forEach((property) => {
        obj.selectedProp = obj.selectedProp || {};
        obj.selectedProp[property.propId] = (property.propValueList || []).map(
          (value) => value.propValueId
        );
      });

      return obj;
    },
  },

  watch: {
    goodsList: {
      handler(goodsList) {
        this.selectedGoods = goodsList.filter((goods) => !!goods.selected);
        this.checkedArr = this.selectedGoods.map((goods) => goods.goodsId);
        this.$emit('changeTotalNum', this.selectedGoods.length);

        this.$nextTick(() => {
          this.computedGoodsPricePadding();
        });
      },
      immediate: true,
    },
  },

  methods: {
    // 切换商品规格触发
    onSkuSelected({ selectedSkuComb }) {
      if (!selectedSkuComb) return;

      // 获取换购价
      const thinSku = this.skuDataGoods.thinSkus.find(
        (sku) => sku.id === selectedSkuComb.id
      );
      this.exchangePrice = thinSku.exchangePrice;
    },

    onConfirmSku(selectedSku) {
      const skuArr = getSelectedSkuFromSkuData(
        this.skuData.tree,
        selectedSku.selectedSkuComb
      );
      const skuData = getSkuData(selectedSku.selectedSkuComb);
      const skuStr = skuArr.map((sku) => sku.name).join(';');

      const properties = get(selectedSku.selectedSkuComb, 'properties', []);

      const picture =
        (skuArr[0] && skuArr[0].imgUrl) || this.skuDataGoods.picture;

      const { messages, selectedSkuComb } = selectedSku;
      const { id: skuId, price } = selectedSkuComb;
      const formatMessages = Object.keys(messages).map((key) => messages[key]);

      this.updateGoodsListData(this.skuDataGoods, {
        skuStr,
        properties: parseGoodsProperties(properties),
        messages: formatMessages.length ? JSON.stringify(formatMessages) : '',
        skuId,
        skuData,
        exchangePrice: price,
        selected: true,
        picture,
      });
    },

    // 获取商品sku
    fetchGoodsSku(goods) {
      this.exchangePrice = 0;
      this.skuDataGoods = goods;
      const goodsSkuIds = goods.thinSkus.map((item) => item.id);
      const params = {
        alias: goods.alias,
      };
      getGoodSkusJson(params)
        .then((skuData) => {
          skuData = mapKeysToSnakeCase(skuData);
          this.skuData = skuData;
          this.goodsProperties = skuData.item_sale_prop_list || [];
          this.isShowSkuPopup =
            !this.skuData.none_sku ||
            !!(this.skuData.messages && this.skuData.messages.length) ||
            !!(
              this.skuData.item_sale_prop_list &&
              this.skuData.item_sale_prop_list.length
            );

          // 过滤不可用sku
          skuData.list = skuData.list.filter((item) =>
            goodsSkuIds.includes(item.id)
          );

          // 设置sku换购价
          skuData.list.forEach((sku) => {
            const thinSku =
              goods.thinSkus.find((item) => item.id === sku.id) || {};
            sku.price = thinSku.exchangePrice || sku.price;
          });

          if (!this.isShowSkuPopup) {
            this.updateGoodsListData(goods, {
              selected: true,
            });
          }
        })
        .catch((err = {}) => {
          const { error: { code, msg } = {} } = err;
          if (code === 99999 && msg === 'Operation canceled by the user.')
            return;
          Toast('获取商品数据失败，请重试');
        });
    },

    // 选择换购商品
    onChange(ids) {
      const selectedGoodsIds = this.selectedGoods.map((goods) => goods.goodsId);

      if (selectedGoodsIds.length < ids.length) {
        if (!this.supportMulti) this.cancelSelectAll();
        // 选中商品
        const selectedId = ids.find((id) => !selectedGoodsIds.includes(id));
        const currentGoods = this.goodsList.find(
          (goods) => goods.goodsId === selectedId
        );
        this.checkedArr = selectedGoodsIds;
        currentGoods && this.fetchGoodsSku(currentGoods);
      } else {
        // 取消选中
        const cancelId = selectedGoodsIds.find((id) => !ids.includes(id));
        const currentGoods = this.goodsList.find(
          (goods) => goods.goodsId === cancelId
        );
        currentGoods &&
          this.updateGoodsListData(currentGoods, {
            skuStr: '',
            selected: false,
          });
      }
    },

    cancelSelectAll() {
      this.selectedGoods.forEach((goods) => {
        this.updateGoodsListData(goods, {
          selected: false,
        });
      });
    },

    // 添加已选换购商品
    updateGoodsListData(currentGoods, options = {}) {
      this.$emit('updateGoodsListData', currentGoods, options);
    },

    handleClickSku(item) {
      if (item.thinSkus.length <= 1) return;
      this.fetchGoodsSku(item);
    },

    getSelectedGoods() {
      return this.selectedGoods;
    },

    // 跳转到商品详情页
    goDetail(alias = '') {
      const { kdtId: newKdtId = 0, kdt_id: oldKdtId = 0 } = window._global;
      const kdtId = newKdtId || oldKdtId;

      SafeLink.redirect({
        url: `/v2/goods/${alias}`,
        kdtId,
      });
    },

    // 获取商品属性和 sku 拼接字符串
    getSkuAndPropertiesStr(goods) {
      const propertiesStr = getGoodsPropertiesStr(goods.properties);
      return [goods.skuStr, propertiesStr].filter((item) => !!item).join('，');
    },

    // 计算商品信息高度，让价格落底
    computedGoodsPricePadding() {
      const goodsDom = this.$refs['radio-group'].$el.querySelectorAll(
        '.exchange-item'
      );
      (goodsDom || []).forEach((dom) => {
        const goodsPrice = dom.querySelector('.goods-price');
        goodsPrice.style.paddingTop = 0; // 重置之前设置过的值
        const goodsPicture = dom.querySelector('.goods-picture');
        const goodsPictureHeight = goodsPicture.offsetHeight;
        const goodsInfo = dom.querySelector('.goods-info');
        const goodsInfoHeight = goodsInfo.offsetHeight;
        if (goodsInfoHeight < goodsPictureHeight) {
          goodsPrice.style.paddingTop = `${goodsPictureHeight -
            goodsInfoHeight}px`;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.exchange-item {
  margin-bottom: 12px;
  border-radius: 8px;
  background: #fff;
  display: flex;
  align-items: center;
  padding: 12px 0 12px 12px;

  .van-checkbox__label {
    flex: 1;
  }

  &.van-radio {
    .van-icon-checked {
      color: #38f;
    }

    .van-radio__label {
      width: 100%;
    }
  }

  &__content {
    display: flex;
    align-items: flex-start;

    .goods-picture {
      width: 96px;
      height: 96px;
      float: left;
      position: relative;
      margin-left: auto;
      margin-right: auto;
      overflow: hidden;
      background: #fff;
      background-size: cover;
      border-radius: 4px;

      img {
        position: absolute;
        margin: auto;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        width: auto;
        height: auto;
        max-width: 100%;
        max-height: 100%;
      }
    }

    .goods-info {
      flex: 1;
      position: relative;
      width: 100%;
      margin-left: 8px;
      padding-right: 12px;

      &:not(:last-child) {
        border-bottom: 1px solid #f2f2f2;
      }

      .goods-title {
        margin-bottom: 8px;
        line-height: 17px;
        color: #323233;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .goods-sku {
        margin-bottom: 4px;
        color: #969799;

        .sku-info {
          display: inline-block;
          line-height: 16px;
          font-size: 12px;

          &.multi-sku {
            padding: 4px 8px;
            background: #f7f8fa;
            border-radius: 4px;
          }

          span {
            vertical-align: middle;
          }

          .multi-sku-icon {
            margin-left: 8px;
            vertical-align: middle;
          }
        }
      }
    }

    .goods-tag {
      border: 1px solid #ee0a24;
      border-radius: 8px;
      font-size: 10px;
      color: #ee0a24;
      line-height: 14px;
      padding: 0 4px;
      text-align: center;
    }

    .goods-price {
      padding-right: 12px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-sizing: border-box;

      .exchange-price {
        font-size: 14px;
        color: #f44;
        margin-right: 5px;
      }

      .original-price {
        text-decoration: line-through;
        color: #999;
      }
    }
  }

  .msg-item {
    background-color: #f7f8fa;

    &:not(:last-child) {
      .van-cell {
        border-bottom: 1px solid #f2f2f2;
      }
    }

    .van-cell {
      margin-left: 15px;
      padding: 10px 15px 10px 0;
      background-color: #f7f8fa;

      &.van-cell--required::before {
        left: -7px;
      }
    }
  }
}
</style>
