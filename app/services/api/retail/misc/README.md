# 零售相关服务

## ShelfProgressService - 零售订单进度查询服务

### 服务描述
用于查询零售订单的进度状态，支持单个订单查询。

### 服务配置
- **Service 路径**: `com.youzan.retail.trade.misc.api.service.shelf.ShelfProgressService`
- **Method 名称**: `queryOrderProgress`

### 使用方式

#### 1. 基本使用
```typescript
import ShelfProgressService from 'app/services/api/retail/misc/ShelfProgressService';

// 在控制器或其他服务中使用
const shelfProgressService = new ShelfProgressService(ctx);

const result = await shelfProgressService.queryOrderProgress({
  orderNo: 'E20241209123456789',
  retailSource: 'h5',
  kdtId: 12345,
  adminId: 67890,
});
```

#### 2. 请求参数说明
```typescript
interface IShelfOrderProgressRequest {
  /** 订单号 */
  orderNo?: string;
  /** 请求来源 */
  retailSource?: string;
  /** 当前店铺id */
  kdtId?: number;
  /** 当前用户id */
  adminId?: number;
}
```

#### 3. 响应数据说明
```typescript
interface IShelfOrderProgressResponse {
  /** 是否成功 */
  success?: boolean;
  /** 错误码 */
  code?: string;
  /** 错误信息 */
  message?: string;
  /** 订单进度数据 */
  data?: IOrderProgressData;
}

interface IOrderProgressData {
  /** 订单号 */
  orderNo?: string;
  /** 订单状态 */
  orderStatus?: string;
  /** 进度状态 */
  progressStatus?: string;
  /** 进度描述 */
  progressDesc?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 扩展信息 */
  extInfo?: Record<string, any>;
}
```

### 使用示例

#### 控制器中使用
参考 `app/controllers/retail/ShelfProgressController.ts` 中的示例：

```typescript
async queryOrderProgress(ctx: Context) {
  const { orderNo, retailSource } = ctx.getPostData();
  const { kdtId, adminId } = ctx;

  const shelfProgressService = new ShelfProgressService(ctx);
  const result = await shelfProgressService.queryOrderProgress({
    orderNo,
    retailSource: retailSource || 'h5',
    kdtId: Number(kdtId),
    adminId: Number(adminId),
  });

  return ctx.json(0, '查询成功', result);
}
```

### 错误处理
服务会自动记录请求和响应日志，错误处理遵循项目统一的错误处理机制。建议在调用时使用 try-catch 进行错误捕获。

### 注意事项
1. 确保传入的 `kdtId` 和 `adminId` 为有效的数字类型
2. `orderNo` 为必填参数，需要进行参数验证
3. `retailSource` 建议传入具体的来源标识，如 'h5'、'weapp'、'app' 等
4. 服务调用会自动记录 dubbo 调用日志，便于问题排查

### 相关文件
- 服务实现：`app/services/api/retail/misc/ShelfProgressService.ts`
- 类型定义：`definitions/api/retail/misc/ShelfProgressService/queryOrderProgress.ts`
- 使用示例：`app/controllers/retail/ShelfProgressController.ts`
