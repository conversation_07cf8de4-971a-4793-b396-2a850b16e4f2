import BaseService from '../base/BaseService';

interface ISaleIdDTO {
  itemId: number;
}

class HotelQueryService extends BaseService {
  get SERVER_NAME(): string {
    return 'com.youzan.ebiz.mall.rigel.api.hotel.service.HotelQueryService';
  }

  /**
   *  根据销售方案id查询酒店信息
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1190823
   *
   *  @param {Object} saleIdDTO -
   *  @param {number} saleIdDTO.saleProjectId - 销售方案id
   *  @return {Promise}
   */
  async getHotelAggDetailBySaleId(saleIdDTO: ISaleIdDTO) {
    return await this.invoke(this.SERVER_NAME, 'getHotelAggDetailBySaleId', [saleIdDTO]);
  }
}

export = HotelQueryService;
