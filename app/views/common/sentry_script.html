<script onerror="_cdnFallback(this)" src="//b.yzcdn.cn/libs/ravenjs/raven-3.17.0.min.js" crossorigin="anonymous"></script>
<script>
  try {
    var _global = window._global || {};
    var mpData = _global.mp_data || {};
    var ravenOptions = {
      ignoreUrls: [
          // Chrome extensions
          /extensions\//i,
          /^chrome:\/\//i,
          // ignore js
          /libs\/ravenjs\//i,
          /qbox\.me\/vds\.js/i
      ],
      whitelistUrls: [
        // 只监听内部错误
        /\/\/b\.yzcdn\.cn/
      ],
      ignoreErrors: [
          /WeixinJSBridge is not defined/,
          /_vds_hybrid/,
          /TouTiao is not defined/,
          /WebViewJavascriptBridge is not defined/,
          /Can't find variable: MyAppGetLinkHREFAtPoint/,
          /ToutiaoJSBridge is not defined/,
          /setWebViewFlag is not defined/,
          /Cannot read property 'remove' of undefined/,
          /sendPic2Native is not defined/
      ]
    };
    Raven.config('https://<EMAIL>/49', ravenOptions).install();
    Raven.setUserContext({
      project: 'wsc-trade',
      kdt_id: _global.kdt_id || _global.kdtId || 0,
      shop_name: mpData.shop_name,
      buyer_id: _global.buyer_id,
    });
  } catch (e) {
    console.log(e);
  }
</script>
