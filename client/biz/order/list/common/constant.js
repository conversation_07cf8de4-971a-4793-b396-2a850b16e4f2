/* eslint-disable */

const { pointsName = '积分' } = window._global || {};

// 按钮配置
export const BTN_CONF = {
  cancel: {
    text: '取消订单',
    type: 'cancel',
    styleType: 'default',
  },
  cancelPayOnDelivery: {
    text: '取消订单',
    type: 'cancelPayOnDelivery',
    styleType: 'default',
  },
  topay: {
    text: '立即付款',
    type: 'topay',
    styleType: 'danger',
  },
  peerpay: {
    text: '代付',
    type: 'peerpay',
    styleType: 'danger',
  },
  transport: {
    text: '查看物流',
    type: 'transport',
    styleType: 'default',
  },
  topayDeposit: {
    text: '支付定金',
    type: 'topayDeposit',
    styleType: 'danger',
  },
  topayRetainage: {
    text: '支付尾款',
    type: 'topayRetainage',
    styleType: 'danger',
  },
  topayRetainageDisabled: {
    text: '支付尾款',
    type: 'topayRetainageDisabled',
    styleType: 'danger',
    disabled: true,
  },
  confirmReceive: {
    text: '确认收货',
    type: 'confirmReceive',
    styleType: 'danger',
  },
  confirmReceiveHotel: {
    text: '确认入住',
    type: 'confirmReceiveHotel',
    styleType: 'danger',
  },
  laterReceive: {
    text: '延长收货',
    type: 'laterReceive',
    styleType: 'default',
  },
  expressReminder: {
    text: '提醒发货',
    type: 'expressReminder',
    styleType: 'default',
  },
  expressReminderHotel: {
    text: '提醒接单',
    type: 'expressReminderHotel',
    styleType: 'default',
  },
  buyAgain: {
    text: '再来一单',
    type: 'buyAgain',
    styleType: 'default',
    showCoupon: true,
  },
  evaluate: {
    text: '立即评价',
    type: 'evaluate',
    styleType: 'danger',
  },
  viewEvaluate: {
    text: '查看评价',
    type: 'viewEvaluate',
    styleType: 'default',
  },
  gift: {
    text: '查看礼单',
    type: 'gift',
    styleType: 'default',
  },
  groupDetail: {
    text: '查看拼团',
    type: 'groupDetail',
    styleType: 'default',
  },
  lotteryResult: {
    text: '抽奖结果',
    type: 'lotteryResult',
    styleType: 'default',
  },
  courseDetail: {
    text: '查看课程',
    type: 'courseDetail',
    styleType: 'default',
  },
  inviteHelp: {
    text: '邀请助力',
    type: 'inviteHelp',
    styleType: 'default',
  },
  shared: {
    text: '我要晒单',
    type: 'shared',
    styleType: 'default',
  },
  delete: {
    text: '删除订单',
    type: 'deleteOrder',
    styleType: 'default',
  },
  paidPromotion: {
    text: '支付有礼',
    type: 'paidPromotion',
    styleType: 'danger',
  },
  liveQrCode1: {
    text: '专属客服',
    type: 'liveQrCode',
    styleType: 'default',
    icon: 'https://b.yzcdn.cn/public_files/8c404f5a2465baa843f1a847832772de.png',
  },
  liveQrCode2: {
    text: '加粉丝群',
    type: 'liveQrCode',
    styleType: 'default',
    icon: 'https://b.yzcdn.cn/public_files/8c404f5a2465baa843f1a847832772de.png',
  },
  coupon: {
    text: '领优惠券',
    type: 'coupon',
    styleType: 'default',
    icon: 'https://b.yzcdn.cn/public_files/e49f5b413529e624ab6a538642bf4eeb.png',
  },
  fissionCoupon: {
    text: '抢优惠券',
    type: 'fissionCoupon',
    styleType: 'default',
    icon: 'https://img01.yzcdn.cn/public_files/01ceebc40fa09f1902d679c1519012a4.png',
  },
  directBuyAgain: {
    text: '再来一单',
    type: 'directBuyAgain',
    styleType: 'default',
    showCoupon: true,
  },
  hotelComboUse: {
    text: '立即预约',
    type: 'hotelComboUse',
    styleType: 'danger',
  }
};

// 标签配置
export const TAG_CONF = {
  presale: { text: '预售' },
  fcode: { text: 'F码专享' },
  enjoyBuy: { text: '随心订' },
  knowledgeGift: { text: '知识付费送礼' },
  group: { text: '拼团' },
  lotteryGroup: { text: '抽奖团' },
  peerpay: { text: '代付' },
  points: { text: `${pointsName}订单` },
  present: { text: `赠品`, plain: true, isSub: true },
  plusBuy: { text: `换购`, plain: true, isSub: true },
  goodsExchange: { text: `兑换商品`, plain: true, isSub: true },
  giftOrder: { text: '送礼' },
  recommendGift: { text: '新客立减' }, // 教育活动
};

// 页面标题配置
export const TITLE_CONF = {
  all: '所有订单',
  topay: '待付款的订单',
  totuan: '待接单的订单',
  tosend: '待发货的订单',
  send: '已发货的订单',
  sign: '已完成的订单',
  toevaluate: '待评价的订单',
};

// CRM 线下订单展示枚举
export const CRM_OFFLINE_TYPE = {
  NONE: 0, // 不展示
  ONLINE: 1, // 线上订单
  OFFLINE: 2, // 线下订单
}

export const CRM_OFFLINE_TEXT = {
  [CRM_OFFLINE_TYPE.NONE]: '',
  [CRM_OFFLINE_TYPE.ONLINE]: '网店订单',
  [CRM_OFFLINE_TYPE.OFFLINE]: '门店订单',
}