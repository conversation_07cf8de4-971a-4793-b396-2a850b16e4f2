import { app, mock } from '@youzan/astroboy-mock/bootstrap';
import BaseController from 'app/controllers/base/BaseController';
import OrderDetailService from 'app/services/order/OrderDetailService';

jest.mock('app/controllers/base/BaseController', () =>
  jest.requireActual('app/__tests__/__mocks__/BaseController')
);

jest.mock('app/services/order/OrderDetailService');

afterEach(() => {
  BaseController.mockClear();
  OrderDetailService.mockClear();
});

test('getIndexHtml', async () => {
  app.mockContext({
    buyerId: 8000035538,
    kdtId: 491391,
    query: {
      order_no: 'E20201207191732083502131',
      kdt_id: 491391,
    },
  });

  const data = {
    mainOrderInfo: {
      state: 60,
    },
  };

  const clientUrl = {
    orderDetailUrl:
      'https://h5.youzan.com/wsctrade/order/detail?order_no=E20201207191732083502131&kdt_id=491391',
  };

  OrderDetailService.mockImplementation(() => {
    return {
      lightDetailByOrderNo: jest.fn().mockResolvedValue(data),
    };
  });

  const res = await app.httpRequest().get('/wsctrade/batch-refund/index');
  const global = mock.getViewGlobal(res.text);
  expect(BaseController.mockAcl.mock.calls.length).toBe(1);
  expect(global.clientUrl).toEqual(clientUrl);
  expect(global.showGoodAndMoneyRefund).toBeTruthy();
  expect(global.query).toEqual({
    order_no: 'E20201207191732083502131',
    kdt_id: 491391,
  });
});
