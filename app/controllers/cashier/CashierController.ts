/* eslint-disable @typescript-eslint/no-explicit-any */
import { Context } from 'astroboy';
import { PageException } from '@youzan/iron-base';
import WapUrl from '@youzan/iron-base/app/lib/WapUrl';
import BaseController from '../base/BaseController';
import QrCodeTradeService from '../../services/cashier/QrCodeTradeService';
import QrCodeUpdateService from '../../services/cashier/QrCodeUpdateService';
import OrderDetailService from '../../services/order/OrderDetailService';
import {
  QrCodeTradeRequestParam,
  QrCodeUpdateRequestParam,
} from '../../../definitions/pay/Cashier';
import {
  isFixedMoney,
  isAnonymous,
  isMutiPay,
  isExpireQrcode,
  isWxOrder,
} from './QrTypeCheck';
import { isInGrayReleaseByKdtId } from '../../lib/WhiteListUtils';
import buildUrlWithCtx from '@youzan/utils/url/buildUrlWithCtx';

class CashierController extends BaseController {
  async init() {
    await super.init({
      validKdtId: true,
      initMpData: true,
      initCopyrightFooter: true,
    });
  }

  async postQrPayJson(ctx: Context) {
    const { kdtId } = this.ctx;
    const {
      buyerId,
      buyerPhone,
      fansId,
      fansType,
      fansNickname,
      youzanFansId,
      outerUserId,
    } = this.buyer;
    const requestBody = ctx.getPostData();

    if (buyerId === 0) {
      return ctx.json(-1, '登录态失效，请刷新重试');
    }

    // @ts-ignore
    const { activityId, qrId, qrPaymentInfo, extra } = requestBody;

    const { canNotUsePromotionAmount = 0, totalPrice = 0 } =
      qrPaymentInfo || {};

    const qrCodeTradeRequestDTO: QrCodeTradeRequestParam = {
      activityId: +activityId,
      qrId,
      qrPaymentInfo: {
        totalPrice: Math.round(totalPrice * 100),
        canNotUsePromotionAmount: Math.round(canNotUsePromotionAmount * 100),
      },
      buyer: {
        buyerId,
        buyerPhone,
      },
      extra,
      qrShopInfoDTO: {
        kdtId,
      },
    };

    if (fansId) {
      qrCodeTradeRequestDTO.fans = {
        fansId,
        fansNickName: fansNickname,
        type: fansType,
        youzanFansId,
        outerUserId,
      };
    }

    const data = await new QrCodeTradeService(ctx).pay(qrCodeTradeRequestDTO);
    ctx.json(0, '', data);
  }

  // 修改付款码
  async postEditQrCode(ctx: Context) {
    const userId = this.buyerId;
    const requestBody = ctx.getPostData();
    const {
      qrId,
      useDiscount,
      needUpdatePromotionInfo = false,
      needUpdateLabelsInfo = false,
    } = requestBody;

    const qrCodeUpdateDTO: QrCodeUpdateRequestParam = {
      operateId: userId,
      qrId,
      useDiscount,
      needUpdatePromotionInfo,
      needUpdateLabelsInfo,
    };
    const result = await new QrCodeUpdateService(ctx).editQrCode(
      qrCodeUpdateDTO
    );
    ctx.json(0, '', result);
  }

  // 检查店铺认证状态
  async checkCertification(ctx: Context) {
    // 检查是否是微小店，禁用微小店扫码付款功能
    if (ctx.isYouzanwxd) {
      ctx.json(0, '', { success: 0 });
      return false;
    }

    const data = await new QrCodeTradeService(ctx).checkCertification({
      kdtId: ctx.query.kdt_id || ctx.kdtId,
      source: 'wsc-h5-trade',
    });

    // 检查店铺是否在有效期内，并且店铺已认证，否则禁止使用扫码付款功能
    if (!data || !data.shopAuthenticated || !data.shopValid) {
      ctx.json(0, '', { success: 0 });
      return false;
    }
    ctx.json(0, '', { success: 1 });
  }

  /**
   * 获取订单详情
   * @param {String} kdtId 店铺ID
   * @param {String} orderNo 订单ID
   */
  public async getOrderInfo(kdtId: number, orderNo: string) {
    const { ctx } = this;
    const params = {
      orderNo,
      kdtId,
      buyerId: 0,
      customerId: 0,
      customerType: 0,
      sourceName: this.sourceName,
    };
    try {
      return await new OrderDetailService(ctx).lightDetailByOrderNo(params);
    } catch (err) {
      throw new PageException(10500, '获取订单详情失败, ' + err.toString());
    }
  }

  /**
   * 根绝QrId查询扫码付款信息
   * @param {String} qrId 订单ID
   */
  public async getQrCodeById(qrId: number) {
    const { ctx } = this;
    const service = new QrCodeTradeService(ctx);
    return service.getQrCodeById({
      qrId: +qrId,
      needLabelInfo: false,
      needPromotionInfo: false,
    });
  }

  /**
   * 渲染首页
   */
  public async getIndexHtml(ctx: Context): Promise<void> {
    await this.needPlatformAcl();

    const { kdt_id: kdtId, qr_id: qrId } = ctx.query;

    return ctx.redirect(
      `https://cashier.youzan.com/pay/wscassets_cashierQr?qr_id=${qrId}&kdt_id=${kdtId}`
    );
  }

  /**
   * 渲染支付成功页面
   */
  public async getSuccessHtml(ctx: Context): Promise<void> {
    await this.needPlatformAcl();

    const { order_no: orderNo = '', kdt_id: kdtId } = ctx.query;

    if (!orderNo.trim()) {
      const error = new PageException(10500, '无此订单' + orderNo);
      throw error;
    }
    try {
      const qrCodeTradeService = new QrCodeTradeService(ctx);
      const orderP = this.getOrderInfo(+kdtId, orderNo); // 获取订单详情
      const qrPayP = qrCodeTradeService.getQrCodePayByOrderNo({
        orderNo,
        payStatus: '1',
      }); // 获取订单对应的付款二维码信息
      const scanReduceP = qrCodeTradeService.getUmpScanReduce(+kdtId, orderNo); // 获取扫码优惠信息
      const [order, qrPay, scanReduce = {}] = await Promise.all([
        orderP,
        qrPayP,
        scanReduceP,
      ]);
      const qrCode = await this.getQrCodeById(qrPay.qrId); // 根据qrId查询扫码付款信息
      delete order.buyerInfo; // 过滤掉买家的敏感信息
      delete order.mainOrderInfo?.afterSaleContact; // 过滤用户联系信息
      ctx.setGlobal({
        order,
        qrCode,
        message: '支付成功',
        isSuccess: 1,
        scanReduce,
        // @ts-ignore
        homePageUrl: new WapUrl(ctx).getHomePageUrlByKdtId(kdtId),
      });
      const mpData: any = ctx.getState('mpData' as never) || {};
      await ctx.render('cashier/success', {
        title: mpData.shopName,
      });
    } catch (err) {
      throw new PageException(10500, err.toString());
    }
  }

  /**
   * 渲染支付失败页面
   */
  public async getFailHtml(ctx: Context): Promise<void> {
    await this.needPlatformAcl();

    const {
      order_no: orderNo = '',
      type = 'unpaid',
      kdt_id: kdtId,
    } = ctx.query;

    if (!orderNo.trim()) {
      const error = new PageException(10500, '无此订单' + orderNo);
      throw error;
    }
    try {
      const qrCodeTradeService = new QrCodeTradeService(ctx);
      const orderP = this.getOrderInfo(+kdtId, orderNo); // 获取订单详情
      const qrPayP = qrCodeTradeService.getQrCodePayByOrderNo({
        orderNo,
        payStatus: '1',
      }); // 获取订单对应的付款二维码信息
      const scanReduceP = qrCodeTradeService.getUmpScanReduce(+kdtId, orderNo); // 获取扫码优惠信息
      const [order, qrPay, scanReduce = {}] = await Promise.all([
        orderP,
        qrPayP,
        scanReduceP,
      ]);
      const qrCode = await this.getQrCodeById(qrPay.qrId); // 根据qrId查询扫码付款信息
      let message = '';
      if (type === 'unpaid') {
        message = '订单未支付';
      } else if (type === 'closed') {
        message = '订单已关闭';
      }
      ctx.setGlobal({
        order,
        qrCode,
        message,
        isSuccess: 0,
        scanReduce,
        // @ts-ignore
        homePageUrl: new WapUrl(ctx).getHomePageUrlByKdtId(kdtId),
      });
      const mpData: any = ctx.getState('mpData' as never) || {};
      await ctx.render('cashier/fail', {
        title: mpData.shopName,
      });
    } catch (err) {
      throw new PageException(10500, err.toString());
    }
  }
}

export = CashierController;