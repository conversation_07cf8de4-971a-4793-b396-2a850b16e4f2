import BaseService from '../base/BaseService';

class MultiStoreSettingService extends BaseService {
  public get SERVICE_NAME(): string {
    return 'com.youzan.multistore.center.api.service.setting.MultiStoreSettingService';
  }

  // 获取多网点单项配置（店铺级配置）
  public async getShopConfig(kdtId: number, key: string): Promise<any> {
    const result = await this.invoke(this.SERVICE_NAME, 'getShopConfig', [kdtId, key]);
    return result;
  }
}

export default MultiStoreSettingService;
