import mapKeysToCamelCase from '@youzan/utils/string/mapKeysToCamelCase';
import PayBaseController from './PayBaseController';
import WscPayService from '../../services/pay/WscPayService';
import TradeService from '../../services/trade/TradeService';
import {
  IPayChannelsReq,
  IPayReq,
  IWscBankCardPayRsp,
  IBankCardSubmit,
  IWeappBankCardPayRsp,
  IPayRsp
} from 'definitions/pay/WscPay';

/**
 * 微商城 - 小程序支付相关接口
 */
class WscWeappPayController extends PayBaseController {
  /**
   * 获取支付列表
   */
  public async postPayChannelsJson() {
    const ctx = this.ctx;
    const postData = mapKeysToCamelCase(ctx.getPostData() || {});
    const isWeappWebview = ctx.isWeapp && !ctx.isWeappNative;

    const cashierBizExt = this.getWeappPayBizExt(postData.orderMark);

    // webview
    if (isWeappWebview) {
      cashierBizExt.BANK_CARD_PAY = 'v1';
      cashierBizExt.INSTALMENT_PAY = 'v1';
    }

    const payParams: IPayChannelsReq = {
      cashierSign: postData.cashierSign,
      cashierSalt: postData.cashierSalt,
      prepayId: postData.prepayId,
      buyerId: this.buyer.buyerId, // 必须是字符串
      environment: this.getEnv(), // 小程序环境
      cashierBizExt: JSON.stringify(cashierBizExt),
      scene: postData.scene
    };
    const payChannelsResp = await new WscPayService(ctx).getWeappPayChannels(postData.partnerId, payParams);

    const payChannels = (payChannelsResp && payChannelsResp.pay_channels) || [];
    // TODO 临时处理 信用卡支付改为银行卡支付 后续收银台增加支付方式 ********
    payChannels.forEach((channel: any) => {
      if (channel.pay_channel === this.PAY_CHANNEL.CREDIT_CARD) {
        channel.pay_channel_name = '银行卡支付';
      }
    });
    ctx.r(0, '处理成功', payChannelsResp);
  }

  /**
   * 支付接口
   */
  public async postPayJson() {
    const ctx = this.ctx;
    const postData = mapKeysToCamelCase(ctx.getPostData() || {});
    const {
      verify_weixin_openid: verifyWeixinOpenid = '' // 自有粉丝 openId
    } = ctx.getLocalSession();
    const isWeappWebview = ctx.isWeapp && !ctx.isWeappNative;

    let verifyType = this.getSupportVerifyType();
    // 爱逛临时处理 || weappWebview
    if (postData.orderMark === 'weapp_guang' || isWeappWebview) {
      verifyType = 'v1';
    }

    const payDataExt = {
      SUPPORT_VERITY_TYPE: verifyType
    };

    // 适配教育小程序 webview 的逻辑, 把 wxSubOpenId 从组件传递的 outBizCtx 中取出来.
    let outBizCtx: { [key: string]: any } = {};
    try {
      outBizCtx = JSON.parse(postData.outBizCtx || '{}');
    } catch (e) {}

    const { wxSubOpenId: outBizWxSubOpenId = '', ...restOutBizCtx } = outBizCtx;
    const payTool: string = postData.payTool;
    const payData: IPayReq = {
      cashierSign: postData.cashierSign,
      cashierSalt: postData.cashierSalt,
      prepayId: postData.prepayId,
      payTool,
      buyerId: this.buyer.buyerId,
      wxSubOpenId: postData.wxSubOpenId || verifyWeixinOpenid || outBizWxSubOpenId,
      payToken: postData.payToken || postData.password,
      acceptPrice: postData.acceptPrice || 0,
      newPrice: postData.newPrice || -1,
      valueCardNo: postData.valueCardNo,
      bizExt: JSON.stringify(restOutBizCtx),
      environment: this.getEnv(), // 小程序环境
      cashierBizExt: JSON.stringify(payDataExt),
      payReqFrom: this.sourceName
    };

    switch (payTool) {
      case this.PAY_CHANNEL.ECARD:
        // E卡需要账号信息
        payData.account = this.buyer.buyerPhone;
        break;
      case this.PAY_CHANNEL.CREDIT_CARD:
      case this.PAY_CHANNEL.BANK_CARD:
        // 信用卡处理
        payData.payRouter = 'credit_mock';
        break;
      case this.PAY_CHANNEL.INSTALMENT:
        payData.payRouter = 'instalment_mock';
        break;
      default:
        break;
    }

    let payResult;
    if (payTool === this.PAY_CHANNEL.CASH_ON_DELIVERY) {
      const { orderNo = '' } = postData.nodeExtra || {};
      payResult = await new TradeService(ctx).codPay(orderNo);
    } else {
      payResult = await new WscPayService(ctx).pay102(postData.partnerId, payData);
      payResult = mapKeysToCamelCase(payResult);
      payResult = this.formatPayResult(postData, payResult, isWeappWebview);
    }
    ctx.r(0, '处理成功', payResult);
  }

  /**
   * 格式化支付结果数据
   */
  private formatPayResult(payParams: any, payResult: IPayRsp, isWeappWebview: boolean) {
    let payReturn = {} as any;
    let deepLinkInfo = {};
    const payTool: string = payParams.payTool;
    // 微信支付 && 银行卡支付
    if (payResult.deepLinkInfo && typeof payResult.deepLinkInfo === 'string') {
      try {
        deepLinkInfo = JSON.parse(payResult.deepLinkInfo);
      } catch (e) {
        // eslint-disable-next-line no-console
        console.error(e);
      }
    }

    const partnerReturnUrl = payResult.partnerReturnUrl;
    const phase: string = this.getPayPhase(partnerReturnUrl);
    // 以下银行卡支付两种情况，继续保留pay_data字段，兼容未升级的小程序版本，新的小程序用以下字段，做webview加载
    if (payTool === this.PAY_CHANNEL.BANK_CARD || payTool === this.PAY_CHANNEL.CREDIT_CARD) {
      payReturn = this.formatBankCardResult(deepLinkInfo, payTool, isWeappWebview, phase);
    } else if (payTool === this.PAY_CHANNEL.INSTALMENT) {
      // 分期支付接口判断跳转注册或分期详情页面
      const { availableStatus = '', kdtId = 0, period = '' } = payParams.nodeExtra || {};
      payReturn = this.formatInstalmentPayResult(
        payTool,
        { ...payParams, availableStatus, kdtId, period },
        payResult.partnerReturnUrl
      );
    } else {
      payReturn.phase = phase;
      payReturn.payData = deepLinkInfo;
    }

    // 改价
    if (payResult.operation === 'ADJUST_PRICE') {
      payReturn.operation = payResult.operation;
      payReturn.newPrice = payResult.newPrice || -1;
      // 以下两字段为兼容小程序webview下单支付保留
      payReturn.adjustPrice = true;
      payReturn.msg = `商家已将交易金额修改为${(payReturn.newPrice / 100).toFixed(2)}元，是否继续支付?`;
    }

    return payReturn;
  }

  private formatBankCardResult(deepLinkInfo: any, payTool: string, isWeappWebview: boolean, phase: string) {
    let payResult: IWscBankCardPayRsp | IWeappBankCardPayRsp;
    const submitUrl: string =
      payTool === this.PAY_CHANNEL.BANK_CARD
        ? 'https://cashier.youzan.com/pay/wsctrade/bankcard/v3'
        : 'https://cashier.youzan.com/pay/wsctrade/bankcard';

    if (isWeappWebview) {
      const submitPayData = {
        submitUrl,
        submitDomain: 'https://cashier.youzan.com',
        submitTitle: '银行卡支付',
        submitMethod: 'GET',
        submitData: Object.assign({ phase }, deepLinkInfo)
      } as IBankCardSubmit;

      const bankCardPayData = {
        payData: submitPayData
      } as IWscBankCardPayRsp;

      payResult = bankCardPayData;
    } else {
      let bankCardPayData: IWeappBankCardPayRsp = {
        payData: Object.assign({}, deepLinkInfo),
        submitUrl,
        submitDomain: 'https://cashier.youzan.com',
        submitTitle: '银行卡支付',
        submitMethod: 'GET',
        submitData: Object.assign({ phase }, deepLinkInfo)
      } as IWeappBankCardPayRsp;

      payResult = bankCardPayData;
    }
    return payResult || {};
  }

  // 获取多网点设置
  public async postMultiStoreSetting() {
    const ctx = this.ctx;
    const postData = ctx.getPostData();
    const data = await new WscPayService(ctx).getMultiStoreSetting(postData);

    ctx.r(0, '请求成功', data);
  }
}

export = WscWeappPayController;
