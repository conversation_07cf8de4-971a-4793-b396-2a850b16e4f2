import BaseService from '../base/BaseService';
import { BizType } from '../../constants/bizType';

/* com.youzan.delivery.service.TimeBucketService -  */
class TimeBucketService extends BaseService {
  get TIME_BUCKET_SERVICE() {
    return "com.youzan.delivery.service.TimeBucketService";
  }

  /**
   *  根据时间片查询限单情况
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1322202
   *
   *  @param {Object} param -
   *  @param {Array.<Object>} param.timeBucketList[] - 时间区间
   *  @param {string} param.timeBucketList[].index - 数组选项唯一Key，用于匹配请求数据与响应数据
   *  @param {string} param.timeBucketList[].startTime - 开始时间 闭区间 包含 yyyy-MM-dd HH:mm:ss
   *  @param {string} param.timeBucketList[].endTime - 结束时间 开区间 不包含 yyyy-MM-dd HH:mm:ss
   *  @param {number} param.dispatchWarehouseId - 派单的仓库
   *  @param {number} param.buyerId - 消费者id
   *  @return {Promise}
   */
  async queryOrderLimit(param: {
    buyerId: number;
    bizType: BizType;
    dispatchWarehouseId: number;
    timeBucketList: Array<{
      index: number;
      startTime: string;
      endTime: string;
    }>;
    offlineId?: number;
  }) {
    return this.invoke(this.TIME_BUCKET_SERVICE, "queryOrderLimit", [param]);
  }
}

export = TimeBucketService;