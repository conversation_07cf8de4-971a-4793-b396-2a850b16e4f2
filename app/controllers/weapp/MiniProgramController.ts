import { Context } from 'astroboy';
import MiniProgramUrlService from '../../services/channels/MiniProgramUrlService'

import BaseController = require('../base/BaseController');

class MiniProgramController extends BaseController {
  // 生成小程序的urlLink的中间链接--有赞短链和长链
  async getUrlLinkRedirectUrl(ctx: Context) {
    const { kdtId, url } = ctx.query;

    let path = url;
    let query = '';

    if (url.includes('?')) {
      [path, query] = url.split('?');
    }

    const params = {
      kdtId,
      path,
      query,
      urlDesc: '',
      expiredTime: new Date().getTime() + 1000 * 60 * 5,
    };

    const res = await new MiniProgramUrlService(ctx).generateUrlLinkRedirectUrl(params);
    ctx.json(0, 'ok', res);
  }
}

export = MiniProgramController;
