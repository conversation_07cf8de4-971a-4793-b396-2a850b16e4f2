<template>
  <div class="wrapper" v-if="formatComboDetail && formatComboDetail.length">
    <div class="title">{{ formatTitle }}已包含</div>
    <div class="combo-list">
      <div class="combo-list__item" v-for="item in formatComboDetail" :key="item.itemId">
        <div class="combo-list__item__image" v-if="item.picture[0] && item.picture[0].url">
          <img :src="item.picture[0].url" />
        </div>
        <div class="combo-list__main">
          <div class="combo-list__main-title">{{ item.title }}</div>
        </div>
        <div class="combo-list__item__specification" v-if="item.skuInfo">{{ item.skuInfo }};</div>
      </div>
    </div>
  </div>
  <div class="placeholder" v-else />
</template>

<script>
export default {
  name: 'combine-goods-block',
  components: {},
  props: {
    chosedComboDetail: Object,
    selectedSkuComb: Object,
  },
  data() {
    return {};
  },
  computed: {
    formatTitle() {
      try {
        if (this.selectedSkuComb?.sku) {
          return JSON.parse(this.selectedSkuComb.sku)[0].v;
        }
      } catch (error) {
        return '';
      }
      return '';
    },
    formatComboDetail() {
      if (!this.chosedComboDetail) return null;
      const result = [];
      try {
        this.chosedComboDetail.comboSubItemModels?.forEach((item) => {
          if (item.isDisplay === 1) {
            const { skuRelatedModels = [], picture = '[]', propModels = [] } = item;
            const skuInfoArr = []; // 属性描述
            propModels.forEach((propModel) => {
              const { textModels = [] } = propModel;
              const textDesc = textModels.map((textModel) => textModel.textName);
              skuInfoArr.push(textDesc);
            });
            const goodsImg = JSON.parse(picture);
            skuRelatedModels.forEach((skuRelatedModel) => {
              const { sku, combineNum = 1 } = skuRelatedModel;
              const skuArr = (sku && JSON.parse(sku)) || [];
              const skuString = skuArr
                .map((item) => item.v)
                .concat(skuInfoArr)
                .join(';'); // 子商品规格描述
              result.push({
                title: `${item.title} ×${combineNum}`,
                picture: goodsImg,
                skuInfo: skuString,
              });
            });
          }
        });
      } catch (err) {
        // console.error(err);
      }
      return result;
    },
  },
};
</script>

<style lang="scss" scoped>
::-webkit-scrollbar {
  display: none; /* Chrome Safari */
}
.placeholder {
  height: 211.5px;
}
.wrapper {
  background: #fff;
  overflow: hidden;
  position: relative;
  margin-top: -12px;
  height: 223.5px;

  &::after {
    content: '';
    border-top: 1px solid #ebedf0;
    position: absolute;
    bottom: 0;
    left: calc(-50% + 32px);
    right: calc(-50% + 32px);
    transform: scale(0.5);
  }
}
.title {
  font-size: 14px;
  color: #969799;
  height: 18px;
  margin: 12px 16px;
}
.combo-list {
  overflow-x: auto;
  display: flex;
  padding: 0 16px;
  &__main {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    margin-top: 8px;
    height: 32px;
    &-title {
      color: #323233;
      font-size: 12px;
      line-height: 16px;
      font-weight: 400;
      -webkit-line-clamp: 2;
    }
  }
  &__item {
    width: 104.5px;
    margin-right: 8px;
    padding-bottom: 12px;
    &__image {
      img {
        width: 104.5px;
        height: 104.5px;
        border-radius: 8px;
        object-fit: cover;
      }
    }
    &__specification {
      margin-top: 4px;
      color: #969799;
      font-size: 12px;
      height: 18px;
      line-height: 18px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      -webkit-line-clamp: 1;
    }
  }
  &__item:last-of-type {
    margin-right: 0;
  }
}
</style>
