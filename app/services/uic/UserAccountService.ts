import BaseService from '../base/BaseService';
import { IUserPrivacyAuthDto } from 'definitions/uic/user-privacy-auth';

class UserAccountService extends BaseService {
  SERVICE_NAME = 'com.youzan.uic.auth.api.service.UserAccountService';

  /**
   * 用户更新店铺授权信息，当用户点击允许或拒绝后，状态从未授权更改为授权
   */
  public async updateAddressAuth(params: any): Promise<any> {
    return this.invoke('updateAddressAuth', [params]);
  }

  /**
   * 用户授权权限信息：手机号授权(1)、昵称头像授权(2)、个人隐私协议签署(3)
   */
  userPrivacyAuth(params: IUserPrivacyAuthDto) {
    return this.invoke('userPrivacyAuth', [params]);
  }
}

export default UserAccountService;
