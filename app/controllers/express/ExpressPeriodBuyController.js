const BaseController = require('../base/BaseController');
const ExpressPeriodBuyService = require('../../services/express/ExpressPeriodBuyService');

class ExpressPeriodBuyController extends BaseController {
  async init() {
    await super.init({ initCopyrightFooter: !this.ctx.acceptJSON });
  }

  // H5 - 周期购物流配送详情页
  async getIndexHtml(ctx) {
    await ctx.render('express/period-buy/delivery-detail.html');
  }

  // H5 - 周期购物流 周期操作页-顺延
  async getPostponeHtml(ctx) {
    ctx.setState('title', '顺延');
    ctx.setGlobal('indexRoute', 'postpone');

    await ctx.render('express/period-buy/delivery-period.html');
  }

  // H5 - 周期购物流 周期操作页-取消顺延
  async getCancelPostponeHtml(ctx) {
    ctx.setState('title', '取消顺延');
    ctx.setGlobal('indexRoute', 'cancelpostpone');

    await ctx.render('express/period-buy/delivery-period.html');
  }

  /**
   * 获取配送期次列表信息
   */
  async getIssueCalendarJson(ctx) {
    const { kdt_id = 0, order_no = '', item_id = 0 } = ctx.query;
    const queryData = {
      kdtId: Number(kdt_id || ctx.kdtId),
      orderNo: order_no,
      itemId: String(item_id),
      buyerId: this.buyerId,
    };
    const result = await new ExpressPeriodBuyService(ctx).getDeliveryCalendar(queryData); // MockCalendarData.data;
    ctx.r(0, '', result);
  }

  /**
   * 获取期次详情信息
   */
  async getIssueDetailJson(ctx) {
    const { order_no = '', item_id = 0, issue } = ctx.query;
    const queryData = {
      orderNo: order_no,
      itemId: String(item_id),
      issue,
      buyerId: this.buyerId,
    };
    const result = await new ExpressPeriodBuyService(ctx).getPeriodDeliveryDetail(queryData); // MockIssueData.data;
    ctx.r(0, '', result);
  }

  /**
   * 顺延配送期次
   */
  async postponeIssueJson(ctx) {
    const { kdt_id = 0, order_no = '', item_id = 0, postpone_items = [] } = ctx.request.body;
    const queryData = {
      kdtId: Number(kdt_id || ctx.kdtId),
      orderNo: order_no,
      itemId: String(item_id),
      buyerId: this.buyerId,
      postponeItems: postpone_items
    };
    const result = await new ExpressPeriodBuyService(ctx).postponeIssue(queryData);
    ctx.r(0, '', result);
  }

  /**
   * 取消顺延配送期次
   */
  async cancelPostponeIssueJson(ctx) {
    const { kdt_id = 0, order_no = '', item_id = 0, postpone_items = [] } = ctx.request.body;
    const queryData = {
      kdtId: Number(kdt_id || ctx.kdtId),
      orderNo: order_no,
      buyerId: this.buyerId,
      itemId: String(item_id),
      postponeItems: postpone_items
    };
    const result = await new ExpressPeriodBuyService(ctx).cancelPostponeIssue(queryData);
    ctx.r(0, '', result);
  }
}

module.exports = ExpressPeriodBuyController;
