import BaseService from '../../../base/BaseService';
import {
  IShelfOrderProgressRequest,
  IShelfOrderProgressResponse,
} from 'definitions/api/retail/misc/ShelfProgressService/queryOrderProgress';

/**
 * 零售订单进度查询服务
 * Service: com.youzan.retail.trade.misc.api.service.shelf.ShelfProgressService
 */
export default class ShelfProgressService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.retail.trade.misc.api.service.shelf.ShelfProgressService';
  }

  /**
   * 查询零售订单进度
   * zanAPI文档地址: [待补充]
   *
   * @param {IShelfOrderProgressRequest} request - 订单进度查询请求参数
   * @param {string} request.orderNo - 订单号
   * @param {string} request.retailSource - 请求来源
   * @param {number} request.kdtId - 当前店铺id
   * @param {number} request.adminId - 当前用户id
   * @returns {Promise<IShelfOrderProgressResponse>} 订单进度查询结果
   */
  async queryOrderProgress(request: IShelfOrderProgressRequest): Promise<IShelfOrderProgressResponse> {
    return this.invoke('queryOrderProgress', [request]);
  }
}
