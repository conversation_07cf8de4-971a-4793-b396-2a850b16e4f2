import BaseController from '../base/BaseController';
import { Context } from 'astroboy';
import OrderDrugService from '../../services/order/OrderDrugService';
import KdtBuyerIdentificationService from '../../services/api/identity/KdtBuyerIdentificationService';

class OrderDrugController extends BaseController {
  async init() {
    await super.init({
      initGlobalTheme: true,
    });
  }

  // 查询页面
  async getIndexHtml(ctx: Context) {
    const query = ctx.getQueryData();
    const { rxNo } = query;
    const params = {
      rxNo,
      adminId: this.buyerId,
      kdtId: ctx.kdtId,
      retailSource: 'wsc-h5-trade',
    };
    // 查看处方单
    const result = await new OrderDrugService(ctx).get(params);
    const { patientInfoDTO, medicalInformationDTO } = result;

    ctx.setGlobal({
      patientInfo: patientInfoDTO,
      prescribeInfo: medicalInformationDTO,
    });
    await ctx.render('order/drug-message.html');
  }

  async getDrugAdvHtml(ctx: Context) {
    await ctx.render('order/drug-adv.html');
  }

  // 疾病史 获取默认疾病史
  async getDefaultDisease(ctx: Context) {
    const query = ctx.getPostData();

    const result = await new OrderDrugService(ctx).getDefaultDisease();
    ctx.json(0, 'ok', result);
  }

  // 处方信息页面，已确诊疾病的列表
  // 根据用药人和处方药 查询对应的确诊疾病列表
  async queryDiagnoseDisease(ctx: Context) {
    const query = ctx.getPostData();
    const { patientId, goodsIds } = query;
    const params = {
      patientId,
      goodsIds,
      adminId: this.buyerId,
      kdtId: ctx.kdtId,
      retailSource: 'wsc-h5-trade',
    };
    const result = await new OrderDrugService(ctx).queryDiagnoseDisease(params);
    ctx.json(0, 'ok', result);
  }

  // 搜索疾病接口
  async searchDiseaseInfo(ctx: Context) {
    const query = ctx.getPostData();
    const { diseaseName, pageSize, pageNo } = query;
    const params = {
      diseaseName,
      pageSize,
      pageNo,
      adminId: this.buyerId,
      kdtId: ctx.kdtId,
      requestId: `${Date.now()}_${this.buyerId}`,
      retailSource: 'wsc-h5-trade',
    };
    const result = await new OrderDrugService(ctx).searchDiseaseInfo(params);
    ctx.json(0, 'ok', result);
  }

  // 查询处方单
  async queryID(ctx: Context) {
    const query = ctx.getPostData();
    const params = {
      ...query,
      adminId: this.buyerId,
      kdtId: ctx.kdtId,
      retailSource: 'wsc-h5-trade',
    };
    const info = await new OrderDrugService(ctx).get(params);

    const { patientInfoDTO, medicalInformationDTO, prescribeInfoDTO, rxStatusDTO } = info;
    const { name, phoneNum } = patientInfoDTO!;
    const { diagnosedDiseaseDTOS } = medicalInformationDTO!;
    const result = {
      patientInfo: {
        name,
        phoneNum,
      },
      prescribeInfo: prescribeInfoDTO,
      medicalInformation: diagnosedDiseaseDTOS,
      rxStatus: rxStatusDTO,
    };
    ctx.json(0, 'ok', result);
  }

  // 创建处方单
  async create(ctx: Context) {
    const query = ctx.getPostData();
    const params = {
      ...query,
      adminId: this.buyerId,
      kdtId: ctx.kdtId,
      retailSource: 'wsc-h5-trade',
    };
    const result = await new OrderDrugService(ctx).create(params);
    ctx.json(0, 'ok', result);
  }

  // 查询用药人
  async queryUser(ctx: Context) {
    const query = ctx.getQueryData();
    const { patientId } = query;
    const params = {
      adminId: this.buyerId,
      kdtId: ctx.kdtId,
      retailSource: 'wsc-h5-trade',
    };
    if (patientId) {
      // @ts-ignore
      params.patientId = patientId;
    }
    const result = await new OrderDrugService(ctx).queryUser(params);
    ctx.json(0, 'ok', result);
  }

  // 创建用药人
  async createUser(ctx: Context) {
    const query = ctx.getPostData();
    const params = {
      ...query,
      adminId: this.buyerId,
      kdtId: ctx.kdtId,
      retailSource: 'wsc-h5-trade',
    };
    const result = await new OrderDrugService(ctx).creatUser(params);
    ctx.json(0, 'ok', result);
  }

  // 更新用药人
  async updateUser(ctx: Context) {
    const query = ctx.getPostData();
    const params = {
      ...query,
      adminId: this.buyerId,
      kdtId: ctx.kdtId,
      retailSource: 'wsc-h5-trade',
    };
    const result = await new OrderDrugService(ctx).updateUser(params);
    ctx.json(0, 'ok', result);
  }

  // 删除用药人
  async deleteUser(ctx: Context) {
    const query = ctx.getPostData();
    const params = {
      ...query,
      adminId: this.buyerId,
      retailSource: 'wsc-h5-trade',
    };
    const result = await new OrderDrugService(ctx).deleteUser(params);
    ctx.json(0, 'ok', result);
  }

  async identifyUser(ctx: Context) {
    const { identityCard, realname: name, identifiableTimesAtMost, identificationTimesLimitEnable } = ctx.getPostData();
    const { kdtId } = ctx;
    const { buyer } = this.ctx.getLocalSession();
    let nickName;
    if (buyer) {
      nickName = buyer.nick_name;
    } 

    const params = {
      kdtId,
      identifiableTimesAtMost,
      identificationTimesLimitEnable,
      requestSource: 'wsc-h5-trade',
      identityDTO: {
        identityCard,
        name,
        identityAccount: nickName || name,
        buyerId: this.buyerId || this.ctx.userId,
      },
    };
    const result = await new KdtBuyerIdentificationService(ctx).identifyKdtBuyer(params);
    ctx.json(0, 'ok', result);
  }

  async getSavedHospital(ctx: Context) {
    const { allHospitals } = await new OrderDrugService(ctx).getHospitalList({
      kdtId: ctx.kdtId,
      retailSource: 'wsc-h5-trade',
      adminId: this.buyerId,
    });

    return ctx.json(
      0,
      'ok',
      allHospitals.find(({ isCheck }) => isCheck)
    );
  }
}

export = OrderDrugController;
