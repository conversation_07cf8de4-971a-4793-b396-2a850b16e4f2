import BaseService from '../base/BaseService';

/**
 * com.youzan.trade.oom.api.dy.request.DyGetPayInfoRequest
 */
interface IDyGetPayInfoRequest {
  /** 订单号 */
  orderNo?: string;
  /** 店铺ID */
  kdtId?: number;
  /** buyerId */
  buyerId?: number;
}

/**
 * com.youzan.trade.oom.api.dy.request.DyApplyRefundParamRequest
 */
interface IDyApplyRefundParamRequest {
  /** 订单号 */
  orderNo?: string;
  /** 退款单号 */
  refundNo?: string;
  /** 店铺ID */
  kdtId?: number;
}

class DyTradeService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.trade.oom.api.dy.DyTradeService';
  }

  /**
   *  获取抖音支付信息
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1393400
   *
   *  @param {Object} requestDTO -
   *  @param {string} requestDTO.orderNo - 订单号
   *  @param {number} requestDTO.kdtId - 店铺ID
   *  @param {number} requestDTO.buyerId - buyerId
   *  @return {Promise}
   */
  async placeDyOrder(requestDTO: IDyGetPayInfoRequest) {
    return this.invoke('placeDyOrder', [requestDTO]);
  }

  /**
   *  获取抖音申请退款入参
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1424516
   *
   *  @param {Object} requestDTO -
   *  @param {string} requestDTO.orderNo - 订单号
   *  @param {string} requestDTO.refundNo - 退款单号
   *  @param {number} requestDTO.kdtId - 店铺ID
   *  @return {Promise}
   */
  async getApplyRefundParam(requestDTO: IDyApplyRefundParamRequest) {
    return this.invoke('getApplyRefundParam', [requestDTO]);
  }
}

export default DyTradeService;
