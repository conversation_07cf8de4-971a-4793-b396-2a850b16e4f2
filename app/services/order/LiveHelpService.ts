import BaseService from '../base/BaseService';

interface ILiveHelpActivityQueryParam {
  /** 店铺ID */
  shopId: number;
  /** 商品ID列表 */
  goodsIds: number[];
}

/**
 * com.youzan.ump.join.model.special.livehelp.LiveHelpActivityDTO 
 * LiveHelpActivityDTO
 */
interface ILiveHelpActivity {
  /** 直播计划活动alias */
  alias?: string;
  /** 直播计划活动开始时间 */
  startTime?: number;
  /** 直播计划活动ID */
  id?: number;
  /** 店铺ID */
  shopId?: number;
  /** 直播计划活动结束时间 */
  endTime?: number;
  /** 直播计划活动名称 */
  title?: string;
}

class LiveHelpService extends BaseService {
  get LIVE_HELP_SERVICE(): string {
    return "com.youzan.ump.join.api.special.LiveHelpService";
  }

  /**
   * 查询活动信息
   * 根据一批商品ID查询
   * zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1048402
   * @param {Object} param
   * @param {string} param.liveScene - 直播来源场景
   * @param {number} param.shopId - 店铺ID
   * @param {Array.<Array>} param.goodsIds[] - 商品ID列表
   * @param {Array} param.goodsIds[]
   * @return {Promise}
   */
  getActivity(param: ILiveHelpActivityQueryParam): Promise<ILiveHelpActivity> {
    return this.invoke(this.LIVE_HELP_SERVICE, "getActivity", [param]);
  }
}

export default LiveHelpService;
