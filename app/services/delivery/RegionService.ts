import BaseService from '../base/BaseService';
import { IRegionQueryParam } from 'definitions/other/Region';

class RegionService extends BaseService {
  get REGION_SERVICE() {
    return 'com.youzan.delivery.service.RegionV3Service';
  }

  // 获取全部省市区数据
  async getAllRegion(params: any) {
    const data = await this.invoke(this.REGION_SERVICE, 'getAllIdNameMap', [
      params,
    ]);

    const province = data.Province;
    const city = data.City;
    const county = data.County;

    // 格式化海外地址
    if (params.showOversea && data.Country) {
      Object.assign(city, data.Country);
      Object.keys(province).forEach((code) => {
        if (code[0] === '9') {
          county[code] = province[code];
          delete province[code];
        }
      });
      province['900000'] = '海外';
    }

    return {
      province_list: province,
      city_list: city,
      county_list: county,
    };
  }

  async getRegionIdByName(params: any) {
    const data = await this.invoke(
      this.REGION_SERVICE,
      'getRegionModelByName',
      [params]
    );
    return data;
  }

  getRegionByLevel(params: any) {
    return this.invoke(this.REGION_SERVICE, 'getRegionByLevel', [params]);
  }

  /**
   * 根据地址名称查询地址信息（国家、省市区id）
   * 支持兼容地址查询，返回兼容地址对应标准地址的信息
   * zan-api http://zanapi.qima-inc.com/site/service/view/163774
   */
  async getRegionModelByName(params: IRegionQueryParam) {
    return this.invoke(this.REGION_SERVICE, 'getRegionModelByName', [params]);
  }
}

export = RegionService;
