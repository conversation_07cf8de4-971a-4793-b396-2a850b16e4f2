import _ from 'lodash';
import BaseService from '../base/BaseService';
import { shouldAutoEnterShopForOrder } from '@youzan/plugin-h5-enter-shop';

class ShopConfigReadService extends BaseService {
  /**
   * 获取店铺会员积分配置名称
   * @param {*} kdtId
   */
  async queryShopPointsName(kdtId: number) {
    const result = await this.ctx.getShopConfigsWithKdtId(kdtId, ['scrm_credit_diy_name']).catch(() => {});

    let pointsNameData = result.scrm_credit_diy_name || '{}';

    try {
      pointsNameData = JSON.parse(pointsNameData);
    } catch (error) {
      pointsNameData = {};
    }

    return _.get(pointsNameData, 'name') || '积分';
  }

  async queryShopConfigsShowPosterPersonalInfo(kdtId: number) {
    const result = await this.ctx.getShopConfigsWithKdtId(kdtId, ['show_poster_personal_info']).catch(() => {});
    let data = result || {};
    if (typeof data === 'string') {
      try {
        data = JSON.parse(data);
      } catch (error) {
        data = {};
      }
    }
    const show = data.show_poster_personal_info ? +data.show_poster_personal_info : 0;
    return show;
  }

  async queryShopConfigs(kdtId: number) {
    const result = await this.ctx.getShopConfigsWithKdtId(kdtId, ['goods_trade_marquee']).catch(() => {});
    let data = result.goods_trade_marquee || '{}';
    try {
      data = JSON.parse(data);
    } catch (error) {
      data = {};
    }
    return data.show || 0;
  }


  async queryShopConfigsBuyerMsg(kdtId: number) {
    const result = await this.ctx.getShopConfigsWithKdtId(kdtId, ['payment_setting_buyer_message']).catch(() => {});
    let data = result.payment_setting_buyer_message || '';
    try {
      data = JSON.parse(data);
    } catch (error) {
      data = "";
    }
    return data;
  }



  async getOrderEnterShopPolicy() {
    return await shouldAutoEnterShopForOrder(this.ctx as any);
  }
}

export = ShopConfigReadService;
