// TODO 检查cdnImage是否正确，确保不要裂开图片
import { cdnImage } from '../../../../lib/UrlUtils';
import { BtnConf, ButtonConfig, ConditionParams } from './types';

const PING = '\u8bc4';
const JIA = '\u4ef7';

const EVALUATION = PING + JIA;

export const BTN_CONF: BtnConf = {
  cancel: {
    text: '取消订单',
    value: 'cancel',
    condition: ({ orderPermission }) => orderPermission.isShowCancelOrder,
  },
  cancelPayOnDelivery: {
    text: '取消订单',
    value: 'cancelPayOnDelivery',
    condition: ({ orderItem, orderMark }) => +orderItem.buyWay === 9 && orderItem.statusCode === 'tosend' && orderMark !== 'weapp_guang',
  },
  topay: {
    text: '立即付款',
    value: 'topay',
    condition: ({ orderPermission, orderItem }) =>
      !!(orderPermission.isShowTopay && orderItem.confirmUrl),
  },
  transport: {
    text: '查看物流',
    value: 'transport',
    condition: ({ orderPermission, orderItem }) =>
      !!(orderPermission.allowViewLogistics && orderItem.expressUrl),
  },
  topayDeposit: {
    text: '支付定金',
    value: 'topayDeposit',
    condition: ({ orderPermission }) => orderPermission.isShowTopayPresaleDownpayment,
  },
  topayRetainage: {
    text: '支付尾款',
    value: 'topayRetainage',
    condition: ({ orderPermission }) => orderPermission.isShowTopayPresaleFinalpayment,
  },
  topayRetainageDisabled: {
    text: '支付尾款',
    value: 'topayRetainageDisabled',
    disabled: true,
    condition: ({ orderPermission }) => orderPermission.isShowBeforePresaleFinalpayment,
  },
  confirmReceive: {
    text: '确认收货',
    value: 'confirmReceive',
    condition: ({ orderPermission, orderExtra }) =>
      orderPermission.isAllowConfirmReceive && !orderExtra.isHotel,
  },
  confirmReceiveHotel: {
    text: '确认入住',
    value: 'confirmReceiveHotel',
    condition: ({ orderPermission, orderExtra }) =>
      orderPermission.isAllowConfirmReceive && orderExtra.isHotel,
  },
  laterReceive: {
    text: '延长收货',
    value: 'laterReceive',
    condition: ({ orderPermission }) => orderPermission.isAllowLaterReceive,
  },
  buyAgain: {
    text: '再来一单',
    value: 'buyAgain',
    showCoupon: true,
    condition: ({ orderPermission, orderExtra, isXhsApp, isKsApp }) =>
      !orderPermission.isAllowDirectBuyAgain &&
      orderPermission.isAllowBuyAgain &&
      !orderExtra.isMallGroupBuy &&
      !isXhsApp &&
      !isKsApp,
  },
  directBuyAgain: {
    text: '再来一单',
    value: 'directBuyAgain',
    showCoupon: true,
    condition: ({
      orderPermission,
      directBuyAgainBtnConfig,
      isAlipayApp,
      isQQApp,
      isXhsApp,
      isKsApp,
    }) =>
      orderPermission.isAllowDirectBuyAgain &&
      directBuyAgainBtnConfig.show &&
      !isAlipayApp &&
      !isQQApp &&
      !isXhsApp &&
      !isKsApp,
  },
  evaluate: {
    text: `立即${EVALUATION}`,
    value: 'evaluate',
    condition: ({ orderPermission, orderItem }) =>
      !!(orderPermission.isShowEvaluate && orderItem.evaluateUrl),
  },
  lotteryResult: {
    text: '抽奖结果',
    value: 'lotteryResult',
    condition: ({ orderPermission, orderItem }) =>
      !!(orderPermission.allowViewLotteryResult && orderItem.lotteryResultUrl),
  },
  inviteHelp: {
    text: '邀请助力',
    value: 'inviteHelp',
    condition: ({ orderPermission }) => orderPermission.isShowInviteHelp,
  },
  deleteOrder: {
    text: '删除订单',
    value: 'deleteOrder',
    condition: ({ orderPermission }) => orderPermission.allowShowDeleteOrder,
  },
  expressReminderHotel: {
    text: '提醒接单',
    value: 'expressReminderHotel',
    condition: ({ orderPermission, orderExtra }) =>
      orderPermission.isShowReminder && orderExtra.isHotel,
  },
  expressReminder: {
    text: '提醒发货',
    value: 'expressReminder',
    condition: ({ orderPermission, orderExtra }) =>
      orderPermission.isShowReminder && !orderExtra.isHotel,
  },
  paidPromotion: {
    text: '支付有礼',
    value: 'paidPromotion',
    condition: true,
  },
  coupon: {
    text: '领优惠券',
    value: 'coupon',
    condition: true,
    icon: cdnImage('/public_files/e49f5b413529e624ab6a538642bf4eeb.png'),
  },
  fissionCoupon: {
    text: '抢优惠券',
    value: 'fissionCoupon',
    icon: cdnImage('/public_files/01ceebc40fa09f1902d679c1519012a4.png'),
    condition: ({ orderPermission }) => orderPermission.isAllowFissionCoupon,
  },
  liveQrCode1: {
    text: '专属客服',
    value: 'liveQrCode',
    icon: cdnImage('/public_files/8c404f5a2465baa843f1a847832772de.png'),
    condition: true,
  },
  liveQrCode2: {
    text: '加粉丝群',
    value: 'liveQrCode',
    condition: true,
    icon: cdnImage('/public_files/8c404f5a2465baa843f1a847832772de.png'),
  },

  /**
   * 以下按钮只有在小程序存在
   * grouponDetail
   * viewCardDetail
   * selfFetchCode
   */
  grouponDetail: {
    text: '拼团详情',
    value: 'grouponDetail',
    condition: ({ orderPermission, isWeapp }) => isWeapp && orderPermission.allowPintuanDetail,
  },
  viewCardDetail: {
    text: '查看卡券',
    value: 'viewCardDetail',
    condition: ({ orderPermission, isWeapp }) => isWeapp && orderPermission.allowViewCardDetail,
  },
  selfFetchCode: {
    text: '核销码',
    value: 'selfFetchCode',
    condition: ({ orderPermission, isWeapp }) => isWeapp && orderPermission.showSelfFetchScancode,
  },

  /**
   * 以下按钮只有在H5存在
   * viewEvaluate
   * gift
   * groupDetail
   * courseDetail
   * hotelComboUse
   * shared
   * peerpay
   */
  viewEvaluate: {
    text: `查看${EVALUATION}`,
    value: 'viewEvaluate',
    condition: ({ orderPermission, orderItem, isWeapp }) =>
      !isWeapp && !!(orderPermission.isShowViewEvaluate && orderItem.viewEvaluateUrl),
  },
  gift: {
    text: '查看礼单',
    value: 'gift',
    condition: ({ orderItem, isWeapp }) => !isWeapp && !!orderItem.giftUrl,
  },
  groupDetail: {
    text: '查看拼团',
    value: 'groupDetail',
    condition: ({ orderPermission, orderItem, orderExtra, isWeapp, isQQApp, isAlipayApp }) => {
      if (
        !isWeapp &&
        orderPermission.allowPintuanDetail &&
        orderItem.groupDetailUrl &&
        !isAlipayApp
      ) {
        if (isQQApp) {
          if (!orderExtra.isLotteryGroup) {
            return true;
          }
        } else {
          return true;
        }
      }
      return false;
    },
  },
  courseDetail: {
    text: '查看课程',
    value: 'courseDetail',
    condition: ({ orderPermission, orderItem, isWeapp, orderMark }) =>
      !isWeapp && !!(orderPermission.allowCourseDetail && orderItem.courseDetailUrl) && orderMark !== 'weapp_guang',
  },
  hotelComboUse: {
    text: '立即预约',
    value: 'hotelComboUse',
    condition: ({ orderPermission, isWeapp }) => !isWeapp && orderPermission.hotelPackageIsUseAble,
  },
  shared: {
    text: '分享好友',
    value: 'shared',
    condition: ({ orderPermission, isWeapp, isAlipayApp, isQQApp, isXhsApp }) =>
      !isWeapp && orderPermission.isAllowShareOrder && !isAlipayApp && !isQQApp && !isXhsApp,
  },
  peerpay: {
    text: '代付',
    value: 'peerpay',
    condition: ({ orderItem, isWeapp }) =>
      !isWeapp && +orderItem.buyWay === 7 && +orderItem.status !== 99,
  },
};

export const ALL_BTN_TYPES: string[] = Object.keys(BTN_CONF).reduce<string[]>(
  (prev, key) => [...prev, BTN_CONF[key as keyof typeof BTN_CONF].value],
  []
);

type PushButtonReturn = (() => Promise<boolean>) | ((params: ConditionParams) => Promise<boolean>);

export const pushButton = (
  actionBtns: any[],
  btn: ButtonConfig,
  customBtnConfig: Record<string, any> = {}
): PushButtonReturn => {
  if (btn === undefined) return () => Promise.resolve(false);
  const { condition = false, ...other } = btn || {};
  const btnConf = { ...other, ...customBtnConfig };
  if (typeof condition === 'boolean' && condition) {
    actionBtns.push(btnConf);
    return () => Promise.resolve(true);
  }
  if (typeof condition === 'function') {
    return (params: ConditionParams): Promise<boolean> => {
      return new Promise((resolve) => {
        if (condition(params)) {
          actionBtns.push(btnConf);
          resolve(true);
        } else {
          resolve(false);
        }
      });
    };
  }
  return () => Promise.resolve(false);
};
