export = [
  [
    'GET',
    '/wsctrade/order/footer.json',
    'order.TradeBizController',
    'getFooterJson'
  ],
  [
    'POST',
    '/wsctrade/order/buyAgain.json',
    'order.TradeBizController',
    'postBuyAgainJson'
  ],
  [
    'POST',
    '/wsctrade/order/remindExpress.json',
    'order.TradeBizController',
    'postRemindExpressJson'
  ],
  [
    'POST',
    '/wsctrade/order/cancelOrder.json',
    'order.TradeBizController',
    'postCancelOrderJson'
  ],
  [
    'POST',
    '/wsctrade/order/deleteOrder.json',
    'order.TradeBizController',
    'postDeleteOrderJson'
  ],
  [
    'POST',
    '/wsctrade/order/book.json',
    'order.TradeBizController',
    'postBookJson'
  ],
  [
    'POST',
    '/wsctrade/order/goodsBook.json',
    'order.TradeBizController',
    'postCacheJson'
  ],
  [
    'GET',
    '/wsctrade/order/money-to-where',
    'order.TradeBizController',
    [
      'checkRedirectToShopDomain',
      'getMoneyToWhereHtml'
    ]
  ],
  [
    'GET',
    '/wsctrade/order/cancel-order/reason-list.json',
    'order.TradeBizController',
    [
      'getCancelReasonList'
    ]
  ],
  [
    'POST',
    '/wsctrade/order/directBuyAgain.json',
    'order.TradeBizController',
    'postDirectBuyAgainJson'
  ],
  [
    'GET',
    '/wsctrade/order/getDirectBuyAgainBtnConfig.json',
    'order.TradeBizController',
    'getDirectBuyAgainBtnConfigJson'
  ],
  [
    'POST',
    '/wsctrade/order/static-config.json',
    'order.TradeBizController',
    'getStaticConfig'
  ],
  [
    'POST',
    '/wsctrade/order/queryOrderLimit',
    'order.OrderBuyController',
    'queryOrderLimit'
  ],
];
