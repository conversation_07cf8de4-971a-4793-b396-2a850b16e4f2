import { Session<PERSON>acheBuyer } from 'astroboy';
import UA from '@youzan/iron-base/app/lib/UA';
import BaseController from '../base/BaseController';
import CompareVersion from '../../lib/CompareVersion';
import {
  PayToolColorMap,
  CommonEnvPayToolColor,
} from '../../constants/PayTool.enum';
import { IWeappPayCashierBizExt } from 'definitions/pay/WscPay';
import args from '@youzan/utils/url/args';
import buildUrlWithCtx from '@youzan/utils/url/buildUrlWithCtx';
import { CommonPlatform } from '../../constants/platformMap';

const WEAPP_VERSION_SUPPORT_BANKCARD = '2.15.3';
const WEAPP_VERSION_SUPPORT_INSTALMENT = '2.30.2';
const MOMO_VERSION_SUPPORT_WX_H5 = '8.20.4';
const INKE_VERSION_SUPPORT_WX_H5 = '7.2.71';

/**
 * Project Base Controller
 */
class PayBaseController extends BaseController {
  public async init() {
    await super.init({
      initMpData: false,
      initMpAccount: false,
      initPlatform: true,
    });
  }

  public get PAY_CHANNEL() {
    return {
      ECARD: 'ECARD', // E卡支付
      WX_APPLET: 'WX_APPLET', // 小程序微信支付
      WX_JS: 'WX_JS', // H5 微信内部支付
      WX_H5: 'WX_H5', // H5 外部拉起微信支付
      WX_APP: 'WX_APP', // H5 拉起客户端微信支付
      WX_WAP: 'WX_WAP',
      BANK_CARD: 'BANK_CARD', // 银行卡支付（银联通道）
      CREDIT_CARD: 'CREDIT_CARD', // 信用卡支付
      BAIFUBAO_WAP: 'BAIFUBAO_WAP', // 储蓄卡支付（百付宝）
      INSTALMENT: 'INSTALMENT', // 分期支付
      CHANGE_PAY: 'CHANGE_PAY', // 有赞零钱
      CASH_ON_DELIVERY: 'CASH_ON_DELIVERY', // 货到付款
      UMP_PAY: 'UMP_PAY', // 红包全额抵扣
    };
  }

  public get OPERATION() {
    return {
      ADJUST_PRICE: 'ADJUST_PRICE', // 改价
      OFFER_MODIFY: 'OFFER_MODIFY', // 优惠信息变动，相关文档：https://doc.qima-inc.com/pages/viewpage.action?pageId=*********
    };
  }

  /**
   * 获取平台来源，用于后端生成对应的监控仪表盘
   * 格式为 `${platform}_{env}`
   * doc: https://doc.qima-inc.com/pages/viewpage.action?pageId=*********
   */
  get sourceName() {
    let platform = '';
    let env = '';

    const isYzCloudApp = /kdtUnion_([^\s]*)/i.test(this.ctx.userAgent);
    platform = isYzCloudApp ? 'yzcloudapp' : this.ctx.platform;
    platform = !CommonPlatform[platform] ? 'other' : platform;

    if (this.ctx.isWeapp) {
      env = 'weapp';
    } else if (this.ctx.isSwanApp) {
      env = 'bdapp';
    } else {
      env = 'webview';
    }

    return `${platform}_${env}`;
  }

  public getEnv(): string {
    const { userAgent } = this.ctx;
    const isYzCloudApp = /kdtUnion_([^\s]*)/i.test(this.ctx.userAgent);
    let env = '';

    if (this.ctx.isWeapp) {
      env = 'WX_APPLET';
    } else if (this.ctx.isAliApp) {
      env = 'ALIPAY_APPLET';
    } else if (this.ctx.isQQApp) {
      env = 'QQ_APPLET';
    } else if (this.ctx.isWeixin) {
      env = 'WX_JS';
    } else if (this.ctx.isQq) {
      env = 'QQWAP';
    } else if (this.ctx.isAlipay) {
      env = 'ALIPAYWAP';
    } else if (this.ctx.isTTApp) {
      env = 'TOUTIAO_APPLET';
    } else if (UA.isKuaishou(userAgent)) {
      env = 'KSHOUWAP';
    } else if (UA.isMomo(userAgent)) {
      env = 'MOMOWAP';
    } else if (UA.isWeibo(userAgent)) {
      env = 'WEIBOWAP';
    } else if (this.ctx.platform === 'huya') {
      env = 'HUYAWAP';
    } else if (this.ctx.platform === 'kugou') {
      env = 'KUGOUWAP';
    } else if (this.ctx.platform === 'iting') {
      env = 'XIMALAYAWAP';
    } else if (this.ctx.platform === 'inke') {
      env = 'INKEWAP';
    } else if (UA.isYouzanwxd(userAgent)) {
      env = 'WXD_APP';
    } else if (UA.isYouzanmars(userAgent)) {
      env = 'JX_APP';
    } else if (this.ctx.isSwanApp) {
      env = 'BD_APPLET';
    } else if (isYzCloudApp) {
      env = 'YZ_CLOUD_APP';
    }

    return env;
  }

  // v1：支持银行卡（银联）
  public getWeappPayBizExt(orderMark: string): IWeappPayCashierBizExt {
    let BANK_CARD_PAY = '';
    let INSTALMENT_PAY = '';
    if (this.ctx.isWeapp) {
      const extraData = this.ctx.xExtraData || {};
      if (!extraData.version) {
        // 空一般为测试，可以按支持对待
        BANK_CARD_PAY = 'v1';
        INSTALMENT_PAY = 'v1';
      } else if (orderMark === 'weapp_guang') {
        BANK_CARD_PAY = 'v1';
        INSTALMENT_PAY = 'v1';
      }

      if (
        CompareVersion.isGte(extraData.version, WEAPP_VERSION_SUPPORT_BANKCARD)
      ) {
        BANK_CARD_PAY = 'v1';
      }

      if (
        CompareVersion.isGte(
          extraData.version,
          WEAPP_VERSION_SUPPORT_INSTALMENT
        )
      ) {
        INSTALMENT_PAY = 'v1';
      }
    }

    return {
      BANK_CARD_PAY,
      INSTALMENT_PAY,
    };
  }

  // v1：短信验证码验证方式
  public getSupportVerifyType(): string {
    if (this.ctx.isWeapp) {
      const extraData = this.ctx.xExtraData || {};
      if (
        CompareVersion.isGte(extraData.version, WEAPP_VERSION_SUPPORT_BANKCARD)
      ) {
        return 'v1';
      }
      return '';
    }
    return '';
  }

  public getRemoteIp(): string {
    const remoteIp = this.ctx.firstXff;
    // 让线上环境首先返回
    if (process.env.NODE_ENV === 'prod') {
      return remoteIp || '';
    }
    if (
      process.env.NODE_ENV === 'pre' ||
      process.env.NODE_ENV === 'qa' ||
      remoteIp === '127.0.0.1'
    ) {
      return this.getConfig('PAY_REMOTE_IP') || '';
    }
    return remoteIp || '';
  }

  public get buyer() {
    const {
      buyer = {} as SessionCacheBuyer,
      fans_id = 0,
      fans_type = 0,
    } = this.ctx.getLocalSession(); // eslint-disable-line
    return {
      buyerId: String(this.buyerId || -1),
      fansId: String(fans_id),
      fansType: String(fans_type),
      buyerPhone: buyer.mobile,
    };
  }

  public getPlatformVersion() {
    const version = this.ctx.platformVersion || '';
    return version;
  }

  /**
   * @description 过滤不需要的支付方式，用作某些外部平台版本不兼容支付方式的情况
   * @returns filteredChannels
   * @memberof PayBaseController
   */
  public getFilteredPayChannels() {
    const { userAgent } = this.ctx;
    const isAndroid = UA.isAndroid(userAgent);
    const isIos = UA.isIos(userAgent);
    const isMomo = UA.isMomo(userAgent);
    const version = this.getPlatformVersion();
    // eslint-disable-next-line no-useless-escape
    const InkeVersionMatchs = /IK([\d\.]+)_Iphone/i.exec(userAgent);
    const InkeVersion = InkeVersionMatchs ? InkeVersionMatchs[1] : '';

    let filteredChannels = '';

    // 安卓陌陌 app 中，低于 8.20.4 版本的屏蔽 微信h5 支付
    if (
      isMomo &&
      isAndroid &&
      CompareVersion.isLt(version, MOMO_VERSION_SUPPORT_WX_H5)
    ) {
      filteredChannels = this.PAY_CHANNEL.WX_H5;
    } else if (
      this.ctx.platform === 'inke' &&
      isIos &&
      CompareVersion.isLt(InkeVersion, INKE_VERSION_SUPPORT_WX_H5)
    ) {
      filteredChannels = this.PAY_CHANNEL.WX_H5;
    }

    // 赶鸭子 app 里暂时屏蔽分期支付方式，待后续修复功能
    if (/kdtUnion_6400770312f0d84f3c/i.test(userAgent)) {
      filteredChannels = this.PAY_CHANNEL.INSTALMENT;
    }

    return filteredChannels;
  }

  public getPayToolColor() {
    const { platform } = this.ctx;
    return PayToolColorMap[platform] || CommonEnvPayToolColor;
  }

  public getPayChannelsWithColor(PayChannels: any) {
    const payToolColor = this.getPayToolColor();
    const payChannelsWithColor = PayChannels.map((PayChannelInfo: any) => {
      const payChannel = PayChannelInfo.pay_channel;
      PayChannelInfo.color = payToolColor[payChannel] || '';
      return PayChannelInfo;
    });
    return payChannelsWithColor;
  }

  protected getPayPhase(returnUrl = ''): string {
    return returnUrl ? args.get('phase', returnUrl) : '';
  }

  protected formatInstalmentPayResult(
    payTool: string,
    payData: any,
    returnUrl = ''
  ) {
    const payReturn: any = {};
    const {
      prepayId,
      partnerId,
      cashierSalt,
      cashierSign,
      kdtId,
      availableStatus,
      period,
    } = payData;
    const instalMentSubmitData: any = {
      prepayId,
      partnerId,
      kdtId,
    };
    const phase: string = this.getPayPhase(returnUrl);
    phase && (instalMentSubmitData.phase = phase);
    let instalMentSubmitUrl = '';
    if (availableStatus === 'INACTIVE') {
      instalMentSubmitUrl = 'https://finance.youzan.com/credit/cert';
    } else if (availableStatus === 'AVALIABLE') {
      instalMentSubmitUrl = 'https://finance.youzan.com/fenqi';
      Object.assign(instalMentSubmitData, {
        period,
        cashierSalt,
        cashierSign,
        payTool,
        returnUrl,
      });
    }

    payReturn.payData = {
      submitUrl: instalMentSubmitUrl,
      submitDomain: 'https://finance.youzan.com',
      submitTitle: '分期支付',
      submitMethod: 'GET',
      submitData: instalMentSubmitData,
    };
    return payReturn;
  }

  protected getPartnerReturnUrl(orderNo: string): string {
    const buildUrl = buildUrlWithCtx(this.ctx);
    return buildUrl(
      `https://h5.youzan.com/wsctrade/order/payresult?request_no=${orderNo}`,
      '',
      this.ctx.kdtId
    );
  }
}

export = PayBaseController;
