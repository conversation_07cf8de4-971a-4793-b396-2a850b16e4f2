import { app, mock } from '@youzan/astroboy-mock/bootstrap';
import BaseController from '../../../controllers/base/BaseController';
import OrderGuideCouponService from '../../services/order/OrderGuideCouponService';

jest.mock('../../../controllers/base/BaseController', () =>
  jest.requireActual('../../__mocks__/BaseController')
);

afterEach(() => {
  BaseController.mockClear();
  OrderGuideCouponService.mockClear();
});

test('getCheckPayGuideCouponJson', async () => {
  app.mockContext({
    kdtId: 491391,
  });

  const data = {
    couponNum: 1,
    couponId: 0,
    couponValue: 0,
  };

  OrderGuideCouponService.mockImplementation(() => {
    return {
      findPromotion: jest.fn().mockResolvedValue(data),
    };
  });
  const res = await app
    .httpRequest()
    .get('/wsctrade/order/payresult/checkPayGuideCoupon.json');

  expect(res.body).toEqual({
    code: 0,
    msg: 'success',
    data:{
      couponNum: 1,
      couponId: 0,
      couponValue: 0,
    }
  });
});
