import BaseService from '../base/BaseService';
import { GetSkuParamsDTO } from 'definitions/other/goodsSku';

class GoodsSkuService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.item.detail.api.ItemSkuService';
  }

  /**
   *  获取商品sku
   *  zanAPI文档地址: com.youzan.item.detail.api.ItemSkuService.getSkuData
   */
  async getSkuData(params: GetSkuParamsDTO) {
    return this.invoke('getSkuData', [params]);
  }
}

export = GoodsSkuService;
