<template>
  <div class="recommend-goods">
    <div class="recommend-goods__tip">
      相关推荐
    </div>
    <div 
      v-if="_recommendList.length===1"
      class="recommend-goods__wrap"
    >
      <div
        class="recommend-goods__img-wrap"
        @click="goToGoodsPage(_recommendList[0])"
      >
        <img alt="商品图片" class="recommend-goods__img" :src="recommendList[0].imgUrl" />
      </div>
      <div class="recommend-goods__desc-wrap">
        <div
          class="recommend-goods__title"
          :class="{
            'recommend-goods__title-not-exist': !_recommendList[0].title,
          }"
        >
          <span class="recommend-goods__title-text">
            {{ _recommendList[0].title | unescape }}
          </span>
        </div>
        <div class="recommend-goods__good-price">
          ￥{{ _recommendList[0].price }}
        </div>
      </div>
    </div>

    <div v-else>
      <div
        v-for="(item, index) in _recommendList" 
        :key="index"
        class="recommend-goods__imgs-for"
      >
        <div
          class="recommend-goods__imgs-wrap"
          @click="goToGoodsPage(item)"
        >
          <img alt="商品图片" class="recommend-goods__imgs" :src="item.imgUrl" />
        </div>
        <div class="recommend-goods__for-price">
          ￥{{ item.price }}
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import image from '@youzan/utils/browser/image';
import urlHelper from '@youzan/utils/url/helper';
import get from 'utils/get';
import ZNB from '@youzan/znb';
import { jumpLink, isTTApp } from '@shared/common/helper';
import { action } from '@youzan/zan-jsbridge';
import {
  GOODS_LINK,
  IS_APP_CART,
  IS_OPEN_NEW,
} from '@biz/cart/constant';
import makeRandomString from '@youzan/utils/string/makeRandomString';
import { Logger } from 'common/log/logger';

export default {
  name: 'recommend-goods',

  props: {
    recommendList: {
      type: Array,
      default: () => [],
    },
  },

  computed: {
    _recommendList() {
      return this.recommendList.map(function (item, index) {
        const goodsItem = { ...item };
        goodsItem.imgUrl = image.toWebp(urlHelper.getCdnImageUrl(item.imageUrl, '!200x200.jpg'));
        goodsItem.price = (goodsItem.price / 100).toFixed(2);

        const randomNumber = makeRandomString(8);
        const bannerId = `cart~expired_goods_recommend~${index}~${randomNumber}`;
        goodsItem.bannerId = bannerId;

        return goodsItem;
      });
    },
  },
  created() {
    this.log();
  },

  methods: {
    goToGoodsPage(goods) {
      // 点击商品图片时所做的操作
      const goodsLink = `${GOODS_LINK}?alias=${goods.alias}&banner_id=${goods.bannerId}&alg=${goods.algs}`;
      const isAlipayApp = get(window, '_global.miniprogram.isAlipayApp', false);
      const isQQApp = get(window, '_global.miniprogram.isQQApp', false);
      const isXhsApp = get(window, '_global.miniprogram.isXhsApp', false);
    
      // 头条小程序内使用jumpLink，内有特殊逻辑
      if (isTTApp) {
        jumpLink(goodsLink);
      }

      if (isAlipayApp || isQQApp || isXhsApp) {
        ZNB.navigate({
          aliappUrl: `/packages/goods/detail/index?alias=${goods.alias}&banner_id=${goods.bannerId}&alg=${goods.algs}`,
          qqUrl: `/packages/goods/detail/index?alias=${goods.alias}&banner_id=${goods.bannerId}&alg=${goods.algs}`,
          xhsUrl: `/packages/goods/detail/index?alias=${goods.alias}&banner_id=${goods.bannerId}&alg=${goods.algs}`,
        });
        return;
      }

      if (!IS_APP_CART) {
        const options = {
          url: goodsLink,
        };
        // 如果页面 url 上有 __openNew__ 参数，说明需要用新页面打开链接
        options.fallback = IS_OPEN_NEW ? 'open' : 'redirect';
        ZNB.navigate(options);
        return;
      }

      action.gotoWebview({
        url: goodsLink,
        page: 'web',
      });
    },

    log() {
      this.recommendList.forEach(function(item) {
        Logger.log({
          et: 'view',
          ei: 'view',
          en: '商品曝光',
          params: {
            alg: item.algs,
            banner_id: item.bannerId,
            item_id: item.id,
            item_type: 'goods',
            component: 'expired_goods_recommend',
            recommend_name: '失效商品推荐'
          }
        });
      });
    },
  },
}
</script>
<style lang="scss" scoped>
.recommend-goods {
  background: #f7f8fa;
  border-radius: 4px;
  width: 210px;
  height: 90px;
  padding-left: 12px;
  padding-top: 6px;
  font-size: 12px;
  line-height: 16px;
  color: #323233;
  position: relative;

  &__tip {
    width: 100%;
    height: 16px;
    float: left;
    font-family: PingFangSC-Medium;
    line-height: 16px;
    margin-bottom: 4px;
  }

  &__wrap {
    width: 100%;
    height: 62px;
    float: left;
  }

  &__img-wrap {
    width: 62px;
    height: 62px;
    float: left;
    position: relative;
    margin-left: auto;
    margin-right: auto;
    border-radius: 6px;
    overflow: hidden;
    background: #fff;
    background-size: cover;
  }

  &__img {
    position: absolute;
    margin: auto;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: auto;
    height: auto;
    max-width: 100%;
    max-height: 100%;
  }

  &__desc-wrap {
    height: 100%;
    margin-left: 70px;
  }

  &__title {
    margin-bottom: 8px;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;

    &-not-exist {
      margin-bottom: 0;
    }
  }

  &__title-text {
    line-height: 16px;
    vertical-align: middle;
  }

  &__good-price {
    bottom: 8px;
    position: absolute;
  }

  &__imgs-for {
    width: 48px;
    margin-right: 8px;
    float: left;
  }

  &__imgs-wrap {
    width: 48px;
    height: 48px;
    position: relative;
    margin-left: auto;
    margin-right: auto;
    border-radius: 6px;
    overflow: hidden;
    background: #fff;
    background-size: cover;
    margin-bottom: 3px;
  }

  &__imgs {
    position: absolute;
    margin: auto;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: auto;
    height: auto;
    max-width: 100%;
    max-height: 100%;
  }

  &__for-price {
    margin-bottom: 8px;
    text-overflow: ellipsis;
    overflow: hidden;
    text-align: center;
  }
}
</style>
