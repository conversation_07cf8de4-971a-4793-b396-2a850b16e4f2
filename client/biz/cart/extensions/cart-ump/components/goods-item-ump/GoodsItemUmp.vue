<template>
  <div v-if="orderSuccessCounter.length" class="activity-info">
    <span class="activity-info__tag">{{ goodsItem.activityTag }}</span>
    还剩
    <span v-for="item in orderSuccessCounter" :key="item.type">
      <span class="order-countdown" :style="`width: ${item.width}`">{{
        item.value
      }}</span>
      <span>{{ item.text }}</span>
    </span>
    结束
  </div>
</template>

<script>
import CountDown from 'common/count-down';
import padZero from 'utils/string/pad-zero';

const TIME_MAP = {
  HOUR: 'hour',
  MIN: 'min',
};

export default {
  name: 'goods-item-ump',

  props: {
    goodsItem: {
      type: Object,
      default: () => ({}),
    },
  },

  data() {
    return {
      countdownData: [
        {
          type: TIME_MAP.HOUR,
          value: '',
          text: '时',
          width: '0',
        },
        {
          type: TIME_MAP.MIN,
          value: '',
          text: '分',
          width: '0',
        },
      ],
      orderSuccessCounter: [],
    };
  },

  computed: {
    showGoodsActivity() {
      // 目前只有限时折扣需要展示商品级优惠提示
      return this.orderSuccessCounter.length;
    },
  },

  mounted() {
    const nowTime = new Date().getTime() / 1000;
    const startTime = this.goodsItem.activityStartTime / 1000 || 0;
    const endTime = this.goodsItem.activityEndTime / 1000 || 0;
    const day = Math.floor((endTime - nowTime) / 86400);
    // 超过24小时不显示倒计时提示
    if (startTime > nowTime || day !== 0) {
      return;
    }
    new CountDown({
      seconds: endTime > nowTime ? endTime - nowTime : 0,
      onTimeChange: ({ day = 0, hour = 0, min = 0, sec = 0 }) => {
        this.countdownData = this.countdownData.map((item) => {
          let value = '';
          switch (item.type) {
            case TIME_MAP.HOUR:
              value = String(padZero(hour));
              break;
            case TIME_MAP.MIN:
              value = String(padZero(min));
              break;
          }
          return {
            ...item,
            width: `${value.length * 8}px`,
            value,
          };
        });
        if (hour !== 0) {
          this.orderSuccessCounter = this.countdownData;
        } else if (sec !== 0) {
          this.orderSuccessCounter = this.countdownData.slice(1);
        } else {
          this.orderSuccessCounter = [];
        }
      },
    }).run();
  },
};
</script>
<style scoped lang="scss">
.activity-info {
  margin-left: 20px;
  box-sizing: border-box;
  padding: 8px 10px 8px 0;
  font-size: 12px;
  line-height: 16px;
  display: flex;
  overflow: hidden;
  position: relative;

  &__tag {
    font-family: PingFangSC-Medium;
    font-size: 12px;
    color: #323233;
    line-height: 16px;
    margin-right: 8px;
  }
}
</style>
