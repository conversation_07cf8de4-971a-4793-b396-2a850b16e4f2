import toYuan from '@youzan/utils/money/toYuan';
import accAdd from '@youzan/utils/number/accAdd';
import pick from '@youzan/utils/object/pick';
import {
  getPostageLabel,
  getPostageValue,
} from '../utils/postageUtils';
import { formatTotalPrice } from '../utils/utils';

// 支付优惠
function getPayUmpDiscountMoney(state) {
  if (state.pay.phasePayment && state.pay.phasePayment.phaseItems) {
    return state.pay.phasePayment.phaseItems.reduce(
      (prev, curr) => prev + curr.payUmpDiscountMoney,
      0
    );
  }
  return 0;
}

// 计价，用于 total-price 组件
function getPriceList({ state = {}, getters = {} }) {
  const { points, pay, pointsName, displayCard } = state;
  const { hasDisplayCard, currentPostage, couponDecrease } = getters;

  const taxPrice = pay.goodsTax + pay.postageTax;

  // 商品金额减去展示卡价格
  const itemPay = hasDisplayCard ? pay.itemPay - displayCard.price : pay.itemPay;

  const res = [
    {
      label: '商品总价',
      value: formatTotalPrice(itemPay, points.totalPoints, pointsName),
    },
  ];

  if (hasDisplayCard) {
    res.push({
      label: displayCard.name,
      value: `+ ¥${toYuan(displayCard.price)}`,
    });
  }

  if (points.totalPoints) {
    res.push({
      label: `${pointsName}兑换`,
      value: `-${points.totalPoints}${pointsName}`,
    });
  }

  if (currentPostage.available) {
    res.push({
      label: getPostageLabel(currentPostage.expressPayMode),
      value: getPostageValue(currentPostage.expressPayMode, pay.postage),
    });
  }

  if (taxPrice) {
    res.push({
      label: '进口税（含运费税款）',
      value: `+ ¥${toYuan(taxPrice)}`,
    });
  }

  (pay.extraFees || []).forEach((v) => {
    const isPackingFee = v.name === '打包费';
    res.push({
      label: v.name,
      desc: v.desc,
      realPay: v.realPay,
      value: `+ ¥${toYuan(v.realPay)}`,
      showTips: isPackingFee,
    });
  });

  // 活动优惠
  (pay.promotions || []).forEach((v) => {
    let { name } = v;
    if (v.promotionTypeId === 256) {
      name = v.name.replace('积分', pointsName);
    }
    if (v.promotionTypeId === 115) {
      name = v.promotionTypeName;
    }
    // 充值优惠
    if (v.promotionTypeId === 515) {
      name = `充值优惠（${v.name}）`;
    }

    res.push({
      label: name,
      value: `${v.decrease >= 0 ? '-' : '+'} ¥${toYuan(Math.abs(v.decrease))}`,
    });
  });

  if (couponDecrease !== 0) {
    res.push({
      label: '优惠',
      value: `- ¥${toYuan(couponDecrease)}`,
    });
  }

  const fissionActivityValue = state.fissionActivity?.fissionTicketNum || 0;
  if (fissionActivityValue) {
    res.push({
      label: '内购券',
      value: `${fissionActivityValue}张`,
    });
  }

  const payUmpDiscountMoney = getPayUmpDiscountMoney(state);

  if (payUmpDiscountMoney !== 0) {
    res.push({
      label: '支付优惠',
      value: `- ¥${toYuan(getPayUmpDiscountMoney(state))}`,
    });
  }

  // 三方券优惠信息
  pay.assetPayInfos.forEach((assetPayInfo) => {
    if (assetPayInfo.realPrice > 0) {
      res.push({
        label: assetPayInfo.payWayStr,
        value: `- ￥${toYuan(assetPayInfo.realPrice)}`,
      });
    }
  });

  return res;
}

// 合计金额
function getFinalPrice({ state, getters }) {
  const { pay } = state;
  const realPay = toYuan(state.pay.realPay);

  // 多阶段支付，返回阶段支付金额
  if (pay.multiPhase) {
    if (state.tradeTag.hasDepositPreSaleGoods) {
      // 阶段支付累加各个阶段的和
      return getters.finalPhasePayment.reduce((prev, curr) => accAdd(+prev, +curr)).toFixed(2);
    }
    // 使用了余额抵扣则展示realPay
    if (getters.prepayCardDecrease) {
      return realPay;
    }
    // 组合支付取最后一个阶段的支付金额
    return Number(getters.finalPhasePayment[pay.phasePayment.currentPhase - 1]).toFixed(2);
  }

  // 支付页直接返回金额
  return realPay;
}

// 最终分阶段支付金额
function getCouponDecreasedPhasePayment({ state, getters }) {
  const { pay } = state;

  if (!pay.multiPhase) {
    return [];
  }

  // 抵扣尾款
  // 邮费无法抵扣
  const prices = pay.phasePayment.phaseItems.map((item) => item.currentPrice);
  let decrease = getters.couponDecrease;
  if (getters.newCouponProcess) {
    decrease = 0;
  }
  const finalPrices = [];
  finalPrices[1] = Math.max(state.pay.postage, 0, prices[1] - decrease);
  decrease -= prices[1] - finalPrices[1];

  // 抵扣定金
  finalPrices[0] = Math.max(0, prices[0] - decrease);

  return finalPrices;
}

function getDepositShow({ state, getters }) {
  const { tradeTag } = state;
  const { formattedGoods } = getters;
  const hasDepositPreSaleGoods = tradeTag?.hasDepositPreSaleGoods;
  const goodsLength = formattedGoods?.length || 0;
  return hasDepositPreSaleGoods && goodsLength > 0;
}

function getChosenCoupons({ getters }) {
  return getters.chosenCoupons.map((coupon) => ({
    ...pick(coupon, ['name', 'condition']),
    decrease: coupon.value || 0,
  }));
}

export default function (args) {
  const { state = {}, getters = {} } = args;

  return {
    // 待支付 价格计算合计 Array<{ label: string, value: string }>
    priceTotalPriceList: getPriceList(args),

    finalPrice: getFinalPrice(args),

    prepayCardDecrease: getters.prepayCardDecrease,

    couponDecreasedPhasePayment: getCouponDecreasedPhasePayment(args),

    // 预售
    depositShow: getDepositShow(args),

    depositOrder: pick(state.order, ['showDepositAgreementVal', 'agreeDeposit']),

    depositPay: pick(state.pay, ['phasePayment', 'multiPhase']),

    // 优惠券
    chosenCoupon: getters.chosenCoupon?.value
      ? {
          ...pick(getters.chosenCoupon, ['name', 'condition']),
          decrease: getters.chosenCoupon?.value,
        }
      : {},
    // 优惠券列表
    chosenCoupons: getChosenCoupons(args),
  };
}
