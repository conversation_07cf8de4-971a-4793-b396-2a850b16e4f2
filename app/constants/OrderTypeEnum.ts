/** ************************************************************************************************
 * 订单类型枚举值
 * 参考自: https://gitlab.qima-inc.com/trade-platform/trade-core/blob/35ec467e6bf1dae32196f1bbd7ab2c3163ddfe5e/trade-core-api/src/main/java/com/youzan/trade/core/service/constant/OrderTypeEnum.java
 ************************************************************************************************ */
enum OrderTypeEnum {
  NORMAL = 0, // 普通订单
  PEERPAY = 2, // 代付
  GIFT = 1, // 送礼订单
  FX_CAIGOUDAN = 3, // 分销采购单
  PRESENT = 4, // 赠品
  WISH = 5, // 心愿单
  QRCODE = 6, // 二维码订单
  QRCODE_3RD = 61, // 线下收银台订单
  FX_MERGED = 7, // 合并付货款
  VERIFIED = 8, // 1分钱实名认证
  PINJIAN = 9, // 品鉴
  REBATE = 15, // 返利
  FX_QUANYUANDIAN = 51, // 全员开店
  FX_DEPOSIT = 52, // 保证金
  PF = 100, // 批发
  GROUP = 10, // 拼团
  HOTEL = 35, // 酒店
  TAKE_AWAY = 40, // 外卖
  CATERING_OFFLINE = 41, // 堂食点餐
  CATERING_QRCODE = 46, // 外卖买单
  BEAUTY_APPOINTMENT = 71, // 美业预约单
  BEAUTY_SERVICE = 72, // 美业服务单
  KNOWLEDGE_PAY = 75, // 知识付费 (已废弃)
  GIFT_CARD = 81, // 礼品卡
  TIME_CARD = 82, // 次卡
  LIVE_SUPPLIER = 85, // 直播带货订单
  SAMPLE = 89 // 样品订单
}

class OrderType {
  private static descriptions: { [key in OrderTypeEnum]: string } = {
    [OrderTypeEnum.NORMAL]: "普通订单",
    [OrderTypeEnum.PEERPAY]: "代付",
    [OrderTypeEnum.GIFT]: "送礼订单",
    [OrderTypeEnum.FX_CAIGOUDAN]: "分销采购单",
    [OrderTypeEnum.PRESENT]: "赠品",
    [OrderTypeEnum.WISH]: "心愿单",
    [OrderTypeEnum.QRCODE]: "二维码订单",
    [OrderTypeEnum.QRCODE_3RD]: "线下收银台订单",
    [OrderTypeEnum.FX_MERGED]: "合并付货款",
    [OrderTypeEnum.VERIFIED]: "1分钱实名认证",
    [OrderTypeEnum.PINJIAN]: "品鉴",
    [OrderTypeEnum.REBATE]: "返利",
    [OrderTypeEnum.FX_QUANYUANDIAN]: "全员开店",
    [OrderTypeEnum.FX_DEPOSIT]: "保证金",
    [OrderTypeEnum.PF]: "批发",
    [OrderTypeEnum.GROUP]: "拼团",
    [OrderTypeEnum.HOTEL]: "酒店",
    [OrderTypeEnum.TAKE_AWAY]: "外卖",
    [OrderTypeEnum.CATERING_OFFLINE]: "堂食点餐",
    [OrderTypeEnum.CATERING_QRCODE]: "外卖买单",
    [OrderTypeEnum.BEAUTY_APPOINTMENT]: "美业预约单",
    [OrderTypeEnum.BEAUTY_SERVICE]: "美业服务单",
    [OrderTypeEnum.KNOWLEDGE_PAY]: "知识付费 (已废弃)",
    [OrderTypeEnum.GIFT_CARD]: "礼品卡",
    [OrderTypeEnum.TIME_CARD]: "次卡",
    [OrderTypeEnum.LIVE_SUPPLIER]: "直播带货订单",
    [OrderTypeEnum.SAMPLE]: "样品订单"
  };

  /**
   * 根据枚举值获取描述
   * @param value 订单的代码值
   * @return string 描述
   */
  static getDesc(value: OrderTypeEnum): string {
    return this.descriptions[value];
  }

  /**
   * 根据枚举值获取枚举名称
   * @param value 订单的代码值
   * @return string | null 枚举名称 (如果找不到)
   */
  static getEnumName(value: number): string | null {
    const entry = Object.entries(OrderTypeEnum).find(([_, val]) => val === value);
    return entry ? entry[0] : null;
  }
}

export {
  OrderTypeEnum,
  OrderType,
}
