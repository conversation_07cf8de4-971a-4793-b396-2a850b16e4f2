const BaseService = require('../base/BaseService');

class FoodOrderService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.ebiz.mall.rigel.api.eatinorder.service.FoodOrderService';
  }

  /**
   *  预下单
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/210875
   *
   *  @param {object} foodOrderPreparationDTO -
   *  @param {string} foodOrderPreparationDTO.buyerPhone -
   *  @param {number} foodOrderPreparationDTO.fansId -
   *  @param {string} foodOrderPreparationDTO.fansNickname -
   *  @param {number} foodOrderPreparationDTO.kdtId - 必传 店铺id
   *  @param {number} foodOrderPreparationDTO.youzanFansId -
   *  @param {string} foodOrderPreparationDTO.bookKey - 必传
   *  @param {number} foodOrderPreparationDTO.buyerId - 必传 提交人buyerId
   *  @param {number} foodOrderPreparationDTO.fansType -
   *  @return {object}
   */

  async prepare(foodOrderPreparationDTO) {
    return this.invoke('prepare', [foodOrderPreparationDTO]);
  }

  /**
   *  获取支付超时时间
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/225222
   *
   *  @param {object} foodOrderPaymentPreparationDTO -
   *  @param {Object} foodOrderPaymentPreparationDTO.orderPaymentPreparation -
   *  @param {string} foodOrderPaymentPreparationDTO.orderPaymentPreparation.orderNo -
   *  @param {integer} foodOrderPaymentPreparationDTO.orderPaymentPreparation.kdtId -
   *  @param {[object Object]} foodOrderPaymentPreparationDTO.orderPaymentPreparation.orderNos -
   *  @param {integer} foodOrderPaymentPreparationDTO.orderPaymentPreparation.forbidWxpay -
   *  @param {integer} foodOrderPaymentPreparationDTO.orderPaymentPreparation.buyerId -
   *  @return {object}
   */

  async preparePayment(foodOrderPaymentPreparationDTO) {
    return this.invoke('preparePayment', [foodOrderPaymentPreparationDTO]);
  }

  /**
   *  创建堂食订单
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/210876
   *
   *  @param {Object} foodOrderCreationDTO.seller - 卖家信息
   *  @param {Object} foodOrderCreationDTO.source - 来源
   *  @param {Array.<Object>} foodOrderCreationDTO.items[] - 商品
   *  @param {Object} foodOrderCreationDTO.buyer - 买家信息
   *  @return {object}
   */

  async create(foodOrderCreationDTO) {
    return this.invoke('create', [foodOrderCreationDTO]);
  }

  /**
   *  堂食订单详情
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/210877
   *
   *  @param {object} foodOrderDetailDTO -
   *  @param {string} foodOrderDetailDTO.orderNo -
   *  @param {number} foodOrderDetailDTO.kdtId -
   *  @param {string} foodOrderDetailDTO.alias - 桌位别名
   *  @param {number} foodOrderDetailDTO.storeId -
   *  @param {number} foodOrderDetailDTO.footSeatId - 桌位id
   *  @return {object}
   */

  async orderDetail(foodOrderDetailDTO) {
    return this.invoke('orderDetail', [foodOrderDetailDTO]);
  }

  /**
   *  支付成功页面调用
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/216490
   *
   *  @param {object} param -
   *  @param {string} param.orderNo -
   *  @param {number} param.kdtId -
   *  @param {number} param.storeId -
   *  @param {number} param.buyerId -
   *  @param {number} param.fansType -
   *  @return {object}
   */

  async paySuccess(param) {
    return this.invoke('paySuccess', [param]);
  }

  /**
   *
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/456291
   *
   *  @param {Object} param -
   *  @param {number} param.mode -
   *  @param {string} param.orderNo -
   *  @param {number} param.kdtId -
   *  @param {string} param.tableAlias -
   *  @param {number} param.storeId -
   *  @return {Promise}
   */
  async reorder(param) {
    return this.invoke('reorder', [param]);
  }
}

module.exports = FoodOrderService;
