import BaseService from '../base/BaseService';

/**
 * ShopConfigWriteService
 */
class ShopConfigWriteService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.shopcenter.shopconfig.api.service.ShopConfigWriteService';
  }

  /**
   *  批量修改店铺配置
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/76615
   *  @param {Object} shopConfigsSetRequest - {@link ShopConfigsSetRequest}
   *  @param {Object} shopConfigsSetRequest.configs - 店铺配置 key-value 映射
   *  @param {number} shopConfigsSetRequest.kdtId - 店铺kdtId
   *  @param {Object} shopConfigsSetRequest.operator - 操作人信息
   *  @param {string} shopConfigsSetRequest.operator.fromApp - 来源应用
   *  @param {string} shopConfigsSetRequest.operator.name - 操作人名称
   *  @param {integer} shopConfigsSetRequest.operator.id - 操作人账号id
   *  @param {integer} shopConfigsSetRequest.operator.type - 操作人账号类型
   *  @return {Promise}
   */
  async setShopConfigs(shopConfigsSetRequest: any) {
    return this.invoke('setShopConfigs', [shopConfigsSetRequest]);
  }

  /**
   *  修改单个店铺配置
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/76614
   *
   *  @param {Object} shopConfigSetRequest - {@link ShopConfigSetRequest}
   *  @param {number} shopConfigSetRequest.kdtId - 店铺kdtId
   *  @param {string} shopConfigSetRequest.value - 配置value
   *  @param {string} shopConfigSetRequest.key - 配置key
   *  @param {Object} shopConfigSetRequest.operator - 操作人信息
   *  @param {string} shopConfigSetRequest.operator.fromApp - 来源应用
   *  @param {string} shopConfigSetRequest.operator.name - 操作人名称
   *  @param {integer} shopConfigSetRequest.operator.id - 操作人账号id
   *  @param {integer} shopConfigSetRequest.operator.type - 操作人账号类型
   *  @return {Promise}
   */
  async setShopConfig(shopConfigSetRequest: any) {
    return this.invoke('setShopConfig', [shopConfigSetRequest]);
  }
}

export = ShopConfigWriteService;
