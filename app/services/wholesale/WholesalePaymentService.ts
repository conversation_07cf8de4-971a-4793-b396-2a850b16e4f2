import BaseService from '../base/BaseService';

/**
 * com.youzan.ebiz.social.trade.seller.api.wholesale.request.OrderQueryRequest
 */
interface IOrderQueryRequest {
  /** 订单号 必填 */
  orderNo?: string;
  /** 店铺Id 必填 */
  kdtId?: number;
  /** 操作人ID */
  operatorId?: number;
  /** 操作人名称 */
  operatorName?: string;
}

/** com.youzan.ebiz.social.wholesale.consumer.api.wholesasler.WholesalerQueryService */
class WholesalerReadService extends BaseService {
  get WHOLESALE_SERVICE_NAME() {
    return 'com.youzan.ebiz.social.trade.seller.api.wholesale.service.WholesalePaymentVoucherService';
  }

  /**
   *  查询批发线下支付订单凭证信息
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1299091
   *
   *  @param {Object} request - {@link OrderQueryRequest}
   *  @param {string} request.orderNo - 订单号 必填
   *  @param {number} request.kdtId - 店铺Id 必填
   *  @param {number} request.operatorId - 操作人ID
   *  @param {string} request.operatorName - 操作人名称
   *  @return {Promise}
   */
  async queryPaymentVoucher(request: IOrderQueryRequest) {
    return this.invoke(this.WHOLESALE_SERVICE_NAME, 'queryPaymentVoucher', [request]);
  }
}

export = WholesalerReadService;
