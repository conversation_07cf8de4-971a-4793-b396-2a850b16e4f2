<template>
  <div class="logistics">
    <div class="logistics__title">
      选择结算商品
      <van-icon class="logistics__title__icon" name="cross" @click="closePopup" />
    </div>
    <div class="logistics__tip">
      <p>{{ buyTips }}</p>
      <p>{{ discountTips }}</p>
    </div>
    <div class="logistics__cell-wrap">
      <div v-for="(items, index) in separateBuy.data" :key="index" class="logistics__cell">
        <div class="logistics__cell__title">
          <span class="title">{{ items.title }}</span>
          <span v-if="isHasHaiTao(items.list)" class="prompt">
            <span>（含</span>
            <i class="icon-haitao" />
            <span>的商品）</span>
          </span>
        </div>
        <div class="logistics__cell__content">
          <div class="logistics__cell__items">
            <div
              v-for="(item, goodsIndex) in goodItemsFilter(items.list)"
              :key="goodsIndex"
              class="logistics__cell__items__img"
              :style="{
                'background-image': `url(${fillImgUrl(item)})`,
              }"
            />
            <span v-if="items.list.length > 3" class="logistics__cell__dot"> ... </span>
          </div>
          <van-button
            class="logistics__cell__button"
            size="small"
            @click="goPay(items, index)"
          >
            {{ buyText }}
          </van-button>
        </div>

        <div class="logistics__cell__total">
          共&nbsp;<span class="num">{{ items.list.length }}</span
          >&nbsp;<span>{{ items.title === '课程' ? '门' : '件' }}</span
          >，合计：
          <Price class="logistics__cell__total-price" :price="items.totalPrice" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import get from 'utils/get';
import each from 'utils/each';
import { Icon, Button } from 'vant';
import Price from '@biz/cart/components/Price';
import format from '@youzan/utils/money/format.js';
import { SEPARATE_BUY_TYPE } from '@biz/cart/constant';
import fullfillImage from '@youzan/utils/fullfillImage';
import sessionStorageHelper from '@youzan/utils/browser/session_storage';

export default {
  name: 'beauty',

  components: { Price, [Icon.name]: Icon, [Button.name]: Button },

  props: {
    separateBuy: {
      type: Object,
      default: () => ({}),
    },
    buyText: {
      type: String,
      default: '去结算',
    },
  },

  data() {
    return {
      format,
    };
  },

  computed: {
    buyTips() {
      return {
        [SEPARATE_BUY_TYPE.COURSE_MIX_TYPE]: '普通商品与课程暂不支持同时结算，请分开结算',
        [SEPARATE_BUY_TYPE.MIX_TYPE]: '以下商品暂不支持同时结算，请分开下单',
        [SEPARATE_BUY_TYPE.LOGISTICS]: '不同配送方式的商品暂不支持同时结算，请分开下单',
      }[this.separateBuy.type];
    },
    discountTips() {
      return {
        [SEPARATE_BUY_TYPE.COURSE_MIX_TYPE]: '以上优惠不包含运费、税费，实际优惠金额请以确认订单页为准',
        [SEPARATE_BUY_TYPE.MIX_TYPE]: '以上优惠不包含运费、税费，实际优惠金额请以确认订单页为准',
        [SEPARATE_BUY_TYPE.LOGISTICS]: '以上优惠不包含运费，实际优惠金额请以确认订单页为准',
      }[this.separateBuy.type];
    },
  },

  methods: {
    closePopup() {
      this.$emit('setSeparateBuy', { show: false });
    },

    isHasHaiTao(list) {
      return list.some((item) => {
        const rule = get(item, 'settlementRule', {});
        const mark = get(rule, 'settlementMark', '');
        return mark === 'HAITAO';
      });
    },
    goodItemsFilter(list) {
      if (list.length > 3) return list.slice(0, 3);
      else return list;
    },
    goPay(items, index) {
      const { type } = this.separateBuy;
      const { list } = items;
      if ([SEPARATE_BUY_TYPE.MIX_TYPE, SEPARATE_BUY_TYPE.COURSE_MIX_TYPE].includes(type)) {
        this.$emit('buy', { list });
      } else {
        this.logisticsBuy(items, index);
      }
    },

    fillImgUrl(item) {
      return fullfillImage(item.attachmentUrl, '!120x120.jpg');
    },

    logisticsBuy(items, index) {
      const { data: separateList } = this.separateBuy;
      const { expressType, list: currentBuyGoods } = items;
      // 去下单的商品
      const currentBuyGoodsIds = [];
      // 排除掉去下单的剩余商品，支付完成后提示继续购买
      const waitBuyGoodsIds = [];

      each(currentBuyGoods, ({ kdtId, goodsId, skuId }) => {
        const id = [kdtId, goodsId, skuId].join('-');
        currentBuyGoodsIds.push(id);
      });

      each(separateList, (value, oIndex) => {
        if (index === oIndex) {
          return;
        }

        each(value.list, ({ kdtId, goodsId, skuId }) => {
          const id = [kdtId, goodsId, skuId].join('-');
          if (waitBuyGoodsIds.indexOf(id) === -1 && currentBuyGoodsIds.indexOf(id) === -1) {
            waitBuyGoodsIds.push(id);
          }
        });
      });

      if (waitBuyGoodsIds.length) {
        sessionStorageHelper.setItem('waitBuyGoodsIds', JSON.stringify(waitBuyGoodsIds));
      }
      this.$emit('buy', { expressType, list: currentBuyGoods });
    },
  },
};
</script>

<style lang="scss">
.logistics {
  padding-top: 50px;

  &__title {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    font-size: 16px;
    color: #323233;
    padding: 11px 0;
    text-align: center;

    &__icon {
      position: absolute;
      right: 16px;
      top: 12px;
      color: #dcdde0;
      font-size: 20px;
    }
  }

  &__tip {
    font-size: 12px;
    padding: 0 16px;
    margin-bottom: 16px;
    color: #969799;
  }

  &__cell-wrap {
    height: 65vh;
    overflow-y: scroll;
  }

  &__cell {
    position: relative;
    padding: 12px;
    background: #f7f8fa;
    border-radius: 8px;
    margin: 0 16px 12px;

    &__title {
      margin-bottom: 8px;

      .title {
        font-size: 16px;
        color: #323233;
        font-weight: 500;
        vertical-align: middle;
      }

      .prompt {
        font-size: 12px;
        color: #969799;
        vertical-align: middle;
        display: inline-flex;
        align-items: center;

        span {
          vertical-align: middle;
        }
      }

      .icon-haitao {
        margin: 0 3px;
      }
    }

    &__content {
      position: relative;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    &__items {
      white-space: nowrap;
      overflow-x: scroll;

      &__img {
        width: 48px;
        height: 48px;
        border-radius: 4px;
        margin-right: 8px;
        vertical-align: middle;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
        background-color: #fff;
        display: inline-block;
      }
    }

    &__dot {
      vertical-align: bottom;
      color: #323233;
    }

    &__button {
      height: 24px;
      line-height: 22px;
      border-radius: 18px;
      color: var(--main-text, #fff);
      background: var(--main-bg, #323233);
    }

    &__total {
      margin-top: 10px;
      font-size: 13px;
      color: #323233;
      line-height: 17px;
      &-price {
        color: var(--price, #323233);
      }
    }
  }
}
</style>
