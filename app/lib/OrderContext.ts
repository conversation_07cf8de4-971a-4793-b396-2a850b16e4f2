import { Context } from 'astroboy';
import buildUrlWithCtx from '@youzan/utils/url/buildUrlWithCtx';

/**
 * NodeJS是单线程，所以可以这样用，否则需要考虑线程安全
 */
class OrderContext {
  static ctx: Context = {} as any;

  public static init(ctx: Context = {} as any) {
    OrderContext.ctx = ctx;
  }

  /**
   * 处理独立域名等
   * @param path
   * @param urlKey
   * @param kdtId
   */
  public static buildUrl(path: string, urlKey: string, kdtId?: JSNumber) {
    const ctx = OrderContext.ctx;
    const buildUrl = buildUrlWithCtx(ctx);
    return buildUrl(path, urlKey, kdtId || ctx.kdtId);
  }
}

export default OrderContext;
