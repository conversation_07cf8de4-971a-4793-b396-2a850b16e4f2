import args from '@youzan/utils/url/args';

const PageException = require('@youzan/iron-base/app/exceptions/PageException');
const BaseController = require('../base/BaseController');
const WechatService = require('../../services/trade/WechatService');
const MarketRemoteService = require('../../services/yop/MarketRemoteService');

class OrderController extends BaseController {
  init() {
    return super.init({
      initMpData: false,
      initMpAccount: false,
      initPlatform: false,
    });
  }

  orderWechatAcl() {
    return this.needPlatformAcl();
  }

  // 微信查看订单详情 / 联系客服 中间页
  // testUrl: https://h5.youzan.com/wsctrade/wechat/dispatch?scene=im&merchant=m12&jump_code=815948dec905cfe87692bd0df7aeb6b3_XN2d2Z
  async getIndexHtml(ctx) {
    await this.orderWechatAcl();
    const {
      auth_code = '',
      sign = '',
      scene = 'order',
      orderNos,
      payDetailNo,
      merchant,
      jump_code,
    } = ctx.query;
    const { fansId, fansType = 0, buyerId } = this.buyer;

    const decideParams = {
      authCode: auth_code,
      fansId,
      sign,
      userId: buyerId,
      scene, // 场景 order: 查看订单 im: 联系客服
      fansType,
      source: 'node',
      payDetailNo,
      merchant,
      jumpCode: jump_code,
    };

    if (orderNos != null) {
      decideParams.orderNos = Array.isArray(orderNos) ? orderNos : [orderNos];
    }

    try {
      const res = await new WechatService(ctx).decide(decideParams);

      if (!res.redirectUrl && scene === 'im') {
        // 微信账单-联系商户
        const [shopOrder = {}] =
          Object.values(res.shopOrderGroup || {})[0] || [];

        res.redirectUrl = `https://h5.youzan.com/v3/im/middle/index?from_biz=wsc&from_scene=bill&kdtId=${shopOrder.kdtId ||
          0}&orderNo=${shopOrder.orderNo || ''}`;
      } else if (
        scene === 'order' &&
        res.redirectUrl &&
        res.redirectUrl.indexOf('/wsctrade/order/detail') > -1
      ) {
        // 微信账单-查看购买详情(微商城订单详情)
        res.redirectUrl = args.add(res.redirectUrl, {
          im_from_scene: 'billorder',
        });
      }

      ctx.redirect(res.redirectUrl);
    } catch (error) {
      const content = error.errorContent;
      if (content) {
        ctx.setGlobal({
          errorContent: error.errorContent,
        });

        await ctx.render('order/wechat.html', {
          type: 'error',
        });
      } else {
        throw error;
      }
    }
  }

  // NOTE: 目前未使用 小程序二维码页面
  async getWeappHtml(ctx) {
    await ctx.render('order/wechat.html', {
      type: 'weapp',
    });
  }

  // NOTE: 目前未使用 获取小程序码
  async getWeappCode(ctx) {
    let { kdtId } = ctx.request.body;
    const { page } = ctx.request.body;
    const wechatService = new WechatService(ctx);
    const marketRemoteService = new MarketRemoteService(ctx);

    // 查询店铺使用的是共享版还是专享版
    const { isValid, useCommon } = marketRemoteService.isWeappAuthTeam(+kdtId);
    kdtId = isValid ? +kdtId : ********;
    if (!isValid && !useCommon) {
      throw new PageException('', '商家没有小程序');
    }
    // 查询 accessToken
    const weappAccount = await wechatService.getWeappAccountBykdtId(kdtId);

    // TODO: 参数存到后端
    const params = {
      path: page,
    };

    // 生成小程序码
    const code = await wechatService.getWeappCode(
      weappAccount.accessToken,
      params
    );
    const base64 = Buffer.from(code).toString('base64');

    ctx.r(0, 'ok', base64);
  }

  // NOTE: 目前未使用 手机号验证页面
  async getMobileHtml(ctx) {
    const { order_no } = ctx.query;

    const data = await new WechatService(ctx).showCheckPhone({
      requestNo: order_no,
    });

    ctx.setGlobal({
      orderPageDTO: data.orderPageDTO,
      lastFourMobilePhoneNum: data.lastFourMobilePhoneNum,
    });

    await ctx.render('order/wechat.html', {
      type: 'mobile',
    });
  }

  // 错误兼容页
  async getEmptyHtml(ctx) {
    await ctx.render('order/empty.html');
  }

  // NOTE: 目前未使用 校验手机号
  async postPhoneJson(ctx) {
    const { receiverPhone, requestNo } = ctx.request.body;

    const data = await new WechatService(ctx).checkPhone({
      receiverPhone,
      requestNo,
    });

    ctx.json(0, 'ok', data);
  }
}

module.exports = OrderController;
