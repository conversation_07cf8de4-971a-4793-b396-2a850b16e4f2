export = [
  // 团长 我的
  [
    'GET',
    ['/wsctrade/groupbuying/myprofit', '/wsctrade/groupbuying/myprofit/index'],
    'groupbuying.MyProfitController',
    ['needPlatformAcl', 'getIndexHtml'],
  ],
  [
    'GET',
    ['/wsctrade/groupbuying/myprofit/getHeaderInfo', '/wsctrade/groupbuying/myprofit/getHeaderInfo.json'],
    'groupbuying.MyProfitController',
    'getHeaderInfoJson',
  ],
  [
    'GET',
    ['/wsctrade/groupbuying/myprofit/getHeaderSettle', '/wsctrade/groupbuying/myprofit/getHeaderSettle.json'],
    'groupbuying.MyProfitController',
    'getHeaderSettleJson',
  ],
  [
    'GET',
    ['/wsctrade/groupbuying/myprofit/getProfitList', '/wsctrade/groupbuying/myprofit/getProfitList.json'],
    'groupbuying.MyProfitController',
    'getProfitListJson',
  ],
  [
    'GET',
    ['/wsctrade/groupbuying/myprofit/getWxBindWallet', '/wsctrade/groupbuying/myprofit/getWxBindWallet.json'],
    'groupbuying.MyProfitController',
    'getWxBindWalletJson',
  ],
  [
    'POST',
    ['/wsctrade/groupbuying/myprofit/postBindWxWallet', '/wsctrade/groupbuying/myprofit/postBindWxWallet.json'],
    'groupbuying.MyProfitController',
    'postBindWxWalletJson',
  ],
  [
    'GET',
    ['/wsctrade/groupbuying/myprofit/getRecordList', '/wsctrade/groupbuying/myprofit/getRecordList.json'],
    'groupbuying.MyProfitController',
    'getRecordListJson',
  ],
  [
    'GET',
    ['/wsctrade/groupbuying/myprofit/getWithdrawDetail', '/wsctrade/groupbuying/myprofit/getWithdrawDetail.json'],
    'groupbuying.MyProfitController',
    'getWithdrawDetailJson',
  ],
  [
    'GET',
    ['/wsctrade/groupbuying/myprofit/getLeaderForward', '/wsctrade/groupbuying/myprofit/getLeaderForward.json'],
    'groupbuying.MyProfitController',
    'getLeaderForwardJson',
  ],
  [
    'POST',
    ['/wsctrade/groupbuying/myprofit/postPutForward', '/wsctrade/groupbuying/myprofit/postPutForward.json'],
    'groupbuying.MyProfitController',
    'postPutForwardJson',
  ],

  // 团长订单
  [
    'GET',
    ['/wsctrade/groupbuying/headerOrder', '/wsctrade/groupbuying/headerOrder/index'],
    'groupbuying.HeaderOrderController',
    ['needPlatformAcl', 'getIndexHtml'],
  ],
  [
    'GET',
    ['/wsctrade/groupbuying/headerOrder/getHeaderOrder', '/wsctrade/groupbuying/headerOrder/getHeaderOrder.json'],
    'groupbuying.HeaderOrderController',
    'getHeaderOrderJson',
  ],
  // 买家订单
  [
    'GET',
    ['/wsctrade/groupbuying/headerOrder/getBuyerOrder.json'],
    'groupbuying.HeaderOrderController',
    'getBuyerOrderJson',
  ],
  [
    'GET',
    ['/wsctrade/groupbuying/headerOrder/getVerifyOrderByCode.json'],
    'groupbuying.HeaderOrderController',
    'getVerifyOrderByCodeJson',
  ],
  [
    'GET',
    ['/wsctrade/groupbuying/headerOrder/getVerifyOrderByMobile.json'],
    'groupbuying.HeaderOrderController',
    'getVerifyOrderByMobileJson',
  ],
  [
    // 核销
    'POST',
    ['/wsctrade/groupbuying/headerOrder/verify.json', '/pay/wsctrade/groupbuying/verify.json'],
    'groupbuying.HeaderOrderController',
    'confirmVerify',
  ],
  [
    // 商品详情
    'GET',
    ['/pay/wsctrade/groupbuying/buyerTrade/itemDetail.json'],
    'groupbuying.BuyerTradeController',
    'getItemDetailJson',
  ],
  // 买家端
  [
    'GET',
    ['/pay/wsctrade_buyerTrade', '/pay/wsctrade_buyerTrade/index'],
    'groupbuying.BuyerTradeController',
    'getTradeHtml',
  ],
  [
    // 下单页
    'GET',
    '/pay/wsctrade_tradeDetail',
    'groupbuying.BuyerTradeController',
    'getDetailHtml',
  ],
  // 滚动订单
  [
    'GET',
    '/pay/wsctrade/groupbuying/buyerTrade/rollOrders.json',
    'groupbuying.BuyerTradeController',
    'getRollOrdersJson',
  ],
  [
    // 门槛费用页
    'GET',
    '/pay/wsctrade_groupbuyJoinFee',
    'groupbuying.JoinFeeController',
    ['needPlatformAcl', 'getIndexHtml'],
  ],
  [
    // 门槛费用页
    'POST',
    '/pay/wsctrade/groupbuying/joinFee/payFee.json',
    'groupbuying.JoinFeeController',
    'postPayFeeJson',
  ],
  [
    'GET',
    [
      '/pay/wsctrade/groupbuying/buyerTrade/getActivityDetail',
      '/pay/wsctrade/groupbuying/buyerTrade/getActivityDetail.json',
    ],
    'groupbuying.BuyerTradeController',
    'getActivityDetailJson',
  ],
  [
    'POST',
    ['/pay/wsctrade/groupbuying/buyerTrade/preTrade', '/pay/wsctrade/groupbuying/buyerTrade/preTrade.json'],
    'groupbuying.BuyerTradeController',
    'postPreTradeJson',
  ],
  [
    'POST',
    ['/pay/wsctrade/groupbuying/buyerTrade/createTrade', '/pay/wsctrade/groupbuying/buyerTrade/createTrade.json'],
    'groupbuying.BuyerTradeController',
    'postCreateTradeJson',
  ],
  [
    'GET',
    ['/pay/wsctrade/groupbuying/buyerTrade/getOrderDetail', '/pay/wsctrade/groupbuying/buyerTrade/getOrderDetail.json'],
    'groupbuying.BuyerTradeController',
    'getOrderDetailJson',
  ],
  [
    'POST',
    ['/pay/wsctrade/groupbuying/buyerTrade/pay', '/pay/wsctrade/groupbuying/buyerTrade/pay.json'],
    'groupbuying.BuyerTradeController',
    'postPaymentOrderJson',
  ],
  [
    // 新模版缓存订单
    'POST',
    '/pay/wsctrade/groupbuying/buyerTrade/cache.json',
    'groupbuying.BuyerTradeController',
    'postCacheJson',
  ],
  [
    // 新模版预下单
    'POST',
    '/pay/wsctrade/groupbuying/buyerTrade/prepare.json',
    'groupbuying.BuyerTradeController',
    'postPrepareJson',
  ],
  [
    // 获取手机号
    'GET',
    '/pay/wsctrade/groupbuying/buyerTrade/phone.json',
    'groupbuying.BuyerTradeController',
    'getPhoneJson',
  ],
  // 获取收益数据
  [
    'GET',
    '/wsctrade/groupbuying/myprofit/getMyProfitList.json',
    'groupbuying.MyProfitController',
    'getMyProfitListJson',
  ],
  [
    // 下单页
    'GET',
    '/pay/wsctrade_cummunityBuyPay',
    'groupbuying.PayController',
    ['needPlatformAcl', 'getIndexHtml'],
  ],
  // 获取订单详情V2
  ['GET', '/wsctrade/groupbuying/getOrderDetailV2.json', 'groupbuying.BuyerTradeController', 'getOrderDetailV2Json'],
  // 汇总
  [
    'GET',
    '/wsctrade/groupbuying/myprofit/queryOrdersSummary.json',
    'groupbuying.HeaderOrderController',
    'queryOrdersSummary',
  ],
  // 获取分销员升级结果
  [
    'GET',
    '/wsctrade/groupbuying/myprofit/querySalemanUpgradeResult.json',
    'groupbuying.MyProfitController',
    'querySalemanUpgradeResult',
  ],
  ['GET', '/wsctrade/groupbuying/settings.json', 'groupbuying.PayController', 'getGroupBuySettings'],
];
