const BaseController = require('../base/BaseController');
const PageException = require('@youzan/iron-base/app/exceptions/PageException');
const FoodOrderService = require('../../services/tangshi/FoodOrderService');
const FoodEatService = require('../../services/tangshi/FoodEatService');
const buildUrlWithCtx = require('@youzan/utils/url/buildUrlWithCtx');

// 点餐页
const ORDER_URL = 'https://h5.youzan.com/wscshop/showcase/tangshi';

class TradeTangshiController extends BaseController {
  async initAcl() {
    await this.acl({
      allowNotLogin: true,
      useAjaxLogin: true,
      forceOauthLogin: true,
      weixinOauthScope: 'snsapi_userinfo',
      kdtId: this.ctx.kdtId
    });
  }

  async getPayHtml(ctx) {
    const queryData = ctx.getQueryData();

    const { bookKey, tableName, mode, alias, orderVersion, orderNo } = queryData;

    const shopSetting = ctx.getState('shopSettings') || {};

    const { kdtId, offlineId, buyerId } = ctx;
    const queryBookKey = queryData.bookKey;

    try {
      const param = {
        alias,
        mode,
        kdtId,
        buyerId,
        fansType: this.buyer.fansType,
        storeId: offlineId
      };
      const buildUrl = buildUrlWithCtx(ctx);

      if (ctx.buyerId > 0) {
        const { path, orderVersion = 0, tableName, orderNo, bookKey } = await new FoodEatService(ctx).getRedirectPath(
          param
        );
        if (path === '/pay') {
          const queryString = `tableName=${encodeURIComponent(
            tableName
          )}&mode=${mode}&alias=${alias}&orderVersion=${orderVersion}&store_id=${offlineId}`;
          // 如果orderNo存在就去Buy页面
          if (orderNo) {
            return ctx.redirect(buildUrl(`/pay/wsctrade_tangshi_buy?orderNo=${orderNo}&${queryString}`, '', kdtId));
          }
          // 如果bookKey更新了，重新设置url的bookKey
          if (queryBookKey !== bookKey) {
            return ctx.redirect(buildUrl(`/pay/wsctrade_tangshi_pay?bookKey=${bookKey}&${queryString}`, '', kdtId));
          }
        } else {
          return ctx.redirect(buildUrl(`${ORDER_URL}?alias=${alias}&kdt_id=${kdtId}&store_id=${offlineId}`, '', kdtId));
        }
      }
    } catch (e) {
      const content = e.errorContent || {};
      throw new PageException(content.code || 11000, content.msg || '数据异常');
    }

    ctx.setGlobal('displayConfig', {
      copyrightPicUrl: shopSetting.isLogoCustomized ? shopSetting.customizedLogo : ''
    });
    ctx.setGlobal('currentPage', '/pay');
    ctx.setGlobal('kdt_id', kdtId);
    ctx.setGlobal('storeId', offlineId);
    ctx.setGlobal('orderNo', orderNo);
    ctx.setGlobal('bookKey', bookKey);
    ctx.setGlobal('tableName', tableName);
    ctx.setGlobal('mode', mode);
    ctx.setGlobal('alias', alias);
    ctx.setGlobal('orderVersion', orderVersion);

    await ctx.render('tangshi/trade.html');
  }

  async getBuyHtml(ctx) {
    const { tableName, mode, alias, orderVersion, orderNo } = ctx.getQueryData();

    const shopSetting = ctx.getState('shopSettings') || {};

    const { kdtId, offlineId, buyerId } = ctx;

    try {
      const param = {
        alias,
        mode,
        kdtId,
        buyerId,
        fansType: this.buyer.fansType,
        storeId: offlineId
      };
      const buildUrl = buildUrlWithCtx(ctx);

      if (ctx.buyerId > 0) {
        const { path } = await new FoodEatService(ctx).getRedirectPath(param);
        if (path !== '/pay') {
          return ctx.redirect(buildUrl(`${ORDER_URL}?alias=${alias}&kdt_id=${kdtId}&storeId=${offlineId}`), '', kdtId);
        }
      }
    } catch (e) {
      const content = e.errorContent || {};
      throw new PageException(content.code || 11000, content.msg || '数据异常');
    }

    ctx.setGlobal('displayConfig', {
      copyrightPicUrl: shopSetting.isLogoCustomized ? shopSetting.customizedLogo : ''
    });
    ctx.setGlobal('currentPage', '/pay');
    ctx.setGlobal('kdt_id', kdtId);
    ctx.setGlobal('storeId', offlineId);
    ctx.setGlobal('orderNo', orderNo);
    ctx.setGlobal('tableName', tableName);
    ctx.setGlobal('mode', mode);
    ctx.setGlobal('alias', alias);
    ctx.setGlobal('orderVersion', orderVersion);

    await ctx.render('tangshi/trade.html');
  }

  async getWaitHtml(ctx) {
    const { orderNo } = ctx.getQueryData();

    const shopSetting = ctx.getState('shopSettings') || {};

    ctx.setGlobal('displayConfig', {
      copyrightPicUrl: shopSetting.isLogoCustomized ? shopSetting.customizedLogo : ''
    });
    ctx.setGlobal('currentPage', '/pay-wait');
    ctx.setGlobal('orderNo', orderNo);
    ctx.setGlobal('kdt_id', ctx.kdtId);

    await ctx.render('tangshi/trade.html');
  }

  async getDetailHtml(ctx) {
    const { orderNo } = ctx.getQueryData();

    const shopSetting = ctx.getState('shopSettings') || {};

    ctx.setGlobal('displayConfig', {
      copyrightPicUrl: shopSetting.isLogoCustomized ? shopSetting.customizedLogo : ''
    });
    ctx.setGlobal('currentPage', '/order-detail');
    ctx.setGlobal('orderNo', orderNo);
    ctx.setGlobal('kdt_id', ctx.kdtId);

    await ctx.render('tangshi/trade.html');
  }

  async getOrderVersionJson(ctx) {
    const { alias } = ctx.getQueryData();
    const data = await new FoodEatService(ctx).getOrderVersion({ alias });
    ctx.json(0, 'ok', data);
  }

  async postPrepareJson(ctx) {
    const { bookKey } = ctx.getPostData();
    const param = {
      buyerId: ctx.buyerId,
      kdtId: ctx.kdtId,
      fansType: this.buyer.fansType,
      bookKey
    };
    const data = await new FoodOrderService(ctx).prepare(param);
    ctx.json(0, 'ok', data);
  }

  async postPreparePaymentJson(ctx) {
    const { orderNo } = ctx.getPostData();
    const param = {
      buyerId: ctx.buyerId,
      kdtId: ctx.kdtId,
      orderNo
    };
    const data = await new FoodOrderService(ctx).preparePayment({
      orderPaymentPreparation: param
    });
    ctx.json(0, 'ok', data);
  }

  async postCreateJson(ctx) {
    const { createParams } = ctx.getPostData();
    const data = await new FoodOrderService(ctx).create(createParams);
    ctx.json(0, 'ok', data);
  }

  async getPaySuccessJson(ctx) {
    const { orderNo } = ctx.getQueryData();
    const param = {
      orderNo,
      fansType: this.buyer.fansType,
      buyerId: ctx.buyerId,
      kdtId: ctx.kdtId,
      storeId: ctx.offlineId
    };
    const data = await new FoodOrderService(ctx).paySuccess(param);
    ctx.json(0, 'ok', data);
  }

  async getOrderDetailJson(ctx) {
    const { alias, orderNo } = ctx.getQueryData();
    const param = {
      alias,
      orderNo,
      buyerId: ctx.buyerId,
      kdtId: ctx.kdtId,
      storeId: ctx.offlineId
    };
    const data = await new FoodOrderService(ctx).orderDetail(param);
    ctx.json(0, 'ok', data);
  }

  async postReOrder(ctx) {
    const { alias, orderNo, mode, storeId } = ctx.getPostData();
    const param = {
      alias,
      mode,
      orderNo,
      kdtId: ctx.kdtId,
      storeId
    };
    const data = await new FoodOrderService(ctx).reorder(param);
    ctx.json(0, 'ok', data);
  }
}

module.exports = TradeTangshiController;
