import { Context } from 'astroboy';
import {
  IGeocoder<PERSON>uery,
  IReverseGeocoderQuery,
  ISearchNearByQuery,
  IGetSuggestionQuery,
  IMapMethods,
} from 'definitions/map/index';

import BaseController from '../base/BaseController';
import MapService from '../../services/map/MapService';

class MapController extends BaseController implements IMapMethods {
  async init() {
    return super.init();
  }

  public currentService(ctx: Context): any {
    return new MapService(ctx);
  }

  async geocoder(ctx: Context) {
    const { query } = ctx.request;
    const params = {
      ...query,
    } as IGeocoderQuery;
    const result = await this.currentService(ctx).geocoder(params);
    return ctx.json(result.status, result.message, result.data);
  }

  async reverseGeocoder(ctx: Context) {
    const { query } = ctx.request;
    const params = {
      ...query,
    } as IReverseGeocoderQuery;
    const result = await this.currentService(ctx).reverseGeocoder(params);
    return ctx.json(result.status, result.message, result.data);
  }

  async searchNearBy(ctx: Context) {
    const { query } = ctx.request;
    const params = {
      ...query,
    } as ISearchNearByQuery;
    const result = await this.currentService(ctx).searchNearBy(params);
    return ctx.json(result.status, result.message, result.data);
  }

  async getSuggestion(ctx: Context) {
    const { query } = ctx.request;
    const params = {
      ...query,
    } as IGetSuggestionQuery;
    const result = await this.currentService(ctx).getSuggestion(params);
    return ctx.json(result.status, result.message, result.data);
  }

  async getLocationByIp(ctx: Context) {
    const params = {
      ip: ctx.firstXff,
    };
    const result = await this.currentService(ctx).getLocationByIp(params);
    return ctx.json(result.status, result.message, result.data);
  }
}

export = MapController;
