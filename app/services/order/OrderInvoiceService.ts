import BaseService from '../base/BaseService';
import {
  IInvoiceDetailParams,
  IInvoiceSwitchInfoParams,
  IApplyInvoiceParams,
  IQueryInvoiceByOrderNoParams,
  IQueryTaxInfoListByCorpNameParams,
  IQueryCompanyDetailTaxInfoParams,
  IGetAuthorUrlParams,
  ISendInvoiceEmail,
} from 'definitions/order/OrderInvoice';

class OrderInvoiceService extends BaseService {
  // 获取发票详情
  public async getInvoiceDetail(params: IInvoiceDetailParams) {
    const { orderNo, kdtId, checkAllowInvoice = true, buyerId } = params;

    return await this.invoke('com.youzan.ebiz.mall.trade.buyer.api.service.OrderService', 'getInvoiceDetail', [
      {
        orderNo,
        kdtId,
        buyerId,
        checkAllowInvoice,
      },
    ]);
  }

  // 获取发票相关后台开关信息
  public async getInvoiceSwitchInfo(params: IInvoiceSwitchInfoParams) {
    return await this.invoke('com.youzan.ebiz.mall.trade.buyer.api.service.OrderService', 'getInvoiceSwitchInfo', [
      params,
    ]);
  }

  // 更新发票信息/开票
  public async applyInvoice(params: IApplyInvoiceParams) {
    const {
      buyerId,
      orderNo,
      kdtId,
      raiseType,
      userName,
      taxpayerId,
      invoiceDetailType,
      emailList,
      address,
      phone,
      openingBankName,
      bankAccount,
      invoiceType,
    } = params;

    return await this.invoke('com.youzan.ebiz.mall.trade.buyer.api.service.OrderService', 'applyInvoice', [
      {
        buyerId,
        orderNo,
        kdtId,
        invoice: {
          raiseType,
          userName,
          taxpayerId,
          invoiceDetailType,
          emailList,
          address,
          phone,
          openingBankName,
          bankAccount,
          invoiceType,
        },
      },
    ]);
  }

  // 获取订单的开票记录（开票成功）
  public async queryInvoiceByOrderNo(params: IQueryInvoiceByOrderNoParams) {
    const { orderNo, includeInvoiceHeaderboolean = true, includeInvoiceDetailboolean = true } = params;

    return await this.invoke('com.youzan.trade.invoice.api.invoice.query.InvoiceQueryService', 'queryInvoiceByBizNo', [
      orderNo,
      {
        includeInvoiceHeaderboolean,
        includeInvoiceDetailboolean,
      },
    ]);
  }

  // 模糊匹配企业名，同时拿到企业 id
  public async queryTaxInfoListByCorpName(params: IQueryTaxInfoListByCorpNameParams) {
    return await this.invoke(
      'com.youzan.trade.invoice.api.taxinfo.query.TaxInfoService',
      'queryTaxInfoListByCorpName',
      [params.corpName]
    );
  }

  // 根据企业 id 查询税收信息（税号）
  public async queryCompanyDetailTaxInfo(params: IQueryCompanyDetailTaxInfoParams) {
    return await this.invoke('com.youzan.trade.invoice.api.taxinfo.query.TaxInfoService', 'queryCompanyDetailTaxInfo', [
      params.companyId,
    ]);
  }

  // 获取微信发票授权链接（发票添加至微信卡包）
  public async getAuthorUrl(params: IGetAuthorUrlParams) {
    const timeStamp = new Date().getTime();

    return await this.invoke('com.youzan.trade.invoice.api.wx.WeChatAuthorizeService', 'getAuthorUrl', [
      Object.assign({}, params, {
        timeStamp: parseInt(String(timeStamp / 1000)), // 微信时间戳（秒）
        bizDate: timeStamp,
        type: 2,
        source: 'web',
        extension: {
          source: {
            fromApp: 'wsc-h5-trade',
          },
        },
      }),
    ]);
  }

  // 发送发票到指定邮箱
  public async sendInvoiceEmail(params: ISendInvoiceEmail) {
    return await this.invoke('com.youzan.trade.invoice.api.tool.InvoiceNoticeService', 'sendInvoiceEmail', [
      Object.assign({}, params, {
        bizDate: new Date().getTime(),
        extension: {
          source: {
            fromApp: 'wsc-h5-trade',
          },
        },
      }),
    ]);
  }
}

export default OrderInvoiceService;
