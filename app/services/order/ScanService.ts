import BaseService from '../base/BaseService';
import { TempQrDTO } from 'definitions/order/TempQr.dto';
import { TempQrVO } from 'definitions/order/TempQr.vo';

class ScanService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.platform.eventbus.api.ScanService';
  }

  /**
   * 获取推送链接
   */
  getTempQr(params: TempQrDTO): Promise<TempQrVO> {
    return this.invoke('getTempQr', [params]);
  }
}

export = ScanService;
