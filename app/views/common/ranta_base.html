{% extends 'common/base.html' %}

{% block cdn_fallback %}
  {{ inlineJs('@youzan/cdn-fallback', 'dist/index.js') | safe }}
{% endblock %}

{% block head_script %}
  {% include 'hummer/init.html' %}
  {% include 'console/init.html' %}
  {% include 'lang-i18n/init.html' %}
{% endblock %}


{% block base_style %}
  {% if preloadBundles %}
    {% for preloadAsset in preloadBundles %}
      <link rel="preload" href="{{preloadAsset}}" as="script">
    {% endfor %}
  {% endif %}
{% endblock %}

{% block base_script %}
  {% include './sentry_script.html' %}
  {{ loadJs('version_ext_entry_js.dll/framework') | safe }}
  {{ loadJs('version_ext_entry_js.vendors') | safe }}
  {% include 'yun-shop-config/extra-operating-area.html' %}
{% endblock %}

{% block project_script %}
  <script src="{{versionUrl}}"></script>
{% endblock %}

{% block base_footer %}
  {% if footerHtml %}
    <div class="common-footer-components">
      {% block page_biz_footer %}
      {% endblock %}
      {{ footerHtml | safe }}
    </div>
  {% endif %}
{% endblock %}
