import _ from 'lodash';
import BaseController from '../base/BaseController';

class AddressController extends BaseController {
  async init() {
    await super.init({
      initMpData: false,
      initMpAccount: false,
      initPlatform: false,
      initGlobalTheme: false,
    });
  }

  /**
   * 扫码购订单mock自提地址（店铺地址作为默认自提地址）
   */
  async getMockSelfFetchJson() {
    const { ctx } = this;
    const { kdtId } = ctx;
     const { store_id } = ctx.getQueryData()

    const shopMetaInfo = await this.getService(
      'iron-base',
      'shop.ShopBaseReadService'
    ).getShopBaseInfoByKdtId(kdtId);

    const pickShopInfo = _.pick(shopMetaInfo, [
      'shopName',
      'kdtId',
      'province',
      'city',
      'area',
      'address',
    ]);
    const result = {
      ...pickShopInfo,
      id: store_id || pickShopInfo.kdtId,
      name: pickShopInfo.shopName,
      county: pickShopInfo.area,
      addressDetail: pickShopInfo.address // 后端防刷单规则需要检验addressDetail，后续json过长可不用address
    };

    ctx.json(0, 'ok', result);
  }
}

export = AddressController;
