import { Context } from 'astroboy';
import BaseController from '../base/BaseController';
import ShelfProgressService from '../../services/api/retail/misc/ShelfProgressService';

/**
 * 零售订单进度查询控制器
 * 使用示例控制器，展示如何使用 ShelfProgressService
 */
class ShelfProgressController extends BaseController {
  /**
   * 查询零售订单进度
   *
   * 请求参数：
   * - orderNo: 订单号
   * - retailSource: 请求来源（可选）
   *
   * 使用示例：
   * POST /retail/shelf-progress/query
   * {
   *   "orderNo": "E20241209123456789",
   *   "retailSource": "h5"
   * }
   */
  async queryOrderProgress(ctx: Context) {
    const { orderNo, retailSource } = ctx.getPostData();
    const { kdtId, adminId } = ctx;

    const shelfProgressService = new ShelfProgressService(ctx);
    const result = await shelfProgressService.queryOrderProgress({
      orderNo,
      retailSource: retailSource || 'h5',
      kdtId: Number(kdtId),
      adminId: Number(adminId),
    });

    return ctx.json(0, '查询成功', result);
  }
}

export = ShelfProgressController;
