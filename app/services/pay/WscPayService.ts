import PayBaseService from './PayBaseService';
import { IPayReq, IPayChannelsReq } from 'definitions/pay/WscPay';
import { PayChannelResponseVO } from 'definitions/pay/PayChannelResponseVO';

interface QueryUmpParams {
  cashierContextRequest: {
    adminId?: string;
    buyerId: string;
    customerType?: string;
    customerId?: string;
    environment: string;
    payReqFrom?: string;
    qqSubOpenId?: string;
    userIp?: string;
    wxSubOpenId?: string;
    wxYzOpenId?: string;
    wxCiticOpenId?: string;
    wxSelfOpenId?: string;
  };
  prepayId: string;
  source: string;
}

class WscPayService extends PayBaseService {
  private get MULTI_STORE_SETTING_SERVICE() {
    return 'com.youzan.multistore.center.api.service.setting.MultiStoreSettingService';
  }

  private get UMP_SERVICE_NAME() {
    return 'com.youzan.pay.unified.cashier.api.v3.PayUmpService';
  }

  public async getWeappPayChannels(partnerId: string, data: IPayChannelsReq): Promise<PayChannelResponseVO> {
    return this.invokePay(
      {
        ...this.PAY_GATEWAY.WEAPP_PAY_CHANNELS,
        partnerId
      },
      Object.assign(
        {
          scene: 'COMMON'
        },
        data
      )
    );
  }

  public async getPayChannels(partnerId: string, data: IPayChannelsReq): Promise<PayChannelResponseVO> {
    return this.invokePay(
      {
        ...this.PAY_GATEWAY.PAY_CHANNELS,
        partnerId
      },
      Object.assign(
        {
          scene: 'COMMON'
        },
        data
      )
    );
  }

  public async pay(partnerId: string, data: IPayReq): Promise<any> {
    return this.invokePay(
      {
        ...this.PAY_GATEWAY.PAY,
        partnerId
      },
      data
    );
  }

  public async pay101(partnerId: string, data: IPayReq): Promise<any> {
    return this.invokePay(
      {
        ...this.PAY_GATEWAY.PAY_101,
        partnerId
      },
      data
    );
  }

  public async pay102(partnerId: string, data: IPayReq): Promise<any> {
    return this.invokePay(
      {
        ...this.PAY_GATEWAY.PAY_102,
        partnerId
      },
      data
    );
  }

  public async pay105(partnerId: string, data: IPayReq): Promise<any> {
    return this.invokePay(
      {
        ...this.PAY_GATEWAY.PAY_105,
        partnerId
      },
      data
    );
  }

  public async pay109(partnerId: string, data: IPayReq): Promise<any> {
    return this.invokePay(
      {
        ...this.PAY_GATEWAY.PAY_109,
        partnerId
      },
      data
    );
  }

  // 获取单是否开启多网点配置
  public async getMultiStoreSetting(params: any) {
    let param = [];
    // eslint-disable-next-line guard-for-in
    for (let key in params) {
      param.push(params[key]);
    }
    return this.invoke(this.MULTI_STORE_SETTING_SERVICE, 'getShopConfig', param);
  }

  public async getUmp(partnerId: string, params: QueryUmpParams) {
    return this.invokePay(
      {
        ...this.PAY_GATEWAY.QUERY_UMP,
        partnerId
      },
      params
    );
  }
}

export = WscPayService;
