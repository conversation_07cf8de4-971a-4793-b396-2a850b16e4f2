import BaseService from '../base/BaseService';
import { IUserContactParam } from 'definitions/order/buy/IUserContactParam';
import { IUserContactModel } from 'definitions/order/buy/IUserContactModel';

class UserContactService extends BaseService {
  get USER_CONTACT_SERVICE() {
    return 'com.youzan.uic.api.userAddress.service.UserContactService';
  }

  // 获取联系人列表
  async getContactList(params: any): Promise<IUserContactModel[]> {
    return this.invoke(this.USER_CONTACT_SERVICE, 'get', [params]);
  }

  // 获取联系人列表
  async getContactListFast(params: any) {
    return this.invokeFast(this.USER_CONTACT_SERVICE, 'get', [params], {
      responseType: 'text'
    });
  }

  // 新增联系人
  async addContact(params: IUserContactParam): Promise<number> {
    return this.invoke(this.USER_CONTACT_SERVICE, 'add', [params]);
  }

  // 更新联系人
  async updateContact(params: IUserContactModel & IUserContactParam) {
    if (params.id) {
      params.contactId = params.id;
    }
    return this.invoke(this.USER_CONTACT_SERVICE, 'update', [params]);
  }

  // 删除联系人
  async deleteContact(params: IUserContactParam) {
    return this.invoke(this.USER_CONTACT_SERVICE, 'delete', [params]);
  }

  // 设置默认联系人
  async setDefaultContact(params: IUserContactParam) {
    return this.invoke(this.USER_CONTACT_SERVICE, 'setDefault', [params]);
  }
}

export = UserContactService;
