import { getFormattedGoods } from './getter/goods';
import { grouponType, isEmptyObject } from './utils/utils';
import toYuan from '@youzan/utils/money/toYuan';

function sum(arr) {
  return arr.reduce((prev, curr) => prev + curr, 0);
}

// 定金预售
function isDepositPresale(state = {}) {
  return state.pay?.multiPhase && state.pay?.phasePayment?.bizCode === 'DOWN_PAYMENT_PRE';
}

// 配送方式
function getExpressType(state) {
  if (state.display.showExpressTab && state.selfFetch.isAllow) {
    return state.address.activeTab === 1 ? 'self-fetch' : 'express';
  }
  return state.selfFetch.isAllow ? 'self-fetch' : 'express';
}

function getIsPeriodBuy(state) {
  return state.periodBuy.info || state.periodBuy.planTime;
}

function getAllCoupons(state) {
  return state.coupon.list.concat(state.coupon.externalList || []).map((coupon) => {
    if (coupon.condition) {
      coupon.condition = coupon.condition.replace(/[,，]/gi, '\n');
    }
    return coupon;
  });
}

// 选中的优惠券
function getChosenCoupon(state, getters = {}) {
  return getters.allCoupons.find((coupon) => coupon.id === state.coupon.chosenId) || {};
}

// 选中的优惠券列表 优惠券叠加
function getChosenCoupons(state, getters = {}) {
  if (state.coupon.chosenIds.length) {
    return getters.allCoupons.filter((coupon) =>
      state.coupon.chosenIds.find((id) => id === coupon.id)
    );
  }
  return Object.keys(getters.chosenCoupon).length ? [getters.chosenCoupon] : [];
}

function getChosenCouponIndex(state, getters) {
  return getters.allCoupons.findIndex((coupon) => coupon.id === state.coupon.chosenId);
}

// 优惠券抵扣后的待支付金额
function getCouponDecreasedPrice(state, getters) {
  if (getters.newCouponProcess) {
    // 新流程中realPay已经减去优惠券的金额了
    return Math.max(0, state.pay.realPay);
  }
  return Math.max(0, state.pay.realPay - getters.couponDecrease);
}

// 支付优惠
function getPayUmpDiscountMoney(state) {
  if (state.pay.phasePayment && state.pay.phasePayment.phaseItems) {
    return state.pay.phasePayment.phaseItems.reduce(
      (prev, curr) => prev.payUmpDiscountMoney + curr.payUmpDiscountMoney
    );
  }
  return 0;
}

// 选中储值卡的总金额
function getValueCardTotalAmount(state) {
  const checked = state.valueCard.list.filter(
    (card) => state.valueCard.checked.indexOf(card.summaryCardNo) !== -1
  );
  return sum(checked.map((item) => item.balance));
}

// 储值卡抵扣金额
function getValueCardDecrease(state, { aggregatedRechargePayAmount }) {
  const { pay, extensions } = state;

  // 随单充并用了余额抵扣 展示realPay
  const useBalancePay = extensions?.AGGREGATED_PAY_TOOL === 'card' && aggregatedRechargePayAmount;
  if (useBalancePay) {
    return pay.realPay || 0;
  }

  // 支付页直接返回金额
  return pay.valueCardPayPrice || 0;
}

// 最终分阶段支付金额
function getCouponDecreasedPhasePayment(state, getters) {
  const { pay } = state;

  if (!pay.multiPhase) {
    return [];
  }

  // 抵扣尾款
  // 邮费无法抵扣
  const prices = pay.phasePayment.phaseItems.map((item) => item.currentPrice);
  let decrease = getters.couponDecrease;
  if (getters.newCouponProcess) {
    decrease = 0;
  }
  const finalPrices = [];
  finalPrices[1] = Math.max(state.pay.postage, 0, prices[1] - decrease);
  decrease -= prices[1] - finalPrices[1];

  // 抵扣定金
  finalPrices[0] = Math.max(0, prices[0] - decrease);

  return finalPrices;
}

function getCurrentPostage(state) {
  const { postage } = state;
  const items = postage?.postageItems || [];
  if (Array.isArray(items)) {
    return items.filter((item) => item.expressType === postage.currentExpressType)[0] || {};
  }
  return {};
}

// 订单最终分阶段支付金额
function getFinalPhasePayment(state) {
  const { pay } = state;

  if (!pay.multiPhase) {
    return [];
  }

  // 支付页直接返回金额
  return pay.phasePayment.phaseItems.map((item) => item.buyerRealPay).map((item) => toYuan(item));
}

// 订单最终待支付金额
function getFinalNeedPayPrice(state, { finalPhasePayment, aggregatedRechargePayAmount }) {
  const { pay, extensions } = state;
  const { AGGREGATED_PAY_TOOL: aggregatedPayTool } = extensions || {};

  // 多阶段支付，返回阶段支付金额
  if (pay.multiPhase) {
    return finalPhasePayment[pay.phasePayment.currentPhase - 1];
  }

  if (aggregatedRechargePayAmount) {
    // 如果使用了余额抵扣 则直接展示充值金额
    if (aggregatedPayTool === 'card') {
      return toYuan(aggregatedRechargePayAmount);
    }
    return toYuan(aggregatedRechargePayAmount + pay.realPay);
  }

  return toYuan(pay.realPay);
}

// 是否是先用后付
function showPriorUseSummary(state) {
  const { show = false, enable = false, confirm = '0' } = state.useBeforePay || {};
  return show && enable && confirm === '1';
}

// 优惠券抵扣金额
function getCouponDecrease(state, getters) {
  // TODO: 优惠券叠加使用多张券
  const total = getters.chosenCoupons.reduce((result, coupon) => {
    const val = coupon.value || coupon.denominations || 0;
    return result + val;
  }, 0);
  return total;
}

function parseJSON(data, defaultValue = {}) {
  if (typeof data === 'string') {
    try {
      return JSON.parse(data);
    } catch (e) {
      return defaultValue;
    }
  }
  return defaultValue;
}

// 获取聚合支付的充值金额
function getAggregatedRechargePayAmount(state) {
  const { extensions } = state;
  const { AGGREGATED_PAY_TYPE: aggregatedPayType, AGGREGATED_PAY_INFO: aggregatedPayInfo } =
    extensions;
  if (aggregatedPayType === 'RECHARGE') {
    const afterParsed = parseJSON(aggregatedPayInfo, null);
    if (!afterParsed) {
      return 0;
    }
    return Array.isArray(afterParsed) ? afterParsed[0].payAmount : afterParsed.payAmount;
  }
}

export default function ({ state = {} } = {}) {
  const expressType = getExpressType(state);
  const isPeriodBuy = getIsPeriodBuy(state);
  const allCoupons = getAllCoupons(state);
  const chosenCoupon = getChosenCoupon(state, { allCoupons });
  const chosenCoupons = getChosenCoupons(state, { allCoupons, chosenCoupon });
  const { newCouponProcess } = state.order;
  const chosenCouponIndex = getChosenCouponIndex(state, { allCoupons });
  const couponDecrease = getCouponDecrease(state, { chosenCoupons });
  const couponDecreasedPrice = getCouponDecreasedPrice(state, { newCouponProcess, couponDecrease });
  const couponDecreasedPhasePayment = getCouponDecreasedPhasePayment(state, { couponDecrease });
  const valueCardTotalAmount = getValueCardTotalAmount(state);
  // 聚合支付的充值金额
  const aggregatedRechargePayAmount = getAggregatedRechargePayAmount(state);
  const valueCardDecrease = getValueCardDecrease(state, {
    valueCardTotalAmount,
    couponDecreasedPhasePayment,
    couponDecreasedPrice,
    aggregatedRechargePayAmount,
  });

  const finalPhasePayment = getFinalPhasePayment(state);
  const finalNeedPayPrice = getFinalNeedPayPrice(state, {
    finalPhasePayment,
    aggregatedRechargePayAmount,
  });

  return {
    isDepositPresale: isDepositPresale(state),

    // 当前配送方式
    currentPostage: getCurrentPostage(state),

    showPriorUseSummary: showPriorUseSummary(state),

    // 配送方式
    expressType,

    // 是否为周期购
    isPeriodBuy,

    //
    formattedGoods: getFormattedGoods({
      state,
      getters: {
        expressType,
        isPeriodBuy,
        postage: state.postage,
      },
    }),

    // 是否有展示卡
    hasDisplayCard: !isEmptyObject(state.displayCard) && state.display.openDisplayCard,

    // 选中的优惠券
    chosenCoupon,
    // 选中的优惠券列表
    chosenCoupons,

    chosenCouponIndex,

    // 优惠券抵扣金额
    couponDecrease,

    // 支付优惠
    payUmpDiscountMoney: getPayUmpDiscountMoney(state),
    couponDecreasedPrice,
    prepayCardDecrease: valueCardDecrease,
    finalPhasePayment,
    finalNeedPayPrice,
    newCouponProcess: state.order.newCouponProcess,

    // 是否显示团长代收栏
    showGrouponCell:
      expressType === 'express' &&
      state.groupon.showAgencyReceive &&
      !(!state.groupon.isHeader && !state.groupon.isChecked),

    // 可选择是否团长代收
    isGrouponOptionalReceive: grouponType.optionalReceive === state.groupon.receiveState,

    aggregatedRechargePayAmount,
  };
}
