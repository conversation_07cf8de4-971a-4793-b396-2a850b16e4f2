type ConditionParams = {
  orderPermission?: any;
  orderItem?: any;
  orderExtra?: any;
  isXhsApp?: boolean;
  isKsApp?: boolean;
  isAlipayApp?: boolean;
  isQQApp?: boolean;
  isWeapp?: boolean;
  directBuyAgainBtnConfig?: any;
  orderMark?: string;
};
type ConditionFunction = (params: ConditionParams) => boolean;
interface ButtonConfig {
  text: string;
  value: string;
  condition?: ConditionFunction | boolean;
  disabled?: boolean;
  showCoupon?: boolean;
  icon?: string;
}
interface BtnConf {
  cancel: ButtonConfig;
  cancelPayOnDelivery: ButtonConfig;
  topay: ButtonConfig;
  transport: ButtonConfig;
  topayDeposit: ButtonConfig;
  topayRetainage: ButtonConfig;
  topayRetainageDisabled: ButtonConfig;
  confirmReceive: ButtonConfig;
  confirmReceiveHotel: ButtonConfig;
  laterReceive: ButtonConfig;
  buyAgain: ButtonConfig;
  directBuyAgain: ButtonConfig;
  evaluate: ButtonConfig;
  lotteryResult: ButtonConfig;
  inviteHelp: ButtonConfig;
  deleteOrder: ButtonConfig;
  expressReminderHotel: ButtonConfig;
  expressReminder: ButtonConfig;
  paidPromotion: ButtonConfig;
  coupon: ButtonConfig;
  fissionCoupon: ButtonConfig;
  liveQrCode1: ButtonConfig;
  liveQrCode2: ButtonConfig;
  grouponDetail: ButtonConfig;
  viewCardDetail: ButtonConfig;
  selfFetchCode: ButtonConfig;
  viewEvaluate: ButtonConfig;
  gift: ButtonConfig;
  groupDetail: ButtonConfig;
  courseDetail: ButtonConfig;
  hotelComboUse: ButtonConfig;
  shared: ButtonConfig;
  peerpay: ButtonConfig;
}
export { BtnConf, ButtonConfig, ConditionParams }