{% extends '../common/base.html' %}

{% block page_title %} {{ title }} {% endblock %}

{% block page_style %}
  {% include '../common/zan_pay_style.html' %}
  {{ loadCss('order/buy') | safe }}
{% endblock %}

{% block page_content %}
  <style>
    .app-wrapper{position:relative}.buy-skeleton{position:absolute;left:0;top:0;width:100%;z-index:10}
  </style>

  <div class="app-wrapper">
    <img class="js-skeleton buy-skeleton" src="https://img01.yzcdn.cn/public_files/2020/09/21/6274ce7d710084ada330e16f6b6f6157.png" />
    <div id="app"></div>
  </div>
{% endblock %}

{% block page_script %}
  {% include 'ecloud-view/extension.html' %}
  {% include '../common/zan_pay_script.html' %}
  {{ loadJs('order/buy') | safe }}
{% endblock %}

{% block page_biz_footer %}
  {% if showHaitaoFooter %}
    <style type="text/css">
      .buy-footer__haitao-notice{padding: 0 15px 12px 15px;color:#323233;font-size:12px;}.buy-footer__haitao-notice span{color:#576B95;cursor:pointer;}
      .buy-footer__haitao-notice__dialog .van-dialog__message{white-space: pre-line;}
      .buy-footer__haitao-notice__dialog .van-dialog__message--has-title {
        padding-top: 32px;
      }
      .buy-footer__haitao-notice__dialog .van-dialog__header{
        padding-top: 20px;
      }
      .buy-footer__haitao-notice__dialog .van-dialog__content {
        min-height: 250px;
        min-height: calc(50vh - 94px);
        max-height: 400px;
        max-height: calc(80vh - 94px);
        overflow-y: scroll;
      }
    </style>
    <div class="buy-footer__haitao-notice">
      提交订单则表示你已同意<span class="buy-footer__haitao-notice__consumers">《消费者告知书》</span>及<span class="buy-footer__haitao-notice__import">《进口个人委托申报委托函》</span>
    </div>
  {% endif %}
{% endblock %}
