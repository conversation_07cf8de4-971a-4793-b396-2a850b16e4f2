{"extensionId": "@wsc-h5-trade/order-list__common-block", "name": "@wsc-h5-trade/order-list__common-block", "version": "1.6.3", "pathInBundle": "common-block", "bundle": "https://b.yzcdn.cn/wsc-h5-trade/ext/ranta-extension_70863473.js", "hasWidget": false, "data": {"provide": {"kdtId": ["r", "w"], "activityTab": ["r", "w"], "searchCondition": ["r", "w"], "bizName": ["r", "w"]}}, "event": {"listen": ["activityTabChange", "searchChange", "doOrderFooterAction"], "emit": ["handleShare"]}, "process": {"define": ["fetchMoreOrder"], "invoke": ["getFetchListExtraData"]}, "lambda": {"consume": ["detailUrlFormatter", "shopHomeUrlFormatter", "shopNameFormatter"]}, "sandbox": {"id": "", "level": "Unsafe"}}