/**
 * 针对map服务接口返回值的格式化：
 * response - 对应接口第三方返回的数据
 * isKeep - 是否保持原样，如果是否，则会处理成统一的数据格式
 */
import { pick } from 'lodash';
import { MAP_VERSION, POI_TYPE } from '../../../constants/map';
import { invokeResponse, getCleanData, stringifyAddress } from './utils';
import {
  IGeocoderVo,
  IGeocoderDTO,
  IReverseGeocoderDTO,
  ISearchNearByVo,
  ISearchNearByDTO,
  IGetSuggestionVo,
  IGetSuggestionDTO,
  IReverseGeocoderVo,
  IQQMapResponseDTO,
  IAMapResponseDTO,
  IMapMethods,
  IGetLocationByIpDTO,
  IGetLocationByIpVo,
} from 'definitions/map/index';

const AMapFormatResponse: IMapMethods = {
  geocoder(response: IAMapResponseDTO, isKeep?: boolean): IGeocoderDTO {
    const { status, info, infocode, geocodes } = response;
    // 0为高德api报错
    if (+status === 0) {
      return invokeResponse(+infocode, info, null);
    }
    if (isKeep) {
      return invokeResponse(0, '', geocodes);
    }
    const geocode = getCleanData(geocodes[0]);
    const { location, province, city, district, street, adcode, formatted_address: formattedAddress } = geocode;
    const [lng, lat] = location.split(',');
    const data: IGeocoderVo = {
      province,
      city,
      district,
      street,
      location: {
        lng: +lng,
        lat: +lat,
      },
      adcode,
      formattedAddress,
    };
    return invokeResponse(0, '', data);
  },
  reverseGeocoder(response: IAMapResponseDTO, isKeep?: boolean): IReverseGeocoderDTO {
    const { status, info, infocode, regeocode } = response;
    // 0为高德api报错
    if (+status === 0) {
      return invokeResponse(+infocode, info, null);
    }
    if (isKeep) {
      return invokeResponse(0, '', regeocode);
    }
    const { formatted_address: formattedAddress, addressComponent } = regeocode;
    const addressData = getCleanData(addressComponent);
    const { country, province, city, district, township, adcode } = addressData;
    return invokeResponse(0, '', {
      formattedAddress,
      country,
      province,
      // 省级直辖市city无值，实际市名存储于province，例如“北京市”
      city: city || province,
      district,
      street: township,
      adcode,
    });
  },
  searchNearBy(response: IAMapResponseDTO, isKeep?: boolean): ISearchNearByDTO {
    const { status, info, infocode, pois } = response;
    // 0为高德api报错
    if (+status === 0) {
      return invokeResponse(+infocode, info, []);
    }
    if (isKeep) {
      return invokeResponse(0, '', pois);
    }
    const data: ISearchNearByVo[] = pois.map(
      (poi: any): ISearchNearByVo => {
        const item = getCleanData(poi);
        const { id, name, distance, pname, cityname, adname, address, adcode, type } = item;
        const [lng, lat] = poi.location.split(',');
        return {
          id,
          title: name,
          address,
          location: { lng: +lng, lat: +lat },
          province: pname,
          city: cityname,
          district: adname,
          adcode,
          distance,
          poiType: POI_TYPE[MAP_VERSION.AMAP],
          formattedAddress: stringifyAddress([pname, cityname, adname, address]),
          category: type,
        };
      }
    );
    return invokeResponse(0, '', data);
  },
  getSuggestion(response: IAMapResponseDTO, isKeep?: boolean): IGetSuggestionDTO {
    const { status, infocode, info, tips } = response;
    // 0为高德api报错
    if (+status === 0) {
      return invokeResponse(+infocode, info, []);
    }
    if (isKeep) {
      return invokeResponse(0, '', tips);
    }
    const data: IGetSuggestionVo[] = tips
      .filter((item: any) => !(Array.isArray(item.address) && !item.address.length)) // 过滤空地址的数据
      .map(
        (poi: any): IGetSuggestionVo => {
          const item = getCleanData(poi);
          const [lng, lat] = item.location.split(',');
          return {
            ...pick(item, ['id', 'adcode']),
            title: item.name,
            address: item.address,
            location: { lng: +lng, lat: +lat },
            district: item.district,
            poiType: POI_TYPE[MAP_VERSION.AMAP],
            formattedAddress: `${item.district}${item.address || ''}`,
          };
        }
      );
    return invokeResponse(0, '', data);
  },
  getLocationByIp(response: IAMapResponseDTO, isKeep?: boolean): IGetLocationByIpDTO {
    const { status, infocode, info, ...rect } = response;
    // 0为高德api报错
    if (+status === 0) {
      return invokeResponse(+infocode, info, {} as IGetLocationByIpVo);
    }
    if (isKeep) {
      return invokeResponse(0, '', rect as IGetLocationByIpVo);
    }
    const { province, city } = getCleanData(rect);
    return invokeResponse(0, '', {
      province,
      city,
    } as IGetLocationByIpVo);
  },
};

const QQMapFormatResponse: IMapMethods = {
  geocoder(response: IQQMapResponseDTO, isKeep?: boolean): IGeocoderDTO {
    const { status, message, result } = response;
    if (status) {
      return invokeResponse(status, message, null);
    }
    if (isKeep) {
      return invokeResponse(0, '', result);
    }
    const { title, location, address_components: addressComponents, ad_info: adInfo } = result;
    const { province, city, district, street } = addressComponents;
    const formattedAddress = stringifyAddress([province, city, district, title]);
    const data: IGeocoderVo = {
      province,
      city,
      district,
      street,
      location,
      adcode: adInfo.adcode,
      formattedAddress,
    };
    return invokeResponse(0, '', data);
  },
  reverseGeocoder(response: IQQMapResponseDTO, isKeep?: boolean): IReverseGeocoderDTO {
    const { status, message, result } = response;
    if (status) {
      return invokeResponse(status, message, null);
    }
    if (isKeep) {
      return invokeResponse(0, '', result);
    }
    const { formatted_addresses: formattedAddresses, address_component: addressComponent, ad_info: adInfo } = result;
    const data: IReverseGeocoderVo = {
      formattedAddress: formattedAddresses.recommend,
      ...pick(addressComponent, ['province', 'city', 'district', 'street']),
      adcode: adInfo.adcode,
      country: addressComponent.nation,
    };
    return invokeResponse(0, '', data);
  },
  searchNearBy(response: IQQMapResponseDTO, isKeep?: boolean): ISearchNearByDTO {
    const { status, message, result } = response;
    if (status) {
      return invokeResponse(status, message, []);
    }
    if (isKeep) {
      return invokeResponse(0, '', result);
    }
    const data: ISearchNearByVo[] = result.pois.map(
      (item: any): ISearchNearByVo => {
        const { id, title, address, location, ad_info: adInfo, _distance, category } = item;
        const { province, city, district, adcode } = adInfo;
        return {
          id,
          title,
          address,
          location,
          province,
          city,
          district,
          adcode,
          distance: _distance,
          poiType: POI_TYPE[MAP_VERSION.QQMAP],
          formattedAddress: stringifyAddress([province, city, district, address || '']),
          category,
        };
      }
    );
    return invokeResponse(0, '', data);
  },
  getSuggestion(response: IQQMapResponseDTO, isKeep?: boolean): IGetSuggestionDTO {
    const { status, message, data } = response;
    if (status) {
      return invokeResponse(status, message, []);
    }
    if (isKeep) {
      return invokeResponse(0, '', data);
    }
    const _data = data.map(
      (item: any): IGetSuggestionVo => {
        const district = stringifyAddress([item.province, item.city, item.district]);
        return {
          ...pick(item, ['id', 'adcode', 'title']),
          address: item.address,
          location: item.location,
          district,
          category: item.category,
          poiType: POI_TYPE[MAP_VERSION.QQMAP],
          formattedAddress: `${district}${item.address || ''}`,
        };
      }
    );

    return invokeResponse(0, '', _data);
  },
  getLocationByIp(response: IQQMapResponseDTO, isKeep?: boolean): IGetLocationByIpDTO {
    const { status, message, result } = response;
    if (status) {
      return invokeResponse(status, message, {} as IGetLocationByIpVo);
    }
    if (isKeep) {
      return invokeResponse(0, '', result);
    }
    const { province, city } = result?.ad_info || {};
    return invokeResponse(0, '', {
      province,
      city,
    } as IGetLocationByIpVo);
  },
};

const formatResponse = {
  [MAP_VERSION.AMAP]: AMapFormatResponse,
  [MAP_VERSION.QQMAP]: QQMapFormatResponse,
};

export default formatResponse;
