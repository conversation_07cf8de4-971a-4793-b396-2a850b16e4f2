<template>
  <van-cell v-if="isYZGuarantee" class="youzan-secured" :border="false" :value="securedLifeCycleStatus">
    <span slot="title" class="youzan-secured__title">
      <div class="youzan-secured__icon-wrap">
        <img class="youzan-secured__icon" src="//b.yzcdn.cn/security/fangxin-green-small.png" alt="" />
      </div>
      <span class="youzan-secured__text">商家赠送</span>
    </span>
  </van-cell>
</template>
<script>
import { Cell } from 'vant';
import get from 'utils/get';

const LIFE_CYCLE_STATE = {
  INIT: {
    label: '保障待生效',
  },
  USING: {
    label: '保障生效中',
  },
  DISCARD: {
    label: '保障未生效',
  },
  INVALID: {
    label: '保障已结束',
  },
  EXPIRED: {
    label: '保障已结束',
  },
  PREPAID: {
    label: '有赞放心购已优先垫付',
  },
  PRE_EXPIRED: {
    label: '保障已结束',
  },
  PRE_DISCARD: {
    label: '保障未生效',
  },
  UNSECURED: {
    show: false,
  },
};

export default {
  name: 'YouzanSecured',

  components: {
    [Cell.name]: Cell,
  },

  props: {
    orderItem: Object,
  },

  data() {
    return {};
  },

  computed: {
    isYZGuarantee() {
      return this.isSecured === null ? get(this.orderItem, 'orderExtra.isYZGuarantee', false) : this.isSecured;
    },

    isSecured() {
      return get(this.orderItem, 'financeServiceVO.yzSecuredServiceVO.isSecured', null);
    },

    securedLifeCycleStatus() {
      const status = get(this.orderItem, 'financeServiceVO.yzSecuredServiceVO.securedLifeCycleStatus', 'USING');

      return get(LIFE_CYCLE_STATE[status], 'label', '');
    },
  },
};
</script>
<style scoped lang="scss">
.youzan-secured {
  padding: 4px 12px;

  ::v-deep.van-cell {
    &__title {
      display: flex;
      align-items: center;
      flex: 3;
      width: 0;
      margin-right: 8px;
    }

    &__value {
      color: #646566;
      font-size: 12px;
      flex: none;
    }
  }

  &__title {
    display: inline-flex;
    align-items: center;
    width: 0;
    flex: 1;
  }

  &__icon-wrap {
    width: 72px;
    margin-right: 8px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  &__icon {
    height: 14px;
  }

  &__text {
    color: #969799;
    line-height: 16px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
    font-size: 12px;
  }
}
</style>
