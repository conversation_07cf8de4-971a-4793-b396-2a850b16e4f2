import BaseService from '../base/BaseService';

interface IreCaptchaCheckDto {
  bizType: number;
  token: string;
}
class BehaviorCaptchaService extends BaseService {
  get BEHAVIOR_CAPTCHA_SERVICE() {
    return 'com.youzan.uic.captcha.api.service.BehaviorCaptchaService';
  }

  /**
   * 行为验证组件二次校验接口,用于校验行为验证的结果
   * http://zanapi.qima-inc.com/site/service/view/456193
   * @param params
   */
  public async secondCheckBehaviorToken(params: IreCaptchaCheckDto): Promise<any> {
    return this.invoke(this.BEHAVIOR_CAPTCHA_SERVICE, 'secondCheckBehaviorToken', [params]);
  }
}

export default BehaviorCaptchaService;
