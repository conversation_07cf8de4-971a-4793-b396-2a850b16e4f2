const BaseService = require('../base/BaseService');

class EvaluationSerivce extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.ebiz.mall.trade.buyer.api.service.EvaluationSerivce';
  }

  get REFUND_EVALUATION_SERVICE_NAME() {
    return 'com.youzan.ebiz.mall.trade.buyer.api.service.RefundEvaluationQueryService';
  }

  async createRefundEvaluation(params: any) {
    return await this.invoke(this.SERVICE_NAME, 'createRefundEvaluation', [params]);
  }

  async getEvaluationByRefundId(params: any) {
    return await this.invoke(this.REFUND_EVALUATION_SERVICE_NAME, 'getByRefundId', [params]);
  }
}

export = EvaluationSerivce;
