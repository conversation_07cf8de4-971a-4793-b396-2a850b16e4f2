/**
 * 地图api路由生成规则，路由格式：/wsctrade/(MAP_TYPE)/api/(MAP_METHODS).json
 * 如何新增一个方法？新增方法名加入：MAP_METHODS枚举即可自动注册3种类型的地图api
 */
import { MAP_METHODS, MAP_TYPE } from '../../constants/map';

// 所有支持的map方法
const mapMethods: MAP_METHODS[] = Object.values(MAP_METHODS);

// 不同地图版本对应的controller
const MAP_CONTROLLER = {
  [MAP_TYPE.MAP]: 'map.MapController',
  [MAP_TYPE.AMAP]: 'map.AMapController',
  [MAP_TYPE.QQMAP]: 'map.QQMapController',
};

// 创建以方法为基准的生成器
const createRoute = (v: MAP_TYPE, method: MAP_METHODS) => {
  return ['GET', [`/wsctrade/${v}/api/${method}.json`], MAP_CONTROLLER[v], method];
};

const routes = mapMethods
  .map((method) => {
    return [createRoute(MAP_TYPE.MAP, method), createRoute(MAP_TYPE.AMAP, method), createRoute(MAP_TYPE.QQMAP, method)];
  })
  .flat();

export = routes;
