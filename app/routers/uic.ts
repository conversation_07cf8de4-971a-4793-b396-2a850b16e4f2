export = [
  [
    'GET',
    '/wsctrade/uic/address/getAllRegion.json',
    'uic.UserAddressController',
    'getAllRegionJson',
  ],
  [
    'GET',
    '/wsctrade/uic/address/getRegionByLevel.json',
    'uic.UserAddressController',
    'getRegionByLevelJson',
  ],
  [
    'POST',
    '/wsctrade/uic/address/getAddressList.json',
    'uic.UserAddressController',
    'postAddressListJson',
  ],
  [
    'POST',
    '/wsctrade/uic/address/addAddress.json',
    'uic.UserAddressController',
    'postAddAddressJson',
  ],
  [
    'POST',
    '/wsctrade/uic/address/updateAddress.json',
    'uic.UserAddressController',
    'postUpdateAddressJson',
  ],
  /* v2/addAddress.json 增加接口验签 */
  [
    'POST',
    '/wsctrade/uic/address/v2/addAddress.json',
    'uic.UserAddressController',
    'postAddAddressJson',
  ],
  /* v2/updateAddress.json 增加接口验签 */
  [
    'POST',
    '/wsctrade/uic/address/v2/updateAddress.json',
    'uic.UserAddressController',
    'postUpdateAddressJson',
  ],
  [
    'POST',
    '/wsctrade/uic/address/deleteAddress.json',
    'uic.UserAddressController',
    'postDeleteAddressJson',
  ],
  [
    'POST',
    '/wsctrade/uic/address/parseAddress.json',
    'uic.UserAddressController',
    'getParseAddressJson',
  ],
  [
    'POST',
    '/wsctrade/uic/address/parseAliAddress.json',
    'uic.UserAddressController',
    'getParseAliAddressJson'
  ],
  [
    'POST',
    '/wsctrade/uic/contact/getContactList.json',
    'uic.UserContactController',
    'postContactListJson',
  ],
  [
    'POST',
    '/wsctrade/uic/contact/addContact.json',
    'uic.UserContactController',
    'postAddContactJson',
  ],
  [
    'POST',
    '/wsctrade/uic/contact/updateContact.json',
    'uic.UserContactController',
    'postUpdateContactJson',
  ],
  [
    'POST',
    '/wsctrade/uic/contact/deleteContact.json',
    'uic.UserContactController',
    'postDeleteContactJson',
  ],
  [
    'POST',
    '/wsctrade/uic/account/updateAddressAuth.json',
    'uic.UserAccountController',
    'postUpdateAddressAuthJson',
  ],
];
