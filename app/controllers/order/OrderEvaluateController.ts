import { get, escape } from 'lodash';
import { Context } from 'astroboy';
import { PageException } from '@youzan/iron-base';
import BaseController from '../base/BaseController';
import SecureService from '../../services/order/SecureService';
import OrderDetailService from '../../services/order/OrderDetailService';
import OrderEvaluateService from '../../services/order/OrderEvaluateService';
import DataAggregateService from '../../services/order/DataAggregateService';
import EvaluateSearchService from '../../services/trade/EvaluateSearchService';
import {
  GetGoodsInfoDto,
  IItemEvaluationDetailModel,
  ICheckRedirectWxCommentPluginOptions,
} from 'definitions/order/OrderEvaluate';
import { IItemInfoDto } from 'definitions/order/detail/ItemInfo';
import { checkPureWscSingleStore } from '@youzan/utils-shop';
import ShopConfigurationService from '../../services/shop/ShopConfigurationService';
import { getTradeApolloConfig } from '../../lib/Apollo';
import CompareVersion from '../../lib/CompareVersion';
import ShopBaseReadOuterService from '../../services/shop/ShopBaseReadOuterService';
import ScrmWhiteListService from '../../services/scrm/ScrmWhiteListService';
import { isInGrayReleaseByKdtId } from '../../lib/WhiteListUtils';

enum ID_TYPE {
  FANS_ID = 1,
  USER_ID = 2,
}

enum EVALUATION_SOURCE {
  REMIND_EVALUATE = 'remind_evaluation_component', // 个人中心催评
  WECHAT_TEMPLATE = 'wechat_template_message', // 微信消息模板
  MANUAL_CONFIRM = 'confirm_goods', // 手动确认收货
  SHORT_MESSAGE_NOTICE = 'evaluate_notice_short_message', // * 评价提醒-短信
  WEAPP_NOTICE = 'evaluate_notice_weapp', // * 评价提醒-小程序订阅消息
}

enum CRM_UPGRADE_STATUS {
  /** scrm项目迁移未开始 */
  NOT_START = 0,
  /** scrm项目迁移中 */
  PROCESSING = 1,
  /** scrm项目迁移完成 */
  COMPLETED = 2,
  // 回退中
  REVERTING = 3,
  // 回退完成
  REVERTED = 4,
  // 灰度商家未开始迁移
  GRAYSCALA = 5,
}

class OrderEvaluateController extends BaseController {
  kosTokenVerified = false;

  async init() {
    await super.init({
      validKdtId: true,
      initGlobalTheme: true,
      initBaseFooter:
        !this.ctx.acceptJSON &&
        !/from=ivr/.test(this.ctx.url) &&
        (/\/wsctrade\/order\/evaluate\/close?/.test(this.ctx.url) ||
          /\/wsctrade\/order\/evaluate\/delete?/.test(this.ctx.url)),
    });
    const globalTheme = this.ctx.getState('globalTheme' as never);
    this.ctx.setGlobal('globalTheme', globalTheme);
  }

  // Acl
  async orderEvaluateAcl(ctx: Context) {
    // kostoken鉴权
    const kosTokenVerified = await new SecureService(ctx).validKosToken();

    this.kosTokenVerified = kosTokenVerified;

    if (kosTokenVerified) {
      await this.basicAcl();
    } else {
      await this.needPlatformAcl();
    }
  }

  // 判断是否教育订单
  async setGlobalIsEduOrder(ctx: Context) {
    try {
      const { goods = [] } = (await this._getGoodsInfo(ctx)) as GetGoodsInfoDto;
      // xss 防御
      this.escapeGoods(goods);
      ctx.setGlobal({
        isEduOrder: get(goods, '[0].goodsType', 1) === 31,
      });
    } catch (error) {
      ctx.setGlobal({
        isEduOrder: false,
      });
    }
  }

  // * 评价关闭页
  async getCloseHtml(ctx: Context) {
    await this._getCrmShopNameAndSetGlobal(ctx);
    const { rootKdtId } = await this._checkIsCrmOfflineOrderAndSetGlobal(ctx);

    await this._getEvaluableTimeoutDaysAndSetGlobal(ctx, rootKdtId);
    await ctx.render('order/evaluate-close.html');
  }

  // * 评价删除页
  async getDeleteHtml(ctx: Context) {
    await this._getCrmShopNameAndSetGlobal(ctx);
    await ctx.render('order/evaluate-delete.html');
  }

  // 评价详情页
  async getDetailHtml(ctx: Context) {
    const { evaluation_alias } = ctx.getQueryData();

    const { isCrmOfflineOrder, isCrmYouzanOfflineSupportEvaluateOrder } =
      await this._checkIsCrmOfflineOrderAndSetGlobal(ctx);
    if (isCrmOfflineOrder || isCrmYouzanOfflineSupportEvaluateOrder) {
      await this._getCrmShopNameAndSetGlobal(ctx);
    }

    if (evaluation_alias) {
      const itemEvaluation = await new OrderEvaluateService(ctx).getItemEvaluation({
        alias: evaluation_alias as string,
        userId: ctx.userId,
        userType: ID_TYPE.USER_ID,
        searchWithDelete: true,
      });

      ctx.setGlobal({ itemEvaluation });
    } else {
      const orderEvaluation = await this.getOrderEvaluation(ctx);

      this.escapeEvaluation(orderEvaluation);
      ctx.setGlobal({ orderEvaluation });
    }

    await this.setGlobalIsEduOrder(ctx);
    await ctx.render('order/evaluate-detail.html');
  }

  // 获取订单对应的评价数据
  async getOrderEvaluation(ctx: Context): Promise<IItemEvaluationDetailModel[]> {
    const { order_no } = ctx.getQueryData();
    const params = { orderNo: order_no, reviewerId: this.buyerId };
    const result = await new OrderEvaluateService(ctx).getOrderEvaluation(params);
    return result;
  }

  // 创建评价页
  async createEvaluateHtml(ctx: Context) {
    const { kdtId, isWeapp, isMiniProgram, isYouzanmars } = ctx;
    const { is_edit, evaluation_alias, from, order_no } = ctx.getQueryData();

    const isEdit = is_edit === '1';

    const { isCrmOfflineOrder, rootKdtId, isCrmYouzanOfflineSupportEvaluateOrder } =
      await this._checkIsCrmOfflineOrderAndSetGlobal(ctx);

    const noticeList = [
      EVALUATION_SOURCE.WECHAT_TEMPLATE,
      EVALUATION_SOURCE.SHORT_MESSAGE_NOTICE,
      EVALUATION_SOURCE.WEAPP_NOTICE,
    ];

    if (noticeList.includes(from)) {
      // 模板消息+短信通知+订阅消息进入需要判断当前订单是否已评价，已评价则跳转至评价详情
      const canEvaluate = await this.getEvaluateState(
        order_no,
        isCrmOfflineOrder || isCrmYouzanOfflineSupportEvaluateOrder ? rootKdtId : kdtId
      ); // * 线下订单只能用总部 kdtId 进行该判断
      if (!canEvaluate) {
        if (isCrmOfflineOrder || isCrmYouzanOfflineSupportEvaluateOrder) {
          const orderEvaluation = await this.getOrderEvaluation(ctx);
          if (!orderEvaluation?.length) {
            return ctx.redirect(
              `/wsctrade/order/evaluate/close?order_no=${order_no}&kdt_id=${kdtId}&is_crm_offline_order=1`
            );
          }
          return ctx.redirect(
            `/wsctrade/order/evaluate/detail?order_no=${order_no}&kdt_id=${kdtId}`
          );
        }
        return ctx.redirect(`/wsctrade/order/evaluate/detail?order_no=${order_no}&kdt_id=${kdtId}`);
      }
    }

    // * 针对 CRM 线下订单进入评价创建做一层兜底拦截
    if (!isEdit && (isCrmOfflineOrder || isCrmYouzanOfflineSupportEvaluateOrder)) {
      // * 创建评价进入-需要判断当前订单是否已评价，已评价则跳转至评价详情
      const canEvaluate = await this.getEvaluateState(
        order_no,
        // * 线下订单只能用总部 kdtId 进行该判断
        rootKdtId
      );
      if (!canEvaluate) {
        try {
          const orderEvaluation = await this.getOrderEvaluation(ctx);
          if (!orderEvaluation?.length) {
            return ctx.redirect(
              `/wsctrade/order/evaluate/close?order_no=${order_no}&kdt_id=${kdtId}&is_crm_offline_order=1`
            );
          }
          return ctx.redirect(
            `/wsctrade/order/evaluate/detail?order_no=${order_no}&kdt_id=${kdtId}`
          );
        } catch (error) {
          ctx.logger.error('获取 CRM 线下订单详情失败', error.toString());
          return ctx.redirect(
            `/wsctrade/order/evaluate/close?order_no=${order_no}&kdt_id=${kdtId}&is_crm_offline_order=1`
          );
        }
      }
    }

    if (isCrmOfflineOrder || isCrmYouzanOfflineSupportEvaluateOrder) {
      await this._getCrmShopNameAndSetGlobal(ctx);
      // * 如果是 CRM 线下订单，需要设置 CRM 订单维度评价配置
      await this._setEvaluationPresentationConfig(ctx, rootKdtId);
    }

    const {
      goods = [],
      hasPayCouponsOrder = false,
      orderKdtId,
    } = (await this._getGoodsInfo(ctx)) as GetGoodsInfoDto;

    const isClientPhotoPermission = await isInGrayReleaseByKdtId(
      ctx,
      { namespace: 'wsc-h5-trade.gray-release', key: 'client-photo-permission' },
      ctx.kdtId
    );

    // c端是否展示相机权限文案提示
    ctx.setGlobal({ isClientPhotoPermission });

    const isPureWscSingleStore = await this.getIsPureWscSingleStore(ctx);
    // 是否展示润色评价，目前仅单店展示
    ctx.setGlobal({ isShowAIButton: isPureWscSingleStore });

    // 获取猜你想说标签
    if (isWeapp || (!isMiniProgram && !isYouzanmars)) {
      // 【猜你想说标签】店铺配置
      const shopShowEvaluateTags = await this.getEvaluateTagsConfig(ctx);
      // 微商城单店使用智能标签接口，其他店铺类型仍然使用当前标签接口
      if (!isPureWscSingleStore && shopShowEvaluateTags) {
        const guessTags = await this.getRecommendTags(kdtId, goods);
        ctx.setGlobal('guessTags', guessTags);
      }
      ctx.setGlobal({ shopShowEvaluateTags });
    }

    const isRedirectWxCommentPlugin = await this.checkRedirectWxCommentPlugin(ctx);

    // xss 防御
    this.escapeGoods(goods);
    ctx.setGlobal({
      goods,
      orderKdtId,
      kdtId: ctx.kdtId,
      // 是否为新酒店商品
      isHotel: get(goods, '[0].extra.BIZ_ITEM_ATTRIBUTE.NEW_HOTEL_GOOD') === '1',
      // 是否为教育课程商品
      isEduOrder: get(goods, '[0].goodsType', 1) === 31,
      // 付费券不展示发货物流评价
      showDeliveryEvaluate: !hasPayCouponsOrder,
      logger_params: from ? { source: from } : undefined,
      // 是否允许跳转评价插件页
      isRedirectWxCommentPlugin,
    });

    if (isEdit && evaluation_alias) {
      const itemEvaluation = await new OrderEvaluateService(ctx).getItemEvaluation({
        alias: evaluation_alias,
        userId: ctx.userId,
        userType: ID_TYPE.USER_ID,
      });

      ctx.setGlobal({ itemEvaluation });
    }
    this.setSpm('evaluateCreate', ctx.kdtId);

    await ctx.render('order/evaluate-create.html');
  }

  async getIsPureWscSingleStore(ctx: Context) {
    const shopMetaInfo = await this.callService(
      'iron-base/shop.ShopMetaReadService',
      'getShopMetaInfo',
      ctx.kdtId
    );
    const isPureWscSingleStore = checkPureWscSingleStore(shopMetaInfo);
    return isPureWscSingleStore;
  }

  // 创建评价
  async createEvaluateJson(ctx: Context) {
    const query = ctx.getPostData();
    const params = {
      ...query,
      reviewerId: ctx.userId,
      reviewerType: ID_TYPE.USER_ID,
    };
    const result = await new OrderEvaluateService(ctx).createOrderEvaluation(params);
    ctx.json(0, 'ok', result);
  }

  // 追加评价页
  async getEvaluationReviewHtml(ctx: Context) {
    const { kdtId } = ctx;
    const { evaluation_alias, order_no } = ctx.getQueryData();

    await this._checkIsCrmOfflineOrderAndSetGlobal(ctx);
    const orderEvaluateService = new OrderEvaluateService(ctx);
    const itemEvaluation = await orderEvaluateService.getItemEvaluation({
      alias: evaluation_alias,
      userId: ctx.userId,
      userType: ID_TYPE.USER_ID,
    });
    if (!itemEvaluation) {
      // * 无评价数据，不可追评，重定向到评价已删除页
      return ctx.redirect(
        `/wsctrade/order/evaluate/delete?order_no=${order_no}&kdt_id=${kdtId}&is_crm_offline_order=1`
      );
    }
    const evaluation = itemEvaluation?.evaluationModels && itemEvaluation?.evaluationModels[0]?.id;
    const isRedirectWxCommentPlugin = await this.checkRedirectWxCommentPlugin(ctx, {
      isAgainComment: true,
      evaluation,
      orderNo: itemEvaluation?.orderNo,
    });

    // 【猜你想说标签】店铺配置属性
    const shopShowEvaluateTags = await this.getEvaluateTagsConfig(ctx);
    const isPureWscSingleStore = await this.getIsPureWscSingleStore(ctx);
    // 是否展示润色评价，目前仅单店展示
    ctx.setGlobal({
      itemEvaluation,
      isRedirectWxCommentPlugin,
      shopShowEvaluateTags,
      isShowAIButton: isPureWscSingleStore,
    });
    await this.setGlobalIsEduOrder(ctx);
    this.setSpm('evaluateReview', ctx.kdtId);
    await ctx.render('order/evaluate-review.html');
  }

  // 评价中心页
  async getEvaluateCenterHtml(ctx: Context) {
    const { kdtId } = ctx;

    await this.setRantaPageInitData(ctx, {
      framework: 'vue',
      bizName: '@wsc-h5-trade/evaluation-list',
      ecloud: {
        setExtensionParams: [
          {
            kdtId,
            // 2.0 页面标，没接2.0 可以不填
            pageNameV2: 'evaluation-list',
            conditionContext: {
              platform: ctx.platform,
              pageType: 'h5',
            },
          },
        ],
      },
    });

    await ctx.render('order/evaluate-center.html');
  }

  // 创建追评
  async postCreateEvaluationJson(ctx: Context) {
    const postData = ctx.getPostData();
    const result = await new OrderEvaluateService(ctx).createItemAdditional({
      ...postData,
      userId: ctx.userId,
      userType: ID_TYPE.USER_ID,
    });
    ctx.json(0, 'ok', result);
  }

  // 获取已评价列表
  async getEvaluationListJson(ctx: Context) {
    const query = ctx.getQueryData();
    const params = {
      ...query,
      userId: ctx.userId,
      userType: ID_TYPE.USER_ID,
    };

    const result = await new OrderEvaluateService(ctx).pageEvaluation4User(params);
    ctx.json(0, 'ok', result);
  }

  // 设置匿名评价
  async postAnonymousEvaluationJson(ctx: Context) {
    const params = ctx.getPostData();
    const result = await new OrderEvaluateService(ctx).anonymousEvaluation({
      ...params,
      userId: ctx.userId,
      userType: 2,
    });

    ctx.json(0, 'ok', result);
  }

  // 设置取消匿名评价
  async postCancelAnonymousEvaluationJson(ctx: Context) {
    const params = ctx.getPostData();
    const result = await new OrderEvaluateService(ctx).cancelAnonymousEvaluation({
      ...params,
      userId: ctx.userId,
      userType: 2,
    });
    ctx.json(0, 'ok', result);
  }

  // 删除评价
  async postDeleteItemEvaluationJson(ctx: Context) {
    const params = ctx.getPostData();

    const result = await new OrderEvaluateService(ctx).deleteItemEvaluation({
      ...params,
      reviewerId: ctx.userId,
      reviewerType: 2,
    });
    ctx.json(0, 'ok', result);
  }

  // 删除追评
  async postDeleteReviewEvaluationJson(ctx: Context) {
    const params = ctx.getPostData();

    const result = await new OrderEvaluateService(ctx).deleteEvaluation({
      ...params,
      reviewerId: ctx.userId,
      reviewerType: 2,
    });
    ctx.json(0, 'ok', result);
  }

  // 修改评价
  async postUpdateItemEvaluationJson(ctx: Context) {
    const params = ctx.getPostData();
    const result = await new OrderEvaluateService(ctx).updateItemEvaluation({
      ...params,
      reviewerId: ctx.userId,
      reviewerType: ID_TYPE.USER_ID,
    });
    ctx.json(0, 'ok', result);
  }

  // 修改追评
  async postUpdateReviewEvaluationJson(ctx: Context) {
    const params = ctx.getPostData();
    const result = await new OrderEvaluateService(ctx).updateEvaluation({
      ...params,
      reviewerId: ctx.userId,
      reviewerType: ID_TYPE.USER_ID,
    });
    ctx.json(0, 'ok', result);
  }

  // 获取商品信息
  async _getGoodsInfo(ctx: Context): Promise<GetGoodsInfoDto | undefined> {
    const { kosTokenVerified } = this;

    if (ctx.status === 302) {
      return;
    }

    const { query, kdtId } = ctx;
    const { buyer } = this;
    const orderNo = query.order_no;

    const params = {
      kdtId,
      orderNo,
      sourceName: this.sourceName,
      buyerId: kosTokenVerified ? 0 : buyer.buyerId,
      customerId: kosTokenVerified ? 0 : buyer.fansId,
      customerType: kosTokenVerified ? 0 : buyer.fansType,
    };

    let data = null;
    try {
      data = await new OrderDetailService(ctx).lightDetailByOrderNo(params);
    } catch (e) {
      const content = e.errorContent || {};
      throw new PageException(content.code || 10500, content.msg || '订单数据异常');
    }
    if (!data || Object.keys(data).length === 0) {
      // 无此订单
      throw new PageException(10500, '无此订单');
    }

    const { itemInfo = [], mainOrderInfo = {} } = data;
    const { kdtId: orderKdtId } = mainOrderInfo;

    return {
      hasPayCouponsOrder: get(data, 'orderBizExtra.hasPayCouponsOrder', false),
      goods: itemInfo,
      orderKdtId,
    };
  }

  escapeGoods(itemGoods = [] as IItemInfoDto[]) {
    itemGoods.forEach((item) => {
      const { goodsInfo } = item;
      if (goodsInfo) {
        goodsInfo.title && (goodsInfo.title = escape(goodsInfo.title));
        goodsInfo.shortTitle && (goodsInfo.shortTitle = escape(goodsInfo.shortTitle));
      }
      item.buyerMemo && (item.buyerMemo = escape(item.buyerMemo));
    });
  }

  escapeEvaluation(orderEvaluation: IItemEvaluationDetailModel[]) {
    orderEvaluation &&
      orderEvaluation.forEach((evaluation) => {
        evaluation.itemTitle && (evaluation.itemTitle = escape(evaluation.itemTitle));
      });
  }

  // 发布评价页-猜你想说标签，最多16个
  async getRecommendTags(kdtId: number, goods: IItemInfoDto[]) {
    const { ctx } = this;
    const params = {
      kdtId,
      itemIds: goods?.map((item) => item.goodsId),
    };

    try {
      const result = await new DataAggregateService(ctx).getItemsEvaluateLabel(params);
      return result || {};
    } catch (error) {
      ctx.logger.warn('获取猜你想说标签失败', error, params);
      // 若接口获取失败，不阻塞用户后续流程，可继续进入到发布评价页
      return {};
    }
  }

  // * 判断是否能评价
  async getEvaluateStateJson(ctx: Context) {
    const { kdtId } = ctx;
    const { order_no } = ctx.getQueryData();

    const { isCrmOfflineOrder, rootKdtId, isCrmYouzanOfflineSupportEvaluateOrder } =
      await this._checkIsCrmOfflineOrderAndSetGlobal(ctx);

    const result = await this.getEvaluateState(
      order_no,
      isCrmOfflineOrder || isCrmYouzanOfflineSupportEvaluateOrder ? rootKdtId : kdtId
    ); // * 线下订单只能用总部 kdtId 进行该判断
    ctx.json(0, 'ok', result);
  }

  // 发布评价页-判断是否能评价
  async getEvaluateState(orderNo: string, kdtId: number) {
    const { ctx } = this;
    try {
      const result = await new EvaluateSearchService(ctx).getEvaluateState({
        kdtId,
        orderNo,
      });
      return result;
    } catch (error) {
      ctx.logger.warn('发布评价页获取评价状态失败', error);
      // 若接口获取失败，不阻塞用户后续流程，可继续进入到发布评价页
      return true;
    }
  }

  // 获取订单评价商品信息
  async getEvaluateOrderGoods(ctx: Context) {
    const result = (await this._getGoodsInfo(ctx)) as GetGoodsInfoDto;
    ctx.json(0, 'ok', result);
  }

  // 获取商品评价信息
  async getItemEvaluation(ctx: Context) {
    const { evaluation_alias } = ctx.getQueryData();
    const result = await new OrderEvaluateService(ctx).getItemEvaluation({
      alias: evaluation_alias,
      userId: ctx.userId,
      userType: ID_TYPE.USER_ID,
    });
    ctx.json(0, 'ok', result);
  }

  // 获取店铺标：是否允许评价传给腾讯 maple_evaluation_report
  async getMapleEvaluationReportState(ctx: Context) {
    const shopConfiguration = new ShopConfigurationService(ctx);
    const result = (await shopConfiguration.queryShopConfig('maple_evaluation_report')) || true;
    return result;
  }

  // 获取wx枫树token
  async getMapleAuthToken(ctx: Context) {
    const result = await new OrderEvaluateService(ctx).getMapleAuthToken({
      kdtId: ctx.kdtId,
      userId: ctx.userId,
      operator: 'wsc-h5-trade',
    });
    ctx.json(0, 'ok', result);
  }

  // 判断商家小程序是否绑定枫树插件
  async checkWeappPluginByAppid(ctx: Context) {
    const result = await new OrderEvaluateService(ctx).checkWeappPluginByAppid({
      kdtId: ctx.kdtId,
      pluginAppId: 'wxff9915435842679f',
    });
    return result;
  }

  // 判断是否支持跳转评价插件
  async checkRedirectWxCommentPlugin(ctx: Context, options?: ICheckRedirectWxCommentPluginOptions) {
    const { isAgainComment, orderNo, evaluation } = options || {};
    const { mpVersion } = ctx.getQueryData();

    const { isCrmOfflineOrder } = await this._checkIsCrmOfflineOrderAndSetGlobal(ctx);
    if (isCrmOfflineOrder) {
      return false;
    }

    let isRedirectWxCommentPlugin = false;
    try {
      let checkEvaluationFromMaple = true;
      // 商家是否允许评价传给腾讯
      const mapleEvaluationReport = await this.getMapleEvaluationReportState(ctx);
      const checkWeappCommentPlugin = await this.checkWeappPluginByAppid(ctx);
      const wxCommetEnabledMpVersionsStr = getTradeApolloConfig(
        'wsc-h5-trade.application',
        'wxCommetEnabledMpVersions'
      );
      const wxCommetEnabledMpVersions = JSON.parse(wxCommetEnabledMpVersionsStr);
      const checkMpVersion = CompareVersion.isGt(
        mpVersion,
        wxCommetEnabledMpVersions.defaultVersion
      );
      // 追评
      if (isAgainComment) {
        checkEvaluationFromMaple = false;
        checkEvaluationFromMaple = await new OrderEvaluateService(ctx).checkEvaluationFromMaple({
          evaluation,
          orderNo,
        });
      }
      // 判断商家是否允许评价传给腾讯 && 商家小程序是否绑定插件 && checkEvaluationFromMaple追评判断是否是腾讯评价 && 小程序版本号是否大于apollo配置
      if (
        mapleEvaluationReport === '1' &&
        checkWeappCommentPlugin &&
        checkEvaluationFromMaple &&
        checkMpVersion
      ) {
        isRedirectWxCommentPlugin = true;
      }
    } catch (err) {
      ctx.logger.info(`${isAgainComment ? '评价' : '追评'}页获取枫树评价逻辑失败` + err.toString());
    }
    return isRedirectWxCommentPlugin;
  }

  // * 获取订单维度评价配置
  async _setEvaluationPresentationConfig(ctx: Context, rootKdtId: number) {
    try {
      // * bizType 为 3，固定值，标识 crm 业务域
      const evaluationPresentationConfig = await new OrderEvaluateService(
        ctx
      ).getEvaluationPresentationConfig({
        bizType: 3,
        kdtId: rootKdtId,
      });

      const { itemEvaluationPresentationConfig = {}, orderEvaluationPresentationConfig = {} } =
        evaluationPresentationConfig;

      const { enableItemEvaluation = true } = itemEvaluationPresentationConfig;
      const {
        orderEvaluationDimensions = [],
        enablePictureComment = false,
        enableTextComment = false,
      } = orderEvaluationPresentationConfig;

      ctx.setGlobal({
        enableItemEvaluation,
        orderEvaluationDimensions: orderEvaluationDimensions.filter(
          (item: { status: number }) => item.status === 1
        ),
        enablePictureAndTextComment: enablePictureComment && enableTextComment,
      });
    } catch (error) {
      ctx.logger.error(`获取订单维度评价配置失败` + error.toString());
    }
  }

  // * 获取店铺名称并 setGlobal
  async _getCrmShopNameAndSetGlobal(ctx: Context) {
    const { kdt_id } = ctx.getQueryData();

    try {
      const result = await new ShopBaseReadOuterService(ctx).queryShopBaseInfo(kdt_id);
      const { shopName = '' } = result || {};
      ctx.setGlobal({ crmShopName: shopName });
    } catch (error) {
      ctx.logger.error(`获取门店店铺名称失败` + error.toString());
    }
  }

  // * 获取店铺设置的评价超时时间
  async _getEvaluableTimeoutDaysAndSetGlobal(ctx: Context, rootKdtId: number) {
    try {
      const shopConfiguration = new ShopConfigurationService(ctx);
      const data = await shopConfiguration.queryShopConfig(
        'crm_order_evaluable_time_range',
        rootKdtId
      );
      const { evaluableTimeoutDays } = JSON.parse(data);
      ctx.setGlobal({ evaluableTimeoutDays });
    } catch (error) {
      ctx.logger.error(`获取门店店铺评价超时时间失败` + error.toString());
    }
  }

  // * 判断是否为 CRM 线下订单
  _checkIsCrmOfflineOrderAndSetGlobal(ctx: Context) {
    let isCrmOfflineOrder = false;
    let rootKdtId = 0;
    /** 是否为开通了crm商家的支持评价的有赞线下订单 */
    let isCrmYouzanOfflineSupportEvaluateOrder = false;
    const { order_no } = ctx.getQueryData();
    return Promise.all([
      new OrderDetailService(ctx).getOrderInfo({
        orderNo: order_no,
        bizGroup: 'crm',
        app: 'wsc-h5-trade',
        options: { checkKdtId: false, withMainOrderInfo: true, withSourceInfo: true },
      }),
      this._checkIsCrmShop(ctx),
    ])
      .then(([orderData, isCrmShop]) => {
        const { mainOrderInfo = {}, sourceInfo = {} } = orderData;
        const { channelType, headKdtId, orderType, teamType } = mainOrderInfo;
        const { orderMark, isOfflineOrder } = sourceInfo;

        isCrmOfflineOrder = channelType === 100; // * 100 代表为 CRM 线下订单

        rootKdtId = headKdtId;

        /** 首先需要开通crm */
        isCrmYouzanOfflineSupportEvaluateOrder = isCrmShop;

        // 二维码订单
        const isQrCodeOrder = orderType === 6;
        //  总部代客下单
        const isValetOrder = orderMark === 35;
        // 超级门店（即零售订单）
        const isCJMD = teamType === 7;

        /** 然后排除不支持的订单类型 */
        if (isQrCodeOrder || isValetOrder || !isCJMD || !isOfflineOrder) {
          isCrmYouzanOfflineSupportEvaluateOrder = false;
        }

        ctx.setGlobal({
          isCrmOfflineOrder,
          isCrmYouzanOfflineSupportEvaluateOrder,
        });

        return { isCrmOfflineOrder, rootKdtId, isCrmYouzanOfflineSupportEvaluateOrder };
      })
      .catch((error) => {
        ctx.logger.warn('获取订单信息失败', error);
        return { isCrmOfflineOrder, rootKdtId, isCrmYouzanOfflineSupportEvaluateOrder };
      });
  }

  // 判断是否为crm店铺
  async _checkIsCrmShop(ctx: Context) {
    const crmShopInfo = await new ScrmWhiteListService(ctx).get({
      kdtId: ctx.kdtId,
    });
    const isCrmShop = crmShopInfo?.status === CRM_UPGRADE_STATUS.COMPLETED;

    return isCrmShop;
  }

  // 设置是否展示评价标签
  async getEvaluateTagsConfig(ctx: Context) {
    let res = false;
    try {
      const shopConfiguration = new ShopConfigurationService(ctx);
      const data = await shopConfiguration.queryShopConfig('evaluate_tags_switch', ctx.kdtId);
      res = +data === 1;
    } catch (error) {
      ctx.logger.error(`获取店铺配置失败：evaluate_tags_switch` + error.toString());
    }
    return res;
  }

  async getItemsEvaluationLabels(ctx: Context) {
    const { items } = ctx.getPostData();
    const result = await new DataAggregateService(ctx).getItemsEvaluationLabels({
      items,
      kdtId: ctx.kdtId,
    });
    ctx.json(0, 'ok', result);
  }

  async enrichEvaluationContent(ctx: Context) {
    const { itemId, orderNo, content, skuId } = ctx.getPostData();
    const result = await new DataAggregateService(ctx).enrichEvaluationContent({
      itemId,
      skuId,
      orderNo,
      content,
      kdtId: ctx.kdtId,
      buyerId: this.buyerId,
    });
    ctx.json(0, 'ok', result);
  }
}

module.exports = OrderEvaluateController;
