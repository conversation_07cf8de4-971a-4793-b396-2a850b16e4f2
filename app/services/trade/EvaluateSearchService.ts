import BaseService from '../base/BaseService';

class EvaluateSearchService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.ebiz.mall.trade.buyer.api.service.EvaluateSearchService';
  }

  /**
   *  判断是否能评价
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/516189
   */
  async getEvaluateState(params: {
    kdtId: number;
    orderNo: string;
  }): Promise<boolean> {
    return this.invoke('getEvaluateState', [params]);
  }
}

export default EvaluateSearchService;
