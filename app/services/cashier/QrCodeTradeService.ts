import BaseService from '../base/BaseService';
import {
  QrCodeTradeRequestParam,
  CheckCertRequestParam,
  QueryShopStatusParam,
  GetQrCodeByIdParam,
  GetQrCodePayByOrderNoParam,
  GetQrCodePayByOrIdParam
} from '.../../../definitions/pay/Cashier';

/* com.youzan.trade.business.qrcode.api.trade.QrCodeTradeService -  */
export default class QrCodeTradeService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.trade.business.qrcode.api.trade.QrCodeTradeService';
  }

  get CERT_SERVICE_NAME() {
    return 'com.youzan.trade.business.service.authentication.AuthenticationService';
  }

  get QUERY_SHOP_SERVICE_NAME() {
    return 'com.youzan.trade.business.shop.ShopQueryService';
  }

  get QUERY_QRCODE_SERVER_NAME() {
    return 'com.youzan.trade.business.qrcode.api.query.QrCodeQueryService';
  }

  get QUERY_QRCODE_PAY_SERVER_NAME() {
    return 'com.youzan.trade.business.qrcode.api.query.QrCodePayQueryService';
  }

  get QUERY_UMP_SCAN_REDUCE_SERVER_NAME() {
    return 'com.youzan.ump.marketing.scanreduce.service.ScanReducePromotionService';
  }

  /**
   *  二维码支付接口
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/209094
   *  @param {QrCodeTradeRequestParam} qrCodeTradeRequestDTO
   */
  async pay(qrCodeTradeRequestDTO: QrCodeTradeRequestParam) {
    return this.invoke('pay', [qrCodeTradeRequestDTO]);
  }

  /**
   * 查询店铺基本状态
   * 1. 是否锁定、2. 是否打烊、3. 是否关闭
   * @param {string} queryShopStatusDTO.kdtId
   * @param {string} queryShopStatusDTO.source
   */
  async queryShopStatus(queryShopStatusDTO: QueryShopStatusParam) {
    return this.invoke(this.QUERY_SHOP_SERVICE_NAME, 'queryShopStatus', [queryShopStatusDTO]);
  }

  /**
   * 检查店铺认证状态
   * 已过期或者未认证的店铺不允许使用二维码
   * @param {String} checkCertRequestDTO.kdtId
   * @param {String} checkCertRequestDTO.source
   */
  async checkCertification(checkCertRequestDTO: CheckCertRequestParam) {
    return this.invoke(this.CERT_SERVICE_NAME, 'queryAuthenticationStatus', [checkCertRequestDTO]);
  }

  /**
   * 检查二维码ID有效性
   * 已过期或者未认证的店铺不允许使用二维码
   * @param {String} getQrCodeByIdDTO.qrId
   * @param {Boolean} getQrCodeByIdDTO.needLabelInfo
   * @param {Boolean} getQrCodeByIdDTO.needPromotionInfo
   */
  async getQrCodeById(getQrCodeByIdDTO: GetQrCodeByIdParam) {
    return this.invoke(this.QUERY_QRCODE_SERVER_NAME, 'queryQrCodeByQrId', [getQrCodeByIdDTO]);
  }

  /**
   * 根据二维码ID查询扫码付款信息
   * @param {String} getQrCodePayByOrIdDTO.qrId 付款码ID
   * @param {String} getQrCodePayByOrIdDTO.orderNo 订单ID
   * @param {String} getQrCodePayByOrIdDTO.payStatus 支付状态
   */
  async getQrCodePayByOrId(getQrCodePayByOrIdDTO: GetQrCodePayByOrIdParam) {
    return this.invoke(this.QUERY_QRCODE_PAY_SERVER_NAME, 'queryByQrId', [getQrCodePayByOrIdDTO]);
  }

  /**
   * 根据支付成功生成的订单号查询扫码付款信息
   * @param {String} getQrCodePayByOrderNoDTO.orderNo 订单ID
   * @param {String} getQrCodePayByOrderNoDTO.payStatus 支付状态
   * @param {String} getQrCodeByIdDTO.qrId 付款码ID
   */
  async getQrCodePayByOrderNo(getQrCodePayByOrderNoDTO: GetQrCodePayByOrderNoParam) {
    return this.invoke(this.QUERY_QRCODE_PAY_SERVER_NAME, 'queryByOrderNo', [getQrCodePayByOrderNoDTO]);
  }

  /**
   * 获取订单扫码优惠信息
   * @param {String} kdtId 店铺ID
   * @param {String} orderNo 订单ID
   */
  async getUmpScanReduce(kdtId: number, orderNo: string) {
    return this.invoke(this.QUERY_UMP_SCAN_REDUCE_SERVER_NAME, 'getLogByOrderNoV2', [kdtId, orderNo]);
  }

  /**
   * 获取当前店铺扫码优惠信息
   * @param {String} kdtId 店铺ID
   */
  async getValidUmpScanReduce(kdtId: number) {
    return this.invoke(this.QUERY_UMP_SCAN_REDUCE_SERVER_NAME, 'getValidByScanTypeV2', [kdtId, 'WSC']);
  }
}
