import WechatService = require('../services/trade/WechatService');

async function getQttAppId(ctx: any) {
  const { query = {}, userAgent = '', apolloClient } = ctx || {};
  const { weappAppId } = query;
  const { login_platform = {} } = ctx.getLocalSession();

  // 从阿波罗中获取小程序appId配置, baseConfig: { wx358a0a518c8eb84d: {}, wxf90444db7b45699a: {}, ... }
  const { baseConfig = {} } = apolloClient.getConfig({
    appId: 'wsc-h5-fenxiao',
    namespace: 'wsc-h5-fenxiao.qtt',
    key: 'online-weapp-config',
  }) || {};

  // 方案1: 从query中取weappAppId
  if (weappAppId && +weappAppId !== 0 && baseConfig[weappAppId]) {
    return weappAppId;
  }

  // 方案2: 从ua中取appId
  for (const appId in baseConfig) {
    if (userAgent.indexOf(appId) > -1) {
      return appId;
    }
  }

  // 方案3: 从session中取kdt_id换appId
  if (login_platform.kdt_id) {
    const weappAccount = await new WechatService(ctx).getWeappAccountBykdtId(login_platform.kdt_id);
    if (weappAccount.appId && baseConfig[weappAccount.appId]) {
      return weappAccount.appId;
    }
  }

  // 观察日志，如果还有问题，后续再增加一些方案...

  return undefined;
}


// 在群团团web-view（如订单详情、待支付页）中获取小程序appId并setGlobal
export async function setQttWeappAppId(ctx: any) {
  const appId = await getQttAppId(ctx);
  if (appId) {
    ctx.setGlobal({
      qttWeappAppId: appId,
    });
  }
}