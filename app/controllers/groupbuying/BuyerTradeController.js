const _ = require('lodash');
const { checkChainStore } = require('@youzan/utils-shop');
const PageException = require('@youzan/iron-base/app/exceptions/PageException');
const BaseController = require('../base/BaseController');
const BuyerTradeService = require('../../services/groupbuying/BuyerTradeService');
const HeaderOrderService = require('../../services/groupbuying/HeaderOrderService');
const CodeImageService = require('../../services/base/CodeImageService');
const { setExtensionParams } = require('@youzan/plugin-h5-ecloud');

class BuyerTradeController extends BaseController {
  async init() {
    await super.init({
      initShopMetaInfo: true,
    });
  }

  async pageAcl() {
    await this.acl({
      allowNotLogin: false,
      useAjaxLogin: false,
      forceOauthLogin: true,
      weixinOauthScope: 'snsapi_userinfo',
      kdtId: this.ctx.kdtId,
      mpId: this.ctx.getLocalSession('mp_id'),
    });
  }

  // 下单页
  async getTradeHtml(ctx) {
    await this.pageAcl();
    if (ctx.query.kdtId) {
      ctx.kdtId = ctx.query.kdtId;
    }
    const { alias, activityId, headerBuyerId } = ctx.getQueryData();
    const params = {
      activityAlias: alias,
      activityId,
      headerBuyerId,
      kdtId: ctx.kdtId,
    };
    const buyerTradeService = new BuyerTradeService(ctx);
    let groupbuyingData;
    if (_.some(params, (value) => !value)) {
      throw new PageException(70, '页面参数错误');
    }
    try {
      groupbuyingData = await buyerTradeService.getActivityDetail(params);
    } catch (error) {
      const content = error.errorContent;
      if (content) {
        throw new PageException(content.code, content.msg);
      } else {
        throw error;
      }
    }

    ctx.setGlobal({
      groupbuyingData,
      kdt_id: ctx.kdtId,
      indexRoute: 'trade',
      spm: {
        logType: 'cgroupon',
        logId: activityId,
      },
      logger_params: {
        act_type: 10002,
        act_id: `${activityId}${headerBuyerId}`,
        agent_id: headerBuyerId,
        groupbuy_act_id: activityId,
      },
    });

    await ctx.render('groupbuying/buyer-trade.html', {
      title: _.get(groupbuyingData, 'activityDetail.name', ''),
    });
  }

  // 订单详情页
  async getDetailHtml(ctx) {
    await this.pageAcl();
    if (ctx.query.kdtId) {
      ctx.kdtId = ctx.query.kdtId;
    }
    const { orderNo, isFetch } = ctx.getQueryData();
    const { kdtId, platform } = ctx;
    const params = {
      orderNo,
      kdtId,
      buyerId: this.buyerId,
    };
    const buyerTradeService = new BuyerTradeService(ctx);
    const codeImageService = new CodeImageService(ctx);
    let detailData;
    let qrCode;
    let header;
    let headerBuyerId;
    let security;
    try {
      [detailData, qrCode] = await Promise.all([
        buyerTradeService.queryOrderDetailV2(params),
        codeImageService.getShortenQrCode(
          this.getDetailUrl({
            orderNo,
            kdtId,
          })
        ),
      ]);
      security = {
        isYzSecured: _.get(detailData, 'isSecuredTransActions', false),
      };
      headerBuyerId = detailData.headerBuyerId;
      header = await buyerTradeService.headerInfo({
        headerBuyerId,
        kdtId,
      });
      detailData.headerAvatar = header.headerAvatar;
    } catch (error) {
      const content = error.errorContent;
      if (+isFetch === 1) {
        throw new PageException(9999, '提货码仅支持团长扫码核销');
      } else if (content) {
        throw new PageException(content.code, content.msg);
      } else {
        throw error;
      }
    }

    setExtensionParams(ctx, {
      kdtId,
      pageName: 'group-order-detail',
      conditionContext: {
        platform,
        pageType: 'h5',
      },
    });

    ctx.setGlobal({
      detailData: escape(JSON.stringify(detailData)),
      header,
      kdt_id: ctx.kdtId,
      indexRoute: 'detail',
      qrCode,
      security,
    });
    await ctx.render('groupbuying/buyer-trade.html', {
      title: '订单详情',
    });
  }

  // 获取活动详情数据
  /* eslint-disable-next-line */
  async getActivityDetailJson(ctx) {
    const { alias, headerBuyerId, activityId } = ctx.getQueryData();

    this.validator
      .required(alias, '没有对应活动')
      .required(activityId, '没有对应活动')
      .required(headerBuyerId, '没有对应团长');

    const params = {
      activityAlias: alias,
      activityId,
      headerBuyerId,
      kdtId: ctx.kdtId,
    };
    const buyerTradeService = new BuyerTradeService(ctx);
    const activityDetail = await buyerTradeService.getActivityDetail(params);
    ctx.r(0, '', activityDetail);
  }

  // 预下单
  async postPreTradeJson(ctx) {
    const params = await this.integrateOrderParams(ctx);
    const buyerTradeService = new BuyerTradeService(ctx);
    const preTradeRes = await buyerTradeService.preTrade(params);
    ctx.r(0, '', preTradeRes);
  }

  // 下单
  async postCreateTradeJson(ctx) {
    const params = await this.integrateOrderParams(ctx);
    const buyerTradeService = new BuyerTradeService(ctx);
    const createTradeRes = await buyerTradeService.createTrade(params);
    ctx.r(0, '', createTradeRes);
  }

  // 整合下单参数
  async integrateOrderParams(ctx) {
    const { alias, activityId, headerBuyerId, url, items, mobile, buyerMsg, orderMark, ump = {} } = ctx.getPostData();
    const buyer = { ...this.buyer, buyerPhone: mobile || '' };
    return {
      seller: {
        kdtId: ctx.kdtId,
      },
      groupBuy: {
        activityId,
        activityAlias: alias,
        headerBuyerId,
      },
      config: {
        paymentSuccessRedirect: url,
        buyerMsg,
        receiveMsg: true,
      },
      buyer,
      items,
      source: {
        orderMark,
      },
      ump,
    };
  }

  // 获取订单详情
  /* eslint-disable-next-line */
  async getOrderDetailJson(ctx) {
    const { orderNo } = ctx.getQueryData();
    const params = {
      kdtId: ctx.kdtId,
      orderNo,
      buyerId: this.buyerId,
    };
    const buyerTradeService = new BuyerTradeService(ctx);
    const orderDetail = await buyerTradeService.queryOrderDetailV2(params);
    // 订单状态处理，兼容老版本小程序
    // 100 === 60
    if (orderDetail.state === 60) {
      orderDetail.state = 100;
    }
    ctx.r(0, '', orderDetail);
  }

  // 新的获取订单详情，不影响老的
  async getOrderDetailV2Json(ctx) {
    const { orderNo, kdt_id: queryKdtId } = ctx.getQueryData();
    const kdtId = +ctx.kdtId;
    const { buyerId } = this;
    const params = {
      kdtId: queryKdtId || ctx.kdtId,
      orderNo,
      buyerId: this.buyerId,
    };
    const buyerTradeService = new BuyerTradeService(ctx);
    const codeImageService = new CodeImageService(ctx);

    const shopMetaInfo = await this.callService('iron-base/shop.ShopMetaReadService', 'getShopMetaInfo', ctx.kdtId);
    const isChainStore = checkChainStore(shopMetaInfo);
    const rootKdtId = shopMetaInfo.rootKdtId || kdtId;

    const [detailData, qrCode] = await Promise.all([
      buyerTradeService.queryOrderDetailV2(params),
      isChainStore
        ? codeImageService.getChainWeappCode(
            'packages/groupbuying/buyer-trade/detail/index',
            {
              orderNo,
              isHeader: 1,
            },
            rootKdtId
          )
        : codeImageService.getWeappCode('packages/groupbuying/buyer-trade/detail/index', {
            orderNo,
            isHeader: 1,
          }),
    ]);
    const header = await buyerTradeService.headerInfo({
      headerBuyerId: detailData.headerBuyerId,
      kdtId,
    });
    // 团长头像处理
    detailData.headerAvatar = header.headerAvatar;
    // 订单状态处理，兼容老版本小程序
    // 100 === 60
    if (detailData.state === 60) {
      detailData.state = 100;
    }
    ctx.json(0, '', {
      qrCode,
      detailData,
      header,
      isHeader: detailData.headerBuyerId === buyerId,
    });
  }

  // 订单详情页去支付
  async postPaymentOrderJson(ctx) {
    const { orderNo } = ctx.getPostData();
    const params = {
      orderNo,
      kdtId: ctx.kdtId,
      forbidWxpay: 0,
      buyerId: this.buyer.buyerId,
    };
    const buyerTradeService = new BuyerTradeService(ctx);
    const res = await buyerTradeService.orderPayment(params);
    ctx.r(0, '', res);
  }

  // 查询商品详情
  /* eslint-disable-next-line */
  async getItemDetailJson(ctx) {
    const { itemId, storeId = 0 } = ctx.getQueryData();
    const { buyerId, fansId = 0, fansType = 0 } = this.buyer;

    this.validator.required(itemId, '没有对应商品');

    const params = {
      itemId,
      kdtId: ctx.kdtId,
      buyerId,
      fansId,
      fansType,
    };
    if (storeId > 0) {
      params.storeId = storeId;
    }
    const buyerTradeService = new BuyerTradeService(ctx);
    const res = await buyerTradeService.getById(params);
    ctx.r(0, '', res);
  }

  // 查询已购买
  /* eslint-disable-next-line */
  async getRollOrdersJson(ctx) {
    const { activityId = '', pageId, pageSize = 20 } = ctx.getQueryData();
    const params = {
      activityId,
      pageSize,
      kdtId: +ctx.kdtId,
    };
    this.validator.isNumeric(activityId, '参数activityId不合法');
    if (pageId) {
      params.pageId = pageId;
    }
    const buyerTradeService = new BuyerTradeService(ctx);
    const res = await buyerTradeService.queryRollOrders(params);
    ctx.json(0, '', res);
  }

  // 根据买家信息查询上一次订单的手机号
  async getPhoneJson(ctx) {
    const { buyerPhone = '', customerId = 0, customerType = 0, buyerId } = this.buyer;
    const orderService = new HeaderOrderService(ctx);
    const params = {
      withBuyerInfo: true,
      sortBy: 'created_at',
      page: 1,
      pageSize: 10,
      customerId,
      customerType,
      customerBuyerId: buyerId,
      kdtId: ctx.kdtId,
    };
    const orders = await orderService.orderQuery(params);
    const customerMobile = _.get(orders, 'list[0].customerMobile', null);
    ctx.json(0, '', customerMobile || buyerPhone);
  }

  async postCacheJson(ctx) {
    const { activityId, alias: activityAlias, headerBuyerId, items, storeId = 0, orderMark } = ctx.getPostData();
    const kdtId = +ctx.kdtId;
    const { platform = {} } = ctx.getLocalSession();
    const { platformType = 0 } = platform;
    const buyer = {
      ...this.buyer,
      platformType,
    };
    items.forEach((one) => {
      one.kdtId = kdtId;
    });
    const params = {
      buyer,
      config: {},
      groupBuy: {
        activityId,
        activityAlias,
        headerBuyerId,
      },
      items,
      seller: {
        kdtId,
        storeId,
      },
      source: {
        orderMark,
      },
    };
    const buyerTradeService = new BuyerTradeService(ctx);
    const res = await buyerTradeService.cache(params);
    ctx.json(0, '', res);
  }

  async postPrepareJson(ctx) {
    const { bookKey } = ctx.getPostData();
    const kdtId = +ctx.kdtId;
    const buyerTradeService = new BuyerTradeService(ctx);
    const res = await buyerTradeService.prepare({
      bookKey,
      kdtId,
      ...this.buyer,
    });
    ctx.json(0, '', res);
  }

  getDetailUrl({ orderNo, kdtId }) {
    return `https://cashier.youzan.com/pay/wsctrade_tradeDetail?orderNo=${orderNo}&isHeader=1&isFetch=1&kdtId=${kdtId}`;
  }
}

module.exports = BuyerTradeController;
