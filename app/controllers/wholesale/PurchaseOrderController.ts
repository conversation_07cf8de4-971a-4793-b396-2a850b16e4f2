import WholesaleBaseController from './WholesaleBaseController';
import PurchaseListService from '../../services/wholesale/PurchaseListService';
import GoodsSkuService from '../../services/goods/GoodsSkuService';
import { GetSkuParamsDTO } from 'definitions/other/goodsSku';

import { Context } from 'astroboy';

class PurchaseOrderController extends WholesaleBaseController {
  async listPurchaseItem(ctx: Context) {
    const result = await new PurchaseListService(ctx).listPurchaseItem({
      kdtId: ctx.kdtId,
      buyerId: this.buyerId,
    });

    ctx.json(0, 'success', result);
  }

  async selectPurchaseItem(ctx: Context) {
    const { purchaseItems } = this.ctx.getPostData();
    const result = await new PurchaseListService(ctx).selectPurchaseItem({
      kdtId: ctx.kdtId,
      buyerId: this.buyerId,
      purchaseItems,
    });

    ctx.json(0, 'success', result);
  }

  async unSelectPurchaseItem(ctx: Context) {
    const { purchaseItems } = this.ctx.getPostData();
    const result = await new PurchaseListService(ctx).unSelectPurchaseItem({
      kdtId: ctx.kdtId,
      buyerId: this.buyerId,
      purchaseItems,
    });

    ctx.json(0, 'success', result);
  }

  async selectAllPurchaseItem(ctx: Context) {
    const result = await new PurchaseListService(ctx).selectAllPurchaseItem({
      kdtId: ctx.kdtId,
      buyerId: this.buyerId,
    });

    ctx.json(0, 'success', result);
  }

  async unSelectAllPurchaseItem(ctx: Context) {
    const result = await new PurchaseListService(ctx).unSelectAllPurchaseItem({
      kdtId: ctx.kdtId,
      buyerId: this.buyerId,
    });

    ctx.json(0, 'success', result);
  }

  async updatePurchaseItemNum(ctx: Context) {
    const { purchaseItem } = this.ctx.getPostData();

    const result = await new PurchaseListService(ctx).updatePurchaseItemNum({
      kdtId: ctx.kdtId,
      buyerId: this.buyerId,
      purchaseItem,
    });

    ctx.json(0, 'success', result);
  }

  async deletePurchaseItem(ctx: Context) {
    const { purchaseItems } = this.ctx.getPostData();

    const result = await new PurchaseListService(ctx).deletePurchaseItem({
      kdtId: ctx.kdtId,
      buyerId: this.buyerId,
      purchaseItems,
      deleteMode: 0,
    });

    ctx.json(0, 'success', result);
  }

  async reselectPurchaseItem(ctx: Context) {
    const { purchaseItems } = this.ctx.getPostData();

    const result = await new PurchaseListService(ctx).reselectPurchaseItem({
      kdtId: ctx.kdtId,
      buyerId: this.buyerId,
      purchaseItems,
    });

    ctx.json(0, 'success', result);
  }

  async countPurchaseItemNum(ctx: Context) {
    const result = await new PurchaseListService(ctx).countPurchaseItemNum({
      kdtId: ctx.kdtId,
      buyerId: this.buyerId,
    });

    ctx.json(0, 'success', result);
  }

  async getSkuData(ctx: Context) {
    const { alias, offlineId = 0 } = ctx.getQueryData();

    const params = {
      alias,
      userId: ctx.buyerId,
      businessType: 'wsc',
      platform: ctx.platform,
      client: ctx.client,
      queryComboDetail: true,
    } as GetSkuParamsDTO;

    if (offlineId) params.offlineId = offlineId;

    const result = await new GoodsSkuService(ctx).getSkuData(params);

    ctx.json(0, '', result);
  }
}

export = PurchaseOrderController;
