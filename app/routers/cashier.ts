export = [
  [
    'POST',
    ['/pay/wsctrade/cashier/qr_pay.json', '/wsctrade/cashier/pay.json'],
    'cashier.CashierController',
    'postQrPayJson'
  ],
  [
    'POST',
    '/wsctrade/cashier/qrcode.json',
    'cashier.CashierController',
    'postEditQrCode'
  ],
  [
    'GET',
    '/wsctrade/cashier/checkCertification.json',
    'cashier.CashierController',
    'checkCertification'
  ],
  [
    'GET',
    ['/pay/wsctrade_cashier', '/pay/wsctrade/cashier'],
    'cashier.CashierController',
    'getIndexHtml'
  ],
  [
    'GET',
    '/wsctrade/cashier/success',
    'cashier.CashierController',
    [
      'checkRedirectToShopDomain',
      'getSuccessHtml'
    ]
  ],
  [
    'GET',
    '/wsctrade/cashier/fail',
    'cashier.CashierController',
    [
      'checkRedirectToShopDomain',
      'getFailHtml'
    ]
  ]
];
