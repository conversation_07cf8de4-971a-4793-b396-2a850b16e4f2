const BaseService = require('../base/BaseService');

class ExpressDetailService extends BaseService {
  get DELIVERY_SERVICE() {
    return 'com.youzan.ebiz.mall.trade.seller.api.service.delivery.OrderDeliveryService';
  }

  get DELIVERY_QUERY_SERVICE() {
    return 'com.youzan.trade.dc.api.service.query.DeliveryQueryService';
  }

  get BUYER_DELIVERY_SERVICE() {
    return 'com.youzan.ebiz.mall.trade.buyer.api.service.DeliveryService';
  }

  async getCityDetail(params) {
    return await this.invoke(this.DELIVERY_SERVICE, 'cityDetail', [params]);
  }

  /**
   * ZanAPI: https://zanapi.qima-inc.com/site/service/view/285427
   * 天网: https://ops.qima-inc.com/v3/skynet/#/main/prod/log/search/all-log?appName=wsc-h5-trade&order=DESC&tags=methodName%3DgetExpressDetail&tags=serviceName%3DDeliveryService
   * @param params
   * @returns {Promise<any>}
   */
  async getExpressDetail(params) {
    const result = await this.invoke(this.BUYER_DELIVERY_SERVICE, 'getExpressDetail', [params]);
    result &&
      result.forEach(deliveryOrder => {
        let expressProgress = deliveryOrder.express && deliveryOrder.express.expressProgress;
        if (expressProgress) {
          try {
            expressProgress = JSON.parse(expressProgress);
          } catch (error) {
            expressProgress = [];
          }
          deliveryOrder.express.expressProgress = expressProgress;
        }
      });
    return result;
  }

  async queryDeliveryByOrderNo(params) {
    let result = await this.invoke(this.DELIVERY_QUERY_SERVICE, 'queryByOrderNo', [params]);
    result =
      result &&
      result.map(deliveryOrder => {
        const { deliveryOrderItems = [], distOrderDTOs = [] } = deliveryOrder;
        const deliveryOrderItem = deliveryOrderItems[0] || {};

        const expressInfo = (distOrderDTOs[0] && distOrderDTOs[0].expressInfo) || {};
        const expressProgressJsonStr = expressInfo.expressDetail && expressInfo.expressDetail.expressProgressInfo;
        let expressProgress = [];
        if (expressProgressJsonStr && expressProgressJsonStr.length > 2) {
          try {
            expressProgress = JSON.parse(expressProgressJsonStr);
          } catch (e) {
            // do nothing
          }
        }
        return {
          deliveryStatus: deliveryOrderItem.deliveryStatus,
          deliveryStatusDesc: deliveryOrderItem.deliveryStatusDesc,
          expressProgress
        };
      });
    return result;
  }
}

module.exports = ExpressDetailService;
