const PayBaseController = require('./PayBaseController');
const MerchantPayService = require('../../services/pay/MerchantPayService');
const WscPayService = require('../../services/pay/WscPayService');
const mapKeysToCamelCase = require('@youzan/utils/string/mapKeysToCamelCase');

const CONST_PAY_TOOL = {
  CREDIT_CARD: 'CREDIT_CARD',
  BANK_CARD: 'BANK_CARD'
};

class MerchantPayController extends PayBaseController {
  async payAcl() {
    await this.acl({
      allowNotLogin: true,
      useAjaxLogin: false,
      forceOauthLogin: false,
      kdtId: this.ctx.kdtId
    });
  }

  // H5 - 物流配送详情页
  async getIndexHtml(ctx) {
    await this.payAcl();
    if (ctx.status === 302) {
      return;
    }

    const {query} = ctx;
    // 设置初始页面路由
    ctx.setGlobal('indexRoute', 'pay');

    const bankCardPayExt = {
      BANK_CARD_PAY: 'v1'
    };
    const partnerId = query.partner_id || '';
    const cashierReqData = {
      prepayId: query.prepay_id || '',
      cashierSalt: query.cashier_salt || '',
      cashierSign: query.cashier_sign || '',
      scene: 'COMMON',
      partnerId,
      platFormSource: 'FROM_H5',
      environment: this.getEnv(),
      buyerId: String(this.buyerId || -1),
      cashierBizExt: JSON.stringify(bankCardPayExt)
    };

    const payChannelData = await new MerchantPayService(ctx).getPayChannels(partnerId, cashierReqData);
    let business = {};
    if (payChannelData) {
      business = {
        ...query,
        partnerReturnUrl: 'https://cashier.youzan.com/pay/wsctrade_mcashier?after_pay=true',
        payWays: payChannelData.pay_channels || [], // merchantPayMock.payWays,
        slideText: payChannelData.slide_text,
        account: payChannelData.account,
        title: payChannelData.mch_name,
        subtitle: payChannelData.goods_name,
        price: payChannelData.pay_amount || 0,
        img:
          payChannelData.logo_url ||
          'https://img01.yzcdn.cn/public_files/2017/02/09/e84aa8cbbf7852688c86218c1f3bbf17.png',
        outBizNo: payChannelData.out_biz_no || ''
      };
    }

    ctx.setGlobal('business', business);

    await ctx.render('pay/merchant-pay.html');
  }

  /**
   * 测试页面
   * @param {*} ctx
   */
  async getPrepayHtml(ctx) {
    // 设置初始页面路由
    ctx.setGlobal('indexRoute', 'createorder');

    await ctx.render('pay/merchant-pay.html');
  }

  /**
   * 支付接口
   */
  async postPayJson(ctx) {
    const reqData = mapKeysToCamelCase(ctx.request.body || {});
    const { citic_weixin_openid = '', proxy_weixin_openid = '', platform = { platform_user_id: '' } } = ctx.getLocalSession();

    const payDataExt = {
      SUPPORT_VERITY_TYPE: 'v1'
    };
    const partnerId = reqData.partnerId || '';
    const payData = {
      prepayId: reqData.prepayId || '',
      partnerId,
      cashierSalt: reqData.cashierSalt || '',
      cashierSign: reqData.cashierSign || '',
      payTool: reqData.payTool || '',
      buyerId: String(this.buyerId || -1),
      wxSubOpenId: proxy_weixin_openid,
      wxYzOpenId: proxy_weixin_openid,
      wxCiticOpenId: citic_weixin_openid,
      environment: this.getEnv(),
      userIp: this.getRemoteIp(),
      partnerReturnUrl: `${reqData.partnerReturnUrl}&prepay_id=${reqData.prepayId}&cashier_sign=${
        reqData.cashierSign
      }&cashier_salt=${reqData.cashierSalt}&partner_id=${reqData.partnerId}`,
      cashierBizExt: JSON.stringify(payDataExt)
      /* userName: reqData.userName || '', */ // 凭证支付才需要
      /* account: '', */ // 不用E卡等先不要这个参数
      /* payTime: '2018-04-28 15:00:00' || '', */ // 凭证支付才需要
    };
    if (CONST_PAY_TOOL.CREDIT_CARD === reqData.payTool || CONST_PAY_TOOL.BANK_CARD === reqData.payTool) {
      // 信用卡处理
      Object.assign(payData, { payRouter: 'credit_mock' });
    }
    // 改价逻辑
    if (reqData.acceptPrice) {
      payData.acceptPrice = 1;
      payData.newPrice = reqData.newPrice;
    }
    let result;
    if (reqData.payTool === 'ALIPAY_JS' ) {
      payData.aliBuyerId = platform.platform_user_id;
      result = await new WscPayService(ctx).pay109(partnerId, payData);
    } else {
      result = await new WscPayService(ctx).pay(partnerId, payData);
    }
    if (result) {
      const data = this._formatPayDataInfoByCommon(result, reqData);

      if (CONST_PAY_TOOL.CREDIT_CARD === reqData.payTool) {
        const payData = data.pay_data || {};
        data.pay_data = {
          submit_url: '/pay/wsctrade/bankcard',
          submit_method: 'GET',
          submit_data: {
            order_no: payData.order_no,
            partner_id: payData.partner_id,
            prepay_id: payData.prepay_id,
            cashier_sign: payData.cashier_sign,
            cashier_salt: payData.cashier_salt,
            pay_amount: payData.pay_amount,
            trade_desc: payData.trade_desc,
            partner_return_url: data.redirect_url
          }
        };
      } else if (CONST_PAY_TOOL.BANK_CARD === reqData.payTool) {
        const payData = data.pay_data || {};
        data.pay_data = {
          submit_url: '/pay/wsctrade/bankcard/v3',
          submit_method: 'GET',
          submit_data: {
            order_no: payData.order_no,
            partner_id: payData.partner_id,
            prepay_id: payData.prepay_id,
            cashier_sign: payData.cashier_sign,
            cashier_salt: payData.cashier_salt,
            pay_amount: payData.pay_amount,
            trade_desc: payData.trade_desc,
            partner_return_url: data.redirect_url
          }
        };
      }
      ctx.r(0, '', data);
    } else {
      ctx.r(9999, '服务器开小差了');
    }
  }

  /**
   * 查询支付状态
   */
  async getPayStatusJson(ctx) {
    const {query} = ctx;

    /**
     * 1. 网关参数组装
     */
    const partnerId = query.partner_id || '';
    const queryData = {
      prepayId: query.prepay_id || ''
    };
    const statusData = await new MerchantPayService(ctx).getPayStatus(partnerId, queryData);

    /**
     * 2.返回结果处理
     */
    if (statusData) {
      const acquireQueryStatus = statusData.acquire_query_status;
      let payStatusCode = 0;
      let payStatusStr;
      if (
        acquireQueryStatus === 'BUYER_PAIED' ||
        acquireQueryStatus === 'PAID_TO_SELLER' ||
        acquireQueryStatus === 'SUCCESS'
      ) {
        payStatusCode = 0;
        payStatusStr = 'success';
      } else if (acquireQueryStatus === 'PAYING' || acquireQueryStatus === 'CANCEL') {
        payStatusCode = 0;
        payStatusStr = 'cancel';
      } else if (acquireQueryStatus === 'FAIL' || acquireQueryStatus === 'CREATE') {
        payStatusCode = 0;
        payStatusStr = 'empty';
      } else {
        payStatusCode = 1001;
        payStatusStr = 'nexist';
      }

      ctx.r(payStatusCode, '', {
        status: payStatusStr
      });
    } else {
      ctx.r(9999, '服务器开小差了');
    }
  }

  async getSuccessHtml(ctx) {
    const {query} = ctx;

    /**
     * 1. 拼接参数
     */
    const queryData = {
      prepayId: query.prepay_id || ''
    };

    /**
     * 2. 请求接口
     */
    const cashierInfoResp = await new MerchantPayService(ctx).getCashierInfo(query.partner_id || '', queryData);

    if (cashierInfoResp) {
      // console.log('[getCashierInfo] ' + JSON.stringify(cashierInfoResp));
      const isJump = cashierInfoResp.is_need_success_jump;

      if (isJump === 0 && cashierInfoResp.return_url) {
        if (cashierInfoResp.return_url.indexOf('?')) {
          cashierInfoResp.return_url += '&success=true';
        } else {
          cashierInfoResp.return_url += '?&success=true';
        }
        return;
      }

      const acquireQueryStatus = cashierInfoResp.acquire_query_status;
      let payStatusStr;
      let payStatus = 0;
      if (
        acquireQueryStatus === 'BUYER_PAIED' ||
        acquireQueryStatus === 'PAID_TO_SELLER' ||
        acquireQueryStatus === 'SUCCESS'
      ) {
        payStatusStr = '支付成功';
        payStatus = 1;
      } else if (acquireQueryStatus === 'PAYING') {
        payStatusStr = '支付中';
      } else if (acquireQueryStatus === 'CANCEL') {
        payStatusStr = '已取消';
      } else if (acquireQueryStatus === 'FAIL' || acquireQueryStatus === 'CREATE') {
        payStatusStr = '支付失败';
      } else {
        payStatusStr = '无效订单';
      }

      const payTool = cashierInfoResp.pay_tool;
      let payChannelName;
      if (payTool === 'WXPAY') {
        payChannelName = '微信安全支付';
      } else if (payTool === 'ALIPAY') {
        payChannelName = '支付宝支付';
      } else if (payTool === 'ECARD') {
        payChannelName = '有赞E卡支付';
      } else if (payTool === 'WXNATIVE') {
        payChannelName = '微信扫码支付';
      } else if (payTool === 'PREPAID_PAY') {
        payChannelName = '储值余额支付';
      } else if (payTool === 'VALUE_CARD') {
        payChannelName = '会员余额支付';
      } else if (payTool === 'CREDIT_CARD') {
        payChannelName = '信用卡支付';
      } else if (payTool === 'BAIFUBAO_WAP') {
        payChannelName = '储蓄卡支付';
      }

      const payDataJson = {
        title: cashierInfoResp.mch_name || '有赞店铺',
        price: cashierInfoResp.pay_amount,
        outBizNo: cashierInfoResp.acquire_no,
        payFinishTime: cashierInfoResp.pay_finish_time
      };
      const business = {
        goodType: '普通商品',
        payDataJson,
        payChannelName,
        payStatus, // 支付状态：1-支付成功，0-其他（支付中，支付失败，取消支付等）
        payStatusStr
      };

      if (cashierInfoResp.return_url) {
        Object.assign(business, {
          returnUrl: cashierInfoResp.return_url
        });
      }
      ctx.setGlobal('business', business);
      await ctx.render('pay/cashier-success.html');
    }
  }

  /**
   *
   * @param $result 支付宝,微信公众号,银行卡等收单支付结果
   * @return array
   */
  _formatPayDataInfoByCommon(data, cashierData) {
    if (!data) {
      return null;
    } 
      const deepLinkInfo = (data && data.deep_link_info) || '{}';
      if (typeof deepLinkInfo === 'string') {
        try {
          data.deep_link_info = JSON.parse(deepLinkInfo);
        } catch (e) {
          //
        }
      }

      const prefix = data.partner_return_url || 'https://cashier.youzan.com/pay/wsctrade_mcashier?after_pay=true';
      const returnUrl = `${prefix}&prepay_id=${cashierData.prepayId}&cashier_sign=${
        cashierData.cashierSign
      }&cashier_salt=${cashierData.cashierSalt}&partner_id=${cashierData.partnerId}`;
      const payDataInfo = {
        raw_data: data,
        pay_data: data.deep_link_info,
        redirect_url: returnUrl
      };
      return payDataInfo;
    
  }
}

module.exports = MerchantPayController;
