<template>
  <div v-if="selectablePresents.length">
    <div class="present">
      <div class="present__title">
        <span class="present__title-type">赠品</span>
        <span v-if="isCanSelect" class="present__title-num">（可选{{ selectablePresentNum }}种）</span>
      </div>
      <div v-if="isCanSelect" @click.stop="handlePickPresent" class="present__pick">
        去挑选
        <van-icon name="arrow" />
      </div>
    </div>
    <present-goods
      v-for="goods in presentList"
      :key="`${goods.alias}-${goods.skuId}`"
      :is-can-choose="false"
      :goods="goods"
      :kdt-id="kdtId"
      :activity-id="activityId"
      :navigate-to-goods="navigateToGoods"
      @change-sku="handleChangeSku"
    />
  </div>
</template>

<script>
import PresentGoods from 'components/present-goods';
import { Icon } from 'vant';

export default {
  name: 'Present',

  components: {
    [Icon.name]: Icon,
    [PresentGoods.name]: PresentGoods,
  },

  props: {
    presentList: {
      type: Array,
      default: [],
    },
    // 活动信息
    groupActivityInfo: {
      type: Object,
      default: {},
    },
    activityId: Number,
    kdtId: {
      type: Number,
      default: 0,
    },
    navigateToGoods: Function,
  },

  computed: {
    selectablePresentNum() {
      return (this.groupActivityInfo && this.groupActivityInfo.selectablePresentNum) || 0;
    },

    selectablePresents() {
      return (this.groupActivityInfo && this.groupActivityInfo.selectablePresents) || [];
    },

    isCanSelect() {
      return this.selectablePresentNum < this.selectablePresents.length;
    },
  },

  methods: {
    handlePickPresent() {
      const { activityId, selectablePresents, selectablePresentNum } = this.groupActivityInfo || {};
      // 因为在外层也可以修改sku，所以需要覆盖selectablePresents当中选中的sku
      const goodsList =
        selectablePresents?.map((item) => {
          this.presentList.some((present) => {
            if (present.id === item.id) {
              item = {
                ...item,
                sku: present.sku,
                skuId: present.skuId,
              };
              return true;
            }
            return false;
          });
          return item;
        }) ?? [];
      this.$emit('on-show', {
        show: true,
        pickGoodsList: this.presentList,
        activityId,
        goodsList,
        selectablePresentNum,
      });
    },

    handleChangeSku(data) {
      this.$emit('change-sku', data);
    },
  },
};
</script>

<style lang="scss" scoped>
.present {
  display: flex;
  justify-content: space-between;
  padding: 0 12px 8px 44px;
  align-items: center;
  font-size: 12px;

  &__title {
    &-type {
      font-weight: 500;
    }

    &-num {
      font-size: 11px;
      color: #969799;
    }
  }

  &__pick {
    display: flex;
    align-items: center;
    color: #969799;
  }
}
</style>
