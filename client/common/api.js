/**
 * 接口请求
 */
import ajax, { rawAjax, jsonp } from '@youzan/zan-h5-ajax';
import cookie from '@youzan/utils/browser/cookie';
import args from '@youzan/utils/url/args';
import <PERSON>an<PERSON>ogger from 'common/log/skynet-report';

const { CancelToken } = rawAjax;

/**
 * 为请求添加 kdt_id 参数
 * @param {Object} options - 请求配置
 * @returns {Object} 增强后的请求配置
 */
function addKdtIdToRequest(options = {}) {
  if (!options.url) return options;
  try {
    const { _global = {} } = window;
    // 如果存在 _global.queryAddKdtId，queryAddKdtId为实际kdt_id值，则添加 kdt_id 到 query
    if (_global.queryAddKdtId) {
      const kdtId = _global.kdt_id || _global.kdtId || _global.queryAddKdtId;
      // 如果链接 query或 data 上没有 kdt_id 或 kdtId，则添加 kdt_id 到 query
      const existingKdtId =
        args.get('kdt_id', options.url) ||
        args.get('kdtId', options.url) ||
        options.data?.kdtId ||
        options.data?.kdt_id;
      if (!existingKdtId && +kdtId > 0) {
        options.url = args.add(options.url, { kdt_id: kdtId });
      }
    }
    return options;
  } catch (error) {
    return options;
  }
}

function formatError(err) {
  if (err.msg === 'Network Error') {
    // eslint-disable-next-line youzan/wsc-standard-words
    err.msg = '网络错误，请稍后再试';
  }
  return err;
}
class Api {
  constructor() {
    this.requestMap = {}; // 记录相同请求的cancelToken
  }

  ajax(options) {
    options = addKdtIdToRequest(options);
    if (options.needAbort) {
      const cancelToken = this.requestMap[options.url];
      if (typeof cancelToken === 'function') {
        cancelToken('请求被取消');
      }
    }

    // 给存在csrf安全问题的请求加上csrf_token
    const csrfToken = cookie('csrf-token') || '';
    if (options.disableCsrfToken === false && csrfToken) {
      options.headers = {
        ...options.headers,
        'csrf-token': csrfToken,
      };

      // scrfToken log
      ZanLogger.paasLog({
        name: 'csrfTokenRequest',
        message: JSON.stringify({
          csrfSecret: cookie('csrf-secret') || '',
          ...options,
        }),
      });
    }

    return ajax({
      withCredentials: true,
      timeout: 10000,
      contentType: 'application/json; charset=utf-8',
      data: options.data,
      cancelToken: new CancelToken((c) => {
        this.requestMap[options.url] = c;
      }),
      options: {
        rawResponse: true,
        useCdn: !!options.useCdn,
      },
      ...options,
    })
      .then((res) => {
        if (options.rawResponse) return res;

        if (String(res.code) === '0') {
          return res;
        }

        throw formatError(res);
      })
      .catch((err) => {
        throw formatError(err);
      });
  }

  jsonp(options) {
    return jsonp({ ...options });
  }
}

Api.prototype.rawAjax = rawAjax;

// 挂载常用方法
['post', 'get', 'delete', 'put'].forEach((method) => {
  Api.prototype[method] = function (options) {
    return this.ajax({ method, ...options });
  };
});

export const api = new Api();
