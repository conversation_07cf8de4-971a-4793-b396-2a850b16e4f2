import BaseService from '../base/BaseService';
import { RiskWarnResponse } from 'definitions/order/buy/RiskWarnResponse';
import { RiskWarnRequest } from 'definitions/order/buy/RiskWarnRequest';

class AdvisoryService extends BaseService {
  get ADVISORY_SERVICE() {
    return 'com.youzan.ctu.list.api.advisory.service.RiskWarnService';
  }

  /**
   * 查询 风险店铺
   * 接口文档  https://doc.qima-inc.com/pages/viewpage.action?pageId=207227230
   * @param params
   */
  queryIsRiskWarnShop(params: RiskWarnRequest): Promise<RiskWarnResponse> {
    return this.invoke(this.ADVISORY_SERVICE, 'match', [params]);
  }

  /**
   * 查询 风险店铺
   * 接口文档  https://doc.qima-inc.com/pages/viewpage.action?pageId=207227230
   * @param params
   */
  queryIsRiskWarnShopFast(params: RiskWarnRequest): Promise<string> {
    return this.invokeFast(this.ADVISORY_SERVICE, 'match', [params], {
      responseType: 'text'
    });
  }
}

export = AdvisoryService;
