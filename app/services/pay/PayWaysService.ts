import BaseService from '../base/BaseService';
import { UserNoRequest, UserNoResponse } from 'definitions/pay/QueryUserNo';
import { PayToolRequest, PayToolResponse } from 'definitions/pay/QueryPayTool';

class PayWaysService extends BaseService {
  /**
   * 根据kdtId 获取 商户号
   */
  async queryUserNoBySourceId(params: UserNoRequest): Promise<UserNoResponse> {
    return this.invoke('com.youzan.pay.merchant.common.api.merchant.MerchantQueryService', 'queryUserNoBySourceId', [
      params
    ]);
  }

  /**
   * 根据userNo和partnerId获取支付方式
   */
  getAvailablePayToolListFromCore(params: PayToolRequest): Promise<PayToolResponse> {
    return this.invoke('com.youzan.pay.customer.api.PayToolsService', 'getAvailablePayToolListFromCore', [params]);
  }
}

export = PayWaysService;
