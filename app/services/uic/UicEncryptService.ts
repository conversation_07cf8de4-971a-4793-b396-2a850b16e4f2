import BaseService from '../base/BaseService';

class UicEncryptService extends BaseService {
  private get UIC_ENCRYPT_SERVICE() {
    return 'com.youzan.uic.session.api.trial.service.UicEncryptService';
  }

  /**
   * kostoken编码
   * @param params
   */
  public async aesEncrypt(params: any): Promise<any> {
    const encryptObject = { encryptObject: params };
    return this.invoke(this.UIC_ENCRYPT_SERVICE, 'aesEncrypt', [encryptObject]);
  }

  /**
   * kostoken解码
   */
  public async aesDecrypt(params: string): Promise<any> {
    const decryptObject = { decryptObject: params };
    return this.invoke(this.UIC_ENCRYPT_SERVICE, 'aesDecrypt', [decryptObject]);
  }
}

export default UicEncryptService;
