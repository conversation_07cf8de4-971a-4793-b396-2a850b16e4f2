/**
 * 埋点上报的逻辑处理
 */
import { Logger } from 'common/log/logger';

// 商品数量修改即上报埋点，与其他业务无关
export const sendGoodsNumChangeLog = (prevNum, curNum, goods) => {
  const { goodsId, title, skuId, sku } = goods;

  const skuName = JSON.parse(sku || '[]');

  const params = {
    goods_id: goodsId,
    goods_name: title,
    num: curNum,
    sku_id: skuId,
    sku_name: skuName,
    no_sku: skuName.length ? 0 : 1,
  };

  if (prevNum > curNum) {
    Logger.log({
      et: 'click',
      ei: 'cart_decrease_goods_num',
      en: '购物车页面-减少商品数量',
      params,
    });
  } else if (prevNum < curNum) {
    Logger.log({
      et: 'click',
      ei: 'cart_increase_goods_num',
      en: '购物车页面-增加商品数量',
      params,
    });
  }
};

const numChangeLog = (num, isShowEstimatePrice, skuId, goodsId) => {
  return {
    et: 'custom', // 事件类型
    ei: 'change_goods_quantity', // 事件标识
    en: '直接修改数量', // 事件名称
    params: {
      initial_value: num,
      isshow_estimate_price: isShowEstimatePrice,
      component: 'quantity_selector',
      sku_id: skuId,
      goods_id: goodsId,
    }, // 事件参数
  };
};

const numAddLog = (num, isShowEstimatePrice, skuId, goodsId) => {
  return {
    et: 'click', // 事件类型
    ei: 'increase_num', // 事件标识
    en: '增加数量', // 事件名称
    params: {
      initial_value: num,
      isshow_estimate_price: isShowEstimatePrice,
      component: 'quantity_selector',
      sku_id: skuId,
      goods_id: goodsId,
    }, // 事件参数
  };
};

const numLessenLog = (num, isShowEstimatePrice, skuId, goodsId) => {
  return {
    et: 'click', // 事件类型
    ei: 'reduce_num', // 事件标识
    en: '减少数量', // 事件名称
    params: {
      initial_value: num,
      isshow_estimate_price: isShowEstimatePrice,
      component: 'quantity_selector',
      sku_id: skuId,
      goods_id: goodsId,
    }, // 事件参数
  };
};

class BaseLoggerUtils {
  constructor() {
    this.skuId = 0;
  }
  getSkuId() {
    return this.skuId;
  }
  setSkuId(id) {
    this.skuId = id;
    return this;
  }
  setGoodsId(id) {
    this.goodsId = id;
    return this;
  }
  reset() {
    this.skuId = 0;
    this.goodsId = 0;
    return this;
  }
}

// 修改数量后的埋点工具
export class NumberLoggerUtils extends BaseLoggerUtils {
  constructor() {
    super();
    this.reset();
  }
  setPreNum(num) {
    this.preNum = num;
    return this;
  }
  setNextNum(num) {
    this.nextNum = num;
    return this;
  }
  setShow(isShowEstimatePrice) {
    this.isShowEstimatePrice = isShowEstimatePrice;
    return this;
  }
  reset() {
    this.preNum = 0;
    this.nextNum = 0;
    this.isShowEstimatePrice = -1;
    super.reset();
    return this;
  }
  getLogData() {
    switch (this.nextNum - this.preNum) {
      case 1: {
        return numAddLog(this.preNum, this.isShowEstimatePrice, this.skuId, this.goodsId);
      }
      case -1: {
        return numLessenLog(this.preNum, this.isShowEstimatePrice, this.skuId, this.goodsId);
      }
      default: {
        return numChangeLog(this.preNum, this.isShowEstimatePrice, this.skuId, this.goodsId);
      }
    }
  }
  log() {
    Logger.log(this.getLogData());
    return this;
  }
}

// 选中商品后的埋点工具
export class SelectedLoggerUtils extends BaseLoggerUtils {
  constructor() {
    super();
    this.reset();
  }
  setNum(num) {
    this.num = num;
    return this;
  }
  reset() {
    this.num = 0;
    super.reset();
    return this;
  }
  log() {
    Logger.log({
      et: 'view', // 事件类型
      ei: 'show_estimated_price', // 事件标识
      en: '预估单价曝光', // 事件名称
      params: {
        goods_nums: this.num,
        component: 'show_estimated_price',
        sku_id: this.skuId,
        goods_id: this.goodsId,
      }, // 事件参数
    });
    return this;
  }
}
