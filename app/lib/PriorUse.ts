import { IPayChannel } from '@youzan/zan-pay-core';

export interface IConfig {
  type: 'buy' | 'detail';
  context: Record<string, any>;
  onSuccess(data: IPriorUseOrderBuyData | IPriorUseOrderDetailData): unknown;
  onError?(error: Error, context: Record<string, any>): unknown;
}

export interface IAsyncConfig extends IConfig {
  transform(
    context: Record<string, any>
  ): Promise<IOrderBuyContext | IOrderDetailContext>;
}

export interface IPriorUseOrderBuyData {
  /** 是否展示使用先用后付radio */
  show: boolean;
  /** 是否可以选择使用先用后付 */
  enable: boolean;
  /** 是否支持展示协议 */
  protocol: false | string;
  /** 先用后付默认的radio状态 */
  confirm: '0' | '1';
  /** 限制金额范围 */
  range: [number | null, number | null];
  /** 限制文案展示 */
  reason: [string | null, string | null];
}

export interface IPriorUseOrderDetailData {
  show: boolean;
  status: 'PENDING' | 'PAID';
}

export interface IOrderBuyContext {
  isPriorUse: boolean;
  selectedPayChannel: string;
  payChannels: IPayChannel[];
}

export interface IOrderDetailContext {
  isPriorUse: boolean;
  isPriorUsePaid: boolean;
}

const PRIOR_USE = 'PRIOR_USE';
const noop = () => {};

function getProtocolLink(no?: number | string) {
  return (!!no && `https://cashier.youzan.com/assets/protocol/${no}`) || void 0;
}

function getPriceLimit(value?: [number, string?]) {
  return !value ? null : value[0];
}

function getPriceMessage(value?: [number, string?]) {
  return !value ? null : value[1] ?? null;
}

function getBuyData({
  isPriorUse = false,
  selectedPayChannel = '',
  payChannels = [],
}: Partial<IOrderBuyContext>) {
  // 先用后付下单页数据
  const useBeforePay: IPriorUseOrderBuyData = {
    show: isPriorUse,
    // 默认允许使用， amount>300的判断在客户端动态做处理
    enable: true,
    // 暂时一律展示协议内容
    protocol: false,
    confirm: selectedPayChannel === PRIOR_USE ? '1' : '0',
    range: [null, null],
    reason: [null, null],
  };
  const found = payChannels.find((i) => i.payChannel === PRIOR_USE);
  if (!found) {
    useBeforePay.show = false;
    useBeforePay.enable = false;
    useBeforePay.protocol = false;
    useBeforePay.confirm = '0';
  } else {
    const { min, max } = found.price ?? {};
    const { no } = found.protocol?.links?.[0] ?? {};
    useBeforePay.protocol = getProtocolLink(no) ?? false;
    useBeforePay.range = [getPriceLimit(min), getPriceLimit(max)];
    useBeforePay.reason = [getPriceMessage(min), getPriceMessage(max)];
  }
  return useBeforePay;
}

function getDetailData({
  isPriorUse = false,
  isPriorUsePaid = false,
}: Partial<IOrderDetailContext>) {
  // 先用后付下单页数据
  const useBeforePay: IPriorUseOrderDetailData = {
    show: !!isPriorUse,
    status: isPriorUsePaid ? 'PAID' : 'PENDING',
  };
  return useBeforePay;
}

export function initOrderData({
  context = {},
  type,
  onSuccess,
  onError = noop,
}: IConfig) {
  try {
    onSuccess(type === 'buy' ? getBuyData(context) : getDetailData(context));
  } catch (error) {
    console.log(error);
    onError(error, context);
  }
}

export async function initOrderDataAsync({
  context = {},
  type,
  onSuccess,
  onError = noop,
  transform,
}: IAsyncConfig) {
  try {
    const realCtx = await transform(context);
    onSuccess(
      type === 'buy'
        ? getBuyData(<IOrderBuyContext>realCtx)
        : getDetailData(<IOrderDetailContext>realCtx)
    );
  } catch (error) {
    console.log(error);
    onError(error, context);
  }
}
