export = [
  [
    'GET',
    ['/wsctrade/order/drug','/wsctrade/order/drug/index'],
    'order.OrderDrugController',
    'getIndexHtml',
  ],
  [
    'GET',
    '/wsctrade/order/drug/getDefaultDisease.json',
    'order.OrderDrugController',
    'getDefaultDisease',
  ],
  [
    'POST',
    '/wsctrade/order/drug/searchDiseaseInfo.json',
    'order.OrderDrugController',
    'searchDiseaseInfo',
  ],
  [
    'POST',
    '/wsctrade/order/drug/queryID.json',
    'order.OrderDrugController',
    'queryID',
  ],
  [
    'POST',
    '/wsctrade/order/drug/create.json',
    'order.OrderDrugController',
    'create',
  ],
  [
    'POST',
    '/wsctrade/order/drug/createUser.json',
    'order.OrderDrugController',
    'createUser',
  ],
  [
    'GET',
    '/wsctrade/order/drug/queryUser.json',
    'order.OrderDrugController',
    'queryUser',
  ],
  [
    'POST',
    '/wsctrade/order/drug/updateUser.json',
    'order.OrderDrugController',
    'updateUser',
  ],
  [
    'POST',
    '/wsctrade/order/drug/queryDiagnoseDisease.json',
    'order.OrderDrugController',
    'queryDiagnoseDisease',
  ],
  [
    'POST',
    '/wsctrade/order/drug/deleteUser.json',
    'order.OrderDrugController',
    'deleteUser',
  ],
  [
    'POST',
    '/wsctrade/order/drug/identifyUser.json',
    'order.OrderDrugController',
    'identifyUser'
  ],
  [
    'GET',
    '/wsctrade/order/drug/getSavedHospital.json',
    'order.OrderDrugController',
    'getSavedHospital'
  ]
];
