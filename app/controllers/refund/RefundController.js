import CompareVersion from '../../lib/CompareVersion';

const _ = require('lodash');
const URL = require('@youzan/iron-base/app/lib/URL');
const PageException = require('@youzan/iron-base/app/exceptions/PageException');
const mapKeysToCamelCase = require('@youzan/utils/string/mapKeysToCamelCase');
const BaseController = require('../base/BaseController');
const RefundService = require('../../services/refund/RefundService');
const EvaluationSerivce = require('../../services/refund/EvaluationSerivce');
const RefundOrderQueryService = require('../../services/refund/RefundOrderQueryService');
const buildUrlWithCtx = require('@youzan/utils/url/buildUrlWithCtx');

const getSource = (ctx, from) => ({
  clientIp: ctx.request.ip,
  from,
});

const REFUND_ACTION = {
  TO_DETAIL: 1, // 跳转到退款申请页
  TO_APPLY: 2, // 跳转到退款申请页
  TOAST: 3,
  NOT_SUPPORT: 4,
  TO_BATCH_APPLY: 5,
};

class RefundController extends BaseController {
  async init() {
    const { kdtId } = this.ctx;
    const initConfig = {
      initGlobalTheme: !!kdtId,
    };
    await super.init(initConfig);
  }

  refundAcl() {
    return this.needPlatformAcl();
  }

  async redirect(ctx) {
    const { orderNo, itemId } = ctx.getQueryData();
    const { kdtId } = ctx;
    const buildUrl = buildUrlWithCtx(this.ctx);

    // NOTE: 如果没有 orderNo 和 itemId，推断需要跳转的页面是退款列表
    if (!orderNo && !itemId) {
      return ctx.redirect(buildUrl('/wscafs/refund/list', 'h5', kdtId));
    }

    try {
      const refundState = await new RefundService(this.ctx).getRefundState({
        orderNo,
        itemId,
        kdtId,
      });
      const { redirectTarget, reason } = refundState;
      if (redirectTarget === REFUND_ACTION.TO_APPLY) {
        ctx.redirect(
          buildUrl(
            `/wscafs/refund/index?kdtId=${kdtId}&itemId=${itemId}&orderNo=${orderNo}#/fill_out_form/`,
            'h5',
            kdtId
          )
        );
      } else if (redirectTarget === REFUND_ACTION.TO_DETAIL) {
        ctx.redirect(
          buildUrl(`/wscafs/refund/index?kdtId=${kdtId}&itemId=${itemId}&orderNo=${orderNo}#/detail/`, 'h5', kdtId)
        );
      } else {
        ctx.redirect(
          buildUrl(`/wscafs/refund/not-support?kdt_id=${kdtId}&reason=${encodeURIComponent(reason)}`, 'h5', kdtId)
        );
      }
    } catch (error) {
      throw new PageException(error.code || 1050, error && error.message);
    }
  }

  async getIndexHtml(ctx) {
    await this.refundAcl();
    if (ctx.status === 302) {
      return;
    }
    const { kdtId } = ctx;
    const { orderNo = '', itemId = '' } = mapKeysToCamelCase(ctx.getQueryData());
    this.setPageCloudBizIds('orderRefund', 'orderNo', orderNo || '');

    // 回写 kdtId，避免 webview 没有 kdtId
    ctx.kdtId = kdtId;
    let refundConcatPhone;
    try {
      refundConcatPhone = await new RefundService(this.ctx).getSellerPhone({
        kdtId: String(kdtId),
      });
    } catch (e) {
      refundConcatPhone = null;
    }

    ctx.setGlobal({
      kdtId,
      orderNo,
      itemId,
      refundConcatPhone,
      theme: ctx.getState('globalTheme'),
      hideYouzanInformation: false,
    });
    // 日志埋点模型
    this.setSpm('refundDetail', kdtId);

    await ctx.render('refund/index.html');
  }

  async getRefundListJson(ctx) {
    const { pageNo, pageSize } = this.ctx.getQueryData();
    const params = {
      buyerId: this.buyerId,
      kdtId: ctx.kdtId,
      pageNo,
      pageSize,
    };
    const refundOrderQueryService = new RefundOrderQueryService(ctx);
    const refundList = await refundOrderQueryService.queryRefundList(params);
    ctx.json(0, 'ok', refundList);
  }

  async getRefundStateJson(ctx) {
    const { orderNo, itemId } = ctx.getQueryData();
    const { kdtId } = ctx;
    this.setPageCloudBizIds('orderRefund', 'orderNo', orderNo || '');
    const [result] = await Promise.all([
      new RefundService(ctx).getRefundState({
        orderNo,
        itemId,
        kdtId: String(kdtId),
      }),
    ]);

    const buildUrl = buildUrlWithCtx(ctx);
    const { redirectTarget, reason = '服务器开小差了' } = result || {};
    const RefundBaseUrl = '/wscafs/refund';
    let redirectUrl = '';
    switch (redirectTarget) {
      case REFUND_ACTION.TO_DETAIL:
        redirectUrl = buildUrl(
          `${RefundBaseUrl}/index?kdtId=${kdtId}&itemId=${itemId}&orderNo=${orderNo}#/detail/`,
          'h5',
          kdtId
        );
        break;
      case REFUND_ACTION.TO_APPLY:
        redirectUrl = buildUrl(
          `${RefundBaseUrl}/index?kdtId=${kdtId}&itemId=${itemId}&orderNo=${orderNo}#/fill_out_form/`,
          'h5',
          kdtId
        );
        break;
      case REFUND_ACTION.NOT_SUPPORT:
        redirectUrl = buildUrl(
          `${RefundBaseUrl}/not-support?kdt_id=${kdtId}&reason=${encodeURIComponent(reason || '')}`,
          'h5',
          kdtId
        );
        break;
      case REFUND_ACTION.TO_BATCH_APPLY:
        redirectUrl = buildUrl(
          `/wscafs/batch-refund/index?kdt_id=${kdtId}&order_no=${orderNo}#/apply-for-refund/`,
          'h5',
          kdtId
        );
        break;
      default:
        break;
    }

    Object.assign(result, { redirectUrl });
    ctx.json(0, 'ok', result);
  }

  async getBuyerGetRefundInfoJson(ctx) {
    const { orderNo, itemId, refundId } = ctx.getQueryData();
    const { kdtId } = ctx;

    this.setPageCloudBizIds('orderRefund', 'orderNo', orderNo || '');
    const refundService = new RefundService(ctx);

    const reqForm = {
      orderNo,
      kdtId: String(kdtId || ''),
      itemId: String(itemId),
      buyerId: String(this.buyerId || 0),
    };
    // 严格来说查询退款详情是对应商品的最新退款单，维权列表里希望是绝对的某个退款单
    if (refundId) {
      reqForm.refundId = String(refundId);
    }

    const result = await refundService.buyerGetRefundInfo(reqForm);

    Object.assign(result, {
      pointsName: _.get(result, 'pointsDetail.pointName', '积分'),
      serverTime: new Date().getTime(),
    });

    ctx.json(0, 'ok', result);
  }

  async getBuyerRefundConsultMessageJson(ctx) {
    const { orderNo, refundId } = ctx.getQueryData();
    const { kdtId } = ctx;
    const result = await new RefundService(this.ctx).buyerRefundConsultMessage({
      orderNo,
      refundId,
      kdtId: String(kdtId || ''),
      operatorId: String(this.buyerId),
    });
    ctx.json(0, 'ok', result);
  }

  async postBuyerCloseRefundJson(ctx) {
    const { orderNo, refundId, from, version } = ctx.request.body;
    const { kdtId } = ctx;
    const source = getSource(ctx, from);

    this.setPageCloudBizIds('orderRefund', 'orderNo', orderNo || '');
    const result = await new RefundService(this.ctx).buyerCloseRefund({
      orderNo,
      refundId,
      kdtId: String(kdtId),
      source,
      version,
      operatorId: String(this.buyerId),
    });

    ctx.json(0, 'ok', result);
  }

  async postBuyerExchangeCloseJson(ctx) {
    const { orderNo, refundId, from, version, itemId } = ctx.request.body;
    const { kdtId } = ctx;
    const source = getSource(ctx, from);

    const result = await new RefundService(this.ctx).buyerExchangeClose({
      itemId,
      kdtId: String(kdtId),
      orderNo,
      operatorId: String(this.buyerId),
      refundId,
      source,
      version,
    });

    ctx.json(0, 'ok', result);
  }

  async getBuyerGetExpressJson(ctx) {
    const { orderNo, refundId } = ctx.getQueryData();
    const { kdtId } = ctx;
    const result = await new RefundService(this.ctx).buyerGetExpress({
      orderNo,
      refundId,
      kdtId: String(kdtId),
    });
    ctx.json(0, 'ok', result);
  }

  async getBuyerRefundInitJson(ctx) {
    const { orderNo, refundId, itemId } = ctx.getQueryData();
    const { kdtId } = ctx;
    this.setPageCloudBizIds('orderRefund', 'orderNo', orderNo || '');
    const result = await new RefundService(this.ctx).buyerRefundInit({
      orderNo,
      refundId,
      kdtId: String(kdtId || ''),
      itemId,
      buyerId: String(this.buyerId || 0),
    });
    ctx.json(0, 'ok', result);
  }

  async postBuyerAddMessageJson(ctx) {
    const { orderNo, content, attachment, refundId, version, from } = ctx.request.body;
    const { kdtId } = ctx;
    const source = getSource(ctx, from);

    const result = await new RefundService(this.ctx).buyerAddMessage({
      orderNo,
      content,
      attachment,
      refundId,
      kdtId: String(kdtId),
      source,
      operatorId: String(this.buyerId),
      version,
    });
    ctx.json(0, 'ok', result);
  }

  async postBuyerInvolveJson(ctx) {
    const { orderNo, remark, attachment, refundId, version, from } = ctx.request.body;
    const { kdtId } = ctx;
    const source = getSource(ctx, from);
    const result = await new RefundService(this.ctx).buyerInvolve({
      orderNo,
      remark,
      attachment,
      refundId,
      kdtId: String(kdtId),
      source,
      operatorId: String(this.buyerId),
      version,
    });
    ctx.json(0, 'ok', result);
  }

  async postBuyerReturnGoodsJson(ctx) {
    const {
      orderNo,
      logisticsNo,
      companyCode,
      attachment,
      remark,
      version,
      from,
      refundId,
      refundType,
    } = ctx.request.body;
    const { kdtId } = ctx;
    const source = getSource(ctx, from);

    const result = await new RefundService(this.ctx).buyerReturnGoods({
      orderNo,
      logisticsNo,
      companyCode,
      attachment,
      remark,
      kdtId: String(kdtId),
      source,
      operatorId: String(this.buyerId),
      version,
      refundId,
      refundType,
    });
    ctx.json(0, 'ok', result);
  }

  async postBuyerRefundUpdateJson(ctx) {
    const form = _.pick(ctx.request.body, [
      'kdtId',
      'orderNo',
      'refundFee',
      'buyerPhone',
      'remark',
      'demand',
      'itemId',
      'attachment',
      'from',
      'receiveGoods',
      'reason',
      'refundId',
      'version',
      'issue',
      'periodBuyRefundDate',
      'disabledTicketCount',
      'returnGoodsCount',
      'returnGoodsType',
    ]);

    this.setPageCloudBizIds('orderRefund', 'orderNo', form.orderNo || '');
    const source = getSource(ctx, form.from);
    const refundDate = Date.now();
    const result = await new RefundService(this.ctx).buyerRefundUpdate({
      ...form,
      kdtId: String(form.kdtId),
      version: String(form.version),
      refundFee: String(form.refundFee),
      operatorId: String(this.buyerId),
      source,
      refundDate,
    });
    ctx.json(0, 'ok', result);
  }

  async postBuyerExchangeUpdateJson(ctx) {
    const {
      attachment,
      buyerPhone,
      demand,
      itemId,
      returnGoodsCount,
      kdtId,
      orderNo,
      remark,
      reason,
      refundId,
      version,
      from,
      returnGoodsType,
    } = ctx.request.body || {};

    const result = await new RefundService(this.ctx).buyerExchangeUpdate({
      attachment,
      buyerPhone,
      demand,
      exchangeGoodsItemDTOList: [{ itemId, returnGoodsCount }],
      itemId,
      kdtId,
      orderNo,
      operatorId: this.buyerId,
      remark,
      reason,
      refundId,
      source: getSource(ctx, from),
      version,
      returnGoodsType,
    });

    ctx.json(0, 'ok', result);
  }

  async postBuyerExchangeReceive(ctx) {
    const { itemId, kdtId, orderNo, refundId, version, from } = ctx.request.body || {};

    const result = await new RefundService(this.ctx).buyerExchangeReceive({
      itemId,
      kdtId,
      orderNo,
      operatorId: this.buyerId,
      refundId,
      source: getSource(ctx, from),
      version,
    });

    ctx.json(0, 'ok', result);
  }

  async postBuyerRefundCreateJson(ctx) {
    const reqBody = ctx.request.body;
    const form = _.pick(reqBody, [
      'kdtId',
      'orderNo',
      'refundFee',
      'buyerPhone',
      'remark',
      'demand',
      'itemId',
      'attachment',
      'from',
      'receiveGoods',
      'reason',
      'issue',
      'periodBuyRefundDate',
      'disabledTicketCount',
      'returnGoodsCount',
      'returnGoodsType',
    ]);
    this.setPageCloudBizIds('orderRefund', 'orderNo', form.orderNo || '');
    const source = getSource(ctx, form.from);
    const refundDate = Date.now();
    // 检查账号绑定
    if (reqBody.bizNeedLogin && !this.buyer.buyerPhone) {
      ctx.logger.warn(`freightBreak_运费补贴退款绑定手机号拦截, ${JSON.stringify(reqBody)}`);
      ctx.json(9999, '为了保证退货运费补贴能正常发放，请先绑定手机号');
      return;
    }

    const result = await new RefundService(this.ctx).buyerRefundCreate({
      ...form,
      kdtId: String(form.kdtId),
      refundFee: String(form.refundFee),
      operatorId: String(this.buyerId),
      source,
      refundDate,
    });
    ctx.json(0, 'ok', result);
  }

  async postBuyerExchangeCreateJson(ctx) {
    const {
      attachment,
      buyerPhone,
      demand,
      itemId,
      returnGoodsCount,
      kdtId,
      orderNo,
      remark,
      reason,
      refundId,
      version,
      from,
      returnGoodsType,
    } = ctx.request.body || {};

    const result = await new RefundService(this.ctx).buyerExchangeCreate({
      attachment,
      buyerPhone,
      demand,
      exchangeGoodsItemDTOList: [{ itemId, returnGoodsCount }],
      itemId,
      kdtId,
      orderNo,
      operatorId: this.buyerId,
      remark,
      reason,
      refundId,
      source: getSource(ctx, from),
      version,
      returnGoodsType,
    });

    ctx.json(0, 'ok', result);
  }

  /**
   * 创建退款评价
   * @param {*} ctx
   */
  async createEvaluationJson(ctx) {
    const evaluationForm = ctx.getPostData();
    const result = await new EvaluationSerivce(ctx).createRefundEvaluation({
      ...evaluationForm,
      reviewerId: this.buyerId,
    });
    ctx.json(0, 'ok', result);
  }

  /**
   * 查询售后评价
   */
  async getEvaluationByRefundId(ctx) {
    const evaluationForm = ctx.getQueryData();
    const result = await new EvaluationSerivce(ctx).getEvaluationByRefundId(evaluationForm.refundId);
    ctx.json(0, 'ok', result);
  }

  /**
   * 通知 - 售后评价弹框已弹
   */
  async addRefundTag(ctx) {
    const evaluationForm = ctx.getPostData();
    const result = await new RefundService(ctx).addRefundTag(evaluationForm);
    ctx.json(0, 'ok', result);
  }

  /**
   * 退款/维权列表
   */
  async getListHtml(ctx) {
    const { kdtId } = mapKeysToCamelCase(ctx.getQueryData());
    if (kdtId && ctx.kdtId) {
      // 回写 kdtId
      ctx.kdtId = kdtId;
    }

    // 以下版本不支持从维权列表跳到退款详情webview
    let isSupportRefundWebview = true;
    const { isWeapp, weappVersion } = ctx;
    if (isWeapp && CompareVersion.isGte(weappVersion, '2.24.3') && CompareVersion.isLte(weappVersion, '2.29.5')) {
      isSupportRefundWebview = false;
    }

    ctx.setGlobal({
      isSupportRefundWebview,
    });
    await ctx.render('refund/list.html');
  }

  async getNotSupportTypeHtml(ctx) {
    await ctx.render('refund/not-support-type.html');
  }
}

module.exports = RefundController;