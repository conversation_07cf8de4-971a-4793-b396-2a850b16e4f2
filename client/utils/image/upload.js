/**
 * 上传图片到 CDN
 */

import { api } from 'common/api';
import { Toast } from 'vant';
import compress from 'utils/image/compress';

// 获取七牛上传 token
function getUploadToken() {
  const tokenUrl = window._global.url.h5 + '/wscshop/token/upload-image.json';
  return api.get({
    url: tokenUrl,
  });
}

// 拼接 form data
function getFormData(file, token) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('token', token);

  const exts = file.name.split('.');
  if (exts.length > 1) {
    formData.append('x:ext', `.${exts[exts.length - 1]}`);
  }

  return formData;
}

function doUploadImg(data) {
  // 调用七牛上传接口
  return api.rawAjax({
    url: '//upload.qiniup.com',
    method: 'POST',
    withCredentials: false,
    contentType: 'multipart/form-data',
    data,
  });
}

function upload(file) {
  return getUploadToken().then((response) => {
    const formData = getFormData(file, response.data.token);
    return doUploadImg(formData);
  });
}

export default function (file, options = {}) {
  if (!options.noToast) {
    Toast('正在压缩图片，请稍候');
  }
  return new Promise((resolve, reject) => {
    compress(file, {
      maxWidth: 500,
      maxHeight: 500,
      type: file.type,
      ...options,
    })
      .then((compressedFile) => {
        // catch中 error 增加size 相关字段，目的是可上报天网，可作为排查问题依据
        upload(compressedFile).then(resolve).catch((err) => {
          if (err) {
            err.compressedFileSize = compressedFile?.size || 0;
          }
          reject(err);
        });
      })
      .catch(reject);
  });
}
