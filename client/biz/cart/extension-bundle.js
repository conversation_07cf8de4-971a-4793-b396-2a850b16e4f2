import './common';
import PageBlock from './extensions/page-block/index';
import ShopHeader from './extensions/shop-header/index';
import CouponBar from './extensions/coupon-bar/index';
import CouponAddonBar from './extensions/coupon-addon-bar/index';
import ValidGoods from './extensions/valid-goods/index';
import InvalidGoods from './extensions/invalid-goods/index';
import CpsGoodsRecommend from './extensions/cps-goods-recommend/index';
import RecommendBlock from './extensions/recommend/index';
import GoodsSku from './extensions/goods-sku/index';
import PresentSku from './extensions/present-sku/index';
import CartUmpBlock from './extensions/cart-ump/index';
import EmptyCart from './extensions/empty-cart/index';
import SubmitBlock from './extensions/submit/index';
import LoginTips from './extensions/level2-login-tips/index';
import SpecialCustomization from './extensions/special-customization/index';

export const extensions = {
  'page-block': PageBlock,
  'shop-header': ShopHeader,
  'valid-goods': ValidGoods,
  'invalid-goods': InvalidGoods,
  'cps-goods-recommend': CpsGoodsRecommend,
  'recommend-block': RecommendBlock,
  'goods-sku': GoodsSku,
  'present-sku': PresentSku,
  'cart-ump-block': CartUmpBlock,
  'empty-cart': EmptyCart,
  'submit-block': SubmitBlock,
  'coupon-bar': CouponBar,
  'coupon-addon-bar': CouponAddonBar,
  'login-tips': LoginTips,
  'special-customization': SpecialCustomization,
};
