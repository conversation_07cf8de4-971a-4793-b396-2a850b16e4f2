import BaseService from '../base/BaseService';

interface IGenerateUrlLinkRedirectUrlDTO {
  /** 获取url link出现错误的时候跳转的链接  比如：链接过期了；获取url link超过额度了，就会跳到这个url */
  errorRedirectUrl?: string;
  /** 源地址链接  【必填】 */
  path?: string;
  /** 链接描述  【必填】 */
  urlDesc?: string;
  /** 是否生成永久短链  注意：和expiredTime参数二选一，但首选expiredTime字段；即填写了expiredTime，就不是永久的；       只有不填expiredTime并且expired为false才会生成永久短链 */
  expired?: boolean;
  /** 店铺kdtId  【必填】 */
  kdtId?: number;
  /** 通过 URL Link 进入小程序时的query，最大1024个字符，只支持数字，大小写英文以及部分特殊字符：!#$&'()*+,/:;=?@-._~  【非必填】 */
  query?: string;
  /** 是否在商家后台展示，默认会展示 */
  display?: boolean;
  /** Url Link失效时间，13位时间戳  注意：和expired参数二选一，但首选expiredTime字段；即填写了expiredTime，就不是永久的；       只有不填expiredTime并且expired为false才会生成永久短链 */
  expiredTime?: number;
}

/** com.youzan.channels.apps.service.MiniProgramUrlService -  */
class MiniProgramUrlService extends BaseService {
  // @ts-ignore
  get SERVICE_NAME() {
    return 'com.youzan.channels.apps.service.MiniProgramUrlService';
  }

  /**
   * 【作用】生成小程序的urlLink的中间链接--有赞短链和长链
 【备注】
     1、有赞短链和长链都能打开
     2、短链：j.youzan.com/gRRaaB   长链：https://h5.youzan.com/v3/wx-scheme-tool?kdtId=91831385&urlCode=bBNoQ27q2z
     3、真正唤起小程序的是url link；由于微信限制，该接口只是生成url link的中间链接有赞短链和长链
     4、由于微信的规则，generateUrl不适用了
     5、urlLink是可以唤起小程序的一种链接
 【逻辑】：利用参数先生成有赞短链以及长链中间链接，然后再预生成微信内使用的url link(微信外使用的在获取的时候生成)
   * zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1295162
   */
  async generateUrlLinkRedirectUrl(generateUrlLinkRedirectUrlDTO: IGenerateUrlLinkRedirectUrlDTO) {
    return this.invoke('generateUrlLinkRedirectUrl', [generateUrlLinkRedirectUrlDTO]);
  }
}

export default MiniProgramUrlService;
