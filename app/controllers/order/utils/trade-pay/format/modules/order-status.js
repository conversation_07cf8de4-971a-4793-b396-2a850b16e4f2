import { formatTime, formtDate } from '../utils/utils';

// 等待尾款开始
function waitPhaseTwoStart({ state }) {
  const { multiPhase, phasePayment } = state.pay;
  return (
    (multiPhase &&
      phasePayment.currentPhase === 2 &&
      phasePayment.phaseItems[1].payStartTime > Date.now()) ||
    false
  );
}

function orderStatusTitle({ state, getters }) {
  if (getters.isDepositPresale) {
    if (state.pay.phasePayment.currentPhase === 1) {
      return '等待买家付定金';
    }

    return getters.waitPhaseTwoStart ? '等待尾款支付开始' : '等待买家付尾款';
  }
  return '等待买家付款';
}

function orderStatusSteps({ prepare }) {
  return prepare?.tradeConfirmation?.steps || [];
}

function orderStatusDesc({ state, getters }) {
  const { phasePayment = {} } = state.pay;

  if (getters.waitPhaseTwoStart) {
    let { payStartTime, payEndTime } = phasePayment.phaseItems[1];
    payStartTime = new Date(payStartTime);
    payEndTime = new Date(payEndTime);

    const formatTimeFn = (time) => `${formtDate(time, 'yyyy.mm.dd')} ${formatTime(time, 'hh:mm')}`;
    return `尾款时间：${formatTimeFn(payStartTime)} - ${formatTimeFn(payEndTime)}`;
  }

  // #counterText 为自定义占位符, 组件内部替换渲染
  return `请在#{counterText}内支付`;
}

export default function ({ prepare, state, getters }) {
  const moduleGetters = {
    waitPhaseTwoStart: waitPhaseTwoStart({ state }),
    ...getters,
  };

  const args = { prepare, state, getters: moduleGetters };

  return {
    orderStatusIcon: 'public_files/2018/08/31/6eb5418154ef15f9454b0500c800cfcb.png',
    orderStatusTitle: orderStatusTitle(args),
    orderStatusSteps: orderStatusSteps(args),
    orderStatusDesc: orderStatusDesc(args),
    orderStatusCountdownInterval: state?.order?.countdownInterval,
  };
}