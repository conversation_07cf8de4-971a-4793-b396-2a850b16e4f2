import BaseService from '../base/BaseService';

class OrderGuideCouponService extends BaseService {
  get SERVER_NAME() {
    return 'com.youzan.ump.marketing.order.service.OrderPromotionService';
  }

  /**
   * 支付结果页轮询导购收款赠送的优惠券
   * 接口文档  http://zanapi.qima-inc.com/site/service/view/1043608
   */
  async findPromotion(params:any) {
    return await this.invoke(this.SERVER_NAME, 'findPromotion', [params]);
  }

}

export = OrderGuideCouponService;
