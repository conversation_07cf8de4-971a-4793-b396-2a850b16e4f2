const BaseService = require('../base/BaseService');

class StatisticsService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.ebiz.mall.rigel.api.groupbuy.service.common.StatisticsService';
  }

  /**
   * 查询利润明细列表
   * @see {http://zanapi.qima-inc.com/site/service/view/315025}
   * @param {number} headerBuyerId
   * @param {number} kdtId
   * @param {number} page
   * @param {number} pageSize
   */
  async queryProfitList(params) {
    const result = await this.invoke('queryProfitList', [params]);
    return result;
  }
}

module.exports = StatisticsService;
