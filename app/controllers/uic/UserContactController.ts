import BaseController from '../base/BaseController';
import UserContactService from '../../services/uic/UserContactService';
import { Context } from 'astroboy';
import { IUserContactModel } from 'definitions/order/buy/IUserContactModel';

class UserContactController extends BaseController {
  // 获取联系人列表
  async postContactListJson(ctx: Context) {
    if (!this.buyerId) {
      return ctx.json(10000, 'userId 为0');
    }
    const list = await new UserContactService(ctx).getContactList({
      userId: this.buyerId,
    });

    ctx.json(0, '', list);
  }

  // 新增联系人
  async postAddContactJson(ctx: Context) {
    const postData = ctx.getPostData<IUserContactModel>() || {};
    postData.userId = this.buyerId;

    const data = await new UserContactService(ctx).addContact(postData);
    ctx.json(0, '联系人添加成功', data);
  }

  // 更新联系人
  async postUpdateContactJson(ctx: Context) {
    const postData = ctx.getPostData<IUserContactModel>() || {};
    postData.userId = this.buyerId;

    const data = await new UserContactService(ctx).updateContact(postData);
    ctx.json(0, '联系人编辑成功', data);
  }

  // 删除联系人
  async postDeleteContactJson(ctx: Context) {
    const postData = ctx.getPostData<IUserContactModel>() || {};
    postData.userId = this.buyerId;

    const data = await new UserContactService(ctx).deleteContact(postData);
    ctx.json(0, '联系人删除成功', data);
  }
}

export = UserContactController;
