import BaseService from '../base/BaseService';

class TokenService extends BaseService {
  get UIC_TOKEN_SERVICE() {
    return 'com.youzan.channels.channel.core.api.service.ChannelTokenService';
  }

  public async getChannelToken(data: {
    kdtId: number;
    accountType: number;
    businessType: number;
  }): Promise<any> {
    const result = await this.invoke(this.UIC_TOKEN_SERVICE, 'getChannelToken', [data]);
    return result;
  }
}

export = TokenService;
