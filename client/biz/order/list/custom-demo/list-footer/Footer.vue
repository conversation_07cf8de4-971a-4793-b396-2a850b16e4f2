<template>
  <div v-if="actionBtns.length > 0" class="list-item-footer">
    <van-button
      v-for="btn in actionBtns"
      :key="btn.type"
      plain
      size="small"
      class="list-item-footer__btn"
      :type="btn.styleType || 'default'"
      :disabled="btn.disabled"
      @click.prevent.stop="onBtnClick(btn)"
    >
      {{ btn.text }}
    </van-button>

    <van-button type="primary" @click="toastOrderNo">查看单号</van-button>
  </div>
</template>

<script>
import { Button, Toast } from 'vant';

export default {
  name: 'list-item-footer',

  components: {
    [Button.name]: Button,
  },

  props: {
    listIndex: {
      type: Number,
      default: 0,
    },
    listItem: Object,
  },

  computed: {
    actionBtns() {
      return this.listItem.btnList || [];
    },
  },

  methods: {
    toastOrderNo() {
      Toast(this.listItem.orderNo);
    },
    onBtnClick(btn) {
      Toast(`${btn.type} ${btn.text}`);
      this.$emit('footer:btn:click', {
        btn,
        listIndex: this.listIndex,
        listItem: this.listItem,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.list-item-footer {
  padding: 4px 12px 0;
  text-align: right;
  box-sizing: border-box;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;

  &__btn {
    min-width: 80px;
    font-size: 14px;

    &:not(:last-child) {
      margin-right: 12px;
    }
  }
}
</style>
