{% extends '../common/base.html' %}

{% block page_title %} 关注公众号 {% endblock %}

{% block page_style %}
<style>
html, body, div, p {
  padding: 0;
  margin: 0;
}
body {
  background: transparent;
}
#container {
  width: 100%;
  height: 400px;
  box-sizing: border-box;
  line-height: 1;
  border-radius: 20px;
  overflow: hidden;
  text-align: center;
  background: #fff;
}

.logo {
  display: block;
  margin: 44px auto 0;
  width: 56px;
  height: 56px;
  border-radius: 2px;
  border: 1px solid rgba(0,0,0,0.05);
}

.name {
  display: block;
  margin: 0 auto;
  font-size: 16px;
  font-weight: 500;
  color: #111;
  line-height: 1;
  padding: 12px 0;
}

.qrcode {
  display: block;
  margin: 0 auto 12px;
  width: 178px;
  height: 178px;
  border: 1px solid #dcdee0;
}

.no-border {
  border: none;
}

.des {
  width: 294px;
  display: block;
  margin: 0 auto 44px;
  font-size: 14px;
  font-weight: 400;
  color: #333;
  line-height: 20px;
}
</style>
{% endblock %}

{% block page_content %}
<article id="container">
  {% if type === 'focusMpAccount' %}
    <img src="{{ logo }}" alt="{{ name }}" class="logo" />
    <p class="name">{{ name }}</p>
    <img src="{{ qrcode }}" alt="二维码" class="qrcode" />
    <p class="des">长按识别二维码关注店铺公众号，查看订单物</p>
  {% elif type === 'addMiniProgram' %}
    <img src="{{ logo }}" alt="{{ name }}" class="logo" />
    <p class="name">{{ name }}</p>
    <img src="{{ qrcode }}" alt="二维码" class="qrcode no-border" />
    <p class="des">扫码添加店铺小程序，查看订单物流</p>
  {% else %}
    <img src="{{ logo }}" alt="{{ name }}" class="logo" />
    <p class="name">{{ name }}</p>
    <img src="{{ qrcode }}" alt="二维码" class="qrcode no-border" />
    <p class="des">关注店铺查看订单物流</p>
  {% endif %}
</article>
{% endblock %}
 