import type { Context } from 'astroboy';
import BaseController from '../base/BaseController';
import { initRanta } from '@youzan/plugin-h5-ranta-config';
import UserAddressService from '../../services/uic/UserAddressService';

class GiftController extends BaseController {
  async init() {
    await super.init({
      validKdtId: true,
      initMpData: true,
      initMpAccount: true,
      initPlatform: true,
    });
  }

  async getGiftCartHtml(ctx: Context) {
    await this.teeRantaAcl();
    await initRanta(ctx as any, {
      framework: 'tee',
      bizName: '@wsc-tee-h5-trade/gift-cart',
      appName: 'wsc-tee-h5',
    });
    await ctx.render(
      'gift/cart.html',
      { title: '我要送礼' },
      { appName: 'wsc-tee-h5', skipLoadOpsWebConfig: true }
    );
  }

  async getGiftListHtml(ctx: Context) {
    await this.teeRantaAcl();
    await initRanta(ctx as any, {
      framework: 'tee',
      bizName: '@wsc-tee-h5-trade/gift-list',
      appName: 'wsc-tee-h5',
    });
    await ctx.render(
      'gift/gift-list.html',
      { title: '礼物记录' },
      { appName: 'wsc-tee-h5', skipLoadOpsWebConfig: true }
    );
  }

  async getGiftGoodsListHtml(ctx: Context) {
    await this.teeRantaAcl();
    await initRanta(ctx as any, {
      framework: 'tee',
      bizName: '@wsc-tee-h5-trade/gift-goods-list',
      appName: 'wsc-tee-h5',
    });
    await ctx.render(
      'gift/goods-list.html',
      { title: '挑选礼物' },
      { appName: 'wsc-tee-h5', skipLoadOpsWebConfig: true }
    );
  }

  async getOpenGiftHtml(ctx: Context) {
    await this.teeRantaAcl();
    await initRanta(ctx as any, {
      framework: 'tee',
      bizName: '@wsc-tee-h5-trade/gift-open-gift',
      appName: 'wsc-tee-h5',
    });
    await ctx.render(
      'gift/open-gift.html',
      { title: '抢礼物' },
      { appName: 'wsc-tee-h5', skipLoadOpsWebConfig: true }
    );
  }

  async getGiftShareHtml(ctx: Context) {
    await this.teeRantaAcl();
    await initRanta(ctx as any, {
      framework: 'tee',
      bizName: '@wsc-tee-h5-trade/gift-share',
      appName: 'wsc-tee-h5',
    });

    let selectedAddress = null;
    if (ctx.query.address_id) {
      try {
        const addressList = await new UserAddressService(ctx).getAddressListFast({
          userId: this.buyerId,
        });
        selectedAddress = addressList.find((address) => +ctx.query.address_id === address.id);
      } catch (error) {
        console.error(error);
      }
    }
    ctx.setGlobal('selectedAddress', selectedAddress);

    await ctx.render(
      'gift/share.html',
      { title: '我要送礼' },
      { appName: 'wsc-tee-h5', skipLoadOpsWebConfig: true }
    );
  }

  async getGiftUsingTipsHtml(ctx: Context) {
    await initRanta(ctx as any, {
      framework: 'tee',
      bizName: '@wsc-tee-h5-trade/gift-using-tips',
      appName: 'wsc-tee-h5',
    });
    await ctx.render(
      'gift/using-tips.html',
      { title: '使用说明' },
      { appName: 'wsc-tee-h5', skipLoadOpsWebConfig: true }
    );
  }

  async getGiftRulesHtml(ctx: Context) {
    await initRanta(ctx as any, {
      framework: 'tee',
      bizName: '@wsc-tee-h5-trade/gift-rules',
      appName: 'wsc-tee-h5',
    });
    await ctx.render(
      'gift/rules.html',
      { title: '用户规则' },
      { appName: 'wsc-tee-h5', skipLoadOpsWebConfig: true }
    );
  }
}

export = GiftController;
