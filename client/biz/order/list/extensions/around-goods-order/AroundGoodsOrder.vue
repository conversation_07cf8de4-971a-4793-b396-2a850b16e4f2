<template>
  <van-cell
    v-if="showCell"
    title="查看周边好物订单"
    icon="goods-collect-o"
    class="around"
    is-link
    @click="showAroundGoodsOrder"
  />
</template>

<script>
import get from 'utils/get';
import ZNB from '@youzan/znb';
import { Cell } from 'vant';

export default {
  name: 'AroundGoodsOrder',

  props: {
    ctx: Object,
  },

  components: {
    [Cell.name]: Cell,
  },

  data() {
    return {
      activityTab: this.ctx.data.activityTab || {},
    };
  },

  created() {
    this.unwatch = this.ctx.watch('activityTab', (val = {}) => {
      this.activityTab = {
        ...val,
      };
    });
  },

  destroyed() {
    this.unwatch && this.unwatch();
  },

  computed: {
    cpsHasOpened() {
      return get(_global, 'shopConfigs.cps_goods_recommend_opened_flag', '0');
    },

    isTabAll() {
      return this.activityTab.type === 'all';
    },

    showCell() {
      return this.isTabAll && this.cpsHasOpened === '1';
    },
  },
  methods: {
    showAroundGoodsOrder() {
      ZNB.navigate({
        url:
          'https://maijia.youzan.com/v3/trade/record#/list/yzrm?type=all&entry=tang',
      });
    },
  },
};
</script>

<style scoped>
.around {
  margin: 12px 12px 0;
  padding-bottom: 12px;
  border-radius: 8px;
  width: auto;
}
</style>
