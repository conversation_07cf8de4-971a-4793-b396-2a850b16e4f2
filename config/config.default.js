/**
 * 默认配置文件
 */
const path = require('path');
const apolloConfig = require('./apollo-config');

module.exports = {
  view: {
    root: [
      path.join(path.dirname(require.resolve('@youzan/iron-base')), 'app/views'),
      path.resolve(__dirname, '../app/views'),
    ],
    script: {
      crossorigin: true,
    },
  },

  opsWebConfig: ['wsc-tee-h5'],

  logger: {
    appName: 'wsc-h5-trade',
    logIndex: 'wsc_trade_log',
  },

  MARKET: {
    WEAPP: 1407,
  },

  tetherCache: {
    keyPrefix: 'diy',
  },

  apolloConfig,

  CDN_PATH_PREFIX: [
    // api
    '/wsctrade/uic/address/getAllRegion.json',
    '/wsctrade/apollo-gray.json',
    '/wsctrade/get-recommend-config.json',
    '/wsctrade/recommend-config.json',
    '/pay/wsctrade/order/buy/scene/check-compatible',
  ],

  apolloDynamicComponents: {
    appId: 'wsc-h5-trade',
    grayRulesNamespace: 'assets-dynamic-libs.gray-rules',
    componentsNamespace: 'assets-dynamic-libs.dynamic-components',
  },

  // abTest 配置
  abTest: {
    domain: 'h5-trade',
  },

  // 天网插件接入 https://doc.qima-inc.com/pages/viewpage.action?pageId=268814039
  skynetLoggerBizConfig: [
    {
      biz: '下单地址',
      match: /\/wsctrade\/order\/address.*$/,
    },
    {
      biz: '下单',
      match: /(\/pay)?\/wsctrade\/order\/buy.*$/,
    },
    {
      biz: '订单FAQ',
      match: /\/wsctrade\/order\/help.*$/,
    },
    {
      biz: '电子发票',
      match: /\/wsctrade\/order\/invoice.*$/,
    },
    {
      biz: '订单评价',
      match: /\/wsctrade\/order\/evaluate.*$/,
    },
    {
      biz: '订单列表',
      match: /\/wsctrade\/order\/list.*$/,
    },
    {
      biz: '支付结果',
      match: /\/wsctrade\/order\/payresult.*$/,
    },
    {
      biz: 'nina待支付页',
      match: /(\/pay\/wsctrade_ninapay.*$)|(\/wsctrade\/order\/ninapay.*$)/,
    },
    {
      biz: '订单权限手机号验证',
      match: /\/wsctrade\/order\/secure\/verifyphone.*$/,
    },
    {
      biz: '订单分享',
      match: /\/wsctrade\/order\/share.*$/,
    },
    {
      biz: '交易快照',
      match: /\/wsctrade\/order\/snapshot.*$/,
    },
    {
      biz: '电子卡券',
      match: /\/wsctrade\/order\/virtualTicket.*$/,
    },
    {
      biz: '支付',
      match: /\/pay\/wsctrade\/merchantpay.*$/,
    },
    {
      biz: '银行卡支付（银联）',
      match: /\/wsctrade\/pay\/bankcard.*$/,
    },
    {
      biz: '小程序支付',
      match: /\/wsctrade\/pay\/wscweapp\/.*$/,
    },
    {
      biz: '微商城支付',
      match: /\/wsctrade\/pay\/wsc\/.*$/,
    },
    {
      biz: '批量退款',
      match: /\/wsctrade\/batch-refund.*$/,
    },
    {
      biz: '购物车',
      match: /\/wsctrade\/cart.*$/,
    },
    {
      biz: '扫码收款',
      match: /\/wsctrade\/cashier.*$/,
    },
    {
      biz: '周期购物流配送详情',
      match: /\/wsctrade\/express\/periodbuy.*$/,
    },
    {
      biz: '商品推荐',
      match: /\/wsctrade\/recommend.*$/,
    },
    {
      biz: '商品sku',
      match: /\/wsctrade\/fetch-sku.*$/,
    },
    {
      biz: '社区群团购',
      match: /\/wsctrade\/groupbuying.*$/,
    },
    {
      biz: 'kfc',
      match: /\/wsctrade\/kfc.*$/,
    },
    {
      biz: '经纬度获取地址详情',
      match: /\/wsctrade\/location\/getRegionModelByName.*$/,
    },
    {
      biz: '多网点',
      match: /\/wsctrade\/multistore.*$/,
    },
    {
      biz: '新希望',
      match: /\/wsctrade\/order\/newhope.*$/,
    },
    {
      biz: '海报服务',
      match: /\/wsctrade\/poster.*$/,
    },
    {
      biz: '申请售后',
      match: /\/wsctrade\/refund.*$/,
    },
    {
      biz: '自提核销',
      match: /\/wsctrade\/(order|orderlist)\/selffetch.*$/,
    },
    {
      biz: '堂食相关',
      match: /\/pay\/wsctrade_tangshi.*$/,
    },
    {
      biz: '收货地址',
      match: /\/wsctrade\/uic\/address.*$/,
    },
    {
      biz: '联系人',
      match: /\/wsctrade\/uic\/contact.*$/,
    },
    {
      biz: '微信购物单',
      match: /\/wsctrade\/weapp\/shopping-list.*$/,
    },
    {
      biz: '微信订单',
      match: /\/wsctrade\/wechat.*$/,
    },
  ],

  shopConfigKeys: {
    'wsc-h5-trade': [
      'custom_cart_name',
      'cps_goods_recommend_opened_flag',
      'goods_recommend',
      'goods_recommend_cart',
      'goods_recommend_order_detail',
      'goods_recommend_order_detail_add_cart',
      'goods_recommend_order_list',
      'goods_recommend_pay',
      'goods_recommend_delivery',
      'goods_recommend_refund',
      'scrm_credit_diy_name',
      'wxpay_test',
      'weixin_pay_big_sign',
      'weixin_pay_big_unbind',
      'weixin_pay',
      'team_physical',
      'subshop_online_shop_switch_show',
      'chain_online_shop_visit_strategy',
    ],
  },

  monitor: [],

  hummer: {
    appKey: 'wsc-h5-trade',
  },

  ranta: {
    appName: 'wsc-tee-h5',
    baseLibraryUrl: 'https://b.yzcdn.cn/ranta-base-library/app.prod.2.3.1.js',
    configInjectScriptUrl: 'https://b.yzcdn.cn/ranta-config-injector/h5@2.6.8/main.js',
  },
};
