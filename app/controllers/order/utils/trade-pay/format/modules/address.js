import { stringifyAddress } from '../utils/stringify-address';
import { grouponType, isEmptyObject } from '../utils/utils';

/** ------ 待支付自提相关-------- */
// 格式化地址中的 distance 字段
function formatDistance(address) {
  return address.distance < 0
    ? ''
    : `${
        address.distance > 1000
          ? (address.distance / 1000).toFixed(2) + 'km'
          : address.distance + 'm'
      }`;
}

function initSelfFetchAddressDetail({ state }) {
  const { selfFetch } = state.user.delivery;
  if (selfFetch) {
    return `${selfFetch.name} ${stringifyAddress(selfFetch)}`;
  }

  const { shop } = state.selfFetch;
  if (!shop) {
    return '选择提货地址';
  }

  const distance = formatDistance(shop);
  return `${shop.name} ${stringifyAddress(shop)} ${distance ? '|' : ''} ${distance}`;
}

// 自提人
function initSelfFetchContact({ state }) {
  const { selfFetch } = state.user.delivery;
  if (selfFetch) {
    return `${selfFetch.appointmentPerson} ${selfFetch.appointmentTel}`;
  }

  const contact = state.currentContact;
  if (contact?.id) {
    return `${contact.userName} ${contact.telephone}`;
  }
  return '选择提货人';
}

// 自提时间
function initSelfFetchTime({ state }) {
  const { selfFetch } = state.user.delivery;
  if (selfFetch) {
    return selfFetch.appointmentTime;
  }

  const { time, shop } = state.selfFetch;
  if (time) {
    return time;
  }

  return shop ? '请按约定时间提货' : '选择提货时间';
}

/** ------ 待支付自提相关结束 -------- */

function getCurrentAddress({ state }) {
  const { address } = state.user.delivery;
  const { groupon = {} } = state;
  if (address) {
    address.userName = address.recipients;
    return address;
  }

  const receiveByGroupHeader =
    groupon.isGroupon &&
    !groupon.isHeader &&
    (groupon.receiveState === grouponType.forceReceive || groupon.isChecked);

  return receiveByGroupHeader
    ? state.groupon.headerAddress
    : state.address.list?.filter((item) => item.id === state.address.id)[0] || {};
}

// 当前使用的联系人
function getCurrentContact({ state }) {
  let current = {};
  const { contacts } = state.user.delivery;
  if (contacts) {
    current = {
      id: contacts.id,
      userName: contacts.recipients,
      tel: contacts.tel,
    };
  } else {
    const contactList = state.contact.list || [];
    current = contactList.filter((item) => item.id === state.contact.id)[0] || {};
  }

  return {
    required: state.contact.required,
    ...current,
  };
}

export default function (args) {
  const { state } = args;

  const delivery = state.delivery || {};
  const currentAddress = getCurrentAddress(args);

  return {
    // 用户电话
    userTel: (delivery.selfFetch || {}).tel,

    currentAddress,

    hasAddress: !isEmptyObject(currentAddress),

    addressShowSelfFetch: Boolean(
      !state.display.hideSelfFetch && state.display.showAddressTab && state.selfFetch.isAllow
    ),

    addressShowLogistics: Boolean(state.display.showAddressTab && state.display.showExpressTab),

    currentContact: getCurrentContact(args),

    // 全地址
    fullAddress: stringifyAddress(currentAddress),

    // 自提详细地址
    selfFetchAddressDetail: initSelfFetchAddressDetail(args),

    // 自提人
    selfFetchContact: initSelfFetchContact(args),

    selfFetchTime: initSelfFetchTime(args),
  };
}
