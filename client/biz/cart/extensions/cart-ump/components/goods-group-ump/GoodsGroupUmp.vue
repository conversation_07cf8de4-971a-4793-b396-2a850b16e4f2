<template>
  <div class="activity-info">
    <van-checkbox
      class="activity-info__checkbox"
      icon-size="18px"
      :value="isAllActivityGoodsChoose"
      @click="chooseAllActivityGoods"
    />

    <div class="activity-info__text-wrap" @click="clickActivityText">
      <van-tag
        v-for="(tag, index) in activityInfo.activityTags"
        :key="index"
        class="activity-info__tag"
        round
      >
        {{ tag }}
      </van-tag>

      <div ref="activity-info__desc" class="activity-info__desc">
        {{ activityInfo.activityDesc }}
      </div>

      <van-icon
        v-if="canShowPopup"
        class="activity-info__icon-info"
        name="info-o"
        color="#969799"
      />
    </div>

    <div
      v-if="activityBtnTxt"
      class="activity-info__tips"
      @click="toActivityUrl"
    >
      <span class="activity-info__item-text">{{ activityBtnTxt }}</span>
      <van-icon name="arrow" class="activity-info__icon-tips" />
    </div>

    <UmpInfoPopup
      v-if="canShowPopup"
      :activity-info="activityInfo"
      :show-popup="showPopup"
      @close="showPopup = false"
    />

    <!-- 换购modal -->
    <exchange-modal
      v-if="activityId"
      :is-show="showExchangeModal"
      :activity-id="activityId"
      :activity-type="24"
      :kdt-id="kdtId"
      :plus-buy-goods="plusBuyGoods"
      :activity-desc="exchangeModalDesc"
      @exchage:close-pop="handleCloseExchangeModal"
      @add-cart="handleExchangeSucceed"
    />
  </div>
</template>
<script>
import { Checkbox, Tag, Icon, Toast } from 'vant';
import ZNB from '@youzan/znb';
import format from '@youzan/utils/money/format';
import mapKeysToSnakeCase from '@youzan/utils/string/mapKeysToSnakeCase';
import ExchangeModel from './plus-buy-exchange/exchange-goods/ExchangeModal';
import UmpInfoPopup from '../UmpInfoPopup';
import { whereCurrentGoodsInList, isEduIosOnlineGoods } from '@biz/cart/utils';
import {
  IS_OPEN_NEW,
  ACTIVITY_TYPE_ALIAS_MAP,
  ACTIVITY_TYPE_ALIAS_NUMBER,
  BUYER_ID,
} from '@biz/cart/constant';
import get from 'utils/get';
import { Logger } from 'common/log/logger';

const {
  PLUS_BUY,
  MEET_REDUCE,
  SECOND_HALF,
  PACKAGE_BUY,
} = ACTIVITY_TYPE_ALIAS_NUMBER;

const ACTIVITY_URL = {
  [PLUS_BUY]: '/packages/ump/plus-buy/index?',
  [MEET_REDUCE]: '/packages/ump/meet-reduce-goods/index?',
  [SECOND_HALF]: '/packages/ump/second-half-discount/index?',
  [PACKAGE_BUY]: '/packages/ump/bale/index?',
};

export default {
  name: 'goods-group-ump',

  components: {
    [Tag.name]: Tag,
    [Icon.name]: Icon,
    [Checkbox.name]: Checkbox,
    UmpInfoPopup,
    [ExchangeModel.name]: ExchangeModel,
  },

  props: {
    kdtId: {
      type: Number,
      required: true,
    },
    isEditing: {
      type: Boolean,
      default: false,
    },
    activityInfo: {
      type: Object,
      default: () => ({}),
    },
    goodsList: {
      type: Array,
      default: () => [],
    },
    checkedGoodsList: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      showPopup: false,
      isDescOver: false,
      showExchangeModal: false,
      plusBuyGoods: [],
      activityBtnTxt: '',
      exchangeModalDesc: '',
    };
  },

  computed: {
    isAllActivityGoodsChoose() {
      let filterGoodsList = this.goodsList;
      if (!this.isEditing) {
        filterGoodsList = this.goodsList.filter((goods) => !isEduIosOnlineGoods(goods));
        if (!filterGoodsList.length) {
          return false;
        }
      }
      return filterGoodsList.every((good) => whereCurrentGoodsInList(this.checkedGoodsList, good) !== -1);
    },

    canShowPopup() {
      return this.isDescOver && this.activityInfo.activityDuration;
    },

    activityTypeAlias() {
      return ACTIVITY_TYPE_ALIAS_MAP[this.activityInfo.activityType];
    },

    activityId() {
      return this.activityInfo.activityId;
    },
  },

  watch: {
    activityInfo: {
      handler() {
        this.isDescOver = false;
        
        if (
          ['meetReduce', 'meetSend', 'packageBuy', 'secondHalfDiscount'].includes(
            this.activityTypeAlias
          )
        ) {
          this.setCommonActivityInfo();
        } else if (['plusBuy'].includes(this.activityTypeAlias)) {
          this.setPlusBuyActivityInfo();
        } else if (['multiCourseApply'].includes(this.activityTypeAlias)) {
          this.setMultiCourseApplyActivityInfo();
        }

        this.$nextTick(() => {
          const descDom = this.$refs['activity-info__desc'];
          if (descDom && descDom.clientWidth < descDom.scrollWidth) {
            this.isDescOver = true;
          }
        });

        Logger.log({
            et: 'view', // 事件类型
            ei: 'cart_activity_view', // 事件标识
            en: '购物车优惠活动曝光', // 事件名称
            params: {
              activity_type: this.activityTypeAlias,
              activity_id: this.activityId,
              kdt_id: this.kdtId,
              buyer_id: BUYER_ID,
            }, // 事件参数
          });
      },
      immediate: true,
    },
  },

  methods: {
    // 设置普通活动
    setCommonActivityInfo() {
      if (this.activityInfo.meet) {
        this.activityBtnTxt = '再逛逛';
      } else if (this.activityInfo.activityUrl) {
        this.activityBtnTxt = '去凑单';
      }
    },

    // 设置加价购活动
    setPlusBuyActivityInfo() {
      // 获取换购商品
      const plusBuyGoods = this.goodsList.filter((item) => item.activityId);
      this.plusBuyGoods = plusBuyGoods.map((item) => {
        item = mapKeysToSnakeCase(item);
        item.skuData = this.formatSku(item.sku);
        return item;
      });

      this.exchangeModalDesc = '';
      const { conditionPrice } = this.activityInfo;
      if (!this.activityInfo.meet) {
        this.activityBtnTxt = '去凑单';
      } else if (!this.plusBuyGoods.length) {
        this.exchangeModalDesc = `已购满${this.moneyToYuan(
          conditionPrice
        )}元，`;
        this.activityBtnTxt = '去换购';
      } else {
        this.exchangeModalDesc = `已购满${this.moneyToYuan(
          conditionPrice
        )}元，`;
        this.activityBtnTxt = '重新换购';
      }
    },

    setMultiCourseApplyActivityInfo() {
      this.activityBtnTxt = '去选报';
    },

    handleCloseExchangeModal() {
      this.showExchangeModal = false;
    },

    // 换购商品成功之后，需要刷新购物车
    handleExchangeSucceed() {
      this.$emit('refresh-cart-goods-list');
    },

    moneyToYuan(cent) {
      return +format(cent, true, false);
    },

    clickActivityText() {
      if (!this.canShowPopup) return;
      this.showPopup = true;
    },

    toActivityUrl() {
      const isAlipayApp = get(window, '_global.miniprogram.isAlipayApp', false);
      const isQQApp = get(window, '_global.miniprogram.isQQApp', false);
      // 埋点
      Logger.log({
        et: 'click', // 事件类型
        ei: 'cart_activity_click', // 事件标识
        en: '购物车页活动点击', // 事件名称
        params: {
          activity_type: this.activityTypeAlias,
          activity_id: this.activityId,
          kdt_id: this.kdtId,
          buyer_id: BUYER_ID,
        }, // 事件参数
      });

      if (
        ['plusBuy'].includes(this.activityTypeAlias) &&
        this.activityBtnTxt !== '去凑单'
      ) {
        this.showExchangeModal = true;
        return;
      }

      let activityUrl = this.activityInfo.activityUrl;
      const { activityType, activityId, activityAlias } = this.activityInfo;

      if (['plusBuy'].includes(this.activityTypeAlias)) {
        // 加价购前端拼接url
        activityUrl = `/wscump/plusbuy/detail?activityId=${activityId}&kdt_id=${this.kdtId}&alias=${activityAlias}`;
      }

      if (['multiCourseApply'].includes(this.activityTypeAlias)) {
        // 多科连报前端拼接url
        activityUrl = `/wscvis/ump/liangfan/${activityAlias}?kdt_id=${this.kdtId}`;
      }

      if (
        (isAlipayApp || isQQApp) &&
        [MEET_REDUCE, PLUS_BUY, SECOND_HALF].includes(activityType)
      ) {
        activityUrl =
          ACTIVITY_URL[activityType] +
          `alias=${activityAlias}&kdtId=${this.kdtId}`;

        if (PLUS_BUY === activityType) {
          activityUrl += `&activityId=${activityId}`;
        }
        ZNB.navigate({
          aliappUrl: activityUrl,
          qqUrl: activityUrl,
        });
        return;
      }

      if (isQQApp && [PACKAGE_BUY].includes(activityType)) {
        activityUrl =
          ACTIVITY_URL[activityType] +
          `alias=${activityAlias}&kdtId=${this.kdtId}`;

        ZNB.navigate({
          qqUrl: activityUrl,
        });
        return;
      }

      const options = {
        url: activityUrl,
      };
      // 如果页面 url 上有 __openNew__ 参数，说明需要用新页面打开链接
      options.fallback = IS_OPEN_NEW ? 'open' : 'redirect';
      ZNB.navigate(options);
    },

    chooseAllActivityGoods() {
      const type = this.isAllActivityGoodsChoose ? 'remove' : 'add';

      let filterGoodsList = this.goodsList;
      if (!this.isEditing) {
        filterGoodsList = this.goodsList.filter((goods) => !isEduIosOnlineGoods(goods));
        if (!filterGoodsList.length) {
          Toast('购物车里面尚未选中商品，请重新返回活动页/商品页加购');
          return;
        }
      }

      this.$emit('change-item-checked', {
        rangeType: 'batch',
        type,
        goodsList: filterGoodsList,
      });
    },

    formatSku(sku) {
      let parsedSku = [];
      try {
        parsedSku = JSON.parse(sku || '[]') || [];
      } catch (e) {
        parsedSku = [];
      }

      return parsedSku;
    },
  },
};
</script>
<style scoped lang="scss">
.activity-info {
  height: 42px;
  box-sizing: border-box;
  padding: 11px 10px 11px 0;
  font-size: 12px;
  line-height: 16px;
  display: flex;
  overflow: hidden;
  position: relative;

  &__tag {
    margin-right: 4px;
    font-size: 12px;
    line-height: 16px;
    padding: 0 4px;
    color: var(--ump-tag-text, #323233);
    background: var(--ump-tag-bg, #f2f2ff);
  }

  &__checkbox {
    width: 44px;
    justify-content: center;
  }

  &__icon-info,
  &__icon-tips {
    flex-shrink: 0;
  }

  &__desc {
    color: #323233;
    flex: 1;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-break: keep-all;
    font-size: 12px;
  }

  &__text-wrap {
    flex: 1;
    overflow: hidden;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding-right: 12px;
  }

  &__tips {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    color: var(--ump-icon, #323233);
  }

  &__item-text {
    vertical-align: top;
    font-size: 12px;
  }
}
</style>
