import { SecurityLevel } from '@youzan/cloud-guard';

const origin = 'https://b.yzcdn.cn/wsc-h5-trade';

export default (config) => {
  // 增加自定义extension
  config.extensions.unshift({
    extensionId: 'list-footer',
    name: 'list-footer',
    version: '1.0.0',
    extensionPathInBundle: 'list-footer-block',
    bundle: origin + '/order/list-v2/custom-demo/list-footer.js',
    hasWidget: false,
    sandbox: {
      id: 'list-footer',
      level: SecurityLevel.Unsafe,
    },
    component: {
      provide: ['ListItemFooter'],
    },
  })

  // 定义module
  config.modules.unshift({
    moduleId: 'list-footer-block@random-string',
    extensionId: 'list-footer',
  })

  // 改写list-block
  const listBlockIndex = config.modules.findIndex(module => module.moduleId === 'list-block@random-string')

  config.modules[listBlockIndex].bindings.component['list-item-footer'] = {
    extensionId: 'list-footer',
    name: 'ListItemFooter',
  }

  // app 增加 footer module
  config.app.pages[0].modules.push('list-footer-block@random-string')

  return config
}