const BaseController = require('../base/BaseController');
const SyncHistoryOrderService = require('../../services/order/SyncHistoryOrderService');

class ShoppingListController extends BaseController {
  async getIndexHtml(ctx) {
    await ctx.render('weapp/shopping-list.html');
  }

  async getQueryRecordJson(ctx) {
    const { fansType } = this.buyer || {};

    const params = {
      buyerId: this.buyerId,
      fansType,
      kdtId: ctx.kdtId,
    };

    const data = await new SyncHistoryOrderService(ctx).queryRecord(params);

    ctx.json(0, 'ok', data);
  }

  async postSyncJson(ctx) {
    const { fansType } = this.buyer || {};

    const params = {
      buyerId: this.buyerId,
      fansType,
      kdtId: ctx.kdtId,
    };

    const data = await new SyncHistoryOrderService(ctx).sync(params);

    ctx.json(0, 'ok', data);
  }
}

module.exports = ShoppingListController;
