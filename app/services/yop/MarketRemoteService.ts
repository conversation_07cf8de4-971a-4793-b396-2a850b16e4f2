import BaseService from '../base/BaseService';

class MarketRemoteService extends BaseService {
  get MARKET_REMOTE_SERVICE() {
    return 'com.youzan.yop.api.MarketRemoteService';
  }

  // 根据 kdtId 判断应用是否过期，只有小程序需要
  async findApplicationStatus(kdtId: number) {
    const CONFIG_MARKET = this.getConfig('MARKET');
    const result = await this.invoke(this.MARKET_REMOTE_SERVICE, 'findApplicationStatus', [kdtId, CONFIG_MARKET.WEAPP]);
    return result;
  }

  /**
   * 小程序是否购买或者点击立即使用
   * http://zanapi.qima-inc.com/site/service/view/204422
   */
  isWeappAuthTeam(params: any) {
    return this.invoke(this.MARKET_REMOTE_SERVICE, 'isWeappAuthTeam', [params]);
  }
}

export = MarketRemoteService;
