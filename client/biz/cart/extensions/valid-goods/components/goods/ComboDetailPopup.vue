<template>
  <van-popup v-model="showPopup" get-container="body" position="bottom" round :style="{ height: '487px' }">
    <div class="popup-content">
      <div class="popup-title">
        <span>套餐详情</span>
        <div class="close-icon" @click="closePopup">
          <van-icon name="cross" size="18" />
        </div>
      </div>
      <div class="popup-body">
        <div class="wrapper" ref="wrapper">
          <div v-for="group in comboDetail" :key="group.id">
            <div class="item" v-for="(item, index) in group.subComboList" :key="index">
              <img class="img" :src="item.thumbUrl" alt="" />
              <div class="right">
                <div class="title">
                  {{ item.title }}
                </div>
                <!-- sku信息 -->
                <div class="sku">
                  <span v-for="(skuItem, i) in item.skuDescArr" :key="'skuItem' + i">
                    <span>{{ skuItem.sn }}</span>
                    <!-- <span class="theme-color" v-if="skuItem.sp"> -->
                    <span class="theme-color">
                      {{ skuItem.sp }}
                    </span>
                    <span v-if="item.propDescArr.length || i !== item.skuDescArr.length - 1"> ; </span>
                  </span>
                  <span v-for="(propItem, i) in item.propDescArr" :key="'propItem' + i">
                    <span>{{ propItem.pn }}</span>
                    <!-- <span class="theme-color" v-if="propItem.pp"> -->
                    <span class="theme-color">
                      {{ propItem.pp }}
                    </span>
                    <span v-if="i !== item.propDescArr.length - 1"> ; </span>
                  </span>
                </div>
                <div class="bottom">
                  <Price class="cart-goods__price theme-color" :price="item.price" />
                  <span class="number">x{{ item.num }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </van-popup>
</template>

<script>
import { Icon, Popup } from 'vant';
import Price from '@biz/cart/components/Price';

export default {
  name: 'combo-detail-popup',
  components: {
    Price,
    [Icon.name]: Icon,
    [Popup.name]: Popup,
  },

  data() {
    return {
      showPopup: false,
      comboDetail: [],
    };
  },

  methods: {
    openPopup(data) {
      if (data?.groupList?.length) {
        this.comboDetail = data.groupList;
        this.showPopup = true;
        // 下滑过的，要滑回来
        setTimeout(() => {
          this.$refs.wrapper.scrollTop = 0;
        }, 0);
      }
    },
    closePopup() {
      this.showPopup = false;
    },
  },
};
</script>

<style scoped lang="scss">
::-webkit-scrollbar {
  display: none;
}

.popup-content {
  background-color: #f7f8fa;
  height: 100%;
  display: flex;
  flex-direction: column;

  .popup-title {
    display: flex;
    width: 100%;
    height: 44px;
    justify-content: center;
    align-items: center;
    font-size: 16px;
    color: #323233;
    background-color: #fff;
    position: relative;
    .close-icon {
      position: absolute;
      top: 13px;
      right: 12px;
    }
  }
  .popup-body {
    padding: 12px;
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;
    .wrapper {
      box-sizing: border-box;
      height: 100%;
      overflow-y: scroll;
      padding-bottom: constant(safe-area-inset-bottom);
      padding-bottom: env(safe-area-inset-bottom);
      .item {
        width: 100%;
        height: 121px;
        display: flex;
        flex-direction: row;
        padding: 12px;
        box-sizing: border-box;
        background-color: #fff;
        border-radius: 8px;
        margin-bottom: 12px;
        &-last-of-type {
          margin-bottom: 0;
        }

        .img {
          width: 96px;
          height: 96px;
          border-radius: 8px;
          object-fit: cover;
        }
        .right {
          margin-left: 8px;
          flex: 1;
          display: flex;
          flex-direction: column;
          // justify-content: space-between;
          overflow: hidden;
          position: relative;
          .title {
            font-size: 14px;
            line-height: 20px;
            color: #323233;
            // height: 40px;
            display: -webkit-box;
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }
          .sku {
            margin-top: 8px;
            font-size: 12px;
            color: #969799;
            height: 18px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            width: 100%;
          }
          .bottom {
            position: absolute;
            bottom: 0px;
            left: 0px;
            display: flex;
            width: 100%;
            flex-direction: row;
            justify-content: space-between;
            align-items: baseline;
            .cart-goods__price {
              padding-top: 4px;
            }
            .number {
              font-size: 12px;
              color: #969799;
            }
          }
        }
      }
    }
  }
}
</style>