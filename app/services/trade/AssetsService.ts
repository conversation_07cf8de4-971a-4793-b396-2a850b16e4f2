import BaseService from '../base/BaseService';
import { ExchangeCouponParams } from 'definitions/trade/Assets';

class OutAssetService extends BaseService {
  get ASSETS_SERVICE() {
    return 'com.youzan.ebiz.mall.trade.buyer.api.service.AssetsService';
  }

  /**
   * 兑换优惠券
   * zan-api: http://zanapi.qima-inc.com/site/service/view/120347
   */
  async exchangeCoupon(params: ExchangeCouponParams) {
    return this.invoke(this.ASSETS_SERVICE, 'exchangeCoupon', [params]);
  }
}

export = OutAssetService;
