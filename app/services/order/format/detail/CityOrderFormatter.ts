import { IAddressInfoDto } from 'definitions/order/detail/AddressInfo';
import { IOrderInfoDto } from 'definitions/order/detail/OrderInfo';

/**
 * 同城订单处理器
 */
class CityOrderFormatter {
  public static format(
    addressInfoDto: IAddressInfoDto,
    orderInfoDto: IOrderInfoDto,
    orderBizExtra: any,
    addressInfoDo: any,
    mainOrderInfo: any
  ): any {
    // 同城配送范围
    if (addressInfoDo.areaList && addressInfoDo.areaList.length > 0) {
      const areaList0 = addressInfoDo.areaList[0].isAccording && addressInfoDo.areaList[0];
      addressInfoDto.localDeliveryScope = areaList0 || {};

      // 显示查看同城配送范围
      orderInfoDto.showCheckDeliveryScope = !!(mainOrderInfo.expressType === 2 && areaList0);
    }

    // 定时达 - 送达时间
    const deliveryTimeDisplay = mainOrderInfo.deliveryTimeDisplay || '';
    
    // 是否显示送达时间
    orderInfoDto.showDeliveryTime = !!deliveryTimeDisplay;
    addressInfoDto.deliveryTimeDisplay = deliveryTimeDisplay;
  }
}

export default CityOrderFormatter;
