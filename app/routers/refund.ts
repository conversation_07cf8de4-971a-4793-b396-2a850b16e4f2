export = [
  [
    'GET',
    '/wsctrade/refund/index',
    'refund.RefundController',
    ['checkRedirectToShopDomain', 'getIndexHtml'],
  ],
  [
    'GET',
    '/wsctrade/refund/list',
    'refund.RefundController',
    ['checkRedirectToShopDomain', 'getListHtml'],
  ],
  [
    'GET',
    '/wsctrade/refund/not-support',
    'refund.RefundController',
    ['checkRedirectToShopDomain', 'getNotSupportTypeHtml'],
  ],
  [
    'GET',
    '/wsctrade/refund/getRefundState.json',
    'refund.RefundController',
    'getRefundStateJson',
  ],
  [
    'GET',
    '/wsctrade/refund/getBuyerGetRefundInfo.json',
    'refund.RefundController',
    'getBuyerGetRefundInfoJson',
  ],
  [
    'GET',
    '/wsctrade/refund/getBuyerRefundConsultMessage.json',
    'refund.RefundController',
    'getBuyerRefundConsultMessageJson',
  ],
  [
    'POST',
    '/wsctrade/refund/postBuyerCloseRefund.json',
    'refund.RefundController',
    'postBuyerCloseRefundJson',
  ],
  [
    'POST',
    '/wsctrade/refund/postBuyerExchangeClose.json',
    'refund.RefundController',
    'postBuyerExchangeCloseJson',
  ],
  [
    'GET',
    '/wsctrade/refund/getBuyerGetExpress.json',
    'refund.RefundController',
    'getBuyerGetExpressJson',
  ],
  [
    'GET',
    '/wsctrade/refund/getBuyerRefundInit.json',
    'refund.RefundController',
    'getBuyerRefundInitJson',
  ],
  [
    'POST',
    '/wsctrade/refund/postBuyerAddMessage.json',
    'refund.RefundController',
    'postBuyerAddMessageJson',
  ],
  [
    'POST',
    '/wsctrade/refund/postBuyerInvolve.json',
    'refund.RefundController',
    'postBuyerInvolveJson',
  ],
  [
    'POST',
    '/wsctrade/refund/postBuyerReturnGoods.json',
    'refund.RefundController',
    'postBuyerReturnGoodsJson',
  ],
  [
    'POST',
    '/wsctrade/refund/postBuyerRefundUpdate.json',
    'refund.RefundController',
    'postBuyerRefundUpdateJson',
  ],
  [
    'POST',
    '/wsctrade/refund/postBuyerExchangeUpdate.json',
    'refund.RefundController',
    'postBuyerExchangeUpdateJson',
  ],
  [
    'POST',
    '/wsctrade/refund/postBuyerExchangeReceive.json',
    'refund.RefundController',
    'postBuyerExchangeReceive',
  ],
  [
    'POST',
    '/wsctrade/refund/postBuyerRefundCreate.json',
    'refund.RefundController',
    'postBuyerRefundCreateJson',
  ],
  [
    'POST',
    '/wsctrade/refund/postBuyerExchangeCreate.json',
    'refund.RefundController',
    'postBuyerExchangeCreateJson',
  ],
  ['GET', '/wsctrade/refund/redirect', 'refund.RefundController', 'redirect'],
  [
    'GET',
    '/wsctrade/refund/list.json',
    'refund.RefundController',
    'getRefundListJson',
  ],
  [
    'POST',
    '/wsctrade/refund/create-evaluation.json',
    'refund.RefundController',
    'createEvaluationJson',
  ],
  [
    'GET',
    '/wsctrade/refund/get-evaluation.json',
    'refund.RefundController',
    'getEvaluationByRefundId',
  ],
  [
    'POST',
    '/wsctrade/refund/add-refund-tag.json',
    'refund.RefundController',
    'addRefundTag',
  ],
  [
    'POST',
    '/wsctrade/refund/advance-fund/order',
    'refund.FastRefundController',
    'payAdvanceFund',
  ],
];
