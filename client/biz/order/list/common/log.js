import { Logger } from 'common/log/logger';

// 取消订单
export const LOG_CANCEL_ORDER = () => {
  // 埋点
  Logger.log({
    et: 'click',
    ei: 'cancel_order',
    en: '取消订单',
  });
};

// 延长收货
export const LOG_DELAY_GOODS = () => {
  // 埋点
  Logger.log({
    et: 'click',
    ei: 'delay_goods',
    en: '延长收货',
  });
};

// 确认收货
export const LOG_CONFIRM_GOODS = (orderNo) => {
  // 埋点
  Logger.log({
    et: 'click',
    ei: 'confirm_goods',
    en: '确认收货',
    params: {
      order_no: orderNo,
    },
  });
};

// 再来一单
export const LOG_BUY_AGAIN = ({ orderNo }) => {
  // 埋点
  Logger.log({
    et: 'click',
    ei: 'buy_again',
    en: '再来一单',
    params: {
      last_order_no: orderNo,
    },
  });
};

// 提醒发货
export const LOG_EXPRESS_REMINDER = () => {
  // 埋点
  Logger.log({
    et: 'click',
    ei: 'express_reminder',
    en: '提醒发货',
  });
};
// 删除订单
export const LOG_ORDER_DELETE = ({ kdt_id, order_no, buyer_id }) => {
  // 埋点
  Logger.log({
    et: 'click',
    ei: 'order_delete_click',
    en: '删除订单按钮点击',
    params: {
      kdt_id: kdt_id,
      order_no: order_no,
      buyer_id: buyer_id,
    },
  });
};

// 确认入住成功
// 后端返回成功，不是点击按钮就打点
// 产品要求 @刘文静
export const LOG_CONFIRM_HOTEL_GOODS = () => {
  // 埋点
  Logger.log({
    et: 'click', // 事件类型
    ei: 'click_checkin', // 事件标识
    en: '点击入住', // 事件名称
  });
};

// 分享埋点
export const LOG_ORDER_SHARE_DETAIL = ({ ei, en }) => {
  // 埋点
  Logger.log({
    et: 'click',
    ei,
    en,
  });
};

// 再来一单，直接进下单页
export const LOG_DIRECT_BUY_AGAIN = (params) => {
  // 埋点
  Logger.log({
    et: 'click',
    ei: 'new_buy_again_click',
    en: '新再来一单点击',
    pt: 'ol',
    params,
  });
};

// 新再来一单曝光
export const LOG_NEW_BUY_AGAIN_VIEW = (params) => {
  // 埋点
  Logger.log({
    et: 'view',
    ei: 'new_buy_again_view',
    en: '新再来一单曝光',
    pt: 'ol',
    params,
  });
};
