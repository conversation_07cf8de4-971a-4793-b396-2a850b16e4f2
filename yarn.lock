# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.10.4", "@babel/code-frame@^7.12.11":
  version "7.12.11"
  resolved "http://registry.npm.qima-inc.com/@babel/code-frame/download/@babel/code-frame-7.12.11.tgz"
  integrity sha1-9K1DWqJj25NbjxDyxVLSP7cWpj8=
  dependencies:
    "@babel/highlight" "^7.10.4"

"@babel/core@^7.1.0", "@babel/core@^7.7.5":
  version "7.12.10"
  resolved "http://registry.npm.qima-inc.com/@babel/core/download/@babel/core-7.12.10.tgz"
  integrity sha1-t5ouG59w7T2Eu/ttjE74JfYGvM0=
  dependencies:
    "@babel/code-frame" "^7.10.4"
    "@babel/generator" "^7.12.10"
    "@babel/helper-module-transforms" "^7.12.1"
    "@babel/helpers" "^7.12.5"
    "@babel/parser" "^7.12.10"
    "@babel/template" "^7.12.7"
    "@babel/traverse" "^7.12.10"
    "@babel/types" "^7.12.10"
    convert-source-map "^1.7.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.1"
    json5 "^2.1.2"
    lodash "^4.17.19"
    semver "^5.4.1"
    source-map "^0.5.0"

"@babel/generator@^7.12.10", "@babel/generator@^7.12.11":
  version "7.12.11"
  resolved "http://registry.npm.qima-inc.com/@babel/generator/download/@babel/generator-7.12.11.tgz"
  integrity sha1-mKffe4w1jJo3qweiQFaFMBaro68=
  dependencies:
    "@babel/types" "^7.12.11"
    jsesc "^2.5.1"
    source-map "^0.5.0"

"@babel/helper-function-name@^7.12.11":
  version "7.12.11"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-function-name/download/@babel/helper-function-name-7.12.11.tgz"
  integrity sha1-H9dziu5dz1PD7P8k8dqcUR7Ee0I=
  dependencies:
    "@babel/helper-get-function-arity" "^7.12.10"
    "@babel/template" "^7.12.7"
    "@babel/types" "^7.12.11"

"@babel/helper-get-function-arity@^7.12.10":
  version "7.12.10"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-get-function-arity/download/@babel/helper-get-function-arity-7.12.10.tgz"
  integrity sha1-sViBejFltfqiBHgl36YZcN3MFs8=
  dependencies:
    "@babel/types" "^7.12.10"

"@babel/helper-member-expression-to-functions@^7.12.7":
  version "7.12.7"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-member-expression-to-functions/download/@babel/helper-member-expression-to-functions-7.12.7.tgz"
  integrity sha1-qne9A5bsgRTl4weH76eFmdh0qFU=
  dependencies:
    "@babel/types" "^7.12.7"

"@babel/helper-module-imports@^7.12.1":
  version "7.12.5"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-module-imports/download/@babel/helper-module-imports-7.12.5.tgz"
  integrity sha1-G/wCKfeUmI927QpNTpCGCFC1Tfs=
  dependencies:
    "@babel/types" "^7.12.5"

"@babel/helper-module-transforms@^7.12.1":
  version "7.12.1"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.12.1.tgz"
  integrity sha1-eVT+xx9bMsSOSzA7Q3w0RT/XJHw=
  dependencies:
    "@babel/helper-module-imports" "^7.12.1"
    "@babel/helper-replace-supers" "^7.12.1"
    "@babel/helper-simple-access" "^7.12.1"
    "@babel/helper-split-export-declaration" "^7.11.0"
    "@babel/helper-validator-identifier" "^7.10.4"
    "@babel/template" "^7.10.4"
    "@babel/traverse" "^7.12.1"
    "@babel/types" "^7.12.1"
    lodash "^4.17.19"

"@babel/helper-optimise-call-expression@^7.12.10":
  version "7.12.10"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-optimise-call-expression/download/@babel/helper-optimise-call-expression-7.12.10.tgz"
  integrity sha1-lMpOMG7hGn3W6fQoI+Ksa0mIHi0=
  dependencies:
    "@babel/types" "^7.12.10"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.8.0":
  version "7.10.4"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.10.4.tgz"
  integrity sha1-L3WoMSadT2d95JmG3/WZJ1M883U=

"@babel/helper-replace-supers@^7.12.1":
  version "7.12.11"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-replace-supers/download/@babel/helper-replace-supers-7.12.11.tgz"
  integrity sha1-6lEWWPxmx5CPkjEG3YjgjRmX1g0=
  dependencies:
    "@babel/helper-member-expression-to-functions" "^7.12.7"
    "@babel/helper-optimise-call-expression" "^7.12.10"
    "@babel/traverse" "^7.12.10"
    "@babel/types" "^7.12.11"

"@babel/helper-simple-access@^7.12.1":
  version "7.12.1"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-simple-access/download/@babel/helper-simple-access-7.12.1.tgz"
  integrity sha1-MkJ+WqYVR9OOsebq9f0UJv2tkTY=
  dependencies:
    "@babel/types" "^7.12.1"

"@babel/helper-split-export-declaration@^7.11.0", "@babel/helper-split-export-declaration@^7.12.11":
  version "7.12.11"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-split-export-declaration/download/@babel/helper-split-export-declaration-7.12.11.tgz"
  integrity sha1-G0zEJEWGQ8R9NwIiI9oz126kYDo=
  dependencies:
    "@babel/types" "^7.12.11"

"@babel/helper-string-parser@^7.25.9":
  version "7.25.9"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-string-parser/download/@babel/helper-string-parser-7.25.9.tgz#1aabb72ee72ed35789b4bbcad3ca2862ce614e8c"
  integrity sha1-Gqu3Lucu01eJtLvK08ooYs5hTow=

"@babel/helper-validator-identifier@^7.10.4", "@babel/helper-validator-identifier@^7.12.11":
  version "7.12.11"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.12.11.tgz"
  integrity sha1-yaHwIZF9y1zPDU5FPjmQIpgfye0=

"@babel/helper-validator-identifier@^7.25.9":
  version "7.25.9"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.25.9.tgz#24b64e2c3ec7cd3b3c547729b8d16871f22cbdc7"
  integrity sha1-JLZOLD7HzTs8VHcpuNFocfIsvcc=

"@babel/helpers@^7.12.5":
  version "7.12.5"
  resolved "http://registry.npm.qima-inc.com/@babel/helpers/download/@babel/helpers-7.12.5.tgz"
  integrity sha1-Ghukp2jZtYMQ7aUWxEmRP+ZHEW4=
  dependencies:
    "@babel/template" "^7.10.4"
    "@babel/traverse" "^7.12.5"
    "@babel/types" "^7.12.5"

"@babel/highlight@^7.10.4":
  version "7.10.4"
  resolved "http://registry.npm.qima-inc.com/@babel/highlight/download/@babel/highlight-7.10.4.tgz"
  integrity sha1-fRvf1ldTU4+r5sOFls23bZrGAUM=
  dependencies:
    "@babel/helper-validator-identifier" "^7.10.4"
    chalk "^2.0.0"
    js-tokens "^4.0.0"

"@babel/parser@^7.1.0", "@babel/parser@^7.12.10", "@babel/parser@^7.12.11", "@babel/parser@^7.12.7":
  version "7.12.11"
  resolved "http://registry.npm.qima-inc.com/@babel/parser/download/@babel/parser-7.12.11.tgz"
  integrity sha1-nONZW810vFxGaQXobFNbiyUBHnk=

"@babel/parser@^7.6.0", "@babel/parser@^7.9.6":
  version "7.26.1"
  resolved "http://registry.npm.qima-inc.com/@babel/parser/download/@babel/parser-7.26.1.tgz#44e02499960df2cdce2c456372a3e8e0c3c5c975"
  integrity sha1-ROAkmZYN8s3OLEVjcqPo4MPFyXU=
  dependencies:
    "@babel/types" "^7.26.0"

"@babel/plugin-syntax-async-generators@^7.8.4":
  version "7.8.4"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-syntax-async-generators/download/@babel/plugin-syntax-async-generators-7.8.4.tgz"
  integrity sha1-qYP7Gusuw/btBCohD2QOkOeG/g0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-bigint@^7.8.3":
  version "7.8.3"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-syntax-bigint/download/@babel/plugin-syntax-bigint-7.8.3.tgz"
  integrity sha1-TJpvZp9dDN8bkKFnHpoUa+UwDOo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.8.3":
  version "7.12.1"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-syntax-class-properties/download/@babel/plugin-syntax-class-properties-7.12.1.tgz"
  integrity sha1-vLKXxTZueb663vUJVJzZOwTxmXg=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-import-meta@^7.8.3":
  version "7.10.4"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-syntax-import-meta/download/@babel/plugin-syntax-import-meta-7.10.4.tgz"
  integrity sha1-7mATSMNw+jNNIge+FYd3SWUh/VE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-json-strings@^7.8.3":
  version "7.8.3"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-syntax-json-strings/download/@babel/plugin-syntax-json-strings-7.8.3.tgz"
  integrity sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-logical-assignment-operators@^7.8.3":
  version "7.10.4"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-syntax-logical-assignment-operators/download/@babel/plugin-syntax-logical-assignment-operators-7.10.4.tgz"
  integrity sha1-ypHvRjA1MESLkGZSusLp/plB9pk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  version "7.8.3"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-syntax-nullish-coalescing-operator/download/@babel/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz"
  integrity sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.8.3":
  version "7.10.4"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-syntax-numeric-separator/download/@babel/plugin-syntax-numeric-separator-7.10.4.tgz"
  integrity sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.8.3":
  version "7.8.3"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-syntax-object-rest-spread/download/@babel/plugin-syntax-object-rest-spread-7.8.3.tgz"
  integrity sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  version "7.8.3"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-syntax-optional-catch-binding/download/@babel/plugin-syntax-optional-catch-binding-7.8.3.tgz"
  integrity sha1-YRGiZbz7Ag6579D9/X0mQCue1sE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.3":
  version "7.8.3"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-syntax-optional-chaining/download/@babel/plugin-syntax-optional-chaining-7.8.3.tgz"
  integrity sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-top-level-await@^7.8.3":
  version "7.12.1"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-syntax-top-level-await/download/@babel/plugin-syntax-top-level-await-7.12.1.tgz"
  integrity sha1-3WwLNXrBuxQtmFN0UKMZYl0T0qA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/runtime@7.x":
  version "7.13.10"
  resolved "http://registry.npm.qima-inc.com/@babel/runtime/download/@babel/runtime-7.13.10.tgz#47d42a57b6095f4468da440388fdbad8bebf0d7d"
  integrity sha1-R9QqV7YJX0Ro2kQDiP262L6/DX0=
  dependencies:
    regenerator-runtime "^0.13.4"

"@babel/template@^7.10.4", "@babel/template@^7.12.7", "@babel/template@^7.3.3":
  version "7.12.7"
  resolved "http://registry.npm.qima-inc.com/@babel/template/download/@babel/template-7.12.7.tgz"
  integrity sha1-yBcjNpYBjjn7tsSR0vtoTgXtQ7w=
  dependencies:
    "@babel/code-frame" "^7.10.4"
    "@babel/parser" "^7.12.7"
    "@babel/types" "^7.12.7"

"@babel/traverse@^7.1.0", "@babel/traverse@^7.12.1", "@babel/traverse@^7.12.10", "@babel/traverse@^7.12.5":
  version "7.12.12"
  resolved "http://registry.npm.qima-inc.com/@babel/traverse/download/@babel/traverse-7.12.12.tgz"
  integrity sha1-0M2HiScE7djaAC1nS8gRzmR0M3Y=
  dependencies:
    "@babel/code-frame" "^7.12.11"
    "@babel/generator" "^7.12.11"
    "@babel/helper-function-name" "^7.12.11"
    "@babel/helper-split-export-declaration" "^7.12.11"
    "@babel/parser" "^7.12.11"
    "@babel/types" "^7.12.12"
    debug "^4.1.0"
    globals "^11.1.0"
    lodash "^4.17.19"

"@babel/types@^7.0.0", "@babel/types@^7.12.1", "@babel/types@^7.12.10", "@babel/types@^7.12.11", "@babel/types@^7.12.12", "@babel/types@^7.12.5", "@babel/types@^7.12.7", "@babel/types@^7.3.0", "@babel/types@^7.3.3":
  version "7.12.12"
  resolved "http://registry.npm.qima-inc.com/@babel/types/download/@babel/types-7.12.12.tgz"
  integrity sha1-Rgim7DE6u9h6+lUATTc60EqWwpk=
  dependencies:
    "@babel/helper-validator-identifier" "^7.12.11"
    lodash "^4.17.19"
    to-fast-properties "^2.0.0"

"@babel/types@^7.26.0", "@babel/types@^7.6.1", "@babel/types@^7.9.6":
  version "7.26.0"
  resolved "http://registry.npm.qima-inc.com/@babel/types/download/@babel/types-7.26.0.tgz#deabd08d6b753bc8e0f198f8709fb575e31774ff"
  integrity sha1-3qvQjWt1O8jg8Zj4cJ+1deMXdP8=
  dependencies:
    "@babel/helper-string-parser" "^7.25.9"
    "@babel/helper-validator-identifier" "^7.25.9"

"@bcoe/v8-coverage@^0.2.3":
  version "0.2.3"
  resolved "http://registry.npm.qima-inc.com/@bcoe/v8-coverage/download/@bcoe/v8-coverage-0.2.3.tgz"
  integrity sha1-daLotRy3WKdVPWgEpZMteqznXDk=

"@cnakazawa/watch@^1.0.3":
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/@cnakazawa/watch/download/@cnakazawa/watch-1.0.4.tgz"
  integrity sha1-+GSuhQBND8q29QvpFBxNo2jRZWo=
  dependencies:
    exec-sh "^0.3.2"
    minimist "^1.2.0"

"@colors/colors@1.5.0":
  version "1.5.0"
  resolved "http://registry.npm.qima-inc.com/@colors/colors/download/@colors/colors-1.5.0.tgz#bb504579c1cae923e6576a4f5da43d25f97bdbd9"
  integrity sha1-u1BFecHK6SPmV2pPXaQ9Jfl729k=

"@cspell/cspell-bundled-dicts@6.31.3":
  version "6.31.3"
  resolved "http://registry.npm.qima-inc.com/@cspell/cspell-bundled-dicts/download/@cspell/cspell-bundled-dicts-6.31.3.tgz#b9d7fc363b6ce3e0e49e945a563d9d012346ceb6"
  integrity sha1-udf8Njts4+DknpRaVj2dASNGzrY=
  dependencies:
    "@cspell/dict-ada" "^4.0.1"
    "@cspell/dict-aws" "^3.0.0"
    "@cspell/dict-bash" "^4.1.1"
    "@cspell/dict-companies" "^3.0.9"
    "@cspell/dict-cpp" "^5.0.2"
    "@cspell/dict-cryptocurrencies" "^3.0.1"
    "@cspell/dict-csharp" "^4.0.2"
    "@cspell/dict-css" "^4.0.5"
    "@cspell/dict-dart" "^2.0.2"
    "@cspell/dict-django" "^4.0.2"
    "@cspell/dict-docker" "^1.1.6"
    "@cspell/dict-dotnet" "^5.0.0"
    "@cspell/dict-elixir" "^4.0.2"
    "@cspell/dict-en-common-misspellings" "^1.0.2"
    "@cspell/dict-en-gb" "1.1.33"
    "@cspell/dict-en_us" "^4.3.2"
    "@cspell/dict-filetypes" "^3.0.0"
    "@cspell/dict-fonts" "^3.0.2"
    "@cspell/dict-fullstack" "^3.1.5"
    "@cspell/dict-gaming-terms" "^1.0.4"
    "@cspell/dict-git" "^2.0.0"
    "@cspell/dict-golang" "^6.0.1"
    "@cspell/dict-haskell" "^4.0.1"
    "@cspell/dict-html" "^4.0.3"
    "@cspell/dict-html-symbol-entities" "^4.0.0"
    "@cspell/dict-java" "^5.0.5"
    "@cspell/dict-k8s" "^1.0.1"
    "@cspell/dict-latex" "^4.0.0"
    "@cspell/dict-lorem-ipsum" "^3.0.0"
    "@cspell/dict-lua" "^4.0.1"
    "@cspell/dict-node" "^4.0.2"
    "@cspell/dict-npm" "^5.0.5"
    "@cspell/dict-php" "^4.0.1"
    "@cspell/dict-powershell" "^5.0.1"
    "@cspell/dict-public-licenses" "^2.0.2"
    "@cspell/dict-python" "^4.0.2"
    "@cspell/dict-r" "^2.0.1"
    "@cspell/dict-ruby" "^5.0.0"
    "@cspell/dict-rust" "^4.0.1"
    "@cspell/dict-scala" "^5.0.0"
    "@cspell/dict-software-terms" "^3.1.6"
    "@cspell/dict-sql" "^2.1.0"
    "@cspell/dict-svelte" "^1.0.2"
    "@cspell/dict-swift" "^2.0.1"
    "@cspell/dict-typescript" "^3.1.1"
    "@cspell/dict-vue" "^3.0.0"

"@cspell/cspell-json-reporter@6.31.3":
  version "6.31.3"
  resolved "http://registry.npm.qima-inc.com/@cspell/cspell-json-reporter/download/@cspell/cspell-json-reporter-6.31.3.tgz#1911b6ef70d97821fa9d71ee9540efd67fc4a679"
  integrity sha1-GRG273DZeCH6nXHulUDv1n/Epnk=
  dependencies:
    "@cspell/cspell-types" "6.31.3"

"@cspell/cspell-pipe@6.31.3":
  version "6.31.3"
  resolved "http://registry.npm.qima-inc.com/@cspell/cspell-pipe/download/@cspell/cspell-pipe-6.31.3.tgz#76e852728fb1b939c8fac045067b8259baa7d46e"
  integrity sha1-duhSco+xuTnI+sBFBnuCWbqn1G4=

"@cspell/cspell-service-bus@6.31.3":
  version "6.31.3"
  resolved "http://registry.npm.qima-inc.com/@cspell/cspell-service-bus/download/@cspell/cspell-service-bus-6.31.3.tgz#2bf2465a60bb6df8f9d301b33f45d37322f9dcab"
  integrity sha1-K/JGWmC7bfj50wGzP0XTcyL53Ks=

"@cspell/cspell-types@6.31.3":
  version "6.31.3"
  resolved "http://registry.npm.qima-inc.com/@cspell/cspell-types/download/@cspell/cspell-types-6.31.3.tgz#ff7493c435778fc76f5f6c6d786c86d12dc76790"
  integrity sha1-/3STxDV3j8dvX2xteGyG0S3HZ5A=

"@cspell/dict-ada@^4.0.1":
  version "4.0.5"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-ada/download/@cspell/dict-ada-4.0.5.tgz#c14aae2faaecbad2d99f0d701e4700a48c68ef60"
  integrity sha1-wUquL6rsutLZnw1wHkcApIxo72A=

"@cspell/dict-aws@^3.0.0":
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-aws/download/@cspell/dict-aws-3.0.0.tgz#7b2db82bb632c664c3d72b83267b93b9b0cafe60"
  integrity sha1-ey24K7YyxmTD1yuDJnuTubDK/mA=

"@cspell/dict-bash@^4.1.1":
  version "4.1.8"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-bash/download/@cspell/dict-bash-4.1.8.tgz#26dc898e06eddea069cf1ad475ee0e867c89e632"
  integrity sha1-JtyJjgbt3qBpzxrUde4OhnyJ5jI=

"@cspell/dict-companies@^3.0.9":
  version "3.1.7"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-companies/download/@cspell/dict-companies-3.1.7.tgz#c9abd6f5293f103062f54dde01f2bee939189f79"
  integrity sha1-yavW9Sk/EDBi9U3eAfK+6TkYn3k=

"@cspell/dict-cpp@^5.0.2":
  version "5.1.23"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-cpp/download/@cspell/dict-cpp-5.1.23.tgz#80d0103bc55105c0e4de8e54fc17397634c3a905"
  integrity sha1-gNAQO8VRBcDk3o5U/Bc5djTDqQU=

"@cspell/dict-cryptocurrencies@^3.0.1":
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-cryptocurrencies/download/@cspell/dict-cryptocurrencies-3.0.1.tgz#de1c235d6427946b679d23aacff12fea94e6385b"
  integrity sha1-3hwjXWQnlGtnnSOqz/Ev6pTmOFs=

"@cspell/dict-csharp@^4.0.2":
  version "4.0.5"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-csharp/download/@cspell/dict-csharp-4.0.5.tgz#c677c50be09ca5bb3a2cc0be15f3cd05141fd2f7"
  integrity sha1-xnfFC+Ccpbs6LMC+FfPNBRQf0vc=

"@cspell/dict-css@^4.0.5":
  version "4.0.16"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-css/download/@cspell/dict-css-4.0.16.tgz#b7b87b5ea0f1157b023205bdb00070a7d231e367"
  integrity sha1-t7h7XqDxFXsCMgW9sABwp9Ix42c=

"@cspell/dict-dart@^2.0.2":
  version "2.2.4"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-dart/download/@cspell/dict-dart-2.2.4.tgz#8b877161ccdc65cead912b742b71aa55099c1706"
  integrity sha1-i4dxYczcZc6tkSt0K3GqVQmcFwY=

"@cspell/dict-data-science@^2.0.5":
  version "2.0.5"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-data-science/download/@cspell/dict-data-science-2.0.5.tgz#816e9b394c2a423d14cdc9a5de5d6fc6141d3900"
  integrity sha1-gW6bOUwqQj0Uzcml3l1vxhQdOQA=

"@cspell/dict-django@^4.0.2":
  version "4.1.3"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-django/download/@cspell/dict-django-4.1.3.tgz#a02a4a9ef8c9f47344f2d4a0c3964bcb62069ef5"
  integrity sha1-oCpKnvjJ9HNE8tSgw5ZLy2IGnvU=

"@cspell/dict-docker@^1.1.6":
  version "1.1.11"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-docker/download/@cspell/dict-docker-1.1.11.tgz#6fce86eb6d86d73f77e18d3e7b9747bad3ca98de"
  integrity sha1-b86G622G1z934Y0+e5dHutPKmN4=

"@cspell/dict-dotnet@^5.0.0":
  version "5.0.8"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-dotnet/download/@cspell/dict-dotnet-5.0.8.tgz#8a110ca302946025e0273a9940079483ec33a88a"
  integrity sha1-ihEMowKUYCXgJzqZQAeUg+wzqIo=

"@cspell/dict-elixir@^4.0.2":
  version "4.0.6"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-elixir/download/@cspell/dict-elixir-4.0.6.tgz#3d8965c558d8afd190356e9a900b02c546741feb"
  integrity sha1-PYllxVjYr9GQNW6akAsCxUZ0H+s=

"@cspell/dict-en-common-misspellings@^1.0.2":
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-en-common-misspellings/download/@cspell/dict-en-common-misspellings-1.0.2.tgz#3c4ebab8e9e906d66d60f53c8f8c2e77b7f108e7"
  integrity sha1-PE66uOnpBtZtYPU8j4wud7fxCOc=

"@cspell/dict-en-gb@1.1.33":
  version "1.1.33"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-en-gb/download/@cspell/dict-en-gb-1.1.33.tgz#7f1fd90fc364a5cb77111b5438fc9fcf9cc6da0e"
  integrity sha1-fx/ZD8Nkpct3ERtUOPyfz5zG2g4=

"@cspell/dict-en_us@^4.3.2":
  version "4.3.27"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-en_us/download/@cspell/dict-en_us-4.3.27.tgz#d71fabdc058d56d24f8078384039d3375343c9f4"
  integrity sha1-1x+r3AWNVtJPgHg4QDnTN1NDyfQ=

"@cspell/dict-filetypes@^3.0.0":
  version "3.0.8"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-filetypes/download/@cspell/dict-filetypes-3.0.8.tgz#016d523ca2c34dea972ea0ca931255868348d81a"
  integrity sha1-AW1SPKLDTeqXLqDKkxJVhoNI2Bo=

"@cspell/dict-fonts@^3.0.2":
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-fonts/download/@cspell/dict-fonts-3.0.2.tgz#657d871cf627466765166cf18c448743c19317e2"
  integrity sha1-ZX2HHPYnRmdlFmzxjESHQ8GTF+I=

"@cspell/dict-fullstack@^3.1.5":
  version "3.2.3"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-fullstack/download/@cspell/dict-fullstack-3.2.3.tgz#f6fff74eff00c6759cba510168acada0619004cc"
  integrity sha1-9v/3Tv8AxnWculEBaKytoGGQBMw=

"@cspell/dict-gaming-terms@^1.0.4":
  version "1.0.8"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-gaming-terms/download/@cspell/dict-gaming-terms-1.0.8.tgz#fb8a737f61e7cf560b4de7b2aaeae952f2550398"
  integrity sha1-+4pzf2Hnz1YLTeeyqurpUvJVA5g=

"@cspell/dict-git@^2.0.0":
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-git/download/@cspell/dict-git-2.0.0.tgz#fa5cb298845da9c69efc01c6af07a99097718dc9"
  integrity sha1-+lyymIRdqcae/AHGrwepkJdxjck=

"@cspell/dict-golang@^6.0.1":
  version "6.0.16"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-golang/download/@cspell/dict-golang-6.0.16.tgz#b247a801404f9a65e7c8674893bdb5aad42353a2"
  integrity sha1-skeoAUBPmmXnyGdIk721qtQjU6I=

"@cspell/dict-haskell@^4.0.1":
  version "4.0.4"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-haskell/download/@cspell/dict-haskell-4.0.4.tgz#37e9cb9a7f5be337a697bcffd0a0d25e80aab50d"
  integrity sha1-N+nLmn9b4zeml7z/0KDSXoCqtQ0=

"@cspell/dict-html-symbol-entities@^4.0.0":
  version "4.0.3"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-html-symbol-entities/download/@cspell/dict-html-symbol-entities-4.0.3.tgz#bf2887020ca4774413d8b1f27c9b6824ba89e9ef"
  integrity sha1-vyiHAgykd0QT2LHyfJtoJLqJ6e8=

"@cspell/dict-html@^4.0.3":
  version "4.0.10"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-html/download/@cspell/dict-html-4.0.10.tgz#7b536b2adca4b58ed92752c9d3c7ffc724dd5991"
  integrity sha1-e1NrKtyktY7ZJ1LJ08f/xyTdWZE=

"@cspell/dict-java@^5.0.5":
  version "5.0.10"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-java/download/@cspell/dict-java-5.0.10.tgz#e6383ca645046b9f05a04a2c2e858fcc80c6fc63"
  integrity sha1-5jg8pkUEa58FoEosLoWPzIDG/GM=

"@cspell/dict-k8s@^1.0.1":
  version "1.0.9"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-k8s/download/@cspell/dict-k8s-1.0.9.tgz#e9392a002797c67ffc3e96893156cc15af3774d1"
  integrity sha1-6TkqACeXxn/8PpaJMVbMFa83dNE=

"@cspell/dict-latex@^4.0.0":
  version "4.0.3"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-latex/download/@cspell/dict-latex-4.0.3.tgz#a1254c7d9c3a2d70cd6391a9f2f7694431b1b2cb"
  integrity sha1-oSVMfZw6LXDNY5Gp8vdpRDGxsss=

"@cspell/dict-lorem-ipsum@^3.0.0":
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-lorem-ipsum/download/@cspell/dict-lorem-ipsum-3.0.0.tgz#c6347660fcab480b47bdcaec3b57e8c3abc4af68"
  integrity sha1-xjR2YPyrSAtHvcrsO1fow6vEr2g=

"@cspell/dict-lua@^4.0.1":
  version "4.0.6"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-lua/download/@cspell/dict-lua-4.0.6.tgz#7de412bfaead794445e26d566aec222e20ad69ba"
  integrity sha1-feQSv66teURF4m1WauwiLiCtabo=

"@cspell/dict-node@^4.0.2":
  version "4.0.3"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-node/download/@cspell/dict-node-4.0.3.tgz#5ae0222d72871e82978049f8e11ea627ca42fca3"
  integrity sha1-WuAiLXKHHoKXgEn44R6mJ8pC/KM=

"@cspell/dict-npm@^5.0.5":
  version "5.1.12"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-npm/download/@cspell/dict-npm-5.1.12.tgz#342b728d6b624f0f86ab73b300e1484c7a863e55"
  integrity sha1-NCtyjWtiTw+Gq3OzAOFITHqGPlU=

"@cspell/dict-php@^4.0.1":
  version "4.0.13"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-php/download/@cspell/dict-php-4.0.13.tgz#86f1e6fb2174b2b0fa012baf86c448b2730f04f9"
  integrity sha1-hvHm+yF0srD6ASuvhsRIsnMPBPk=

"@cspell/dict-powershell@^5.0.1":
  version "5.0.13"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-powershell/download/@cspell/dict-powershell-5.0.13.tgz#f557aa04ee9bda4fe091308a0bcaea09ed12fa76"
  integrity sha1-9VeqBO6b2k/gkTCKC8rqCe0S+nY=

"@cspell/dict-public-licenses@^2.0.2":
  version "2.0.11"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-public-licenses/download/@cspell/dict-public-licenses-2.0.11.tgz#37550c4e0cd445991caba528bf4ba58ce7a935c3"
  integrity sha1-N1UMTgzURZkcq6Uov0uljOepNcM=

"@cspell/dict-python@^4.0.2":
  version "4.2.12"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-python/download/@cspell/dict-python-4.2.12.tgz#ea6298bb72a6bcf2c188d5c55142e0afab8a6c1c"
  integrity sha1-6mKYu3KmvPLBiNXFUULgr6uKbBw=
  dependencies:
    "@cspell/dict-data-science" "^2.0.5"

"@cspell/dict-r@^2.0.1":
  version "2.0.4"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-r/download/@cspell/dict-r-2.0.4.tgz#31b5abd91cc12aebfffdde4be4d2902668789311"
  integrity sha1-MbWr2RzBKuv//d5L5NKQJmh4kxE=

"@cspell/dict-ruby@^5.0.0":
  version "5.0.7"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-ruby/download/@cspell/dict-ruby-5.0.7.tgz#3593a955baaffe3c5d28fb178b72fdf93c7eec71"
  integrity sha1-NZOpVbqv/jxdKPsXi3L9+Tx+7HE=

"@cspell/dict-rust@^4.0.1":
  version "4.0.10"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-rust/download/@cspell/dict-rust-4.0.10.tgz#8ae6eaf31a0ebce9dc8fd8dd68e5925e1d5290ee"
  integrity sha1-iubq8xoOvOncj9jdaOWSXh1SkO4=

"@cspell/dict-scala@^5.0.0":
  version "5.0.6"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-scala/download/@cspell/dict-scala-5.0.6.tgz#5e925def2fe6dc27ee2ad1c452941c3d6790fb6d"
  integrity sha1-XpJd7y/m3CfuKtHEUpQcPWeQ+20=

"@cspell/dict-software-terms@^3.1.6":
  version "3.4.10"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-software-terms/download/@cspell/dict-software-terms-3.4.10.tgz#1e461abf6a639b8771763a5953dbcfd611bc6dc0"
  integrity sha1-HkYav2pjm4dxdjpZU9vP1hG8bcA=

"@cspell/dict-sql@^2.1.0":
  version "2.1.8"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-sql/download/@cspell/dict-sql-2.1.8.tgz#45ea53b3e57fd2cc5f839f49b644aa743dac4990"
  integrity sha1-RepTs+V/0sxfg59JtkSqdD2sSZA=

"@cspell/dict-svelte@^1.0.2":
  version "1.0.5"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-svelte/download/@cspell/dict-svelte-1.0.5.tgz#09752e01ff6667e737566d9dfc704c8dcc9a6492"
  integrity sha1-CXUuAf9mZ+c3Vm2d/HBMjcyaZJI=

"@cspell/dict-swift@^2.0.1":
  version "2.0.4"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-swift/download/@cspell/dict-swift-2.0.4.tgz#bc19522418ed68cf914736b612c4e4febbf07e8d"
  integrity sha1-vBlSJBjtaM+RRza2EsTk/rvwfo0=

"@cspell/dict-typescript@^3.1.1":
  version "3.1.11"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-typescript/download/@cspell/dict-typescript-3.1.11.tgz#40586f13b0337bd9cba958e0661b35888580b249"
  integrity sha1-QFhvE7Aze9nLqVjgZhs1iIWAskk=

"@cspell/dict-vue@^3.0.0":
  version "3.0.3"
  resolved "http://registry.npm.qima-inc.com/@cspell/dict-vue/download/@cspell/dict-vue-3.0.3.tgz#295c288f6fd363879898223202ec3be048663b98"
  integrity sha1-KVwoj2/TY4eYmCIyAuw74EhmO5g=

"@cspell/dynamic-import@6.31.3":
  version "6.31.3"
  resolved "http://registry.npm.qima-inc.com/@cspell/dynamic-import/download/@cspell/dynamic-import-6.31.3.tgz#f98ee7a8210e457b6f885be9a918b9608571b677"
  integrity sha1-+Y7nqCEORXtviFvpqRi5YIVxtnc=
  dependencies:
    import-meta-resolve "^2.2.2"

"@cspell/strong-weak-map@6.31.3":
  version "6.31.3"
  resolved "http://registry.npm.qima-inc.com/@cspell/strong-weak-map/download/@cspell/strong-weak-map-6.31.3.tgz#dd7470ce86c2372ec2cd889439c0f9808122b9c7"
  integrity sha1-3XRwzobCNy7CzYiUOcD5gIEiucc=

"@grpc/grpc-js@^1.1.5":
  version "1.2.4"
  resolved "http://registry.npm.qima-inc.com/@grpc/grpc-js/download/@grpc/grpc-js-1.2.4.tgz"
  integrity sha1-BPC777JjYpbRfoIfPVIVL74vaYk=
  dependencies:
    "@types/node" "^12.12.47"
    google-auth-library "^6.1.1"
    semver "^6.2.0"

"@grpc/proto-loader@0.5.4":
  version "0.5.4"
  resolved "http://registry.npm.qima-inc.com/@grpc/proto-loader/download/@grpc/proto-loader-0.5.4.tgz"
  integrity sha1-A4o4IFQPYh7rGwXYH77fsEXhTeA=
  dependencies:
    lodash.camelcase "^4.3.0"
    protobufjs "^6.8.6"

"@hapi/address@2.x.x":
  version "2.1.4"
  resolved "http://registry.npm.qima-inc.com/@hapi/address/download/@hapi/address-2.1.4.tgz"
  integrity sha1-XWftQ/P9QaadS5/3tW58DR0KgeU=

"@hapi/bourne@1.x.x":
  version "1.3.2"
  resolved "http://registry.npm.qima-inc.com/@hapi/bourne/download/@hapi/bourne-1.3.2.tgz"
  integrity sha1-CnCVreoGckPOMoPhtWuKj0U7JCo=

"@hapi/bourne@^2.0.0":
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/@hapi/bourne/download/@hapi/bourne-2.0.0.tgz"
  integrity sha1-W7IZPraFwAB1QMph0WbU4e2vkY0=

"@hapi/hoek@8.x.x", "@hapi/hoek@^8.3.0":
  version "8.5.1"
  resolved "http://registry.npm.qima-inc.com/@hapi/hoek/download/@hapi/hoek-8.5.1.tgz"
  integrity sha1-/elgZMpEbeyMVajC8TCVewcMbgY=

"@hapi/joi@15.1.1":
  version "15.1.1"
  resolved "http://registry.npm.qima-inc.com/@hapi/joi/download/@hapi/joi-15.1.1.tgz"
  integrity sha1-xnW4pxKW8Cgz+NbSQ7NMV7jOGdc=
  dependencies:
    "@hapi/address" "2.x.x"
    "@hapi/bourne" "1.x.x"
    "@hapi/hoek" "8.x.x"
    "@hapi/topo" "3.x.x"

"@hapi/topo@3.x.x":
  version "3.1.6"
  resolved "http://registry.npm.qima-inc.com/@hapi/topo/download/@hapi/topo-3.1.6.tgz"
  integrity sha1-aNk1+j6uf91asNf5U/MgXYsr/Ck=
  dependencies:
    "@hapi/hoek" "^8.3.0"

"@istanbuljs/load-nyc-config@^1.0.0":
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/@istanbuljs/load-nyc-config/download/@istanbuljs/load-nyc-config-1.1.0.tgz"
  integrity sha1-/T2x1Z7PfPEh6AZQu4ZxL5tV7O0=
  dependencies:
    camelcase "^5.3.1"
    find-up "^4.1.0"
    get-package-type "^0.1.0"
    js-yaml "^3.13.1"
    resolve-from "^5.0.0"

"@istanbuljs/schema@^0.1.2":
  version "0.1.2"
  resolved "http://registry.npm.qima-inc.com/@istanbuljs/schema/download/@istanbuljs/schema-0.1.2.tgz"
  integrity sha1-JlIL8Jq+SlZEzVQU43ElqJVCQd0=

"@jest/console@^26.6.2":
  version "26.6.2"
  resolved "http://registry.npm.qima-inc.com/@jest/console/download/@jest/console-26.6.2.tgz"
  integrity sha1-TgS8RkAUNYsDq0k3gF7jagrrmPI=
  dependencies:
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    chalk "^4.0.0"
    jest-message-util "^26.6.2"
    jest-util "^26.6.2"
    slash "^3.0.0"

"@jest/core@^26.6.3":
  version "26.6.3"
  resolved "http://registry.npm.qima-inc.com/@jest/core/download/@jest/core-26.6.3.tgz"
  integrity sha1-djn8s4M9dIpGVq2lS94ZMFHkX60=
  dependencies:
    "@jest/console" "^26.6.2"
    "@jest/reporters" "^26.6.2"
    "@jest/test-result" "^26.6.2"
    "@jest/transform" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    ansi-escapes "^4.2.1"
    chalk "^4.0.0"
    exit "^0.1.2"
    graceful-fs "^4.2.4"
    jest-changed-files "^26.6.2"
    jest-config "^26.6.3"
    jest-haste-map "^26.6.2"
    jest-message-util "^26.6.2"
    jest-regex-util "^26.0.0"
    jest-resolve "^26.6.2"
    jest-resolve-dependencies "^26.6.3"
    jest-runner "^26.6.3"
    jest-runtime "^26.6.3"
    jest-snapshot "^26.6.2"
    jest-util "^26.6.2"
    jest-validate "^26.6.2"
    jest-watcher "^26.6.2"
    micromatch "^4.0.2"
    p-each-series "^2.1.0"
    rimraf "^3.0.0"
    slash "^3.0.0"
    strip-ansi "^6.0.0"

"@jest/environment@^26.6.2":
  version "26.6.2"
  resolved "http://registry.npm.qima-inc.com/@jest/environment/download/@jest/environment-26.6.2.tgz"
  integrity sha1-ujZMxy4iHnnMjwqZVVv111d8+Sw=
  dependencies:
    "@jest/fake-timers" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    jest-mock "^26.6.2"

"@jest/fake-timers@^26.6.2":
  version "26.6.2"
  resolved "http://registry.npm.qima-inc.com/@jest/fake-timers/download/@jest/fake-timers-26.6.2.tgz"
  integrity sha1-RZwym89wzuSvTX4/PmeEgSNTWq0=
  dependencies:
    "@jest/types" "^26.6.2"
    "@sinonjs/fake-timers" "^6.0.1"
    "@types/node" "*"
    jest-message-util "^26.6.2"
    jest-mock "^26.6.2"
    jest-util "^26.6.2"

"@jest/globals@^26.6.2":
  version "26.6.2"
  resolved "http://registry.npm.qima-inc.com/@jest/globals/download/@jest/globals-26.6.2.tgz"
  integrity sha1-W2E7eKGqJlWukI66Y4zJaiDfcgo=
  dependencies:
    "@jest/environment" "^26.6.2"
    "@jest/types" "^26.6.2"
    expect "^26.6.2"

"@jest/reporters@^26.6.2":
  version "26.6.2"
  resolved "http://registry.npm.qima-inc.com/@jest/reporters/download/@jest/reporters-26.6.2.tgz"
  integrity sha1-H1GLmWN6Xxgwe9Ps+SdfaIKmZ/Y=
  dependencies:
    "@bcoe/v8-coverage" "^0.2.3"
    "@jest/console" "^26.6.2"
    "@jest/test-result" "^26.6.2"
    "@jest/transform" "^26.6.2"
    "@jest/types" "^26.6.2"
    chalk "^4.0.0"
    collect-v8-coverage "^1.0.0"
    exit "^0.1.2"
    glob "^7.1.2"
    graceful-fs "^4.2.4"
    istanbul-lib-coverage "^3.0.0"
    istanbul-lib-instrument "^4.0.3"
    istanbul-lib-report "^3.0.0"
    istanbul-lib-source-maps "^4.0.0"
    istanbul-reports "^3.0.2"
    jest-haste-map "^26.6.2"
    jest-resolve "^26.6.2"
    jest-util "^26.6.2"
    jest-worker "^26.6.2"
    slash "^3.0.0"
    source-map "^0.6.0"
    string-length "^4.0.1"
    terminal-link "^2.0.0"
    v8-to-istanbul "^7.0.0"
  optionalDependencies:
    node-notifier "^8.0.0"

"@jest/source-map@^26.6.2":
  version "26.6.2"
  resolved "http://registry.npm.qima-inc.com/@jest/source-map/download/@jest/source-map-26.6.2.tgz"
  integrity sha1-Ka9eHi4yTK/MyTbyGDCfVKtp1TU=
  dependencies:
    callsites "^3.0.0"
    graceful-fs "^4.2.4"
    source-map "^0.6.0"

"@jest/test-result@^26.6.2":
  version "26.6.2"
  resolved "http://registry.npm.qima-inc.com/@jest/test-result/download/@jest/test-result-26.6.2.tgz"
  integrity sha1-VdpYti3xNFdsyVR276X3lJ4/Xxg=
  dependencies:
    "@jest/console" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/istanbul-lib-coverage" "^2.0.0"
    collect-v8-coverage "^1.0.0"

"@jest/test-sequencer@^26.6.3":
  version "26.6.3"
  resolved "http://registry.npm.qima-inc.com/@jest/test-sequencer/download/@jest/test-sequencer-26.6.3.tgz"
  integrity sha1-mOikUQCGOIbQdCBej/3Fp+tYKxc=
  dependencies:
    "@jest/test-result" "^26.6.2"
    graceful-fs "^4.2.4"
    jest-haste-map "^26.6.2"
    jest-runner "^26.6.3"
    jest-runtime "^26.6.3"

"@jest/transform@^26.6.2":
  version "26.6.2"
  resolved "http://registry.npm.qima-inc.com/@jest/transform/download/@jest/transform-26.6.2.tgz"
  integrity sha1-WsV8X6GtF7Kq6D5z5FgTiU3PLks=
  dependencies:
    "@babel/core" "^7.1.0"
    "@jest/types" "^26.6.2"
    babel-plugin-istanbul "^6.0.0"
    chalk "^4.0.0"
    convert-source-map "^1.4.0"
    fast-json-stable-stringify "^2.0.0"
    graceful-fs "^4.2.4"
    jest-haste-map "^26.6.2"
    jest-regex-util "^26.0.0"
    jest-util "^26.6.2"
    micromatch "^4.0.2"
    pirates "^4.0.1"
    slash "^3.0.0"
    source-map "^0.6.1"
    write-file-atomic "^3.0.0"

"@jest/types@^26.6.2":
  version "26.6.2"
  resolved "http://registry.npm.qima-inc.com/@jest/types/download/@jest/types-26.6.2.tgz"
  integrity sha1-vvWlMgMOHYii9abZM/hOlyJu1I4=
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^3.0.0"
    "@types/node" "*"
    "@types/yargs" "^15.0.0"
    chalk "^4.0.0"

"@jscpd/core@^3.5.4":
  version "3.5.4"
  resolved "http://registry.npm.qima-inc.com/@jscpd/core/download/@jscpd/core-3.5.4.tgz#0dff08af5e071d22ba35dcb5701fb008338d2b32"
  integrity sha1-Df8Ir14HHSK6Ndy1cB+wCDONKzI=
  dependencies:
    eventemitter3 "^4.0.4"

"@jscpd/finder@^3.5.5":
  version "3.5.5"
  resolved "http://registry.npm.qima-inc.com/@jscpd/finder/download/@jscpd/finder-3.5.5.tgz#d46211c762ed62c72428914313f528a3cd550f41"
  integrity sha1-1GIRx2LtYsckKJFDE/Uoo81VD0E=
  dependencies:
    "@jscpd/core" "^3.5.4"
    "@jscpd/tokenizer" "^3.5.4"
    blamer "^1.0.3"
    bytes "^3.1.0"
    cli-table3 "^0.6.0"
    colors "1.4.0"
    fast-glob "^3.2.2"
    fs-extra "^9.0.0"
    markdown-table "^2.0.0"
    pug "^3.0.1"

"@jscpd/html-reporter@^3.5.9":
  version "3.5.9"
  resolved "http://registry.npm.qima-inc.com/@jscpd/html-reporter/download/@jscpd/html-reporter-3.5.9.tgz#96d5773a5133aa097dc5cfb5eb2d5652c6dce85b"
  integrity sha1-ltV3OlEzqgl9xc+16y1WUsbc6Fs=
  dependencies:
    "@jscpd/finder" "^3.5.5"
    colors "1.4.0"
    fs-extra "^9.0.1"
    pug "^3.0.2"

"@jscpd/tokenizer@^3.5.4":
  version "3.5.4"
  resolved "http://registry.npm.qima-inc.com/@jscpd/tokenizer/download/@jscpd/tokenizer-3.5.4.tgz#23a54d359f0848f418bd01c77129384e24acc12d"
  integrity sha1-I6VNNZ8ISPQYvQHHcSk4TiSswS0=
  dependencies:
    "@jscpd/core" "^3.5.4"
    reprism "^0.0.11"
    spark-md5 "^3.0.1"

"@mrmlnc/readdir-enhanced@^2.2.1":
  version "2.2.1"
  resolved "http://registry.npm.qima-inc.com/@mrmlnc/readdir-enhanced/download/@mrmlnc/readdir-enhanced-2.2.1.tgz"
  integrity sha1-UkryQNGjYFJ7cwR17PoTRKpUDd4=
  dependencies:
    call-me-maybe "^1.0.1"
    glob-to-regexp "^0.3.0"

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "http://registry.npm.qima-inc.com/@nodelib/fs.scandir/download/@nodelib/fs.scandir-2.1.5.tgz#7619c2eb21b25483f6d167548b4cfd5a7488c3d5"
  integrity sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.5", "@nodelib/fs.stat@^2.0.2":
  version "2.0.5"
  resolved "http://registry.npm.qima-inc.com/@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.5.tgz#5bd262af94e9d25bd1e71b05deed44876a222e8b"
  integrity sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=

"@nodelib/fs.stat@^1.1.2":
  version "1.1.3"
  resolved "http://registry.npm.qima-inc.com/@nodelib/fs.stat/download/@nodelib/fs.stat-1.1.3.tgz"
  integrity sha1-K1o6s/kYzKSKjHVMCBaOPwPrphs=

"@nodelib/fs.walk@^1.2.3":
  version "1.2.8"
  resolved "http://registry.npm.qima-inc.com/@nodelib/fs.walk/download/@nodelib/fs.walk-1.2.8.tgz#e95737e8bb6746ddedf69c556953494f196fe69a"
  integrity sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@opencensus/core@0.0.9":
  version "0.0.9"
  resolved "http://registry.npm.qima-inc.com/@opencensus/core/download/@opencensus/core-0.0.9.tgz#b16f775435ee309433e4126af194d37313fc93b3"
  integrity sha1-sW93VDXuMJQz5BJq8ZTTcxP8k7M=
  dependencies:
    continuation-local-storage "^3.2.1"
    log-driver "^1.2.7"
    semver "^5.5.0"
    shimmer "^1.2.0"
    uuid "^3.2.1"

"@opencensus/core@^0.0.8":
  version "0.0.8"
  resolved "http://registry.npm.qima-inc.com/@opencensus/core/download/@opencensus/core-0.0.8.tgz#df01f200c2d2fbfe14dae129a1a86fb87286db92"
  integrity sha1-3wHyAMLS+/4U2uEpoahvuHKG25I=
  dependencies:
    continuation-local-storage "^3.2.1"
    log-driver "^1.2.7"
    semver "^5.5.0"
    shimmer "^1.2.0"
    uuid "^3.2.1"

"@opencensus/propagation-b3@0.0.8":
  version "0.0.8"
  resolved "http://registry.npm.qima-inc.com/@opencensus/propagation-b3/download/@opencensus/propagation-b3-0.0.8.tgz#0751e6fd75f09400d9d3c419001e9e15a0df68e9"
  integrity sha1-B1Hm/XXwlADZ08QZAB6eFaDfaOk=
  dependencies:
    "@opencensus/core" "^0.0.8"
    uuid "^3.2.1"

"@pm2/agent-node@^1.1.10":
  version "1.1.10"
  resolved "http://registry.npm.qima-inc.com/@pm2/agent-node/download/@pm2/agent-node-1.1.10.tgz#29fafc9d1b75288dec87b6af1216ddfab8ea9b06"
  integrity sha1-Kfr8nRt1KI3sh7avEhbd+rjqmwY=
  dependencies:
    debug "^3.1.0"
    eventemitter2 "^5.0.1"
    proxy-agent "^3.0.3"
    ws "^6.0.0"

"@pm2/agent@~1.0.2":
  version "1.0.8"
  resolved "http://registry.npm.qima-inc.com/@pm2/agent/download/@pm2/agent-1.0.8.tgz#cd15d84dbfc95427e6fccce72bc165b79f1d8579"
  integrity sha1-zRXYTb/JVCfm/MznK8Flt58dhXk=
  dependencies:
    async "~3.2.0"
    chalk "~3.0.0"
    dayjs "~1.8.24"
    debug "~4.3.1"
    eventemitter2 "~5.0.1"
    fclone "~1.0.11"
    nssocket "0.6.0"
    pm2-axon "~4.0.1"
    pm2-axon-rpc "~0.7.0"
    proxy-agent "~4.0.1"
    semver "~7.2.0"
    ws "~7.2.0"

"@pm2/io@~4.3.5":
  version "4.3.5"
  resolved "http://registry.npm.qima-inc.com/@pm2/io/download/@pm2/io-4.3.5.tgz#57025ab821fd09d2afe6d0ab981f8a39ccec8860"
  integrity sha1-VwJauCH9CdKv5tCrmB+KOczsiGA=
  dependencies:
    "@opencensus/core" "0.0.9"
    "@opencensus/propagation-b3" "0.0.8"
    "@pm2/agent-node" "^1.1.10"
    async "~2.6.1"
    debug "4.1.1"
    eventemitter2 "^6.3.1"
    require-in-the-middle "^5.0.0"
    semver "6.3.0"
    shimmer "^1.2.0"
    signal-exit "^3.0.3"
    tslib "1.9.3"

"@pm2/js-api@~0.6.0":
  version "0.6.7"
  resolved "http://registry.npm.qima-inc.com/@pm2/js-api/download/@pm2/js-api-0.6.7.tgz#ed28c3b7b6d26f03f826318754fdc5468afa589f"
  integrity sha1-7SjDt7bSbwP4JjGHVP3FRor6WJ8=
  dependencies:
    async "^2.6.3"
    axios "^0.21.0"
    debug "~4.3.1"
    eventemitter2 "^6.3.1"
    ws "^7.0.0"

"@pm2/pm2-version-check@latest":
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/@pm2/pm2-version-check/download/@pm2/pm2-version-check-1.0.4.tgz#cf97fbb14b0eca95430ca05eedccbd2683806e43"
  integrity sha1-z5f7sUsOypVDDKBe7cy9JoOAbkM=
  dependencies:
    debug "^4.3.1"

"@protobufjs/aspromise@^1.1.1", "@protobufjs/aspromise@^1.1.2":
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/@protobufjs/aspromise/download/@protobufjs/aspromise-1.1.2.tgz"
  integrity sha1-m4sMxmPWaafY9vXQiToU00jzD78=

"@protobufjs/base64@^1.1.2":
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/@protobufjs/base64/download/@protobufjs/base64-1.1.2.tgz"
  integrity sha1-TIVzDlm5ofHzSQR9vyQpYDS7JzU=

"@protobufjs/codegen@^2.0.4":
  version "2.0.4"
  resolved "http://registry.npm.qima-inc.com/@protobufjs/codegen/download/@protobufjs/codegen-2.0.4.tgz"
  integrity sha1-fvN/DQEPsCitGtWXIuUG2SYoFcs=

"@protobufjs/eventemitter@^1.1.0":
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/@protobufjs/eventemitter/download/@protobufjs/eventemitter-1.1.0.tgz"
  integrity sha1-NVy8mLr61ZePntCV85diHx0Ga3A=

"@protobufjs/fetch@^1.1.0":
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/@protobufjs/fetch/download/@protobufjs/fetch-1.1.0.tgz"
  integrity sha1-upn7WYYUr2VwDBYZ/wbUVLDYTEU=
  dependencies:
    "@protobufjs/aspromise" "^1.1.1"
    "@protobufjs/inquire" "^1.1.0"

"@protobufjs/float@^1.0.2":
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/@protobufjs/float/download/@protobufjs/float-1.0.2.tgz"
  integrity sha1-Xp4avctz/Ap8uLKR33jIy9l7h9E=

"@protobufjs/inquire@^1.1.0":
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/@protobufjs/inquire/download/@protobufjs/inquire-1.1.0.tgz"
  integrity sha1-/yAOPnzyQp4tyvwRQIKOjMY48Ik=

"@protobufjs/path@^1.1.2":
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/@protobufjs/path/download/@protobufjs/path-1.1.2.tgz"
  integrity sha1-bMKyDFya1q0NzP0hynZz2Nf79o0=

"@protobufjs/pool@^1.1.0":
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/@protobufjs/pool/download/@protobufjs/pool-1.1.0.tgz"
  integrity sha1-Cf0V8tbTq/qbZbw2ZQbWrXhG/1Q=

"@protobufjs/utf8@^1.1.0":
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/@protobufjs/utf8/download/@protobufjs/utf8-1.1.0.tgz"
  integrity sha1-p3c2C1s5oaLlEG+OhY8v0tBgxXA=

"@sinonjs/commons@^1.7.0":
  version "1.8.1"
  resolved "http://registry.npm.qima-inc.com/@sinonjs/commons/download/@sinonjs/commons-1.8.1.tgz"
  integrity sha1-598A+YogMyT23HzGBsrZ1KirIhc=
  dependencies:
    type-detect "4.0.8"

"@sinonjs/fake-timers@^6.0.1":
  version "6.0.1"
  resolved "http://registry.npm.qima-inc.com/@sinonjs/fake-timers/download/@sinonjs/fake-timers-6.0.1.tgz"
  integrity sha1-KTZ0/MsyYqx4LHqt/eyoaxDHXEA=
  dependencies:
    "@sinonjs/commons" "^1.7.0"

"@tootallnate/once@1":
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/@tootallnate/once/download/@tootallnate/once-1.1.2.tgz#ccb91445360179a04e7fe6aff78c00ffc1eeaf82"
  integrity sha1-zLkURTYBeaBOf+av94wA/8Hur4I=

"@types/accepts@*":
  version "1.3.5"
  resolved "http://registry.npm.qima-inc.com/@types/accepts/download/@types/accepts-1.3.5.tgz"
  integrity sha1-w0vsEVz8dG4E/loFnfTOfns5FXU=
  dependencies:
    "@types/node" "*"

"@types/babel__core@^7.0.0", "@types/babel__core@^7.1.7":
  version "7.1.12"
  resolved "http://registry.npm.qima-inc.com/@types/babel__core/download/@types/babel__core-7.1.12.tgz"
  integrity sha1-TY6eUesmVVKn5PH/IhmrYTO9+y0=
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"
    "@types/babel__generator" "*"
    "@types/babel__template" "*"
    "@types/babel__traverse" "*"

"@types/babel__generator@*":
  version "7.6.2"
  resolved "http://registry.npm.qima-inc.com/@types/babel__generator/download/@types/babel__generator-7.6.2.tgz"
  integrity sha1-89cReOGHhY98ReMDgPjxt0FaEtg=
  dependencies:
    "@babel/types" "^7.0.0"

"@types/babel__template@*":
  version "7.4.0"
  resolved "http://registry.npm.qima-inc.com/@types/babel__template/download/@types/babel__template-7.4.0.tgz"
  integrity sha1-DIiN1ws+6e67bk8gDoCdoAdiYr4=
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"

"@types/babel__traverse@*", "@types/babel__traverse@^7.0.4", "@types/babel__traverse@^7.0.6":
  version "7.11.0"
  resolved "http://registry.npm.qima-inc.com/@types/babel__traverse/download/@types/babel__traverse-7.11.0.tgz"
  integrity sha1-uaHvpjUgG6m8hQMjqHk+4tNsBKA=
  dependencies:
    "@babel/types" "^7.3.0"

"@types/body-parser@*":
  version "1.19.0"
  resolved "http://registry.npm.qima-inc.com/@types/body-parser/download/@types/body-parser-1.19.0.tgz"
  integrity sha1-BoWzxH6zAG/+0RfN1VFkth+AU48=
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/chance@^1.1.3":
  version "1.1.3"
  resolved "http://registry.npm.qima-inc.com/@types/chance/download/@types/chance-1.1.3.tgz#d19fe9391288d60fdccd87632bfc9ab2b4523fea"
  integrity sha1-0Z/pORKI1g/czYdjK/yasrRSP+o=

"@types/connect@*":
  version "3.4.34"
  resolved "http://registry.npm.qima-inc.com/@types/connect/download/@types/connect-3.4.34.tgz"
  integrity sha1-FwpAIjptZmAG2TyhKK8r6x2bGQE=
  dependencies:
    "@types/node" "*"

"@types/content-disposition@*":
  version "0.5.3"
  resolved "http://registry.npm.qima-inc.com/@types/content-disposition/download/@types/content-disposition-0.5.3.tgz"
  integrity sha1-CqEWcBlVwvqgcX/GnNFZYJXknZY=

"@types/cookies@*", "@types/cookies@^0.7.1":
  version "0.7.6"
  resolved "http://registry.npm.qima-inc.com/@types/cookies/download/@types/cookies-0.7.6.tgz"
  integrity sha1-cSEsU5GpdtO65X1LCfrCD8a9pQQ=
  dependencies:
    "@types/connect" "*"
    "@types/express" "*"
    "@types/keygrip" "*"
    "@types/node" "*"

"@types/debug@^4.1.4":
  version "4.1.5"
  resolved "http://registry.npm.qima-inc.com/@types/debug/download/@types/debug-4.1.5.tgz"
  integrity sha1-sU76iFK3do2JiQZhPCP2iHE+As0=

"@types/eslint-visitor-keys@^1.0.0":
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/@types/eslint-visitor-keys/download/@types/eslint-visitor-keys-1.0.0.tgz"
  integrity sha1-HuMNeVRMqE1o1LPNsK9PIFZj3S0=

"@types/express-serve-static-core@^4.17.18":
  version "4.17.18"
  resolved "http://registry.npm.qima-inc.com/@types/express-serve-static-core/download/@types/express-serve-static-core-4.17.18.tgz"
  integrity sha1-g3HiYPQODhygwRapr82UJvoJTEA=
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"

"@types/express@*":
  version "4.17.11"
  resolved "http://registry.npm.qima-inc.com/@types/express/download/@types/express-4.17.11.tgz"
  integrity sha1-3r48qm+OX82pa0e9VOL0DE7llUU=
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^4.17.18"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/fs-extra@^9.0.1":
  version "9.0.12"
  resolved "http://registry.npm.qima-inc.com/@types/fs-extra/download/@types/fs-extra-9.0.12.tgz#9b8f27973df8a7a3920e8461517ebf8a7d4fdfaf"
  integrity sha1-m48nlz34p6OSDoRhUX6/in1P368=
  dependencies:
    "@types/node" "*"

"@types/graceful-fs@^4.1.2":
  version "4.1.4"
  resolved "http://registry.npm.qima-inc.com/@types/graceful-fs/download/@types/graceful-fs-4.1.4.tgz"
  integrity sha1-T/n2QafG0aNQj/iLwxQbFSdy51M=
  dependencies:
    "@types/node" "*"

"@types/hapi__joi@*":
  version "17.1.6"
  resolved "http://registry.npm.qima-inc.com/@types/hapi__joi/download/@types/hapi__joi-17.1.6.tgz"
  integrity sha1-uEZjZ2qpdTwXGDcYM43UDdy9N1Q=

"@types/hapi__joi@^15.0.3":
  version "15.0.4"
  resolved "http://registry.npm.qima-inc.com/@types/hapi__joi/download/@types/hapi__joi-15.0.4.tgz"
  integrity sha1-SeLh5toVreD91ttNr5Suywe7ORs=
  dependencies:
    "@types/hapi__joi" "*"

"@types/http-assert@*":
  version "1.5.1"
  resolved "http://registry.npm.qima-inc.com/@types/http-assert/download/@types/http-assert-1.5.1.tgz"
  integrity sha1-13XpNjDCRpwvmA/CfjFDJAM12zs=

"@types/http-errors@*":
  version "1.8.0"
  resolved "http://registry.npm.qima-inc.com/@types/http-errors/download/@types/http-errors-1.8.0.tgz"
  integrity sha1-aCR327vQfNAycxyzsOfq7j0Ca2k=

"@types/ioredis@*", "@types/ioredis@^4.0.10":
  version "4.19.2"
  resolved "http://registry.npm.qima-inc.com/@types/ioredis/download/@types/ioredis-4.19.2.tgz"
  integrity sha1-aCFdL6uT3U9dHRIlk4OJwy57mKI=
  dependencies:
    "@types/node" "*"

"@types/ip@^1.1.0":
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/@types/ip/download/@types/ip-1.1.0.tgz"
  integrity sha1-rsT1v9SeSkxTtZDYjDbrB4gnp8A=
  dependencies:
    "@types/node" "*"

"@types/istanbul-lib-coverage@*", "@types/istanbul-lib-coverage@^2.0.0", "@types/istanbul-lib-coverage@^2.0.1":
  version "2.0.3"
  resolved "http://registry.npm.qima-inc.com/@types/istanbul-lib-coverage/download/@types/istanbul-lib-coverage-2.0.3.tgz"
  integrity sha1-S6jdtyAiH0MuRDvV+RF/0iz9R2I=

"@types/istanbul-lib-report@*":
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/@types/istanbul-lib-report/download/@types/istanbul-lib-report-3.0.0.tgz"
  integrity sha1-wUwk8Y6oGQwRjudWK3/5mjZVJoY=
  dependencies:
    "@types/istanbul-lib-coverage" "*"

"@types/istanbul-reports@^3.0.0":
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/@types/istanbul-reports/download/@types/istanbul-reports-3.0.0.tgz"
  integrity sha1-UIsTqjRPpJdiNOdd3cw0klc32CE=
  dependencies:
    "@types/istanbul-lib-report" "*"

"@types/jest@26.x", "@types/jest@^26.0.14":
  version "26.0.20"
  resolved "http://registry.npm.qima-inc.com/@types/jest/download/@types/jest-26.0.20.tgz"
  integrity sha1-zS8nAuz2noa1huH1Ijpg5FQFYwc=
  dependencies:
    jest-diff "^26.0.0"
    pretty-format "^26.0.0"

"@types/jest@^26.0.24":
  version "26.0.24"
  resolved "http://registry.npm.qima-inc.com/@types/jest/download/@types/jest-26.0.24.tgz#943d11976b16739185913a1936e0de0c4a7d595a"
  integrity sha1-lD0Rl2sWc5GFkToZNuDeDEp9WVo=
  dependencies:
    jest-diff "^26.0.0"
    pretty-format "^26.0.0"

"@types/json-schema@^7.0.3":
  version "7.0.6"
  resolved "http://registry.npm.qima-inc.com/@types/json-schema/download/@types/json-schema-7.0.6.tgz"
  integrity sha1-9MfsQ+gbMZqYFRFQMXCfJph4kfA=

"@types/json5@^0.0.29":
  version "0.0.29"
  resolved "http://registry.npm.qima-inc.com/@types/json5/download/@types/json5-0.0.29.tgz"
  integrity sha1-7ihweulOEdK4J7y+UnC86n8+ce4=

"@types/keygrip@*":
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/@types/keygrip/download/@types/keygrip-1.0.2.tgz"
  integrity sha1-UTq/0lbXrQvx7hhzYGMXszsbKnI=

"@types/koa-bodyparser@^4.2.2":
  version "4.3.0"
  resolved "http://registry.npm.qima-inc.com/@types/koa-bodyparser/download/@types/koa-bodyparser-4.3.0.tgz"
  integrity sha1-VOzWYsRfOk+p3oSVKN5fyKsmm6U=
  dependencies:
    "@types/koa" "*"

"@types/koa-compose@*", "@types/koa-compose@^3.2.5":
  version "3.2.5"
  resolved "http://registry.npm.qima-inc.com/@types/koa-compose/download/@types/koa-compose-3.2.5.tgz"
  integrity sha1-hesugKxQvpXzfM+MQHwJu+NGjp0=
  dependencies:
    "@types/koa" "*"

"@types/koa-router@*", "@types/koa-router@^7.0.40":
  version "7.4.1"
  resolved "http://registry.npm.qima-inc.com/@types/koa-router/download/@types/koa-router-7.4.1.tgz"
  integrity sha1-NwKkyr5FWMxO7HDVV0rMBL7s/3w=
  dependencies:
    "@types/koa" "*"

"@types/koa-router@7.0.40":
  version "7.0.40"
  resolved "http://registry.npm.qima-inc.com/@types/koa-router/download/@types/koa-router-7.0.40.tgz"
  integrity sha1-llTbxDN1oDgMRMScRQS02/w+Tmo=
  dependencies:
    "@types/koa" "*"

"@types/koa@*", "@types/koa@^2.0.46":
  version "2.11.6"
  resolved "http://registry.npm.qima-inc.com/@types/koa/download/@types/koa-2.11.6.tgz"
  integrity sha1-twMMqmtEr4AcKuoTunfXSv90hNU=
  dependencies:
    "@types/accepts" "*"
    "@types/content-disposition" "*"
    "@types/cookies" "*"
    "@types/http-assert" "*"
    "@types/http-errors" "*"
    "@types/keygrip" "*"
    "@types/koa-compose" "*"
    "@types/node" "*"

"@types/koa@2.0.48":
  version "2.0.48"
  resolved "http://registry.npm.qima-inc.com/@types/koa/download/@types/koa-2.0.48.tgz"
  integrity sha1-KRYngwKdPl34tYxV9r8NNfePw58=
  dependencies:
    "@types/accepts" "*"
    "@types/cookies" "*"
    "@types/http-assert" "*"
    "@types/keygrip" "*"
    "@types/koa-compose" "*"
    "@types/node" "*"

"@types/lodash@4.14.122":
  version "4.14.122"
  resolved "http://registry.npm.qima-inc.com/@types/lodash/download/@types/lodash-4.14.122.tgz"
  integrity sha1-PjE5TDjPHllJ+1TBGSy8QG8VLGw=

"@types/lodash@^4.14.123":
  version "4.14.167"
  resolved "http://registry.npm.qima-inc.com/@types/lodash/download/@types/lodash-4.14.167.tgz"
  integrity sha1-zn14VT48iG1OpkPDfsftwg8Wdl4=

"@types/lodash@^4.14.161":
  version "4.14.198"
  resolved "http://registry.npm.qima-inc.com/@types/lodash/download/@types/lodash-4.14.198.tgz#4d27465257011aedc741a809f1269941fa2c5d4c"
  integrity sha1-TSdGUlcBGu3HQagJ8SaZQfosXUw=

"@types/long@^4.0.1":
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/@types/long/download/@types/long-4.0.1.tgz"
  integrity sha1-RZxl+hhn2v5qjzIsTFFpVmPMVek=

"@types/microtime@^2.1.0":
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/@types/microtime/download/@types/microtime-2.1.0.tgz"
  integrity sha1-rb2Z9QGoXIhpXrHv01FYEPJWOTI=

"@types/mime@*", "@types/mime@^2.0.3":
  version "2.0.3"
  resolved "http://registry.npm.qima-inc.com/@types/mime/download/@types/mime-2.0.3.tgz"
  integrity sha1-yJO3NyHbc2mZQ7/DZTsd63+qSjo=

"@types/node@*":
  version "14.14.22"
  resolved "http://registry.npm.qima-inc.com/@types/node/download/@types/node-14.14.22.tgz"
  integrity sha1-DSnzgkcsTM872W/wzkfa9be4Sxg=

"@types/node@11.9.5":
  version "11.9.5"
  resolved "http://registry.npm.qima-inc.com/@types/node/download/@types/node-11.9.5.tgz"
  integrity sha1-AR7s6dP4OagGtjlz4ij4WWe3ntM=

"@types/node@^12.12.47":
  version "12.19.15"
  resolved "http://registry.npm.qima-inc.com/@types/node/download/@types/node-12.19.15.tgz"
  integrity sha1-DefpePtD22LaNp2xjqCIpjZzwYI=

"@types/node@^13.7.0":
  version "13.13.40"
  resolved "http://registry.npm.qima-inc.com/@types/node/download/@types/node-13.13.40.tgz"
  integrity sha1-9lXvMnNizIORLy5pM23cYqJKn4g=

"@types/normalize-package-data@^2.4.0":
  version "2.4.0"
  resolved "http://registry.npm.qima-inc.com/@types/normalize-package-data/download/@types/normalize-package-data-2.4.0.tgz"
  integrity sha1-5IbQ2XOW15vu3QpuM/RTT/a0lz4=

"@types/nunjucks@*", "@types/nunjucks@^3.0.0":
  version "3.1.3"
  resolved "http://registry.npm.qima-inc.com/@types/nunjucks/download/@types/nunjucks-3.1.3.tgz"
  integrity sha1-Vfor9v00ZBVFpmhiFzJP3mbTEWQ=

"@types/parse-json@^4.0.0":
  version "4.0.2"
  resolved "http://registry.npm.qima-inc.com/@types/parse-json/download/@types/parse-json-4.0.2.tgz#5950e50960793055845e956c427fc2b0d70c5239"
  integrity sha1-WVDlCWB5MFWEXpVsQn/CsNcMUjk=

"@types/pino-std-serializers@*":
  version "2.4.1"
  resolved "http://registry.npm.qima-inc.com/@types/pino-std-serializers/download/@types/pino-std-serializers-2.4.1.tgz"
  integrity sha1-+L1SognIs8l9FTOxuif1fIFjgr8=
  dependencies:
    "@types/node" "*"

"@types/pino@^6.3.0":
  version "6.3.5"
  resolved "http://registry.npm.qima-inc.com/@types/pino/download/@types/pino-6.3.5.tgz"
  integrity sha1-dYc0lC+SNKYFf+ZtD3zX4wnWF/c=
  dependencies:
    "@types/node" "*"
    "@types/pino-std-serializers" "*"
    "@types/sonic-boom" "*"

"@types/prettier@^2.0.0":
  version "2.1.6"
  resolved "http://registry.npm.qima-inc.com/@types/prettier/download/@types/prettier-2.1.6.tgz"
  integrity sha1-9LHvp4To20ec24sUQD4hRLHp/wM=

"@types/qs@*":
  version "6.9.5"
  resolved "http://registry.npm.qima-inc.com/@types/qs/download/@types/qs-6.9.5.tgz"
  integrity sha1-Q0cRvdSete5p2QwdZ8NUqajssYs=

"@types/range-parser@*":
  version "1.2.3"
  resolved "http://registry.npm.qima-inc.com/@types/range-parser/download/@types/range-parser-1.2.3.tgz"
  integrity sha1-fuMwunyq+5gJC+zoal7kQRWQTCw=

"@types/serve-static@*":
  version "1.13.8"
  resolved "http://registry.npm.qima-inc.com/@types/serve-static/download/@types/serve-static-1.13.8.tgz"
  integrity sha1-hREp1DRDPHCCFIV0/+wmPVgwnEY=
  dependencies:
    "@types/mime" "*"
    "@types/node" "*"

"@types/sonic-boom@*":
  version "0.7.0"
  resolved "http://registry.npm.qima-inc.com/@types/sonic-boom/download/@types/sonic-boom-0.7.0.tgz"
  integrity sha1-ODNwNik5kqHfZd0xYavd+PubcXY=
  dependencies:
    "@types/node" "*"

"@types/stack-utils@^2.0.0":
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/@types/stack-utils/download/@types/stack-utils-2.0.0.tgz"
  integrity sha1-cDZkC04hzC8lmugmzoQ9J32tjP8=

"@types/url-parse@^1.4.3":
  version "1.4.3"
  resolved "http://registry.npm.qima-inc.com/@types/url-parse/download/@types/url-parse-1.4.3.tgz"
  integrity sha1-+6SdkPg0lRywAKZ07+49byCWgyk=

"@types/yargs-parser@*":
  version "20.2.0"
  resolved "http://registry.npm.qima-inc.com/@types/yargs-parser/download/@types/yargs-parser-20.2.0.tgz"
  integrity sha1-3T5mmboyN/A0jNCF5GmHgCBIQvk=

"@types/yargs@^15.0.0":
  version "15.0.12"
  resolved "http://registry.npm.qima-inc.com/@types/yargs/download/@types/yargs-15.0.12.tgz"
  integrity sha1-YjTOPj4/oyxdswGhcPlqWZyWDXQ=
  dependencies:
    "@types/yargs-parser" "*"

"@typescript-eslint/eslint-plugin@^2.34.0":
  version "2.34.0"
  resolved "http://registry.npm.qima-inc.com/@typescript-eslint/eslint-plugin/download/@typescript-eslint/eslint-plugin-2.34.0.tgz"
  integrity sha1-b4zopGx96kpvHRcdK7j7rm2sK+k=
  dependencies:
    "@typescript-eslint/experimental-utils" "2.34.0"
    functional-red-black-tree "^1.0.1"
    regexpp "^3.0.0"
    tsutils "^3.17.1"

"@typescript-eslint/experimental-utils@2.34.0":
  version "2.34.0"
  resolved "http://registry.npm.qima-inc.com/@typescript-eslint/experimental-utils/download/@typescript-eslint/experimental-utils-2.34.0.tgz"
  integrity sha1-01JLZEzbQO687KZ/jPPkzJyPmA8=
  dependencies:
    "@types/json-schema" "^7.0.3"
    "@typescript-eslint/typescript-estree" "2.34.0"
    eslint-scope "^5.0.0"
    eslint-utils "^2.0.0"

"@typescript-eslint/parser@^2.34.0":
  version "2.34.0"
  resolved "http://registry.npm.qima-inc.com/@typescript-eslint/parser/download/@typescript-eslint/parser-2.34.0.tgz"
  integrity sha1-UCUmMMoxloVCDpo5ygX+GFola8g=
  dependencies:
    "@types/eslint-visitor-keys" "^1.0.0"
    "@typescript-eslint/experimental-utils" "2.34.0"
    "@typescript-eslint/typescript-estree" "2.34.0"
    eslint-visitor-keys "^1.1.0"

"@typescript-eslint/typescript-estree@2.34.0":
  version "2.34.0"
  resolved "http://registry.npm.qima-inc.com/@typescript-eslint/typescript-estree/download/@typescript-eslint/typescript-estree-2.34.0.tgz"
  integrity sha1-FK62NTs57wcyzH8bgoUpSTfPN9U=
  dependencies:
    debug "^4.1.1"
    eslint-visitor-keys "^1.1.0"
    glob "^7.1.6"
    is-glob "^4.0.1"
    lodash "^4.17.15"
    semver "^7.3.2"
    tsutils "^3.17.1"

"@youzan-cloud/utils@3.2.5":
  version "3.2.5"
  resolved "http://registry.npm.qima-inc.com/@youzan-cloud/utils/download/@youzan-cloud/utils-3.2.5.tgz#39c4d50a8aef531c1ed59906afbeb9c0521235b9"
  integrity sha1-OcTVCorvUxwe1ZkGr765wFISNbk=
  dependencies:
    tslib "^2.5.3"

"@youzan-types/astroboy@0.0.39":
  version "0.0.39"
  resolved "http://registry.npm.qima-inc.com/@youzan-types/astroboy/download/@youzan-types/astroboy-0.0.39.tgz"
  integrity sha1-FJq7GgjKpq99jdTIF5ioRMC5gkE=
  dependencies:
    "@types/koa" "*"
    "@types/koa-router" "*"
    "@types/node" "*"

"@youzan-types/iron-base@0.1.21":
  version "0.1.21"
  resolved "http://registry.npm.qima-inc.com/@youzan-types/iron-base/download/@youzan-types/iron-base-0.1.21.tgz"
  integrity sha1-jVxt2XAn390e9ceFcW2UOmHqGMQ=
  dependencies:
    "@youzan-types/youzan-framework" "^0.5.2-bete.1"
    zan-ajax "^2.1.0"

"@youzan-types/youzan-framework@^0.5.2-bete.1":
  version "0.5.2"
  resolved "http://registry.npm.qima-inc.com/@youzan-types/youzan-framework/download/@youzan-types/youzan-framework-0.5.2.tgz"
  integrity sha1-ekUMAvsRgQWqycYOPs5k6mZGaq8=
  dependencies:
    "@types/ioredis" "*"
    "@types/nunjucks" "*"
    "@youzan-types/astroboy" "0.0.39"
    zan-ajax "*"

"@youzan/apollox-dynamic-components@0.0.4":
  version "0.0.4"
  resolved "http://registry.npm.qima-inc.com/@youzan/apollox-dynamic-components/download/@youzan/apollox-dynamic-components-0.0.4.tgz"
  integrity sha1-K8TvUqPAVEHTvonNzalo2UUCL/A=

"@youzan/apollox@2.2.4":
  version "2.2.4"
  resolved "http://registry.npm.qima-inc.com/@youzan/apollox/download/@youzan/apollox-2.2.4.tgz#a00a113bc3348f7b0c93cce24d37aad2b4dcf334"
  integrity sha1-oAoRO8M0j3sMk8ziTTeq0rTc8zQ=
  dependencies:
    "@youzan/lightning-request" "^1.0.3"
    "@youzan/youzan-env" "1.0.2"
    debug "4.1.1"
    properties-parser "0.3.1"

"@youzan/astroboy-mock@^2.0.2":
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/@youzan/astroboy-mock/download/@youzan/astroboy-mock-2.0.2.tgz#4c692d869c30f8f467e7b4f76095ba02e59d6dcb"
  integrity sha1-TGkthpww+PRn57T3YJW6AuWdbcs=
  dependencies:
    "@types/jest" "^26.0.14"
    accepts "^1.3.7"
    chalk "^4.1.0"
    commander "^6.0.0"
    cookies "^0.8.0"
    debug "^4.1.1"
    delegates "^1.0.0"
    detect-port "^1.3.0"
    execa "^5.1.1"
    fs-extra "^9.0.1"
    get-ready "^2.0.1"
    is-type-of "^1.2.1"
    jest "^26.6.3"
    jest-cli "^26.6.3"
    jsdom "^16.4.0"
    merge-descriptors "^1.0.1"
    methods "^1.1.2"
    micromatch "^4.0.4"
    mm "^3.2.0"
    supertest "^4.0.2"
    ts-jest "^26.1.4"

"@youzan/biz-plugin-logger@1.1.1":
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/@youzan/biz-plugin-logger/download/@youzan/biz-plugin-logger-1.1.1.tgz#f8b4c35cf324afa0394cd08bf31e5e7ce8b0db07"
  integrity sha1-+LTDXPMkr6A5TNCL8x5efOiw2wc=

"@youzan/cdn-fallback@0.0.8":
  version "0.0.8"
  resolved "http://registry.npm.qima-inc.com/@youzan/cdn-fallback/download/@youzan/cdn-fallback-0.0.8.tgz"
  integrity sha1-h02EOA2Y2YK6XiEJen/F4CMavSo=

"@youzan/config-matcher@1.2.4":
  version "1.2.4"
  resolved "http://registry.npm.qima-inc.com/@youzan/config-matcher/download/@youzan/config-matcher-1.2.4.tgz#395aec277413bdb0a2c2db4339572f4053cc045e"
  integrity sha1-OVrsJ3QTvbCiwttDOVcvQFPMBF4=

"@youzan/crypto@1.0.1":
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/@youzan/crypto/download/@youzan/crypto-1.0.1.tgz#89d2cc1cb877ec8b41d1f5e59e2f566c14c31ffc"
  integrity sha1-idLMHLh37ItB0fXlni9WbBTDH/w=

"@youzan/crypto@^1.1.1":
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/@youzan/crypto/download/@youzan/crypto-1.1.1.tgz#42b09fa737a6a251bfb628cce369fb4ce23d5662"
  integrity sha1-QrCfpzemolG/tijM42n7TOI9VmI=

"@youzan/dubbo-client-node@0.3.4":
  version "0.3.4"
  resolved "http://registry.npm.qima-inc.com/@youzan/dubbo-client-node/download/@youzan/dubbo-client-node-0.3.4.tgz"
  integrity sha1-+uI1R5hM8hg508ZDw3pZMoo+IFs=
  dependencies:
    byte "^2.0.0"
    hessian.js "^2.9.0"
    ip "^1.1.5"

"@youzan/dubbo-client-node@1.0.0":
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/dubbo-client-node/download/@youzan/dubbo-client-node-1.0.0.tgz#38b0c3ca343c8d19770d1da43f0e49dcf6a068bd"
  integrity sha1-OLDDyjQ8jRl3DR2kPw5J3PagaL0=
  dependencies:
    byte "^2.0.0"
    hessian.js "^2.9.0"
    ip "^1.1.5"

"@youzan/dubbo-invoke@1.3.3", "@youzan/dubbo-invoke@^1.1.0":
  version "1.3.3"
  resolved "http://registry.npm.qima-inc.com/@youzan/dubbo-invoke/download/@youzan/dubbo-invoke-1.3.3.tgz#19194e880aefdd5f3801c7ca37ca2d0f822f3fbc"
  integrity sha1-GRlOiArv3V84AcfKN8otD4IvP7w=
  dependencies:
    "@youzan/dubbo-client-node" "1.0.0"
    "@youzan/rontgen" "2.4.1"
    "@youzan/youzan-env" "1.1.0"
    graceful-error "^1.1.5"
    zan-json-parse "1.0.2"

"@youzan/dubbo-invoke@^0.0.7":
  version "0.0.7"
  resolved "http://registry.npm.qima-inc.com/@youzan/dubbo-invoke/download/@youzan/dubbo-invoke-0.0.7.tgz"
  integrity sha1-RCGCvMQuF2MwBIu6OnGCapJg/p0=
  dependencies:
    "@youzan/dubbo-client-node" "0.3.4"
    "@youzan/youzan-env" "0.0.6"
    graceful-config "0.0.1"
    graceful-error "^1.1.5"
    zan-json-parse "1.0.2"

"@youzan/dubbo-invoke@^1.1.1":
  version "1.2.1"
  resolved "http://registry.npm.qima-inc.com/@youzan/dubbo-invoke/download/@youzan/dubbo-invoke-1.2.1.tgz#5af4c9349626af318e0c7996f84c79ad895d8148"
  integrity sha1-WvTJNJYmrzGODHmW+Ex5rYldgUg=
  dependencies:
    "@youzan/dubbo-client-node" "1.0.0"
    "@youzan/rontgen" "2.0.0"
    "@youzan/youzan-env" "1.0.2"
    graceful-config "^1.0.0"
    graceful-error "^1.1.5"
    zan-json-parse "1.0.2"

"@youzan/eslint-config-koko@3.0.1":
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/@youzan/eslint-config-koko/download/@youzan/eslint-config-koko-3.0.1.tgz"
  integrity sha1-Pw9oVzWH82xg9wUSN5jPxvXmVRk=
  dependencies:
    "@typescript-eslint/eslint-plugin" "^2.34.0"
    "@typescript-eslint/parser" "^2.34.0"
    "@youzan/eslint-plugin-koko" "3.0.1"
    "@youzan/eslint-plugin-location-check" "^1.0.0"
    eslint-config-airbnb-base "^14.1.0"
    eslint-config-prettier "^6.11.0"
    eslint-plugin-import "^2.20.2"
    eslint-plugin-vue "^6.2.2"
    eslint-plugin-youzan "^0.3.0"

"@youzan/eslint-plugin-koko@3.0.1":
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/@youzan/eslint-plugin-koko/download/@youzan/eslint-plugin-koko-3.0.1.tgz"
  integrity sha1-8yhC+asl+nmID462dunir0kmWEE=

"@youzan/eslint-plugin-location-check@^1.0.0":
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/eslint-plugin-location-check/download/@youzan/eslint-plugin-location-check-1.0.0.tgz"
  integrity sha1-6gyUeUowXgWn88difMIrevdcTkY=
  dependencies:
    requireindex "~1.1.0"

"@youzan/femp-runtime-node@^2.2.2":
  version "2.2.3"
  resolved "http://registry.npm.qima-inc.com/@youzan/femp-runtime-node/download/@youzan/femp-runtime-node-2.2.3.tgz#059eb5bfee2d81a551648086ff10daedc85f5ce9"
  integrity sha1-BZ61v+4tgaVRZICG/xDa7chfXOk=
  dependencies:
    "@youzan/montage-config-fetcher" "1.2.0"
    lodash "^4.17.21"

"@youzan/guide-b-utils@2.0.10":
  version "2.0.10"
  resolved "http://registry.npm.qima-inc.com/@youzan/guide-b-utils/download/@youzan/guide-b-utils-2.0.10.tgz#4ccc716cf34e30958f205441eb1c627eaa240261"
  integrity sha1-TMxxbPNOMJWPIFRB6xxifqokAmE=
  dependencies:
    "@youzan/salesman-params-handler" "2.0.2"
    "@youzan/utils" "^3.0.7"
    "@youzan/youzan-skynet-logger" "^3.2.2"

"@youzan/h5-cpns-injector-plugin@1.1.0":
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/h5-cpns-injector-plugin/download/@youzan/h5-cpns-injector-plugin-1.1.0.tgz#521f6bfa8af39202ef4fea78f3b6fead2676fcba"
  integrity sha1-Uh9r+orzkgLvT+p487b+rSZ2/Lo=
  dependencies:
    "@youzan/utils" "^2.3.18"
    "@youzan/utils-shop" "^1.1.1"
    zan-ajax "^3.0.0"

"@youzan/im-base-pure@0.2.3":
  version "0.2.3"
  resolved "http://registry.npm.qima-inc.com/@youzan/im-base-pure/download/@youzan/im-base-pure-0.2.3.tgz"
  integrity sha1-YlLnAur+oHErZC/RvwcRlOK/+W8=
  dependencies:
    axios "~0.16"
    lodash "^4.17.5"

"@youzan/iron-base@5.3.23":
  version "5.3.23"
  resolved "http://registry.npm.qima-inc.com/@youzan/iron-base/download/@youzan/iron-base-5.3.23.tgz#67101e632711d285eb00cbcfc84fcf891880d01b"
  integrity sha1-ZxAeYycR0oXrAMvPyE/PiRiA0Bs=
  dependencies:
    "@youzan/biz-plugin-logger" "1.1.1"
    "@youzan/cdn-fallback" "0.0.8"
    "@youzan/platform-constants" "1.3.2"
    "@youzan/plugin-biz-shop-config" "^1.0.1"
    "@youzan/plugin-h5-access" "1.0.3"
    "@youzan/plugin-h5-acl" "1.8.4"
    "@youzan/plugin-h5-base" "1.4.0"
    "@youzan/plugin-h5-cdn" "1.2.6"
    "@youzan/plugin-h5-exception" "1.2.4"
    "@youzan/plugin-h5-kdtId" "1.3.2"
    "@youzan/plugin-h5-platform" "1.5.5"
    "@youzan/plugin-h5-session" "1.4.2"
    "@youzan/plugin-h5-shop" "1.0.4"
    "@youzan/plugin-h5-traffic-identification" "2.0.0"
    "@youzan/plugin-h5-url" "^1.0.0"
    "@youzan/plugin-plus-progress" "^1.0.0"
    "@youzan/plugin-plus-view" "^1.0.1"
    "@youzan/skynet-logger-plugin" "1.1.0"
    "@youzan/utils" "2.4.0"
    "@youzan/utils-shop" "0.1.0"
    "@youzan/youzan-diy-check" "0.0.16"
    "@youzan/youzan-framework" "5.2.4"
    "@youzan/youzan-vip-domain" "^3.1.4"
    debug "^3.1.0"
    lodash "^4.17.5"
    zan-ajax "^1.1.7"

"@youzan/lightning-request@^1.0.3":
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/@youzan/lightning-request/download/@youzan/lightning-request-1.0.3.tgz#330a6fd775148b1c1d0ba9fedd699fef6b1f59c9"
  integrity sha1-Mwpv13UUixwdC6n+3Wmf72sfWck=
  dependencies:
    "@youzan/sync-rpc" "^1.3.8"

"@youzan/matrix-cli@2.0.2":
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/@youzan/matrix-cli/download/@youzan/matrix-cli-2.0.2.tgz#ec0b8027241947006e23e818276d729384fca34a"
  integrity sha1-7AuAJyQZRwBuI+gYJ21yk4T8o0o=
  dependencies:
    "@youzan/monitor-core" "1.0.1"
    "@youzan/youzan-env" "0.0.6"
    chalk "4.1.0"
    cli-table "0.3.1"
    commander "5.1.0"
    graceful-config "0.0.1"
    lightning-request "0.2.1"
    lodash "4.17.19"
    minimist "1.2.5"
    open "7.1.0"
    pm2 "4.4.0"

"@youzan/monitor-core@1.0.1":
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/@youzan/monitor-core/download/@youzan/monitor-core-1.0.1.tgz"
  integrity sha1-fBj61nLkxqxKSiJuWFnxnUGKmCw=
  dependencies:
    "@grpc/grpc-js" "^1.1.5"
    "@grpc/proto-loader" "0.5.4"
    "@youzan/youzan-skynet-logger" "^0.0.3"
    debug "^4.1.1"
    split2 "^3.1.1"

"@youzan/monitor@1.0.3":
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/@youzan/monitor/download/@youzan/monitor-1.0.3.tgz#5917d8cbcc4651820ef75dce336ad3224bb715b5"
  integrity sha1-WRfYy8xGUYIO913OM2rTIku3FbU=
  dependencies:
    "@youzan/youzan-env" "^1.0.0"
    "@youzan/youzan-skynet-logger" "^1.0.0"
    debug "^4.1.1"
    fast-json-stringify "^2.2.3"
    split2 "^3.1.1"

"@youzan/montage-config-fetcher@1.2.0":
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/montage-config-fetcher/download/@youzan/montage-config-fetcher-1.2.0.tgz#c8a9b80cd38fe1398062d965b79f2aa810c380bd"
  integrity sha1-yKm4DNOP4TmAYtllt58qqBDDgL0=
  dependencies:
    "@youzan/dubbo-invoke" "^1.1.0"
    "@youzan/utils-shop" "^1.1.1"
    "@youzan/youzan-env" "^1.0.0"
    "@youzan/youzan-oss" "^1.0.0"
    ioredis "^4.26.0"
    lodash "^4.17.21"
    lru-cache "^6.0.0"
    zan-ajax "^3.0.0"

"@youzan/node-qiniu@1.0.2":
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/@youzan/node-qiniu/download/@youzan/node-qiniu-1.0.2.tgz"
  integrity sha1-W7svdSphPPA2pUDuLb8bmtH3hi8=
  dependencies:
    agentkeepalive "3.3.0"
    crc32 "0.2.2"
    encodeurl "^1.0.1"
    formstream "1.1.0"
    mime "2.3.1"
    tunnel-agent "0.6.0"
    urllib "2.22.0"

"@youzan/platform-constants@1.3.2":
  version "1.3.2"
  resolved "http://registry.npm.qima-inc.com/@youzan/platform-constants/download/@youzan/platform-constants-1.3.2.tgz#e37a7102d73b0a98663182b880ed5eeb75ffb363"
  integrity sha1-43pxAtc7CphmMYK4gO1e63X/s2M=

"@youzan/plugin-biz-shop-config@^1.0.1":
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/@youzan/plugin-biz-shop-config/download/@youzan/plugin-biz-shop-config-1.0.1.tgz"
  integrity sha1-nGGLVmRkdTpLfwyS74ASiu8iK48=

"@youzan/plugin-h5-abtest@2.0.1":
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/@youzan/plugin-h5-abtest/download/@youzan/plugin-h5-abtest-2.0.1.tgz"
  integrity sha1-uX7Zs5Esbk3cSQll3ZHF7jbB+ow=

"@youzan/plugin-h5-access@1.0.3":
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/@youzan/plugin-h5-access/download/@youzan/plugin-h5-access-1.0.3.tgz#b42569f847638b62b93cd72a21a457d89577d28e"
  integrity sha1-tCVp+Edji2K5PNcqIaRX2JV30o4=

"@youzan/plugin-h5-acl@1.8.4":
  version "1.8.4"
  resolved "http://registry.npm.qima-inc.com/@youzan/plugin-h5-acl/download/@youzan/plugin-h5-acl-1.8.4.tgz#eaeac14e791c764c5c41a8433a3758baeae36366"
  integrity sha1-6urBTnkcdkxcQahDOjdYuurjY2Y=
  dependencies:
    "@youzan/utils" "^2.3.12"

"@youzan/plugin-h5-auto-salesman@1.0.0":
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/plugin-h5-auto-salesman/download/@youzan/plugin-h5-auto-salesman-1.0.0.tgz"
  integrity sha1-D3G4THtxgvYQU/n6Ro08t97KfmA=
  dependencies:
    "@youzan/plugin-h5-base" "^1.4.0"

"@youzan/plugin-h5-base@1.4.0", "@youzan/plugin-h5-base@^1.4.0":
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/plugin-h5-base/download/@youzan/plugin-h5-base-1.4.0.tgz"
  integrity sha1-pIX/KtCKS+s+atZSy/JzPTnaUmw=
  dependencies:
    "@youzan/utils" "^2.3.12"
    lodash "^4.17.5"

"@youzan/plugin-h5-base@1.4.2":
  version "1.4.2"
  resolved "http://registry.npm.qima-inc.com/@youzan/plugin-h5-base/download/@youzan/plugin-h5-base-1.4.2.tgz#49fd3f6d482b73aa8ec2e61f889f7c639142db92"
  integrity sha1-Sf0/bUgrc6qOwuYfiJ98Y5FC25I=
  dependencies:
    "@youzan/utils" "^2.3.12"
    lodash "^4.17.5"

"@youzan/plugin-h5-cdn@1.2.6":
  version "1.2.6"
  resolved "http://registry.npm.qima-inc.com/@youzan/plugin-h5-cdn/download/@youzan/plugin-h5-cdn-1.2.6.tgz#64e11768671753e2cb631c65f61a6929149b83a4"
  integrity sha1-ZOEXaGcXU+LLYxxl9hppKRSbg6Q=

"@youzan/plugin-h5-cdn@^1.2.0":
  version "1.2.1"
  resolved "http://registry.npm.qima-inc.com/@youzan/plugin-h5-cdn/download/@youzan/plugin-h5-cdn-1.2.1.tgz#5b4fd44f79ff976faa923234f45c3d5ca54af2d5"
  integrity sha1-W0/UT3n/l2+qkjI09Fw9XKVK8tU=

"@youzan/plugin-h5-cdn@^1.2.2":
  version "1.2.2"
  resolved "http://registry.npm.qima-inc.com/@youzan/plugin-h5-cdn/download/@youzan/plugin-h5-cdn-1.2.2.tgz#76bd1e0a745170ece2a954cd4a785bdda557d587"
  integrity sha1-dr0eCnRRcOziqVTNSnhb3aVX1Yc=

"@youzan/plugin-h5-ecloud@1.3.0":
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/plugin-h5-ecloud/download/@youzan/plugin-h5-ecloud-1.3.0.tgz#1b778f81560a7b75cd542ad55f12d2fac5c82caf"
  integrity sha1-G3ePgVYKe3XNVCrVXxLS+sXILK8=
  dependencies:
    "@youzan/plugin-h5-base" "1.4.2"

"@youzan/plugin-h5-enter-shop-core@1.0.4":
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/@youzan/plugin-h5-enter-shop-core/download/@youzan/plugin-h5-enter-shop-core-1.0.4.tgz#fb0118cfa84cad6c2341ed4ed5ca64f6ded04c49"
  integrity sha1-+wEYz6hMrWwjQe1O1cpk9t7QTEk=
  dependencies:
    "@youzan/utils" "2.4.0"
    "@youzan/utils-shop" "1.1.1"
    "@youzan/youzan-skynet-logger" "3.2.1"
    lodash "^4.17.15"

"@youzan/plugin-h5-enter-shop@3.0.4":
  version "3.0.4"
  resolved "http://registry.npm.qima-inc.com/@youzan/plugin-h5-enter-shop/download/@youzan/plugin-h5-enter-shop-3.0.4.tgz#0e785b826496617e4f6dc9dc3edfe8dad4856e54"
  integrity sha1-DnhbgmSWYX5PbcncPt/o2tSFblQ=
  dependencies:
    "@youzan/plugin-h5-enter-shop-core" "1.0.4"
    "@youzan/utils" "2.4.0"
    "@youzan/utils-shop" "1.1.1"
    "@youzan/youzan-skynet-logger" "1.0.0"
    lodash "^4.17.15"

"@youzan/plugin-h5-exception@1.2.4":
  version "1.2.4"
  resolved "http://registry.npm.qima-inc.com/@youzan/plugin-h5-exception/download/@youzan/plugin-h5-exception-1.2.4.tgz#be361549df91ee06f0603eff4502cfa2ebe77aa2"
  integrity sha1-vjYVSd+R7gbwYD7/RQLPouvneqI=

"@youzan/plugin-h5-global-theme@1.2.10":
  version "1.2.10"
  resolved "http://registry.npm.qima-inc.com/@youzan/plugin-h5-global-theme/download/@youzan/plugin-h5-global-theme-1.2.10.tgz#9d79cdbc4ce29352edb777ce6430d7d3625a45fe"
  integrity sha1-nXnNvEzik1Ltt3fOZDDX02JaRf4=

"@youzan/plugin-h5-global-theme@^1.1.1":
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/@youzan/plugin-h5-global-theme/download/@youzan/plugin-h5-global-theme-1.1.1.tgz#da504f7341f2cb769124fdde27488c3f1957ca5a"
  integrity sha1-2lBPc0Hyy3aRJP3eJ0iMPxlXylo=

"@youzan/plugin-h5-hummer@^1.3.0":
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/plugin-h5-hummer/download/@youzan/plugin-h5-hummer-1.3.0.tgz#e369504ab117a2a2fd1e98ff83da7f13456dadbb"
  integrity sha1-42lQSrEXoqL9Hpj/g9p/E0Vtrbs=

"@youzan/plugin-h5-inject-console@0.0.2":
  version "0.0.2"
  resolved "http://registry.npm.qima-inc.com/@youzan/plugin-h5-inject-console/download/@youzan/plugin-h5-inject-console-0.0.2.tgz#3906ee6669969463ddd6d67a0ec33bfbf5e6b0e2"
  integrity sha1-OQbuZmmWlGPd1tZ6DsM7+/XmsOI=

"@youzan/plugin-h5-kdtId@1.3.2":
  version "1.3.2"
  resolved "http://registry.npm.qima-inc.com/@youzan/plugin-h5-kdtId/download/@youzan/plugin-h5-kdtId-1.3.2.tgz#b7a62df8f9baca0a897704cbd1a4774a841c2a44"
  integrity sha1-t6Yt+Pm6ygqJdwTL0aR3SoQcKkQ=
  dependencies:
    lodash "^4.17.5"

"@youzan/plugin-h5-language@1.0.2":
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/@youzan/plugin-h5-language/download/@youzan/plugin-h5-language-1.0.2.tgz#15a31e1e4cd016642fe25d89f48eee74bb864bd7"
  integrity sha1-FaMeHkzQFmQv4l2J9I7udLuGS9c=
  dependencies:
    "@youzan/plugin-h5-session" "1.4.6"
    "@youzan/utils" "2.4.0"
    "@youzan/utils-shop" "2.7.9"

"@youzan/plugin-h5-platform@1.5.5":
  version "1.5.5"
  resolved "http://registry.npm.qima-inc.com/@youzan/plugin-h5-platform/download/@youzan/plugin-h5-platform-1.5.5.tgz#9a436079ca7773c7021c7cf3c59c2ff7b2fd06b9"
  integrity sha1-mkNgecp3c8cCHHzzxZwv97L9Brk=
  dependencies:
    "@youzan/platform-constants" "1.3.2"
    graceful-json-parse "^0.0.2"
    ua-parser-js "^0.7.39"

"@youzan/plugin-h5-ranta-config@1.6.2":
  version "1.6.2"
  resolved "http://registry.npm.qima-inc.com/@youzan/plugin-h5-ranta-config/download/@youzan/plugin-h5-ranta-config-1.6.2.tgz#65257cdb03daae67ccc243d9f3eea55f7925552f"
  integrity sha1-ZSV82wParmfMwkPZ8+6lX3klVS8=

"@youzan/plugin-h5-redirect@2.1.0":
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/plugin-h5-redirect/download/@youzan/plugin-h5-redirect-2.1.0.tgz"
  integrity sha1-7reoz2wd7X/2jlPuZ70lSLUQEVE=
  dependencies:
    "@youzan/plugin-h5-cdn" "^1.2.0"
    "@youzan/utils" "^2.3.13"
    "@youzan/youzan-vip-domain" "^2.1.3"

"@youzan/plugin-h5-session@1.4.2":
  version "1.4.2"
  resolved "http://registry.npm.qima-inc.com/@youzan/plugin-h5-session/download/@youzan/plugin-h5-session-1.4.2.tgz#5e4ff513bcb406fbd8ee33470c81b6a008a24192"
  integrity sha1-Xk/1E7y0BvvY7jNHDIG2oAiiQZI=
  dependencies:
    "@youzan/utils" "^2.4.0"
    "@youzan/youzan-vip-domain" "^3.1.0"
    cookies "~0.7.0"

"@youzan/plugin-h5-session@1.4.6":
  version "1.4.6"
  resolved "http://registry.npm.qima-inc.com/@youzan/plugin-h5-session/download/@youzan/plugin-h5-session-1.4.6.tgz#e0e72cfc45a00f2c8cac4ecef6db5d68fd4b9884"
  integrity sha1-4Ocs/EWgDyyMrE7O9ttdaP1LmIQ=
  dependencies:
    "@youzan/utils" "^2.4.0"
    "@youzan/youzan-vip-domain" "^3.1.0"
    cookies "~0.7.0"

"@youzan/plugin-h5-shop@1.0.4":
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/@youzan/plugin-h5-shop/download/@youzan/plugin-h5-shop-1.0.4.tgz#88c6f5c9e2db5ffcbf5e1c1dcc0499b33ce03fa0"
  integrity sha1-iMb1yeLbX/y/XhwdzASZszzgP6A=
  dependencies:
    "@youzan/plugin-biz-shop-config" "^1.0.1"
    "@youzan/plugin-h5-base" "^1.4.0"

"@youzan/plugin-h5-shop@^1.4.1":
  version "1.4.1"
  resolved "http://registry.npm.qima-inc.com/@youzan/plugin-h5-shop/download/@youzan/plugin-h5-shop-1.4.1.tgz#f623757abb4abfd8b9819cbd33433b69c87e294a"
  integrity sha1-9iN1ertKv9i5gZy9M0M7ach+KUo=
  dependencies:
    "@youzan/plugin-biz-shop-config" "^1.0.1"
    "@youzan/plugin-h5-base" "^1.4.0"
    "@youzan/plugin-h5-cdn" "^1.2.2"
    "@youzan/plugin-h5-global-theme" "^1.1.1"
    "@youzan/utils" "^3.0.3"

"@youzan/plugin-h5-traffic-identification@2.0.0":
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/plugin-h5-traffic-identification/download/@youzan/plugin-h5-traffic-identification-2.0.0.tgz"
  integrity sha1-9hZt3um7z0MGbYc8ux+iCE36kRQ=

"@youzan/plugin-h5-url@^1.0.0":
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/plugin-h5-url/download/@youzan/plugin-h5-url-1.0.0.tgz"
  integrity sha1-/pGkV34ZpMO+OEeP9NjmQNSAfU0=

"@youzan/plugin-h5-yun-shop-config@1.2.0":
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/plugin-h5-yun-shop-config/download/@youzan/plugin-h5-yun-shop-config-1.2.0.tgz"
  integrity sha1-8cR/PnIzfdxwhB8gW1f4gYwpOow=

"@youzan/plugin-plus-progress@^1.0.0":
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/@youzan/plugin-plus-progress/download/@youzan/plugin-plus-progress-1.0.1.tgz"
  integrity sha1-5AOpncy6EaHmqcRbmk9YXDq+HNE=

"@youzan/plugin-plus-view@^1.0.1":
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/@youzan/plugin-plus-view/download/@youzan/plugin-plus-view-1.0.1.tgz"
  integrity sha1-qZSgJ13iyWEXYzXJYwRWNx7gwzE=

"@youzan/retail-h5-monitor-logger@1.0.3":
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/@youzan/retail-h5-monitor-logger/download/@youzan/retail-h5-monitor-logger-1.0.3.tgz#1ed2ce66d8d3ba9452bbaca15aeeda0cb2faf005"
  integrity sha1-HtLOZtjTupRSu6yhWu7aDLL68AU=
  dependencies:
    "@youzan/plugin-h5-session" "1.4.6"
    "@youzan/utils" "2.4.0"
    "@youzan/utils-shop" "2.7.9"

"@youzan/rontgen-core@1.0.0":
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/rontgen-core/download/@youzan/rontgen-core-1.0.0.tgz#acf62001085499142f39e05cb9fe93ba8950be77"
  integrity sha1-rPYgAQhUmRQvOeBcuf6TuolQvnc=

"@youzan/rontgen-core@2.1.0":
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/rontgen-core/download/@youzan/rontgen-core-2.1.0.tgz#1f0a5f14c4a92800f913c87b3a787baf5d6759d0"
  integrity sha1-HwpfFMSpKAD5E8h7Onh7r11nWdA=

"@youzan/rontgen@2.0.0":
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/rontgen/download/@youzan/rontgen-2.0.0.tgz#0d4d46b537e968880f35e965f6ba2c95bdb8a2a0"
  integrity sha1-DU1GtTfpaIgPNell9roslb24oqA=
  dependencies:
    "@youzan/rontgen-core" "1.0.0"
    "@youzan/youzan-env" "^1.0.1"
    "@youzan/youzan-skynet-logger" "1.0.0"
    debug "^4.3.1"
    typescript "^3.8.2"

"@youzan/rontgen@2.4.0":
  version "2.4.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/rontgen/download/@youzan/rontgen-2.4.0.tgz#08edc9d7a725c3e85b4ded28149b7d6a8bf8ca36"
  integrity sha1-CO3J16clw+hbTe0oFJt9aov4yjY=
  dependencies:
    "@youzan/rontgen-core" "2.1.0"
    "@youzan/youzan-env" "^1.0.1"
    "@youzan/youzan-skynet-logger" "1.0.0"
    debug "^4.3.1"

"@youzan/rontgen@2.4.1":
  version "2.4.1"
  resolved "http://registry.npm.qima-inc.com/@youzan/rontgen/download/@youzan/rontgen-2.4.1.tgz#1f9f5f8cfa41a78bbd804579116375ee7068fc28"
  integrity sha1-H59fjPpBp4u9gEV5EWN17nBo/Cg=
  dependencies:
    "@youzan/rontgen-core" "2.1.0"
    "@youzan/youzan-env" "^1.0.1"
    "@youzan/youzan-skynet-logger" "1.0.0"
    debug "^4.3.1"

"@youzan/runtime@3.1.3":
  version "3.1.3"
  resolved "http://registry.npm.qima-inc.com/@youzan/runtime/download/@youzan/runtime-3.1.3.tgz#d6f58dcb823124a0db46c08ab0312fe2ee926f7a"
  integrity sha1-1vWNy4IxJKDbRsCKsDEv4u6Sb3o=
  dependencies:
    "@youzan/apollox" "2.2.4"
    "@youzan/monitor" "1.0.3"
    "@youzan/youzan-env" "1.0.2"
    "@youzan/youzan-skynet-logger" "3.2.2"
    dnscache "1.0.2"
    graceful-error "^1.1.5"
    lightning-request "1.0.0"
    lru-cache "5.1.1"
    tslib "1.10.0"

"@youzan/salesman-params-handler@2.0.2":
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/@youzan/salesman-params-handler/download/@youzan/salesman-params-handler-2.0.2.tgz#dbc1920b71691a00893104a53e78d3a1d76b8d81"
  integrity sha1-28GSC3FpGgCJMQSlPnjToddrjYE=
  dependencies:
    "@youzan/utils" "^3.0.6"

"@youzan/scrm-common@1.1.0":
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/scrm-common/download/@youzan/scrm-common-1.1.0.tgz#3ce483fea6dbabfdde3d4715ea3afc5713fcb890"
  integrity sha1-POSD/qbbq/3ePUcV6jr8VxP8uJA=
  dependencies:
    "@types/lodash" "^4.14.161"
    "@youzan/utils" "^2.3.30"
    lodash "^4.17.19"

"@youzan/skynet-logger-plugin@1.1.0":
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/skynet-logger-plugin/download/@youzan/skynet-logger-plugin-1.1.0.tgz"
  integrity sha1-cS2y6yRKzoXwJLQ9LzpkbfeGlyI=
  dependencies:
    util "^0.12.2"

"@youzan/sync-rpc@^1.3.8":
  version "1.3.8"
  resolved "http://registry.npm.qima-inc.com/@youzan/sync-rpc/download/@youzan/sync-rpc-1.3.8.tgz#a552dd1ecce15f1cae7a6e12a2373f3a6a0013f0"
  integrity sha1-pVLdHszhXxyuem4Sojc/OmoAE/A=
  dependencies:
    get-port "^3.1.0"

"@youzan/utils-shop@0.1.0":
  version "0.1.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/utils-shop/download/@youzan/utils-shop-0.1.0.tgz"
  integrity sha1-HBQKAN/3xRMJSHQki8Mqx8HWqqY=

"@youzan/utils-shop@1.1.1":
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/@youzan/utils-shop/download/@youzan/utils-shop-1.1.1.tgz#46ae8ba35fbd54514aa05781b2823c87e8a87cf2"
  integrity sha1-Rq6Lo1+9VFFKoFeBsoI8h+iofPI=

"@youzan/utils-shop@1.2.0":
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/utils-shop/download/@youzan/utils-shop-1.2.0.tgz#eae6fc2d6752c788e02f820783c758b4e9e5b23e"
  integrity sha1-6ub8LWdSx4jgL4IHg8dYtOnlsj4=

"@youzan/utils-shop@2.7.9":
  version "2.7.9"
  resolved "http://registry.npm.qima-inc.com/@youzan/utils-shop/download/@youzan/utils-shop-2.7.9.tgz#227a1e2f1d549e33506f2b2c54d94f87ad2e2276"
  integrity sha1-InoeLx1UnjNQbyssVNlPh60uInY=

"@youzan/utils-shop@^1.1.1":
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/utils-shop/download/@youzan/utils-shop-1.3.0.tgz#e75df528b8cbd7d9b7dac77f1784ceeae2b9573d"
  integrity sha1-5131KLjL19m32sd/F4TO6uK5Vz0=

"@youzan/utils@2.4.0":
  version "2.4.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/utils/download/@youzan/utils-2.4.0.tgz"
  integrity sha1-nw8851grD3605PX4v7JTJn0BW/4=
  dependencies:
    "@types/url-parse" "^1.4.3"
    big.js "^5.2.2"
    compare-versions "^3.4.0"
    exif-js "^2.3.0"
    fecha "^3.0.2"
    lodash "^4.17.11"
    query-string "5"
    raf "^3.4.1"
    tslib "^1.9.3"
    url-parse "^1.4.4"
    zan-jquery "^1.0.2"

"@youzan/utils@2.x", "@youzan/utils@^2.3.30":
  version "2.4.6"
  resolved "http://registry.npm.qima-inc.com/@youzan/utils/download/@youzan/utils-2.4.6.tgz#92b6b94cd6c2a7da0d300d6ecaa775b005f9442b"
  integrity sha1-kra5TNbCp9oNMA1uyqd1sAX5RCs=
  dependencies:
    "@types/url-parse" "^1.4.3"
    big.js "^5.2.2"
    compare-versions "^3.4.0"
    exif-js "^2.3.0"
    fecha "^3.0.2"
    lodash "^4.17.11"
    query-string "5"
    raf "^3.4.1"
    tslib "^1.9.3"
    url-parse "^1.4.4"
    zan-jquery "^1.0.2"

"@youzan/utils@3.2.5":
  version "3.2.5"
  resolved "http://registry.npm.qima-inc.com/@youzan/utils/download/@youzan/utils-3.2.5.tgz#06dad83cd437a783c32eddca73d7eb2b28eb94df"
  integrity sha1-BtrYPNQ3p4PDLt3Kc9frKyjrlN8=
  dependencies:
    "@youzan-cloud/utils" "3.2.5"
    big.js "^6.0.2"
    exif-js "^2.3.0"
    fecha "^4.2.0"
    query-string "5"
    raf "^3.4.1"
    tslib "^2.5.3"
    url-parse "^1.4.7"

"@youzan/utils@^2.3.12", "@youzan/utils@^2.3.13", "@youzan/utils@^2.3.18", "@youzan/utils@^2.4.0":
  version "2.4.1"
  resolved "http://registry.npm.qima-inc.com/@youzan/utils/download/@youzan/utils-2.4.1.tgz"
  integrity sha1-uMfLdHFMJ3zy8Ht6XuKcCO3htjE=
  dependencies:
    "@types/url-parse" "^1.4.3"
    big.js "^5.2.2"
    compare-versions "^3.4.0"
    exif-js "^2.3.0"
    fecha "^3.0.2"
    lodash "^4.17.11"
    query-string "5"
    raf "^3.4.1"
    tslib "^1.9.3"
    url-parse "^1.4.4"
    zan-jquery "^1.0.2"

"@youzan/utils@^3.0.3":
  version "3.0.7"
  resolved "http://registry.npm.qima-inc.com/@youzan/utils/download/@youzan/utils-3.0.7.tgz#f60d4320b43783cd8a6c55ed6c903d070114ecce"
  integrity sha1-9g1DILQ3g82KbFXtbJA9BwEU7M4=
  dependencies:
    "@types/url-parse" "^1.4.3"
    big.js "^6.0.2"
    exif-js "^2.3.0"
    fecha "^4.2.0"
    query-string "^6.13.7"
    raf "^3.4.1"
    tslib "^2.0.3"
    url-parse "^1.4.7"

"@youzan/utils@^3.0.6", "@youzan/utils@^3.0.7":
  version "3.0.20"
  resolved "http://registry.npm.qima-inc.com/@youzan/utils/download/@youzan/utils-3.0.20.tgz#e8ad5f5678ff750dbded8a8a176116edfb9ee403"
  integrity sha1-6K1fVnj/dQ297YqKF2EW7fue5AM=
  dependencies:
    "@types/url-parse" "^1.4.3"
    big.js "^6.0.2"
    exif-js "^2.3.0"
    fecha "^4.2.0"
    query-string "^6.13.7"
    raf "^3.4.1"
    tslib "^2.0.3"
    url-parse "^1.4.7"

"@youzan/wholesale-plugin@1.0.0":
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/wholesale-plugin/download/@youzan/wholesale-plugin-1.0.0.tgz#ad7cbbf771e07f115dee81b5259d731da8d73e40"
  integrity sha1-rXy793HgfxFd7oG1JZ1zHajXPkA=
  dependencies:
    "@youzan/utils" "^2.4.0"

"@youzan/youzan-diy-check@0.0.16":
  version "0.0.16"
  resolved "http://registry.npm.qima-inc.com/@youzan/youzan-diy-check/download/@youzan/youzan-diy-check-0.0.16.tgz"
  integrity sha1-e2ltfI4qXWrFs5Ajx5sMrA4OhJI=
  dependencies:
    zan-ajax "3.0.0"

"@youzan/youzan-env@0.0.6":
  version "0.0.6"
  resolved "http://registry.npm.qima-inc.com/@youzan/youzan-env/download/@youzan/youzan-env-0.0.6.tgz"
  integrity sha1-mLsxmBfDP1bOFuLzMS2WxWDNH+4=
  dependencies:
    ip "1.1.5"

"@youzan/youzan-env@1.0.0", "@youzan/youzan-env@^1.0.0":
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/youzan-env/download/@youzan/youzan-env-1.0.0.tgz#80d95db7b50076599dc697984235c4de19d39195"
  integrity sha1-gNldt7UAdlmdxpeYQjXE3hnTkZU=
  dependencies:
    ip "1.1.5"

"@youzan/youzan-env@1.0.2", "@youzan/youzan-env@^1.0.1", "@youzan/youzan-env@^1.0.2":
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/@youzan/youzan-env/download/@youzan/youzan-env-1.0.2.tgz#b07fc222ced1f2b7dc1ff70a4cdce513765f9723"
  integrity sha1-sH/CIs7R8rfcH/cKTNzlE3ZflyM=
  dependencies:
    ip "1.1.5"

"@youzan/youzan-env@1.1.0":
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/youzan-env/download/@youzan/youzan-env-1.1.0.tgz#82e58f862789dfb36245f34967a99558e9e6b4e3"
  integrity sha1-guWPhieJ37NiRfNJZ6mVWOnmtOM=
  dependencies:
    ip "1.1.5"

"@youzan/youzan-framework@5.2.4":
  version "5.2.4"
  resolved "http://registry.npm.qima-inc.com/@youzan/youzan-framework/download/@youzan/youzan-framework-5.2.4.tgz#e7b91f5e763efe0e05bc08f3fbe12fe7c05e9766"
  integrity sha1-57kfXnY+/g4FvAjz++Ev58Bel2Y=
  dependencies:
    "@hapi/joi" "15.1.1"
    "@types/cookies" "^0.7.1"
    "@types/debug" "^4.1.4"
    "@types/hapi__joi" "^15.0.3"
    "@types/ioredis" "^4.0.10"
    "@types/ip" "^1.1.0"
    "@types/koa-bodyparser" "^4.2.2"
    "@types/koa-router" "^7.0.40"
    "@types/microtime" "^2.1.0"
    "@types/nunjucks" "^3.0.0"
    "@types/pino" "^6.3.0"
    "@youzan/dubbo-invoke" "1.3.3"
    "@youzan/node-qiniu" "1.0.2"
    "@youzan/runtime" "3.1.3"
    "@youzan/youzan-rontgen" "3.1.0"
    astroboy "3.1.5"
    debug "3.1.0"
    graceful-error "^1.1.5"
    ioredis "4.3.0"
    ip "1.1.5"
    jsonp-body "1.0.0"
    lightning-request "^1.0.0"
    lodash "4.17.10"
    microtime "3.0.0"
    nunjucks "3.1.6"
    pino "6.3.2"
    pino-http "5.2.0"
    pino-pretty "4.0.0"
    tslib "^1.10.0"
    utility-types "^3.7.0"
    zan-ajax "2.1.0"

"@youzan/youzan-oss@^1.0.0":
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/youzan-oss/download/@youzan/youzan-oss-1.0.0.tgz#47fec927f0142779b3deee0d1db42457dc213e5e"
  integrity sha1-R/7JJ/AUJ3mz3u4NHbQkV9whPl4=
  dependencies:
    "@types/mime" "^2.0.3"
    "@youzan/youzan-env" "1.0.0"
    chalk "^4.1.0"
    graceful-error "^1.1.5"
    is-stream "^2.0.0"
    mime "^2.4.6"
    urllib "^2.36.1"
    xml2js "^0.4.23"

"@youzan/youzan-rontgen@3.1.0":
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/youzan-rontgen/download/@youzan/youzan-rontgen-3.1.0.tgz#1216cf7315e55f5a91ee9940e5eacf67a3dc0d3e"
  integrity sha1-EhbPcxXlX1qR7plA5erPZ6PcDT4=
  dependencies:
    "@youzan/rontgen" "2.4.0"
    "@youzan/youzan-env" "^1.0.2"
    lodash.merge "^4.6.2"

"@youzan/youzan-skynet-logger@1.0.0", "@youzan/youzan-skynet-logger@^1.0.0":
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/youzan-skynet-logger/download/@youzan/youzan-skynet-logger-1.0.0.tgz#1a13495bd237824a045abeaee977b2b2ac5994e9"
  integrity sha1-GhNJW9I3gkoEWr6u6XeysqxZlOk=
  dependencies:
    debug "4.1.1"
    ip "1.1.5"

"@youzan/youzan-skynet-logger@3.2.1":
  version "3.2.1"
  resolved "http://registry.npm.qima-inc.com/@youzan/youzan-skynet-logger/download/@youzan/youzan-skynet-logger-3.2.1.tgz#ff2ef9789442b30468899f6a3a76754886262fc4"
  integrity sha1-/y75eJRCswRoiZ9qOnZ1SIYmL8Q=
  dependencies:
    "@youzan/youzan-env" "^1.0.2"
    ip "^1.1.5"

"@youzan/youzan-skynet-logger@3.2.2", "@youzan/youzan-skynet-logger@^3.2.2":
  version "3.2.2"
  resolved "http://registry.npm.qima-inc.com/@youzan/youzan-skynet-logger/download/@youzan/youzan-skynet-logger-3.2.2.tgz#60163d904fff616934edbdac4d57a2cc53a6cd8e"
  integrity sha1-YBY9kE//YWk07b2sTVeizFOmzY4=
  dependencies:
    "@youzan/youzan-env" "^1.0.2"
    ip "^1.1.5"

"@youzan/youzan-skynet-logger@^0.0.3":
  version "0.0.3"
  resolved "http://registry.npm.qima-inc.com/@youzan/youzan-skynet-logger/download/@youzan/youzan-skynet-logger-0.0.3.tgz"
  integrity sha1-utO+iuokWXOJ1uK5hf9LJzNwEnw=
  dependencies:
    debug "4.1.1"
    ip "1.1.5"

"@youzan/youzan-vip-domain@^2.1.3":
  version "2.1.7"
  resolved "http://registry.npm.qima-inc.com/@youzan/youzan-vip-domain/download/@youzan/youzan-vip-domain-2.1.7.tgz"
  integrity sha1-zYKeOTo8VppDufjgQjyxcvyUYCo=
  dependencies:
    "@youzan/dubbo-invoke" "^0.0.7"
    lightning-request "0.2.0"

"@youzan/youzan-vip-domain@^3.1.0":
  version "3.1.1"
  resolved "http://registry.npm.qima-inc.com/@youzan/youzan-vip-domain/download/@youzan/youzan-vip-domain-3.1.1.tgz#296469afd98ead8023b53278bb4cd0b57b6997cc"
  integrity sha1-KWRpr9mOrYAjtTJ4u0zQtXtpl8w=
  dependencies:
    "@youzan/dubbo-invoke" "^1.1.1"
    lightning-request "^1.0.0"

"@youzan/youzan-vip-domain@^3.1.4":
  version "3.1.4"
  resolved "http://registry.npm.qima-inc.com/@youzan/youzan-vip-domain/download/@youzan/youzan-vip-domain-3.1.4.tgz#6bdc042cb91fe715665bda3c18b065893f7cf1c8"
  integrity sha1-a9wELLkf5xVmW9o8GLBliT988cg=
  dependencies:
    "@youzan/dubbo-invoke" "^1.1.1"
    lightning-request "^1.0.0"

"@youzan/yz-aes@1.0.0":
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/yz-aes/download/@youzan/yz-aes-1.0.0.tgz"
  integrity sha1-z8U5D/ulhHwQnnEqpjoieZ4tvKA=

"@youzan/zan-node-tracker@2.0.12":
  version "2.0.12"
  resolved "http://registry.npm.qima-inc.com/@youzan/zan-node-tracker/download/@youzan/zan-node-tracker-2.0.12.tgz#4c7bd405d65d442737660ba8dcf79ad478f21985"
  integrity sha1-THvUBdZdRCc3Zguo3Pea1HjyGYU=
  dependencies:
    "@youzan/utils" "^3.0.3"
    lodash "^4.17.21"
    uuid "^8.3.2"

"@youzan/zan-pay-core@1.1.2":
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/@youzan/zan-pay-core/download/@youzan/zan-pay-core-1.1.2.tgz#f00f1363e53d02aab2ca81552f0b4b9c698f91ce"
  integrity sha1-8A8TY+U9AqqyyoFVLwtLnGmPkc4=
  dependencies:
    "@babel/runtime" "7.x"
    "@youzan/crypto" "1.0.1"
    "@youzan/utils" "2.x"
    core-js "3.x"
    eventemitter3 "4.x"
    miniprogram-api-typings "2.x"
    tslib "1.x"

a-sync-waterfall@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/a-sync-waterfall/download/a-sync-waterfall-1.0.1.tgz"
  integrity sha1-dba2qnJZi0l6El56J3DxT0yKH6c=

abab@^2.0.3:
  version "2.0.5"
  resolved "http://registry.npm.qima-inc.com/abab/download/abab-2.0.5.tgz"
  integrity sha1-wLZ4+zLWD8EhnHhNaoJv44Wut5o=

abbrev@1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/abbrev/download/abbrev-1.1.1.tgz"
  integrity sha1-+PLIh60Qv2f2NPAFtph/7TF5qsg=

abort-controller@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/abort-controller/download/abort-controller-3.0.0.tgz"
  integrity sha1-6vVNU7YrrkE46AnKIlyEOabvs5I=
  dependencies:
    event-target-shim "^5.0.0"

accepts@^1.2.2, accepts@^1.3.7:
  version "1.3.7"
  resolved "http://registry.npm.qima-inc.com/accepts/download/accepts-1.3.7.tgz"
  integrity sha1-UxvHJlF6OytB+FACHGzBXqq1B80=
  dependencies:
    mime-types "~2.1.24"
    negotiator "0.6.2"

acorn-globals@^6.0.0:
  version "6.0.0"
  resolved "http://registry.npm.qima-inc.com/acorn-globals/download/acorn-globals-6.0.0.tgz"
  integrity sha1-Rs3Tnw+P8IqHZhm1X1rIptx3C0U=
  dependencies:
    acorn "^7.1.1"
    acorn-walk "^7.1.1"

acorn-jsx@^5.2.0:
  version "5.3.1"
  resolved "http://registry.npm.qima-inc.com/acorn-jsx/download/acorn-jsx-5.3.1.tgz"
  integrity sha1-/IZh4Rt6wVOcR9v+oucrOvNNJns=

acorn-walk@^7.1.1:
  version "7.2.0"
  resolved "http://registry.npm.qima-inc.com/acorn-walk/download/acorn-walk-7.2.0.tgz"
  integrity sha1-DeiJpgEgOQmw++B7iTjcIdLpZ7w=

acorn@^7.1.1:
  version "7.4.1"
  resolved "http://registry.npm.qima-inc.com/acorn/download/acorn-7.4.1.tgz"
  integrity sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo=

address@>=0.0.1, address@^1.0.1:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/address/download/address-1.1.2.tgz"
  integrity sha1-vxEWycdYxRt6kz0pa3LCIe2UKLY=

agent-base@4, agent-base@^4.2.0, agent-base@^4.3.0:
  version "4.3.0"
  resolved "http://registry.npm.qima-inc.com/agent-base/download/agent-base-4.3.0.tgz"
  integrity sha1-gWXwHENgCbzK0LHRIvBe13Dvxu4=
  dependencies:
    es6-promisify "^5.0.0"

agent-base@6, agent-base@^6.0.0, agent-base@^6.0.2:
  version "6.0.2"
  resolved "http://registry.npm.qima-inc.com/agent-base/download/agent-base-6.0.2.tgz"
  integrity sha1-Sf/1hXfP7j83F2/qtMIuAPhtf3c=
  dependencies:
    debug "4"

agent-base@~4.2.1:
  version "4.2.1"
  resolved "http://registry.npm.qima-inc.com/agent-base/download/agent-base-4.2.1.tgz"
  integrity sha1-2J5ZmfeXh1Z0wH2H8mD8Qeg+jKk=
  dependencies:
    es6-promisify "^5.0.0"

agentkeepalive@3.3.0:
  version "3.3.0"
  resolved "http://registry.npm.qima-inc.com/agentkeepalive/download/agentkeepalive-3.3.0.tgz"
  integrity sha1-bV3lgpr9O+JxIgGjknX9EcZRhXw=
  dependencies:
    humanize-ms "^1.2.1"

aggregate-error@^3.0.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/aggregate-error/download/aggregate-error-3.1.0.tgz#92670ff50f5359bdb7a3e0d40d0ec30c5737687a"
  integrity sha1-kmcP9Q9TWb23o+DUDQ7DDFc3aHo=
  dependencies:
    clean-stack "^2.0.0"
    indent-string "^4.0.0"

ajv@^6.10.0, ajv@^6.10.2, ajv@^6.11.0, ajv@^6.12.3, ajv@^6.12.6:
  version "6.12.6"
  resolved "http://registry.npm.qima-inc.com/ajv/download/ajv-6.12.6.tgz"
  integrity sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

amd-loader@0.0.8:
  version "0.0.8"
  resolved "http://registry.npm.qima-inc.com/amd-loader/download/amd-loader-0.0.8.tgz"
  integrity sha1-ECKSgEDlZ+jmpvtYvaBS+BkYkgY=

amp-message@~0.1.1:
  version "0.1.2"
  resolved "http://registry.npm.qima-inc.com/amp-message/download/amp-message-0.1.2.tgz#a78f1c98995087ad36192a41298e4db49e3dfc45"
  integrity sha1-p48cmJlQh602GSpBKY5NtJ49/EU=
  dependencies:
    amp "0.3.1"

amp@0.3.1, amp@~0.3.1:
  version "0.3.1"
  resolved "http://registry.npm.qima-inc.com/amp/download/amp-0.3.1.tgz#6adf8d58a74f361e82c1fa8d389c079e139fc47d"
  integrity sha1-at+NWKdPNh6CwfqNOJwHnhOfxH0=

ansi-align@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/ansi-align/download/ansi-align-2.0.0.tgz"
  integrity sha1-w2rsy6VjuJzrVW82kPCx2eNUf38=
  dependencies:
    string-width "^2.0.0"

ansi-colors@^3.2.1:
  version "3.2.4"
  resolved "http://registry.npm.qima-inc.com/ansi-colors/download/ansi-colors-3.2.4.tgz#e3a3da4bfbae6c86a9c285625de124a234026fbf"
  integrity sha1-46PaS/uubIapwoViXeEkojQCb78=

ansi-colors@^4.1.1:
  version "4.1.3"
  resolved "http://registry.npm.qima-inc.com/ansi-colors/download/ansi-colors-4.1.3.tgz#37611340eb2243e70cc604cad35d63270d48781b"
  integrity sha1-N2ETQOsiQ+cMxgTK011jJw1IeBs=

ansi-escapes@^4.2.1, ansi-escapes@^4.3.1:
  version "4.3.1"
  resolved "http://registry.npm.qima-inc.com/ansi-escapes/download/ansi-escapes-4.3.1.tgz"
  integrity sha1-pcR8xDGB8fOP/XB2g3cA05VSKmE=
  dependencies:
    type-fest "^0.11.0"

ansi-escapes@^4.3.0:
  version "4.3.2"
  resolved "http://registry.npm.qima-inc.com/ansi-escapes/download/ansi-escapes-4.3.2.tgz#6b2291d1db7d98b6521d5f1efa42d0f3a9feb65e"
  integrity sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=
  dependencies:
    type-fest "^0.21.3"

ansi-regex@^2.0.0:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/ansi-regex/download/ansi-regex-2.1.1.tgz"
  integrity sha1-w7M6te42DYbg5ijwRorn7yfWVN8=

ansi-regex@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/ansi-regex/download/ansi-regex-3.0.0.tgz"
  integrity sha1-7QMXwyIGT3lGbAKWa922Bas32Zg=

ansi-regex@^4.1.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/ansi-regex/download/ansi-regex-4.1.0.tgz"
  integrity sha1-i5+PCM8ay4Q3Vqg5yox+MWjFGZc=

ansi-regex@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/ansi-regex/download/ansi-regex-5.0.0.tgz"
  integrity sha1-OIU59VF5vzkznIGvMKZU1p+Hy3U=

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "http://registry.npm.qima-inc.com/ansi-regex/download/ansi-regex-5.0.1.tgz#082cb2c89c9fe8659a311a53bd6a4dc5301db304"
  integrity sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=

ansi-styles@^2.2.1:
  version "2.2.1"
  resolved "http://registry.npm.qima-inc.com/ansi-styles/download/ansi-styles-2.2.1.tgz"
  integrity sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=

ansi-styles@^3.2.0, ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "http://registry.npm.qima-inc.com/ansi-styles/download/ansi-styles-3.2.1.tgz"
  integrity sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "http://registry.npm.qima-inc.com/ansi-styles/download/ansi-styles-4.3.0.tgz"
  integrity sha1-7dgDYornHATIWuegkG7a00tkiTc=
  dependencies:
    color-convert "^2.0.1"

ansi-term@>=0.0.2:
  version "0.0.2"
  resolved "http://registry.npm.qima-inc.com/ansi-term/download/ansi-term-0.0.2.tgz"
  integrity sha1-/XU++kvq2g6smZgbxSo/b/AZ3rc=
  dependencies:
    x256 ">=0.0.1"

ansicolors@~0.3.2:
  version "0.3.2"
  resolved "http://registry.npm.qima-inc.com/ansicolors/download/ansicolors-0.3.2.tgz"
  integrity sha1-ZlWX3oap/+Oqm/vmyuXG6kJrSXk=

any-promise@^1.0.0, any-promise@^1.1.0, any-promise@^1.3.0:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/any-promise/download/any-promise-1.3.0.tgz"
  integrity sha1-q8av7tzqUugJzcA3au0845Y10X8=

anymatch@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/anymatch/download/anymatch-2.0.0.tgz"
  integrity sha1-vLJLTzeTTZqnrBe0ra+J58du8us=
  dependencies:
    micromatch "^3.1.4"
    normalize-path "^2.1.1"

anymatch@^3.0.3:
  version "3.1.1"
  resolved "http://registry.npm.qima-inc.com/anymatch/download/anymatch-3.1.1.tgz"
  integrity sha1-xV7PAhheJGklk5kxDBc84xIzsUI=
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

anymatch@~3.1.2:
  version "3.1.2"
  resolved "http://registry.npm.qima-inc.com/anymatch/download/anymatch-3.1.2.tgz#c0557c096af32f106198f4f4e2a383537e378716"
  integrity sha1-wFV8CWrzLxBhmPT04qODU343hxY=
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

argparse@^1.0.7:
  version "1.0.10"
  resolved "http://registry.npm.qima-inc.com/argparse/download/argparse-1.0.10.tgz"
  integrity sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=
  dependencies:
    sprintf-js "~1.0.2"

argparse@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/argparse/download/argparse-2.0.1.tgz#246f50f3ca78a3240f6c997e8a9bd1eac49e4b38"
  integrity sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=

args@^5.0.1:
  version "5.0.1"
  resolved "http://registry.npm.qima-inc.com/args/download/args-5.0.1.tgz"
  integrity sha1-S/KY35CkeZoJUhNixXknjML912E=
  dependencies:
    camelcase "5.0.0"
    chalk "2.4.2"
    leven "2.1.0"
    mri "1.1.4"

arr-diff@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/arr-diff/download/arr-diff-4.0.0.tgz"
  integrity sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=

arr-flatten@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/arr-flatten/download/arr-flatten-1.1.0.tgz"
  integrity sha1-NgSLv/TntH4TZkQxbJlmnqWukfE=

arr-union@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/arr-union/download/arr-union-3.1.0.tgz"
  integrity sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=

array-filter@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/array-filter/download/array-filter-1.0.0.tgz"
  integrity sha1-uveeYubvTCpMC4MSMtr/7CUfnYM=

array-includes@^3.1.1:
  version "3.1.2"
  resolved "http://registry.npm.qima-inc.com/array-includes/download/array-includes-3.1.2.tgz"
  integrity sha1-qNsD4LiMjGrt3EnLEy+byrTr+cg=
  dependencies:
    call-bind "^1.0.0"
    define-properties "^1.1.3"
    es-abstract "^1.18.0-next.1"
    get-intrinsic "^1.0.1"
    is-string "^1.0.5"

array-timsort@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/array-timsort/download/array-timsort-1.0.3.tgz#3c9e4199e54fb2b9c3fe5976396a21614ef0d926"
  integrity sha1-PJ5BmeVPsrnD/ll2OWohYU7w2SY=

array-unique@^0.3.2:
  version "0.3.2"
  resolved "http://registry.npm.qima-inc.com/array-unique/download/array-unique-0.3.2.tgz"
  integrity sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=

array.prototype.flat@^1.2.3:
  version "1.2.4"
  resolved "http://registry.npm.qima-inc.com/array.prototype.flat/download/array.prototype.flat-1.2.4.tgz"
  integrity sha1-bvY4tDMSvUAbTGGZ/ex+LcnpoSM=
  dependencies:
    call-bind "^1.0.0"
    define-properties "^1.1.3"
    es-abstract "^1.18.0-next.1"

arrify@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/arrify/download/arrify-1.0.1.tgz"
  integrity sha1-iYUI2iIm84DfkEcoRWhJwVAaSw0=

arrify@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/arrify/download/arrify-2.0.1.tgz"
  integrity sha1-yWVekzHgq81YjSp8rX6ZVvZnAfo=

asap@^2.0.3, asap@^2.0.6, asap@~2.0.3:
  version "2.0.6"
  resolved "http://registry.npm.qima-inc.com/asap/download/asap-2.0.6.tgz"
  integrity sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY=

asn1@~0.2.3:
  version "0.2.4"
  resolved "http://registry.npm.qima-inc.com/asn1/download/asn1-0.2.4.tgz"
  integrity sha1-jSR136tVO7M+d7VOWeiAu4ziMTY=
  dependencies:
    safer-buffer "~2.1.0"

assert-never@^1.2.1:
  version "1.2.1"
  resolved "http://registry.npm.qima-inc.com/assert-never/download/assert-never-1.2.1.tgz#11f0e363bf146205fb08193b5c7b90f4d1cf44fe"
  integrity sha1-EfDjY78UYgX7CBk7XHuQ9NHPRP4=

assert-plus@1.0.0, assert-plus@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/assert-plus/download/assert-plus-1.0.0.tgz"
  integrity sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=

assign-symbols@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/assign-symbols/download/assign-symbols-1.0.0.tgz"
  integrity sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=

ast-types@0.x.x:
  version "0.14.2"
  resolved "http://registry.npm.qima-inc.com/ast-types/download/ast-types-0.14.2.tgz"
  integrity sha1-YAuILfhYPjzU8t9fog+oN1nUvf0=
  dependencies:
    tslib "^2.0.1"

ast-types@^0.13.2:
  version "0.13.4"
  resolved "http://registry.npm.qima-inc.com/ast-types/download/ast-types-0.13.4.tgz#ee0d77b343263965ecc3fb62da16e7222b2b6782"
  integrity sha1-7g13s0MmOWXsw/ti2hbnIisrZ4I=
  dependencies:
    tslib "^2.0.1"

astral-regex@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/astral-regex/download/astral-regex-1.0.0.tgz"
  integrity sha1-bIw/uCfdQ+45GPJ7gngqt2WKb9k=

astral-regex@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/astral-regex/download/astral-regex-2.0.0.tgz#483143c567aeed4785759c0865786dc77d7d2e31"
  integrity sha1-SDFDxWeu7UeFdZwIZXhtx319LjE=

astroboy-cli@0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.qima-inc.com/astroboy-cli/download/astroboy-cli-0.2.0.tgz"
  integrity sha1-WY4fqt66CGR+TRA47MpXqyD1RR4=
  dependencies:
    blessed "^0.1.81"
    blessed-contrib "^4.8.5"
    boxen "^1.3.0"
    chalk "^2.3.2"
    commander "^2.15.1"
    fs-extra "^5.0.0"
    nodemon "^1.17.3"
    opn "^5.3.0"
    ora "^2.0.0"
    shelljs "^0.8.2"
    ts-node "^7.0.0"
    tsconfig-paths "^3.4.2"
    typescript "3.7.5"

astroboy@3.1.5:
  version "3.1.5"
  resolved "http://registry.npm.qima-inc.com/astroboy/download/astroboy-3.1.5.tgz#f06541d495c24b3f90b91d36e87ca338ea9e8ea2"
  integrity sha1-8GVB1JXCSz+QuR026HyjOOqejqI=
  dependencies:
    "@types/fs-extra" "^9.0.1"
    "@types/koa" "^2.0.46"
    "@types/koa-compose" "^3.2.5"
    "@types/lodash" "^4.14.123"
    ajv "^6.12.6"
    complete-assign "0.0.2"
    fast-glob "2.2.6"
    fs-extra "9.0.1"
    koa "2.5.0"
    koa-body "2.5.0"
    koa-bodyparser "4.2.1"
    koa-compose "4.1.0"
    koa-router "7.4.0"
    koa-static "4.0.2"
    lodash "4.17.11"
    path-matching "0.0.2"
    tslib "^2.2.0"
    xss "0.3.7"

async-each@^1.0.1:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/async-each/download/async-each-1.0.3.tgz"
  integrity sha1-tyfb+H12UWAvBvTUrDh/R9kbDL8=

async-limiter@~1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/async-limiter/download/async-limiter-1.0.1.tgz#dd379e94f0db8310b08291f9d64c3209766617fd"
  integrity sha1-3TeelPDbgxCwgpH51kwyCXZmF/0=

async-listener@^0.6.0:
  version "0.6.10"
  resolved "http://registry.npm.qima-inc.com/async-listener/download/async-listener-0.6.10.tgz#a7c97abe570ba602d782273c0de60a51e3e17cbc"
  integrity sha1-p8l6vlcLpgLXgic8DeYKUePhfLw=
  dependencies:
    semver "^5.3.0"
    shimmer "^1.1.0"

async@1.5:
  version "1.5.2"
  resolved "http://registry.npm.qima-inc.com/async/download/async-1.5.2.tgz#ec6a61ae56480c0c3cb241c95618e20892f9672a"
  integrity sha1-7GphrlZIDAw8skHJVhjiCJL5Zyo=

async@^2.6.3, async@~2.6.1:
  version "2.6.4"
  resolved "http://registry.npm.qima-inc.com/async/download/async-2.6.4.tgz#706b7ff6084664cd7eae713f6f965433b5504221"
  integrity sha1-cGt/9ghGZM1+rnE/b5ZUM7VQQiE=
  dependencies:
    lodash "^4.17.14"

async@~3.2.0:
  version "3.2.4"
  resolved "http://registry.npm.qima-inc.com/async/download/async-3.2.4.tgz#2d22e00f8cddeb5fde5dd33522b56d1cf569a81c"
  integrity sha1-LSLgD4zd61/eXdM1IrVtHPVpqBw=

asynckit@^0.4.0:
  version "0.4.0"
  resolved "http://registry.npm.qima-inc.com/asynckit/download/asynckit-0.4.0.tgz"
  integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=

at-least-node@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/at-least-node/download/at-least-node-1.0.0.tgz"
  integrity sha1-YCzUtG6EStTv/JKoARo8RuAjjcI=

atob@^2.1.2:
  version "2.1.2"
  resolved "http://registry.npm.qima-inc.com/atob/download/atob-2.1.2.tgz"
  integrity sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k=

atomic-sleep@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/atomic-sleep/download/atomic-sleep-1.0.0.tgz"
  integrity sha1-64W3emAfyTLP5DLFrNNkqeLJB1s=

available-typed-arrays@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/available-typed-arrays/download/available-typed-arrays-1.0.2.tgz"
  integrity sha1-awmMqdgDkHnuP3f3t4PESAulE/U=
  dependencies:
    array-filter "^1.0.0"

aws-sign2@~0.7.0:
  version "0.7.0"
  resolved "http://registry.npm.qima-inc.com/aws-sign2/download/aws-sign2-0.7.0.tgz"
  integrity sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=

aws4@^1.8.0:
  version "1.11.0"
  resolved "http://registry.npm.qima-inc.com/aws4/download/aws4-1.11.0.tgz"
  integrity sha1-1h9G2DslGSUOJ4Ta9bCUeai0HFk=

axios@0.16.2, axios@~0.16:
  version "0.16.2"
  resolved "http://registry.npm.qima-inc.com/axios/download/axios-0.16.2.tgz"
  integrity sha1-uk+S8XFn37q0CYN4VFS5rBScPG0=
  dependencies:
    follow-redirects "^1.2.3"
    is-buffer "^1.1.5"

axios@0.18.0:
  version "0.18.0"
  resolved "http://registry.npm.qima-inc.com/axios/download/axios-0.18.0.tgz"
  integrity sha1-MtU+SFHv3AoRmTts0AB4nXDAUQI=
  dependencies:
    follow-redirects "^1.3.0"
    is-buffer "^1.1.5"

axios@0.19.0:
  version "0.19.0"
  resolved "http://registry.npm.qima-inc.com/axios/download/axios-0.19.0.tgz"
  integrity sha1-jgm/89kSLhM/e4EByPvdAO09Krg=
  dependencies:
    follow-redirects "1.5.10"
    is-buffer "^2.0.2"

axios@^0.21.0:
  version "0.21.4"
  resolved "http://registry.npm.qima-inc.com/axios/download/axios-0.21.4.tgz#c67b90dc0568e5c1cf2b0b858c43ba28e2eda575"
  integrity sha1-xnuQ3AVo5cHPKwuFjEO6KOLtpXU=
  dependencies:
    follow-redirects "^1.14.0"

babel-jest@^26.6.3:
  version "26.6.3"
  resolved "http://registry.npm.qima-inc.com/babel-jest/download/babel-jest-26.6.3.tgz"
  integrity sha1-2H0lywA3V3oMifguV1XF0pPAEFY=
  dependencies:
    "@jest/transform" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/babel__core" "^7.1.7"
    babel-plugin-istanbul "^6.0.0"
    babel-preset-jest "^26.6.2"
    chalk "^4.0.0"
    graceful-fs "^4.2.4"
    slash "^3.0.0"

babel-plugin-istanbul@^6.0.0:
  version "6.0.0"
  resolved "http://registry.npm.qima-inc.com/babel-plugin-istanbul/download/babel-plugin-istanbul-6.0.0.tgz"
  integrity sha1-4VnM3Jr5XgtXDHW0Vzt8NNZx12U=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@istanbuljs/load-nyc-config" "^1.0.0"
    "@istanbuljs/schema" "^0.1.2"
    istanbul-lib-instrument "^4.0.0"
    test-exclude "^6.0.0"

babel-plugin-jest-hoist@^26.6.2:
  version "26.6.2"
  resolved "http://registry.npm.qima-inc.com/babel-plugin-jest-hoist/download/babel-plugin-jest-hoist-26.6.2.tgz"
  integrity sha1-gYW9AwNI0lTG192XQ1Xmoosh5i0=
  dependencies:
    "@babel/template" "^7.3.3"
    "@babel/types" "^7.3.3"
    "@types/babel__core" "^7.0.0"
    "@types/babel__traverse" "^7.0.6"

babel-preset-current-node-syntax@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/babel-preset-current-node-syntax/download/babel-preset-current-node-syntax-1.0.1.tgz"
  integrity sha1-tDmSObibKgEfndvj5PQB/EDP9zs=
  dependencies:
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-bigint" "^7.8.3"
    "@babel/plugin-syntax-class-properties" "^7.8.3"
    "@babel/plugin-syntax-import-meta" "^7.8.3"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.8.3"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.8.3"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-top-level-await" "^7.8.3"

babel-preset-jest@^26.6.2:
  version "26.6.2"
  resolved "http://registry.npm.qima-inc.com/babel-preset-jest/download/babel-preset-jest-26.6.2.tgz"
  integrity sha1-dHhysRcd8DIlJCZYaIHWLTF5j+4=
  dependencies:
    babel-plugin-jest-hoist "^26.6.2"
    babel-preset-current-node-syntax "^1.0.0"

babel-walk@3.0.0-canary-5:
  version "3.0.0-canary-5"
  resolved "http://registry.npm.qima-inc.com/babel-walk/download/babel-walk-3.0.0-canary-5.tgz#f66ecd7298357aee44955f235a6ef54219104b11"
  integrity sha1-9m7Ncpg1eu5ElV8jWm71QhkQSxE=
  dependencies:
    "@babel/types" "^7.9.6"

balanced-match@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/balanced-match/download/balanced-match-1.0.0.tgz"
  integrity sha1-ibTRmasr7kneFk6gK4nORi1xt2c=

base64-js@^1.3.0:
  version "1.5.1"
  resolved "http://registry.npm.qima-inc.com/base64-js/download/base64-js-1.5.1.tgz"
  integrity sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=

base@^0.11.1:
  version "0.11.2"
  resolved "http://registry.npm.qima-inc.com/base/download/base-0.11.2.tgz"
  integrity sha1-e95c7RRbbVUakNuH+DxVi060io8=
  dependencies:
    cache-base "^1.0.1"
    class-utils "^0.3.5"
    component-emitter "^1.2.1"
    define-property "^1.0.0"
    isobject "^3.0.1"
    mixin-deep "^1.2.0"
    pascalcase "^0.1.1"

bcrypt-pbkdf@^1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.2.tgz"
  integrity sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=
  dependencies:
    tweetnacl "^0.14.3"

big.js@^5.2.2:
  version "5.2.2"
  resolved "http://registry.npm.qima-inc.com/big.js/download/big.js-5.2.2.tgz"
  integrity sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg=

big.js@^6.0.2:
  version "6.1.1"
  resolved "http://registry.npm.qima-inc.com/big.js/download/big.js-6.1.1.tgz#63b35b19dc9775c94991ee5db7694880655d5537"
  integrity sha1-Y7NbGdyXdclJke5dt2lIgGVdVTc=

bignumber.js@^9.0.0:
  version "9.0.1"
  resolved "http://registry.npm.qima-inc.com/bignumber.js/download/bignumber.js-9.0.1.tgz"
  integrity sha1-jXuhJMiCv9jkMmDGdHVRjQaJ5OU=

binary-extensions@^1.0.0:
  version "1.13.1"
  resolved "http://registry.npm.qima-inc.com/binary-extensions/download/binary-extensions-1.13.1.tgz"
  integrity sha1-WYr+VHVbKGilMw0q/51Ou1Mgm2U=

binary-extensions@^2.0.0:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/binary-extensions/download/binary-extensions-2.2.0.tgz#75f502eeaf9ffde42fc98829645be4ea76bd9e2d"
  integrity sha1-dfUC7q+f/eQvyYgpZFvk6na9ni0=

bindings@^1.5.0:
  version "1.5.0"
  resolved "http://registry.npm.qima-inc.com/bindings/download/bindings-1.5.0.tgz"
  integrity sha1-EDU8npRTNLwFEabZCzj7x8nFBN8=
  dependencies:
    file-uri-to-path "1.0.0"

blamer@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/blamer/download/blamer-1.0.3.tgz#a130c1d82028167eba0250b3d01d066d1b7f2621"
  integrity sha1-oTDB2CAoFn66AlCz0B0GbRt/JiE=
  dependencies:
    execa "^4.0.0"
    which "^2.0.2"

blessed-contrib@^4.8.5:
  version "4.8.21"
  resolved "http://registry.npm.qima-inc.com/blessed-contrib/download/blessed-contrib-4.8.21.tgz"
  integrity sha1-R8PfQd1b014J2tqDDUHDj/PRzJc=
  dependencies:
    ansi-term ">=0.0.2"
    chalk "^1.1.0"
    drawille-canvas-blessed-contrib ">=0.1.3"
    lodash "~>=4.17.11"
    map-canvas ">=0.1.5"
    marked "^0.7.0"
    marked-terminal "^4.0.0"
    memory-streams "^0.1.0"
    memorystream "^0.3.1"
    picture-tuber "^1.0.1"
    sparkline "^0.1.1"
    strip-ansi "^3.0.0"
    term-canvas "0.0.5"
    x256 ">=0.0.1"

blessed@0.1.81, blessed@^0.1.81:
  version "0.1.81"
  resolved "http://registry.npm.qima-inc.com/blessed/download/blessed-0.1.81.tgz"
  integrity sha1-+WLWh+wsNpVwrnGvhDJW5tDKESk=

boxen@^1.2.1, boxen@^1.3.0:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/boxen/download/boxen-1.3.0.tgz"
  integrity sha1-VcbDmouljZxhrSLNh3Uy3rZlogs=
  dependencies:
    ansi-align "^2.0.0"
    camelcase "^4.0.0"
    chalk "^2.0.1"
    cli-boxes "^1.0.0"
    string-width "^2.0.0"
    term-size "^1.2.0"
    widest-line "^2.0.0"

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "http://registry.npm.qima-inc.com/brace-expansion/download/brace-expansion-1.1.11.tgz"
  integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

braces@^2.3.1, braces@^2.3.2:
  version "2.3.2"
  resolved "http://registry.npm.qima-inc.com/braces/download/braces-2.3.2.tgz"
  integrity sha1-WXn9PxTNUxVl5fot8av/8d+u5yk=
  dependencies:
    arr-flatten "^1.1.0"
    array-unique "^0.3.2"
    extend-shallow "^2.0.1"
    fill-range "^4.0.0"
    isobject "^3.0.1"
    repeat-element "^1.1.2"
    snapdragon "^0.8.1"
    snapdragon-node "^2.0.1"
    split-string "^3.0.2"
    to-regex "^3.0.1"

braces@^3.0.1, braces@~3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/braces/download/braces-3.0.2.tgz"
  integrity sha1-NFThpGLujVmeI23zNs2epPiv4Qc=
  dependencies:
    fill-range "^7.0.1"

braces@^3.0.3:
  version "3.0.3"
  resolved "http://registry.npm.qima-inc.com/braces/download/braces-3.0.3.tgz#490332f40919452272d55a8480adc0c441358789"
  integrity sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=
  dependencies:
    fill-range "^7.1.1"

bresenham@0.0.3:
  version "0.0.3"
  resolved "http://registry.npm.qima-inc.com/bresenham/download/bresenham-0.0.3.tgz"
  integrity sha1-q9q55bGU4nx1fNMU2ERDFPKZh3o=

browser-process-hrtime@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/browser-process-hrtime/download/browser-process-hrtime-1.0.0.tgz"
  integrity sha1-PJtLfXgsgSHlbxAQbYTA0P/JRiY=

bs-logger@0.x:
  version "0.2.6"
  resolved "http://registry.npm.qima-inc.com/bs-logger/download/bs-logger-0.2.6.tgz"
  integrity sha1-6302UwenLPl0zGzadraDVK0za9g=
  dependencies:
    fast-json-stable-stringify "2.x"

bser@2.1.1:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/bser/download/bser-2.1.1.tgz"
  integrity sha1-5nh9og7OnQeZhTPP2d5vXDj0vAU=
  dependencies:
    node-int64 "^0.4.0"

buffer-equal-constant-time@1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/buffer-equal-constant-time/download/buffer-equal-constant-time-1.0.1.tgz"
  integrity sha1-+OcRMvf/5uAaXJaXpMbz5I1cyBk=

buffer-from@1.x, buffer-from@^1.0.0, buffer-from@^1.1.0:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/buffer-from/download/buffer-from-1.1.1.tgz"
  integrity sha1-MnE7wCj3XAL9txDXx7zsHyxgcO8=

buffers@~0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/buffers/download/buffers-0.1.1.tgz"
  integrity sha1-skV5w77U1tOWru5tmorn9Ugqt7s=

byte@^1.4.1:
  version "1.4.1"
  resolved "http://registry.npm.qima-inc.com/byte/download/byte-1.4.1.tgz"
  integrity sha1-qAVT0qrlOxhWq1T6d0PgOiDcyUQ=
  dependencies:
    debug "^2.6.6"
    long "^3.2.0"
    utility "^1.12.0"

byte@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/byte/download/byte-2.0.0.tgz"
  integrity sha1-xhiM9+S+ktqsIvRzEvWh9kCRsYo=
  dependencies:
    debug "^3.1.0"
    long "^4.0.0"
    utility "^1.13.1"

bytes@3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/bytes/download/bytes-3.1.0.tgz"
  integrity sha1-9s95M6Ng4FiPqf3oVlHNx/gF0fY=

bytes@^3.1.0:
  version "3.1.2"
  resolved "http://registry.npm.qima-inc.com/bytes/download/bytes-3.1.2.tgz#8b0beeb98605adf1b128fa4386403c009e0221a5"
  integrity sha1-iwvuuYYFrfGxKPpDhkA8AJ4CIaU=

cache-base@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/cache-base/download/cache-base-1.0.1.tgz"
  integrity sha1-Cn9GQWgxyLZi7jb+TnxZ129marI=
  dependencies:
    collection-visit "^1.0.0"
    component-emitter "^1.2.1"
    get-value "^2.0.6"
    has-value "^1.0.0"
    isobject "^3.0.1"
    set-value "^2.0.0"
    to-object-path "^0.3.0"
    union-value "^1.0.0"
    unset-value "^1.0.0"

call-bind@^1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/call-bind/download/call-bind-1.0.2.tgz"
  integrity sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=
  dependencies:
    function-bind "^1.1.1"
    get-intrinsic "^1.0.2"

call-bind@^1.0.2:
  version "1.0.7"
  resolved "http://registry.npm.qima-inc.com/call-bind/download/call-bind-1.0.7.tgz#06016599c40c56498c18769d2730be242b6fa3b9"
  integrity sha1-BgFlmcQMVkmMGHadJzC+JCtvo7k=
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    set-function-length "^1.2.1"

call-me-maybe@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/call-me-maybe/download/call-me-maybe-1.0.1.tgz"
  integrity sha1-JtII6onje1y95gJQoV8DHBak1ms=

caller-callsite@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/caller-callsite/download/caller-callsite-2.0.0.tgz"
  integrity sha1-hH4PzgoiN1CpoCfFSzNzGtMVQTQ=
  dependencies:
    callsites "^2.0.0"

caller-path@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/caller-path/download/caller-path-2.0.0.tgz"
  integrity sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ=
  dependencies:
    caller-callsite "^2.0.0"

callsites@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/callsites/download/callsites-2.0.0.tgz"
  integrity sha1-BuuE8A7qQT2oav/vrL/7Ngk7PFA=

callsites@^3.0.0, callsites@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/callsites/download/callsites-3.1.0.tgz"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

camelcase@5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/camelcase/download/camelcase-5.0.0.tgz"
  integrity sha1-AylVJ9WL081Kp1Nj81sujZe+L0I=

camelcase@^2.0.1:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/camelcase/download/camelcase-2.1.1.tgz"
  integrity sha1-fB0W1nmhu+WcoCys7PsBHiAfWh8=

camelcase@^4.0.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/camelcase/download/camelcase-4.1.0.tgz"
  integrity sha1-1UVjW+HjPFQmScaRc+Xeas+uNN0=

camelcase@^5.0.0, camelcase@^5.3.1:
  version "5.3.1"
  resolved "http://registry.npm.qima-inc.com/camelcase/download/camelcase-5.3.1.tgz"
  integrity sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=

camelcase@^6.0.0:
  version "6.2.0"
  resolved "http://registry.npm.qima-inc.com/camelcase/download/camelcase-6.2.0.tgz"
  integrity sha1-kkr4gcnVJaydh/QNlk5c6pgqGAk=

capture-exit@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/capture-exit/download/capture-exit-2.0.0.tgz"
  integrity sha1-+5U7+uvreB9iiYI52rtCbQilCaQ=
  dependencies:
    rsvp "^4.8.4"

capture-stack-trace@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/capture-stack-trace/download/capture-stack-trace-1.0.1.tgz"
  integrity sha1-psC74fOPOqC5Ijjstv9Cw0TUE10=

cardinal@^2.1.1:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/cardinal/download/cardinal-2.1.1.tgz"
  integrity sha1-fMEFXYItISlU0HsIXeolHMe8VQU=
  dependencies:
    ansicolors "~0.3.2"
    redeyed "~2.1.0"

caseless@~0.12.0:
  version "0.12.0"
  resolved "http://registry.npm.qima-inc.com/caseless/download/caseless-0.12.0.tgz"
  integrity sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=

chalk@2.4.2, chalk@^2.0.0, chalk@^2.0.1, chalk@^2.1.0, chalk@^2.3.1, chalk@^2.3.2, chalk@^2.4.1:
  version "2.4.2"
  resolved "http://registry.npm.qima-inc.com/chalk/download/chalk-2.4.2.tgz"
  integrity sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@3.0.0, chalk@^3.0.0, chalk@~3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/chalk/download/chalk-3.0.0.tgz"
  integrity sha1-P3PCv1JlkfV0zEksUeJFY0n4ROQ=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@4.1.0, chalk@^4.0.0, chalk@^4.1.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/chalk/download/chalk-4.1.0.tgz"
  integrity sha1-ThSHCmGNni7dl92DRf2dncMVZGo=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@^1.1.0:
  version "1.1.3"
  resolved "http://registry.npm.qima-inc.com/chalk/download/chalk-1.1.3.tgz"
  integrity sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=
  dependencies:
    ansi-styles "^2.2.1"
    escape-string-regexp "^1.0.2"
    has-ansi "^2.0.0"
    strip-ansi "^3.0.0"
    supports-color "^2.0.0"

chalk@^4.1.2:
  version "4.1.2"
  resolved "http://registry.npm.qima-inc.com/chalk/download/chalk-4.1.2.tgz#aac4e2b7734a740867aeb16bf02aad556a1e7a01"
  integrity sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chance@^1.1.8:
  version "1.1.8"
  resolved "http://registry.npm.qima-inc.com/chance/download/chance-1.1.8.tgz#5d6c2b78c9170bf6eb9df7acdda04363085be909"
  integrity sha1-XWwreMkXC/brnfes3aBDYwhb6Qk=

char-regex@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/char-regex/download/char-regex-1.0.2.tgz"
  integrity sha1-10Q1giYhf5ge1Y9Hmx1rzClUXc8=

character-parser@^2.2.0:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/character-parser/download/character-parser-2.2.0.tgz#c7ce28f36d4bcd9744e5ffc2c5fcde1c73261fc0"
  integrity sha1-x84o821LzZdE5f/CxfzeHHMmH8A=
  dependencies:
    is-regex "^1.0.3"

chardet@^0.7.0:
  version "0.7.0"
  resolved "http://registry.npm.qima-inc.com/chardet/download/chardet-0.7.0.tgz"
  integrity sha1-kAlISfCTfy7twkJdDSip5fDLrZ4=

charm@~0.1.0, charm@~0.1.1:
  version "0.1.2"
  resolved "http://registry.npm.qima-inc.com/charm/download/charm-0.1.2.tgz"
  integrity sha1-BsIe7RobBq62dVPNxT4jJ0usIpY=

chokidar@^2.0.0, chokidar@^2.1.8:
  version "2.1.8"
  resolved "http://registry.npm.qima-inc.com/chokidar/download/chokidar-2.1.8.tgz"
  integrity sha1-gEs6e2qZNYw8XGHnHYco8EHP+Rc=
  dependencies:
    anymatch "^2.0.0"
    async-each "^1.0.1"
    braces "^2.3.2"
    glob-parent "^3.1.0"
    inherits "^2.0.3"
    is-binary-path "^1.0.0"
    is-glob "^4.0.0"
    normalize-path "^3.0.0"
    path-is-absolute "^1.0.0"
    readdirp "^2.2.1"
    upath "^1.1.1"
  optionalDependencies:
    fsevents "^1.2.7"

chokidar@^3.3.0:
  version "3.5.3"
  resolved "http://registry.npm.qima-inc.com/chokidar/download/chokidar-3.5.3.tgz#1cf37c8707b932bd1af1ae22c0432e2acd1903bd"
  integrity sha1-HPN8hwe5Mr0a8a4iwEMuKs0ZA70=
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

ci-info@^1.5.0:
  version "1.6.0"
  resolved "http://registry.npm.qima-inc.com/ci-info/download/ci-info-1.6.0.tgz"
  integrity sha1-LKINu5zrMtRSSmgzAzE/AwSx5Jc=

ci-info@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/ci-info/download/ci-info-2.0.0.tgz"
  integrity sha1-Z6npZL4xpR4V5QENWObxKDQAL0Y=

cjs-module-lexer@^0.6.0:
  version "0.6.0"
  resolved "http://registry.npm.qima-inc.com/cjs-module-lexer/download/cjs-module-lexer-0.6.0.tgz"
  integrity sha1-QYb8yg6uF1lwruhwuf4tbPjVZV8=

class-utils@^0.3.5:
  version "0.3.6"
  resolved "http://registry.npm.qima-inc.com/class-utils/download/class-utils-0.3.6.tgz"
  integrity sha1-+TNprouafOAv1B+q0MqDAzGQxGM=
  dependencies:
    arr-union "^3.1.0"
    define-property "^0.2.5"
    isobject "^3.0.0"
    static-extend "^0.1.1"

clean-stack@^2.0.0:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/clean-stack/download/clean-stack-2.2.0.tgz#ee8472dbb129e727b31e8a10a427dee9dfe4008b"
  integrity sha1-7oRy27Ep5yezHooQpCfe6d/kAIs=

clear-module@^4.1.2:
  version "4.1.2"
  resolved "http://registry.npm.qima-inc.com/clear-module/download/clear-module-4.1.2.tgz#5a58a5c9f8dccf363545ad7284cad3c887352a80"
  integrity sha1-WlilyfjczzY1Ra1yhMrTyIc1KoA=
  dependencies:
    parent-module "^2.0.0"
    resolve-from "^5.0.0"

cli-boxes@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/cli-boxes/download/cli-boxes-1.0.0.tgz"
  integrity sha1-T6kXw+WclKAEzWH47lCdplFocUM=

cli-cursor@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/cli-cursor/download/cli-cursor-2.1.0.tgz"
  integrity sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=
  dependencies:
    restore-cursor "^2.0.0"

cli-cursor@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/cli-cursor/download/cli-cursor-3.1.0.tgz"
  integrity sha1-JkMFp65JDR0Dvwybp8kl0XU68wc=
  dependencies:
    restore-cursor "^3.1.0"

cli-spinners@^1.1.0:
  version "1.3.1"
  resolved "http://registry.npm.qima-inc.com/cli-spinners/download/cli-spinners-1.3.1.tgz"
  integrity sha1-ACwZkJEtDVlYDJO9NsBW3pnkJZo=

cli-table3@^0.6.0:
  version "0.6.5"
  resolved "http://registry.npm.qima-inc.com/cli-table3/download/cli-table3-0.6.5.tgz#013b91351762739c16a9567c21a04632e449bf2f"
  integrity sha1-ATuRNRdic5wWqVZ8IaBGMuRJvy8=
  dependencies:
    string-width "^4.2.0"
  optionalDependencies:
    "@colors/colors" "1.5.0"

cli-table@0.3.1:
  version "0.3.1"
  resolved "http://registry.npm.qima-inc.com/cli-table/download/cli-table-0.3.1.tgz"
  integrity sha1-9TsFJmqLGguTSz0IIebi3FkUriM=
  dependencies:
    colors "1.0.3"

cli-table@^0.3.1:
  version "0.3.4"
  resolved "http://registry.npm.qima-inc.com/cli-table/download/cli-table-0.3.4.tgz"
  integrity sha1-Wzf9cjdR8abp5w1VlTp14W6rlY4=
  dependencies:
    chalk "^2.4.1"
    string-width "^4.2.0"

cli-tableau@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/cli-tableau/download/cli-tableau-2.0.1.tgz#baa78d83e08a2d7ab79b7dad9406f0254977053f"
  integrity sha1-uqeNg+CKLXq3m32tlAbwJUl3BT8=
  dependencies:
    chalk "3.0.0"

cli-truncate@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/cli-truncate/download/cli-truncate-2.1.0.tgz#c39e28bf05edcde5be3b98992a22deed5a2b93c7"
  integrity sha1-w54ovwXtzeW+O5iZKiLe7Vork8c=
  dependencies:
    slice-ansi "^3.0.0"
    string-width "^4.2.0"

cli-width@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/cli-width/download/cli-width-3.0.0.tgz"
  integrity sha1-ovSEN6LKqaIkNueUvwceyeYc7fY=

cliui@^3.0.3:
  version "3.2.0"
  resolved "http://registry.npm.qima-inc.com/cliui/download/cliui-3.2.0.tgz"
  integrity sha1-EgYBU3qRbSmUD5NNo7SNWFo5IT0=
  dependencies:
    string-width "^1.0.1"
    strip-ansi "^3.0.1"
    wrap-ansi "^2.0.0"

cliui@^6.0.0:
  version "6.0.0"
  resolved "http://registry.npm.qima-inc.com/cliui/download/cliui-6.0.0.tgz"
  integrity sha1-UR1wLAxOQcoVbX0OlgIfI+EyJbE=
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^6.2.0"

clone@^1.0.2:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/clone/download/clone-1.0.4.tgz"
  integrity sha1-2jCcwmPfFZlMaIypAheco8fNfH4=

cluster-key-slot@^1.0.6:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/cluster-key-slot/download/cluster-key-slot-1.1.0.tgz"
  integrity sha1-MEdLKpgfsSFyaVgzBSvA0BM20Q0=

cluster-key-slot@^1.1.0:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/cluster-key-slot/download/cluster-key-slot-1.1.2.tgz#88ddaa46906e303b5de30d3153b7d9fe0a0c19ac"
  integrity sha1-iN2qRpBuMDtd4w0xU7fZ/goMGaw=

co-body@^5.1.1:
  version "5.2.0"
  resolved "http://registry.npm.qima-inc.com/co-body/download/co-body-5.2.0.tgz"
  integrity sha1-WgpljEYCkTHg46MG9nZHMC9xwSQ=
  dependencies:
    inflation "^2.0.0"
    qs "^6.4.0"
    raw-body "^2.2.0"
    type-is "^1.6.14"

co-body@^6.0.0:
  version "6.1.0"
  resolved "http://registry.npm.qima-inc.com/co-body/download/co-body-6.1.0.tgz"
  integrity sha1-2HqO/DVk+b/jrO2O9c0Ex6h2ZUc=
  dependencies:
    inflation "^2.0.0"
    qs "^6.5.2"
    raw-body "^2.3.3"
    type-is "^1.6.16"

co@^4.6.0:
  version "4.6.0"
  resolved "http://registry.npm.qima-inc.com/co/download/co-4.6.0.tgz"
  integrity sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=

code-point-at@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/code-point-at/download/code-point-at-1.1.0.tgz"
  integrity sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c=

collect-v8-coverage@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/collect-v8-coverage/download/collect-v8-coverage-1.0.1.tgz"
  integrity sha1-zCyOlPwYu9/+ZNZTRXDIpnOyf1k=

collection-visit@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/collection-visit/download/collection-visit-1.0.0.tgz"
  integrity sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=
  dependencies:
    map-visit "^1.0.0"
    object-visit "^1.0.0"

color-convert@^1.9.0:
  version "1.9.3"
  resolved "http://registry.npm.qima-inc.com/color-convert/download/color-convert-1.9.3.tgz"
  integrity sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/color-convert/download/color-convert-2.0.1.tgz"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@1.1.3:
  version "1.1.3"
  resolved "http://registry.npm.qima-inc.com/color-name/download/color-name-1.1.3.tgz"
  integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=

color-name@~1.1.4:
  version "1.1.4"
  resolved "http://registry.npm.qima-inc.com/color-name/download/color-name-1.1.4.tgz"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

colorette@^2.0.16:
  version "2.0.20"
  resolved "http://registry.npm.qima-inc.com/colorette/download/colorette-2.0.20.tgz#9eb793e6833067f7235902fcd3b09917a000a95a"
  integrity sha1-nreT5oMwZ/cjWQL807CZF6AAqVo=

colors@1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/colors/download/colors-1.0.3.tgz"
  integrity sha1-BDP0TYCWgP3rYO0mDxsMJi6CpAs=

colors@1.4.0:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/colors/download/colors-1.4.0.tgz#c50491479d4c1bdaed2c9ced32cf7c7dc2360f78"
  integrity sha1-xQSRR51MG9rtLJztMs98fcI2D3g=

combined-stream@^1.0.6, combined-stream@~1.0.6:
  version "1.0.8"
  resolved "http://registry.npm.qima-inc.com/combined-stream/download/combined-stream-1.0.8.tgz"
  integrity sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=
  dependencies:
    delayed-stream "~1.0.0"

commander@2.15.1:
  version "2.15.1"
  resolved "http://registry.npm.qima-inc.com/commander/download/commander-2.15.1.tgz#df46e867d0fc2aec66a34662b406a9ccafff5b0f"
  integrity sha1-30boZ9D8Kuxmo0ZitAapzK//Ww8=

commander@5.1.0, commander@^5.0.0:
  version "5.1.0"
  resolved "http://registry.npm.qima-inc.com/commander/download/commander-5.1.0.tgz"
  integrity sha1-Rqu9FlL44Fm92u+Zu9yyrZzxea4=

commander@^10.0.0:
  version "10.0.1"
  resolved "http://registry.npm.qima-inc.com/commander/download/commander-10.0.1.tgz#881ee46b4f77d1c1dccc5823433aa39b022cbe06"
  integrity sha1-iB7ka0930cHczFgjQzqjmwIsvgY=

commander@^2.15.1, commander@^2.9.0:
  version "2.20.3"
  resolved "http://registry.npm.qima-inc.com/commander/download/commander-2.20.3.tgz"
  integrity sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=

commander@^6.0.0, commander@^6.2.0:
  version "6.2.1"
  resolved "http://registry.npm.qima-inc.com/commander/download/commander-6.2.1.tgz"
  integrity sha1-B5LraC37wyWZm7K4T93duhEKxzw=

comment-json@^4.2.3:
  version "4.2.5"
  resolved "http://registry.npm.qima-inc.com/comment-json/download/comment-json-4.2.5.tgz#482e085f759c2704b60bc6f97f55b8c01bc41e70"
  integrity sha1-SC4IX3WcJwS2C8b5f1W4wBvEHnA=
  dependencies:
    array-timsort "^1.0.3"
    core-util-is "^1.0.3"
    esprima "^4.0.1"
    has-own-prop "^2.0.0"
    repeat-string "^1.6.1"

compare-versions@^3.4.0, compare-versions@^3.6.0:
  version "3.6.0"
  resolved "http://registry.npm.qima-inc.com/compare-versions/download/compare-versions-3.6.0.tgz"
  integrity sha1-GlaJkTaF5ah2N7jT/8p1UU7EHWI=

complete-assign@0.0.2:
  version "0.0.2"
  resolved "http://registry.npm.qima-inc.com/complete-assign/download/complete-assign-0.0.2.tgz"
  integrity sha1-410Uvg6fCH8FCytyyOh81/kWkeU=

component-emitter@^1.2.0, component-emitter@^1.2.1:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/component-emitter/download/component-emitter-1.3.0.tgz"
  integrity sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A=

concat-map@0.0.1:
  version "0.0.1"
  resolved "http://registry.npm.qima-inc.com/concat-map/download/concat-map-0.0.1.tgz"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

configstore@^3.0.0:
  version "3.1.5"
  resolved "http://registry.npm.qima-inc.com/configstore/download/configstore-3.1.5.tgz"
  integrity sha1-6a8zH63BTavVRNPn523ERqCaUw8=
  dependencies:
    dot-prop "^4.2.1"
    graceful-fs "^4.1.2"
    make-dir "^1.0.0"
    unique-string "^1.0.0"
    write-file-atomic "^2.0.0"
    xdg-basedir "^3.0.0"

configstore@^5.0.1:
  version "5.0.1"
  resolved "http://registry.npm.qima-inc.com/configstore/download/configstore-5.0.1.tgz#d365021b5df4b98cdd187d6a3b0e3f6a7cc5ed96"
  integrity sha1-02UCG130uYzdGH1qOw4/anzF7ZY=
  dependencies:
    dot-prop "^5.2.0"
    graceful-fs "^4.1.2"
    make-dir "^3.0.0"
    unique-string "^2.0.0"
    write-file-atomic "^3.0.0"
    xdg-basedir "^4.0.0"

confusing-browser-globals@^1.0.10:
  version "1.0.10"
  resolved "http://registry.npm.qima-inc.com/confusing-browser-globals/download/confusing-browser-globals-1.0.10.tgz"
  integrity sha1-MNHn89G4grJexJM9HRraw1PSClk=

constantinople@^4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/constantinople/download/constantinople-4.0.1.tgz#0def113fa0e4dc8de83331a5cf79c8b325213151"
  integrity sha1-De8RP6Dk3I3oMzGlz3nIsyUhMVE=
  dependencies:
    "@babel/parser" "^7.6.0"
    "@babel/types" "^7.6.1"

contains-path@^0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.qima-inc.com/contains-path/download/contains-path-0.1.0.tgz"
  integrity sha1-/ozxhP9mcLa67wGp1IYaXL7EEgo=

content-disposition@~0.5.0:
  version "0.5.3"
  resolved "http://registry.npm.qima-inc.com/content-disposition/download/content-disposition-0.5.3.tgz"
  integrity sha1-4TDK9+cnkIfFYWwgB9BIVpiYT70=
  dependencies:
    safe-buffer "5.1.2"

content-type@^1.0.0, content-type@^1.0.2:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/content-type/download/content-type-1.0.4.tgz"
  integrity sha1-4TjMdeBAxyexlm/l5fjJruJW/js=

continuation-local-storage@^3.2.1:
  version "3.2.1"
  resolved "http://registry.npm.qima-inc.com/continuation-local-storage/download/continuation-local-storage-3.2.1.tgz#11f613f74e914fe9b34c92ad2d28fe6ae1db7ffb"
  integrity sha1-EfYT906RT+mzTJKtLSj+auHbf/s=
  dependencies:
    async-listener "^0.6.0"
    emitter-listener "^1.1.1"

convert-source-map@^1.4.0, convert-source-map@^1.6.0, convert-source-map@^1.7.0:
  version "1.7.0"
  resolved "http://registry.npm.qima-inc.com/convert-source-map/download/convert-source-map-1.7.0.tgz"
  integrity sha1-F6LLiC1/d9NJBYXizmxSRCSjpEI=
  dependencies:
    safe-buffer "~5.1.1"

cookiejar@^2.1.0:
  version "2.1.2"
  resolved "http://registry.npm.qima-inc.com/cookiejar/download/cookiejar-2.1.2.tgz"
  integrity sha1-3YojVTB1L5iPmghE8/xYnjERElw=

cookies@^0.8.0:
  version "0.8.0"
  resolved "http://registry.npm.qima-inc.com/cookies/download/cookies-0.8.0.tgz"
  integrity sha1-EpPOSzkXQKhAbjyYcOgoxLVPP5A=
  dependencies:
    depd "~2.0.0"
    keygrip "~1.1.0"

cookies@~0.7.0:
  version "0.7.3"
  resolved "http://registry.npm.qima-inc.com/cookies/download/cookies-0.7.3.tgz"
  integrity sha1-eRLOIfvy6MLacM8cPzUa7PWdrfo=
  dependencies:
    depd "~1.1.2"
    keygrip "~1.0.3"

copy-descriptor@^0.1.0:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/copy-descriptor/download/copy-descriptor-0.1.1.tgz"
  integrity sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=

copy-to@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/copy-to/download/copy-to-2.0.1.tgz"
  integrity sha1-JoD7uAaKSNCGVrYJgJK9r8kG9KU=

core-js@3.x:
  version "3.9.1"
  resolved "http://registry.npm.qima-inc.com/core-js/download/core-js-3.9.1.tgz#cec8de593db8eb2a85ffb0dbdeb312cb6e5460ae"
  integrity sha1-zsjeWT246yqF/7Db3rMSy25UYK4=

core-util-is@1.0.2, core-util-is@^1.0.2, core-util-is@~1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/core-util-is/download/core-util-is-1.0.2.tgz"
  integrity sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=

core-util-is@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/core-util-is/download/core-util-is-1.0.3.tgz#a6042d3634c2b27e9328f837b965fac83808db85"
  integrity sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=

cosmiconfig@5.0.7:
  version "5.0.7"
  resolved "http://registry.npm.qima-inc.com/cosmiconfig/download/cosmiconfig-5.0.7.tgz"
  integrity sha1-OYJrKS7g147aE336MXO9HCGkOwQ=
  dependencies:
    import-fresh "^2.0.0"
    is-directory "^0.3.1"
    js-yaml "^3.9.0"
    parse-json "^4.0.0"

cosmiconfig@8.0.0:
  version "8.0.0"
  resolved "http://registry.npm.qima-inc.com/cosmiconfig/download/cosmiconfig-8.0.0.tgz#e9feae014eab580f858f8a0288f38997a7bebe97"
  integrity sha1-6f6uAU6rWA+Fj4oCiPOJl6e+vpc=
  dependencies:
    import-fresh "^3.2.1"
    js-yaml "^4.1.0"
    parse-json "^5.0.0"
    path-type "^4.0.0"

cosmiconfig@^7.0.0:
  version "7.1.0"
  resolved "http://registry.npm.qima-inc.com/cosmiconfig/download/cosmiconfig-7.1.0.tgz#1443b9afa596b670082ea46cbd8f6a62b84635f6"
  integrity sha1-FEO5r6WWtnAILqRsvY9qYrhGNfY=
  dependencies:
    "@types/parse-json" "^4.0.0"
    import-fresh "^3.2.1"
    parse-json "^5.0.0"
    path-type "^4.0.0"
    yaml "^1.10.0"

crc32@0.2.2:
  version "0.2.2"
  resolved "http://registry.npm.qima-inc.com/crc32/download/crc32-0.2.2.tgz"
  integrity sha1-etIg1v/c0Rn5/BJ6d3LKzqOQpLo=

create-error-class@^3.0.0:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/create-error-class/download/create-error-class-3.0.2.tgz"
  integrity sha1-Br56vvlHo/FKMP1hBnHUAbyot7Y=
  dependencies:
    capture-stack-trace "^1.0.0"

cron@1.8.2:
  version "1.8.2"
  resolved "http://registry.npm.qima-inc.com/cron/download/cron-1.8.2.tgz#4ac5e3c55ba8c163d84f3407bde94632da8370ce"
  integrity sha1-SsXjxVuowWPYTzQHvelGMtqDcM4=
  dependencies:
    moment-timezone "^0.5.x"

cross-spawn@^5.0.1:
  version "5.1.0"
  resolved "http://registry.npm.qima-inc.com/cross-spawn/download/cross-spawn-5.1.0.tgz"
  integrity sha1-6L0O/uWPz/b4+UUQoKVUu/ojVEk=
  dependencies:
    lru-cache "^4.0.1"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^6.0.0, cross-spawn@^6.0.5:
  version "6.0.5"
  resolved "http://registry.npm.qima-inc.com/cross-spawn/download/cross-spawn-6.0.5.tgz"
  integrity sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q=
  dependencies:
    nice-try "^1.0.4"
    path-key "^2.0.1"
    semver "^5.5.0"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^7.0.0, cross-spawn@^7.0.3:
  version "7.0.3"
  resolved "http://registry.npm.qima-inc.com/cross-spawn/download/cross-spawn-7.0.3.tgz"
  integrity sha1-9zqFudXUHQRVUcF34ogtSshXKKY=
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

crypto-random-string@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/crypto-random-string/download/crypto-random-string-1.0.0.tgz"
  integrity sha1-ojD2T1aDEOFJgAmUB5DsmVRbyn4=

crypto-random-string@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/crypto-random-string/download/crypto-random-string-2.0.0.tgz#ef2a7a966ec11083388369baa02ebead229b30d5"
  integrity sha1-7yp6lm7BEIM4g2m6oC6+rSKbMNU=

cspell-dictionary@6.31.3:
  version "6.31.3"
  resolved "http://registry.npm.qima-inc.com/cspell-dictionary/download/cspell-dictionary-6.31.3.tgz#5ab069e11e9c2231b01f66ecf250fff23215648c"
  integrity sha1-WrBp4R6cIjGwH2bs8lD/8jIVZIw=
  dependencies:
    "@cspell/cspell-pipe" "6.31.3"
    "@cspell/cspell-types" "6.31.3"
    cspell-trie-lib "6.31.3"
    fast-equals "^4.0.3"
    gensequence "^5.0.2"

cspell-gitignore@6.31.3:
  version "6.31.3"
  resolved "http://registry.npm.qima-inc.com/cspell-gitignore/download/cspell-gitignore-6.31.3.tgz#324ff7c8f2e35b103eeec81a185d578bf37c42f0"
  integrity sha1-Mk/3yPLjWxA+7sgaGF1Xi/N8QvA=
  dependencies:
    cspell-glob "6.31.3"
    find-up "^5.0.0"

cspell-glob@6.31.3:
  version "6.31.3"
  resolved "http://registry.npm.qima-inc.com/cspell-glob/download/cspell-glob-6.31.3.tgz#99ed7e39f83735fbda9a77f13636a18fd54bef39"
  integrity sha1-me1+Ofg3NfvamnfxNjahj9VL7zk=
  dependencies:
    micromatch "^4.0.5"

cspell-grammar@6.31.3:
  version "6.31.3"
  resolved "http://registry.npm.qima-inc.com/cspell-grammar/download/cspell-grammar-6.31.3.tgz#1c8ebb9f623ec866d307c78ace9fa4ba73c94822"
  integrity sha1-HI67n2I+yGbTB8eKzp+kunPJSCI=
  dependencies:
    "@cspell/cspell-pipe" "6.31.3"
    "@cspell/cspell-types" "6.31.3"

cspell-io@6.31.3:
  version "6.31.3"
  resolved "http://registry.npm.qima-inc.com/cspell-io/download/cspell-io-6.31.3.tgz#a62e95196027ab9c7c8a35d9ec347ded8d054658"
  integrity sha1-pi6VGWAnq5x8ijXZ7DR97Y0FRlg=
  dependencies:
    "@cspell/cspell-service-bus" "6.31.3"
    node-fetch "^2.6.9"

cspell-lib@6.31.3:
  version "6.31.3"
  resolved "http://registry.npm.qima-inc.com/cspell-lib/download/cspell-lib-6.31.3.tgz#4b5034adb3af3e3d6acb49bbf251b9c18b280f50"
  integrity sha1-S1A0rbOvPj1qy0m78lG5wYsoD1A=
  dependencies:
    "@cspell/cspell-bundled-dicts" "6.31.3"
    "@cspell/cspell-pipe" "6.31.3"
    "@cspell/cspell-types" "6.31.3"
    "@cspell/strong-weak-map" "6.31.3"
    clear-module "^4.1.2"
    comment-json "^4.2.3"
    configstore "^5.0.1"
    cosmiconfig "8.0.0"
    cspell-dictionary "6.31.3"
    cspell-glob "6.31.3"
    cspell-grammar "6.31.3"
    cspell-io "6.31.3"
    cspell-trie-lib "6.31.3"
    fast-equals "^4.0.3"
    find-up "^5.0.0"
    gensequence "^5.0.2"
    import-fresh "^3.3.0"
    resolve-from "^5.0.0"
    resolve-global "^1.0.0"
    vscode-languageserver-textdocument "^1.0.8"
    vscode-uri "^3.0.7"

cspell-trie-lib@6.31.3:
  version "6.31.3"
  resolved "http://registry.npm.qima-inc.com/cspell-trie-lib/download/cspell-trie-lib-6.31.3.tgz#73060aa1e9926faeffe509f9f8b8fd74aa55d1a4"
  integrity sha1-cwYKoemSb67/5Qn5+Lj9dKpV0aQ=
  dependencies:
    "@cspell/cspell-pipe" "6.31.3"
    "@cspell/cspell-types" "6.31.3"
    gensequence "^5.0.2"

cspell@^6.31.2:
  version "6.31.3"
  resolved "http://registry.npm.qima-inc.com/cspell/download/cspell-6.31.3.tgz#d06176e7fe7ac80c4dd5c4a927139446e433385a"
  integrity sha1-0GF25/56yAxN1cSpJxOURuQzOFo=
  dependencies:
    "@cspell/cspell-json-reporter" "6.31.3"
    "@cspell/cspell-pipe" "6.31.3"
    "@cspell/cspell-types" "6.31.3"
    "@cspell/dynamic-import" "6.31.3"
    chalk "^4.1.2"
    commander "^10.0.0"
    cspell-gitignore "6.31.3"
    cspell-glob "6.31.3"
    cspell-io "6.31.3"
    cspell-lib "6.31.3"
    fast-glob "^3.2.12"
    fast-json-stable-stringify "^2.1.0"
    file-entry-cache "^6.0.1"
    get-stdin "^8.0.0"
    imurmurhash "^0.1.4"
    semver "^7.3.8"
    strip-ansi "^6.0.1"
    vscode-uri "^3.0.7"

cssfilter@0.0.10:
  version "0.0.10"
  resolved "http://registry.npm.qima-inc.com/cssfilter/download/cssfilter-0.0.10.tgz"
  integrity sha1-xtJnJjKi5cg+AT5oZKQs6N79IK4=

cssom@^0.4.4:
  version "0.4.4"
  resolved "http://registry.npm.qima-inc.com/cssom/download/cssom-0.4.4.tgz"
  integrity sha1-WmbPk9LQtmHYC/akT7ZfXC5OChA=

cssom@~0.3.6:
  version "0.3.8"
  resolved "http://registry.npm.qima-inc.com/cssom/download/cssom-0.3.8.tgz"
  integrity sha1-nxJ29bK0Y/IRTT8sdSUK+MGjb0o=

cssstyle@^2.2.0:
  version "2.3.0"
  resolved "http://registry.npm.qima-inc.com/cssstyle/download/cssstyle-2.3.0.tgz"
  integrity sha1-/2ZaDdvcMYZLCWR/NBY0Q9kLCFI=
  dependencies:
    cssom "~0.3.6"

dashdash@^1.12.0:
  version "1.14.1"
  resolved "http://registry.npm.qima-inc.com/dashdash/download/dashdash-1.14.1.tgz"
  integrity sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=
  dependencies:
    assert-plus "^1.0.0"

data-uri-to-buffer@1:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/data-uri-to-buffer/download/data-uri-to-buffer-1.2.0.tgz"
  integrity sha1-dxY+qcINhkG0cH6PGKvfmnjzSDU=

data-uri-to-buffer@3:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/data-uri-to-buffer/download/data-uri-to-buffer-3.0.1.tgz#594b8973938c5bc2c33046535785341abc4f3636"
  integrity sha1-WUuJc5OMW8LDMEZTV4U0GrxPNjY=

data-urls@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/data-urls/download/data-urls-2.0.0.tgz"
  integrity sha1-FWSFpyljqXD11YIar2Qr7yvy25s=
  dependencies:
    abab "^2.0.3"
    whatwg-mimetype "^2.3.0"
    whatwg-url "^8.0.0"

dateformat@^3.0.3:
  version "3.0.3"
  resolved "http://registry.npm.qima-inc.com/dateformat/download/dateformat-3.0.3.tgz"
  integrity sha1-puN0maTZqc+F71hyBE1ikByYia4=

dayjs@~1.8.24, dayjs@~1.8.25:
  version "1.8.36"
  resolved "http://registry.npm.qima-inc.com/dayjs/download/dayjs-1.8.36.tgz#be36e248467afabf8f5a86bae0de0cdceecced50"
  integrity sha1-vjbiSEZ6+r+PWoa64N4M3O7M7VA=

debug@*, debug@4, debug@^4.0.1, debug@^4.1.0, debug@^4.1.1, debug@^4.3.1:
  version "4.3.1"
  resolved "http://registry.npm.qima-inc.com/debug/download/debug-4.3.1.tgz"
  integrity sha1-8NIpxQXgxtjEmsVT0bE9wYP2su4=
  dependencies:
    ms "2.1.2"

debug@2, debug@^2.2.0, debug@^2.3.3, debug@^2.6.0, debug@^2.6.3, debug@^2.6.6, debug@^2.6.8, debug@^2.6.9:
  version "2.6.9"
  resolved "http://registry.npm.qima-inc.com/debug/download/debug-2.6.9.tgz"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

debug@3.1.0, debug@=3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/debug/download/debug-3.1.0.tgz"
  integrity sha1-W7WgZyYotkFJVmuhaBnmFRjGcmE=
  dependencies:
    ms "2.0.0"

debug@4.1.1:
  version "4.1.1"
  resolved "http://registry.npm.qima-inc.com/debug/download/debug-4.1.1.tgz"
  integrity sha1-O3ImAlUQnGtYnO4FDx1RYTlmR5E=
  dependencies:
    ms "^2.1.1"

debug@^3.0, debug@^3.1.0, debug@^3.2.6:
  version "3.2.7"
  resolved "http://registry.npm.qima-inc.com/debug/download/debug-3.2.7.tgz"
  integrity sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=
  dependencies:
    ms "^2.1.1"

debug@^4.2.0:
  version "4.3.7"
  resolved "http://registry.npm.qima-inc.com/debug/download/debug-4.3.7.tgz#87945b4151a011d76d95a198d7111c865c360a52"
  integrity sha1-h5RbQVGgEddtlaGY1xEchlw2ClI=
  dependencies:
    ms "^2.1.3"

debug@~4.3.1:
  version "4.3.4"
  resolved "http://registry.npm.qima-inc.com/debug/download/debug-4.3.4.tgz#1319f6579357f2338d3337d2cdd4914bb5dcc865"
  integrity sha1-Exn2V5NX8jONMzfSzdSRS7XcyGU=
  dependencies:
    ms "2.1.2"

decamelize@^1.1.1, decamelize@^1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/decamelize/download/decamelize-1.2.0.tgz"
  integrity sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=

decimal.js@^10.2.0:
  version "10.2.1"
  resolved "http://registry.npm.qima-inc.com/decimal.js/download/decimal.js-10.2.1.tgz"
  integrity sha1-I4rnsPDHk9PjzqQQEIs1osAUJqM=

decode-uri-component@^0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.qima-inc.com/decode-uri-component/download/decode-uri-component-0.2.0.tgz"
  integrity sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU=

dedent@^0.7.0:
  version "0.7.0"
  resolved "http://registry.npm.qima-inc.com/dedent/download/dedent-0.7.0.tgz#2495ddbaf6eb874abb0e1be9df22d2e5a544326c"
  integrity sha1-JJXduvbrh0q7Dhvp3yLS5aVEMmw=

deep-equal@~1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/deep-equal/download/deep-equal-1.0.1.tgz"
  integrity sha1-9dJgKStmDghO/0zbyfCK0yR0SLU=

deep-extend@^0.6.0:
  version "0.6.0"
  resolved "http://registry.npm.qima-inc.com/deep-extend/download/deep-extend-0.6.0.tgz"
  integrity sha1-xPp8lUBKF6nD6Mp+FTcxK3NjMKw=

deep-is@~0.1.3:
  version "0.1.3"
  resolved "http://registry.npm.qima-inc.com/deep-is/download/deep-is-0.1.3.tgz"
  integrity sha1-s2nW+128E+7PUk+RsHD+7cNXzzQ=

deepmerge@^4.2.2:
  version "4.2.2"
  resolved "http://registry.npm.qima-inc.com/deepmerge/download/deepmerge-4.2.2.tgz"
  integrity sha1-RNLqNnm49NT/ujPwPYZfwee/SVU=

default-user-agent@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/default-user-agent/download/default-user-agent-1.0.0.tgz"
  integrity sha1-FsRu/cq6PtxF8k8r1IaLAbfCrcY=
  dependencies:
    os-name "~1.0.3"

defaults@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/defaults/download/defaults-1.0.3.tgz"
  integrity sha1-xlYFHpgX2f8I7YgUd/P+QBnz730=
  dependencies:
    clone "^1.0.2"

define-data-property@^1.1.4:
  version "1.1.4"
  resolved "http://registry.npm.qima-inc.com/define-data-property/download/define-data-property-1.1.4.tgz#894dc141bb7d3060ae4366f6a0107e68fbe48c5e"
  integrity sha1-iU3BQbt9MGCuQ2b2oBB+aPvkjF4=
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    gopd "^1.0.1"

define-properties@^1.1.3:
  version "1.1.3"
  resolved "http://registry.npm.qima-inc.com/define-properties/download/define-properties-1.1.3.tgz"
  integrity sha1-z4jabL7ib+bbcJT2HYcMvYTO6fE=
  dependencies:
    object-keys "^1.0.12"

define-property@^0.2.5:
  version "0.2.5"
  resolved "http://registry.npm.qima-inc.com/define-property/download/define-property-0.2.5.tgz"
  integrity sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=
  dependencies:
    is-descriptor "^0.1.0"

define-property@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/define-property/download/define-property-1.0.0.tgz"
  integrity sha1-dp66rz9KY6rTr56NMEybvnm/sOY=
  dependencies:
    is-descriptor "^1.0.0"

define-property@^2.0.2:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/define-property/download/define-property-2.0.2.tgz"
  integrity sha1-1Flono1lS6d+AqgX+HENcCyxbp0=
  dependencies:
    is-descriptor "^1.0.2"
    isobject "^3.0.1"

degenerator@^1.0.4:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/degenerator/download/degenerator-1.0.4.tgz"
  integrity sha1-/PSQo37OJmRk2cxDGrmMWBnO0JU=
  dependencies:
    ast-types "0.x.x"
    escodegen "1.x.x"
    esprima "3.x.x"

degenerator@^2.2.0:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/degenerator/download/degenerator-2.2.0.tgz#49e98c11fa0293c5b26edfbb52f15729afcdb254"
  integrity sha1-SemMEfoCk8Wybt+7UvFXKa/NslQ=
  dependencies:
    ast-types "^0.13.2"
    escodegen "^1.8.1"
    esprima "^4.0.0"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/delayed-stream/download/delayed-stream-1.0.0.tgz"
  integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=

delegates@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/delegates/download/delegates-1.0.0.tgz"
  integrity sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o=

denque@^1.1.0:
  version "1.5.0"
  resolved "http://registry.npm.qima-inc.com/denque/download/denque-1.5.0.tgz"
  integrity sha1-dz3gaG/y2Owv+SkUMWpHtzscc94=

depd@^1.1.0, depd@~1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/depd/download/depd-1.1.2.tgz"
  integrity sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=

depd@~2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/depd/download/depd-2.0.0.tgz"
  integrity sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8=

destroy@^1.0.3, destroy@^1.0.4:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/destroy/download/destroy-1.0.4.tgz"
  integrity sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA=

detect-newline@^3.0.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/detect-newline/download/detect-newline-3.1.0.tgz"
  integrity sha1-V29d/GOuGhkv8ZLYrTr2MImRtlE=

detect-port@^1.3.0:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/detect-port/download/detect-port-1.3.0.tgz"
  integrity sha1-2cQOmsyt1N9crGp4Ku/QFNVz0fE=
  dependencies:
    address "^1.0.1"
    debug "^2.6.0"

diff-sequences@^26.6.2:
  version "26.6.2"
  resolved "http://registry.npm.qima-inc.com/diff-sequences/download/diff-sequences-26.6.2.tgz"
  integrity sha1-SLqZFX3hkjQS7tQdtrbUqpynwLE=

diff@^3.1.0:
  version "3.5.0"
  resolved "http://registry.npm.qima-inc.com/diff/download/diff-3.5.0.tgz"
  integrity sha1-gAwN0eCov7yVg1wgKtIg/jF+WhI=

digest-header@^0.0.1:
  version "0.0.1"
  resolved "http://registry.npm.qima-inc.com/digest-header/download/digest-header-0.0.1.tgz"
  integrity sha1-Ecz23uxXZqw3l0TZAcEsuklRS+Y=
  dependencies:
    utility "0.1.11"

digest-header@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/digest-header/download/digest-header-1.1.0.tgz#e16ab6cf4545bc4eea878c8c35acd1b89664d800"
  integrity sha1-4Wq2z0VFvE7qh4yMNazRuJZk2AA=

dnscache@1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/dnscache/download/dnscache-1.0.2.tgz"
  integrity sha1-/Twk1mwUFiX1lMd756ja/uKmbIo=
  dependencies:
    asap "^2.0.6"
    lodash.clone "^4.5.0"

doctrine@1.5.0:
  version "1.5.0"
  resolved "http://registry.npm.qima-inc.com/doctrine/download/doctrine-1.5.0.tgz"
  integrity sha1-N53Ocw9hZvds76TmcHoVmwLFpvo=
  dependencies:
    esutils "^2.0.2"
    isarray "^1.0.0"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/doctrine/download/doctrine-3.0.0.tgz"
  integrity sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=
  dependencies:
    esutils "^2.0.2"

doctypes@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/doctypes/download/doctypes-1.1.0.tgz#ea80b106a87538774e8a3a4a5afe293de489e0a9"
  integrity sha1-6oCxBqh1OHdOijpKWv4pPeSJ4Kk=

domexception@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/domexception/download/domexception-2.0.1.tgz"
  integrity sha1-+0Su+6eT4VdLCvau0oAdBXUp8wQ=
  dependencies:
    webidl-conversions "^5.0.0"

dot-prop@^4.2.1:
  version "4.2.1"
  resolved "http://registry.npm.qima-inc.com/dot-prop/download/dot-prop-4.2.1.tgz"
  integrity sha1-RYhBlKcfws2nHLtLzrOk3S9DO6Q=
  dependencies:
    is-obj "^1.0.0"

dot-prop@^5.2.0:
  version "5.3.0"
  resolved "http://registry.npm.qima-inc.com/dot-prop/download/dot-prop-5.3.0.tgz#90ccce708cd9cd82cc4dc8c3ddd9abdd55b20e88"
  integrity sha1-kMzOcIzZzYLMTcjD3dmr3VWyDog=
  dependencies:
    is-obj "^2.0.0"

drawille-blessed-contrib@>=0.0.1:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/drawille-blessed-contrib/download/drawille-blessed-contrib-1.0.0.tgz"
  integrity sha1-FcJ5NPV6AFatE1luFWFje8lB8Lc=

drawille-canvas-blessed-contrib@>=0.0.1, drawille-canvas-blessed-contrib@>=0.1.3:
  version "0.1.3"
  resolved "http://registry.npm.qima-inc.com/drawille-canvas-blessed-contrib/download/drawille-canvas-blessed-contrib-0.1.3.tgz"
  integrity sha1-IS8HinIr/S7MJn6oarbd3BCB/Ug=
  dependencies:
    ansi-term ">=0.0.2"
    bresenham "0.0.3"
    drawille-blessed-contrib ">=0.0.1"
    gl-matrix "^2.1.0"
    x256 ">=0.0.1"

duplexer3@^0.1.4:
  version "0.1.4"
  resolved "http://registry.npm.qima-inc.com/duplexer3/download/duplexer3-0.1.4.tgz"
  integrity sha1-7gHdHKwO08vH/b6jfcCo8c4ALOI=

ecc-jsbn@~0.1.1:
  version "0.1.2"
  resolved "http://registry.npm.qima-inc.com/ecc-jsbn/download/ecc-jsbn-0.1.2.tgz"
  integrity sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=
  dependencies:
    jsbn "~0.1.0"
    safer-buffer "^2.1.0"

ecdsa-sig-formatter@1.0.11, ecdsa-sig-formatter@^1.0.11:
  version "1.0.11"
  resolved "http://registry.npm.qima-inc.com/ecdsa-sig-formatter/download/ecdsa-sig-formatter-1.0.11.tgz"
  integrity sha1-rg8PothQRe8UqBfao86azQSJ5b8=
  dependencies:
    safe-buffer "^5.0.1"

ee-first@1.1.1, ee-first@~1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/ee-first/download/ee-first-1.1.1.tgz"
  integrity sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=

emitter-listener@^1.1.1:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/emitter-listener/download/emitter-listener-1.1.2.tgz#56b140e8f6992375b3d7cb2cab1cc7432d9632e8"
  integrity sha1-VrFA6PaZI3Wz18ssqxzHQy2WMug=
  dependencies:
    shimmer "^1.2.0"

emittery@^0.7.1:
  version "0.7.2"
  resolved "http://registry.npm.qima-inc.com/emittery/download/emittery-0.7.2.tgz"
  integrity sha1-JVlZCOE68PVnSrQZOW4vs5TN+oI=

emoji-regex@^7.0.1:
  version "7.0.3"
  resolved "http://registry.npm.qima-inc.com/emoji-regex/download/emoji-regex-7.0.3.tgz"
  integrity sha1-kzoEBShgyF6DwSJHnEdIqOTHIVY=

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "http://registry.npm.qima-inc.com/emoji-regex/download/emoji-regex-8.0.0.tgz"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

encodeurl@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/encodeurl/download/encodeurl-1.0.2.tgz"
  integrity sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=

end-of-stream@^1.1.0:
  version "1.4.4"
  resolved "http://registry.npm.qima-inc.com/end-of-stream/download/end-of-stream-1.4.4.tgz"
  integrity sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=
  dependencies:
    once "^1.4.0"

enquirer@2.3.5:
  version "2.3.5"
  resolved "http://registry.npm.qima-inc.com/enquirer/download/enquirer-2.3.5.tgz#3ab2b838df0a9d8ab9e7dff235b0e8712ef92381"
  integrity sha1-OrK4ON8KnYq559/yNbDocS75I4E=
  dependencies:
    ansi-colors "^3.2.1"

enquirer@^2.3.6:
  version "2.4.1"
  resolved "http://registry.npm.qima-inc.com/enquirer/download/enquirer-2.4.1.tgz#93334b3fbd74fc7097b224ab4a8fb7e40bf4ae56"
  integrity sha1-kzNLP710/HCXsiSrSo+35Av0rlY=
  dependencies:
    ansi-colors "^4.1.1"
    strip-ansi "^6.0.1"

error-ex@^1.2.0, error-ex@^1.3.1:
  version "1.3.2"
  resolved "http://registry.npm.qima-inc.com/error-ex/download/error-ex-1.3.2.tgz"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

error-inject@~1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/error-inject/download/error-inject-1.0.0.tgz"
  integrity sha1-4rPZG1Su1nLzCdlQ0VSFD6EdTzc=

es-abstract@^1.18.0-next.1:
  version "1.18.0-next.1"
  resolved "http://registry.npm.qima-inc.com/es-abstract/download/es-abstract-1.18.0-next.1.tgz"
  integrity sha1-bjoKS9pxflAjqzuOkL7DYQjSLGg=
  dependencies:
    es-to-primitive "^1.2.1"
    function-bind "^1.1.1"
    has "^1.0.3"
    has-symbols "^1.0.1"
    is-callable "^1.2.2"
    is-negative-zero "^2.0.0"
    is-regex "^1.1.1"
    object-inspect "^1.8.0"
    object-keys "^1.1.1"
    object.assign "^4.1.1"
    string.prototype.trimend "^1.0.1"
    string.prototype.trimstart "^1.0.1"

es-define-property@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/es-define-property/download/es-define-property-1.0.0.tgz#c7faefbdff8b2696cf5f46921edfb77cc4ba3845"
  integrity sha1-x/rvvf+LJpbPX0aSHt+3fMS6OEU=
  dependencies:
    get-intrinsic "^1.2.4"

es-errors@^1.3.0:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/es-errors/download/es-errors-1.3.0.tgz#05f75a25dab98e4fb1dcd5e1472c0546d5057c8f"
  integrity sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=

es-to-primitive@^1.2.1:
  version "1.2.1"
  resolved "http://registry.npm.qima-inc.com/es-to-primitive/download/es-to-primitive-1.2.1.tgz"
  integrity sha1-5VzUyc3BiLzvsDs2bHNjI/xciYo=
  dependencies:
    is-callable "^1.1.4"
    is-date-object "^1.0.1"
    is-symbol "^1.0.2"

es6-promise@^4.0.3:
  version "4.2.8"
  resolved "http://registry.npm.qima-inc.com/es6-promise/download/es6-promise-4.2.8.tgz"
  integrity sha1-TrIVlMlyvEBVPSduUQU5FD21Pgo=

es6-promisify@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/es6-promisify/download/es6-promisify-5.0.0.tgz"
  integrity sha1-UQnWLz5W6pZ8S2NQWu8IKRyKUgM=
  dependencies:
    es6-promise "^4.0.3"

escape-html@^1.0.3, escape-html@~1.0.1:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/escape-html/download/escape-html-1.0.3.tgz"
  integrity sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=

escape-regexp@0.0.1:
  version "0.0.1"
  resolved "http://registry.npm.qima-inc.com/escape-regexp/download/escape-regexp-0.0.1.tgz#f44bda12d45bbdf9cb7f862ee7e4827b3dd32254"
  integrity sha1-9EvaEtRbvfnLf4Yu5+SCez3TIlQ=

escape-string-regexp@^1.0.2, escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "http://registry.npm.qima-inc.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

escape-string-regexp@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/escape-string-regexp/download/escape-string-regexp-2.0.0.tgz"
  integrity sha1-owME6Z2qMuI7L9IPUbq9B8/8o0Q=

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/escape-string-regexp/download/escape-string-regexp-4.0.0.tgz#14ba83a5d373e3d311e5afca29cf5bfad965bf34"
  integrity sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=

escodegen@1.x.x, escodegen@^1.14.1, escodegen@^1.8.1:
  version "1.14.3"
  resolved "http://registry.npm.qima-inc.com/escodegen/download/escodegen-1.14.3.tgz"
  integrity sha1-TnuB+6YVgdyXWC7XjKt/Do1j9QM=
  dependencies:
    esprima "^4.0.1"
    estraverse "^4.2.0"
    esutils "^2.0.2"
    optionator "^0.8.1"
  optionalDependencies:
    source-map "~0.6.1"

eslint-config-airbnb-base@^14.1.0:
  version "14.2.1"
  resolved "http://registry.npm.qima-inc.com/eslint-config-airbnb-base/download/eslint-config-airbnb-base-14.2.1.tgz"
  integrity sha1-ii6zhFXcWjElUBk7MZza7vBCzR4=
  dependencies:
    confusing-browser-globals "^1.0.10"
    object.assign "^4.1.2"
    object.entries "^1.1.2"

eslint-config-prettier@^6.11.0:
  version "6.15.0"
  resolved "http://registry.npm.qima-inc.com/eslint-config-prettier/download/eslint-config-prettier-6.15.0.tgz"
  integrity sha1-f5P2y31FqS8VN6cOzAY2bhrG/tk=
  dependencies:
    get-stdin "^6.0.0"

eslint-import-resolver-node@^0.3.4:
  version "0.3.4"
  resolved "http://registry.npm.qima-inc.com/eslint-import-resolver-node/download/eslint-import-resolver-node-0.3.4.tgz"
  integrity sha1-hf+oGULCUBLYIxCW3fZ5wDBCxxc=
  dependencies:
    debug "^2.6.9"
    resolve "^1.13.1"

eslint-module-utils@^2.6.0:
  version "2.6.0"
  resolved "http://registry.npm.qima-inc.com/eslint-module-utils/download/eslint-module-utils-2.6.0.tgz"
  integrity sha1-V569CU9Wr3eX0ZyYZsnJSGYpv6Y=
  dependencies:
    debug "^2.6.9"
    pkg-dir "^2.0.0"

eslint-plugin-import@^2.20.2:
  version "2.22.1"
  resolved "http://registry.npm.qima-inc.com/eslint-plugin-import/download/eslint-plugin-import-2.22.1.tgz"
  integrity sha1-CJbH5qDPRBCaLZe5WQPCu2iddwI=
  dependencies:
    array-includes "^3.1.1"
    array.prototype.flat "^1.2.3"
    contains-path "^0.1.0"
    debug "^2.6.9"
    doctrine "1.5.0"
    eslint-import-resolver-node "^0.3.4"
    eslint-module-utils "^2.6.0"
    has "^1.0.3"
    minimatch "^3.0.4"
    object.values "^1.1.1"
    read-pkg-up "^2.0.0"
    resolve "^1.17.0"
    tsconfig-paths "^3.9.0"

eslint-plugin-prettier@^4.2.1:
  version "4.2.1"
  resolved "http://registry.npm.qima-inc.com/eslint-plugin-prettier/download/eslint-plugin-prettier-4.2.1.tgz#651cbb88b1dab98bfd42f017a12fa6b2d993f94b"
  integrity sha1-ZRy7iLHauYv9QvAXoS+mstmT+Us=
  dependencies:
    prettier-linter-helpers "^1.0.0"

eslint-plugin-vue@^6.2.2:
  version "6.2.2"
  resolved "http://registry.npm.qima-inc.com/eslint-plugin-vue/download/eslint-plugin-vue-6.2.2.tgz"
  integrity sha1-J/7NmjokeJsPER7N1UCp5WGY4P4=
  dependencies:
    natural-compare "^1.4.0"
    semver "^5.6.0"
    vue-eslint-parser "^7.0.0"

eslint-plugin-youzan@^0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.qima-inc.com/eslint-plugin-youzan/download/eslint-plugin-youzan-0.3.0.tgz"
  integrity sha1-kU/0fzDdAAkQlxJkGJTbDuCuZ9U=
  dependencies:
    requireindex "~1.1.0"

eslint-scope@^5.0.0:
  version "5.1.1"
  resolved "http://registry.npm.qima-inc.com/eslint-scope/download/eslint-scope-5.1.1.tgz"
  integrity sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

eslint-utils@^1.4.3:
  version "1.4.3"
  resolved "http://registry.npm.qima-inc.com/eslint-utils/download/eslint-utils-1.4.3.tgz"
  integrity sha1-dP7HxU0Hdrb2fgJRBAtYBlZOmB8=
  dependencies:
    eslint-visitor-keys "^1.1.0"

eslint-utils@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/eslint-utils/download/eslint-utils-2.1.0.tgz"
  integrity sha1-0t5eA0JOcH3BDHQGjd7a5wh0Gyc=
  dependencies:
    eslint-visitor-keys "^1.1.0"

eslint-visitor-keys@^1.1.0:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/eslint-visitor-keys/download/eslint-visitor-keys-1.3.0.tgz"
  integrity sha1-MOvR73wv3/AcOk8VEESvJfqwUj4=

eslint@^6.0.0:
  version "6.8.0"
  resolved "http://registry.npm.qima-inc.com/eslint/download/eslint-6.8.0.tgz"
  integrity sha1-YiYtZylzn5J1cjgkMC+yJ8jJP/s=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    ajv "^6.10.0"
    chalk "^2.1.0"
    cross-spawn "^6.0.5"
    debug "^4.0.1"
    doctrine "^3.0.0"
    eslint-scope "^5.0.0"
    eslint-utils "^1.4.3"
    eslint-visitor-keys "^1.1.0"
    espree "^6.1.2"
    esquery "^1.0.1"
    esutils "^2.0.2"
    file-entry-cache "^5.0.1"
    functional-red-black-tree "^1.0.1"
    glob-parent "^5.0.0"
    globals "^12.1.0"
    ignore "^4.0.6"
    import-fresh "^3.0.0"
    imurmurhash "^0.1.4"
    inquirer "^7.0.0"
    is-glob "^4.0.0"
    js-yaml "^3.13.1"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.3.0"
    lodash "^4.17.14"
    minimatch "^3.0.4"
    mkdirp "^0.5.1"
    natural-compare "^1.4.0"
    optionator "^0.8.3"
    progress "^2.0.0"
    regexpp "^2.0.1"
    semver "^6.1.2"
    strip-ansi "^5.2.0"
    strip-json-comments "^3.0.1"
    table "^5.2.3"
    text-table "^0.2.0"
    v8-compile-cache "^2.0.3"

espree@^6.1.2, espree@^6.2.1:
  version "6.2.1"
  resolved "http://registry.npm.qima-inc.com/espree/download/espree-6.2.1.tgz"
  integrity sha1-d/xy4f10SiBSwg84pbV1gy6Cc0o=
  dependencies:
    acorn "^7.1.1"
    acorn-jsx "^5.2.0"
    eslint-visitor-keys "^1.1.0"

esprima@3.x.x:
  version "3.1.3"
  resolved "http://registry.npm.qima-inc.com/esprima/download/esprima-3.1.3.tgz"
  integrity sha1-/cpRzuYTOJXjyI1TXOSdv/YqRjM=

esprima@^4.0.0, esprima@^4.0.1, esprima@~4.0.0:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/esprima/download/esprima-4.0.1.tgz"
  integrity sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=

esquery@^1.0.1:
  version "1.3.1"
  resolved "http://registry.npm.qima-inc.com/esquery/download/esquery-1.3.1.tgz"
  integrity sha1-t4tYKKqOIU4p+3TE1bdS4cAz2lc=
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "http://registry.npm.qima-inc.com/esrecurse/download/esrecurse-4.3.0.tgz"
  integrity sha1-eteWTWeauyi+5yzsY3WLHF0smSE=
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1, estraverse@^4.2.0:
  version "4.3.0"
  resolved "http://registry.npm.qima-inc.com/estraverse/download/estraverse-4.3.0.tgz"
  integrity sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=

estraverse@^5.1.0, estraverse@^5.2.0:
  version "5.2.0"
  resolved "http://registry.npm.qima-inc.com/estraverse/download/estraverse-5.2.0.tgz"
  integrity sha1-MH30JUfmzHMk088DwVXVzbjFOIA=

esutils@^2.0.2:
  version "2.0.3"
  resolved "http://registry.npm.qima-inc.com/esutils/download/esutils-2.0.3.tgz"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

event-stream@~0.9.8:
  version "0.9.8"
  resolved "http://registry.npm.qima-inc.com/event-stream/download/event-stream-0.9.8.tgz"
  integrity sha1-XanPPHkAl1mJ21powo5bPJjr4Do=
  dependencies:
    optimist "0.2"

event-target-shim@^5.0.0:
  version "5.0.1"
  resolved "http://registry.npm.qima-inc.com/event-target-shim/download/event-target-shim-5.0.1.tgz"
  integrity sha1-XU0+vflYPWOlMzzi3rdICrKwV4k=

eventemitter2@5.0.1, eventemitter2@^5.0.1, eventemitter2@~5.0.1:
  version "5.0.1"
  resolved "http://registry.npm.qima-inc.com/eventemitter2/download/eventemitter2-5.0.1.tgz#6197a095d5fb6b57e8942f6fd7eaad63a09c9452"
  integrity sha1-YZegldX7a1folC9v1+qtY6CclFI=

eventemitter2@^6.3.1:
  version "6.4.7"
  resolved "http://registry.npm.qima-inc.com/eventemitter2/download/eventemitter2-6.4.7.tgz#a7f6c4d7abf28a14c1ef3442f21cb306a054271d"
  integrity sha1-p/bE16vyihTB7zRC8hyzBqBUJx0=

eventemitter2@~0.4.14:
  version "0.4.14"
  resolved "http://registry.npm.qima-inc.com/eventemitter2/download/eventemitter2-0.4.14.tgz#8f61b75cde012b2e9eb284d4545583b5643b61ab"
  integrity sha1-j2G3XN4BKy6esoTUVFWDtWQ7Yas=

eventemitter3@4.x, eventemitter3@^4.0.4:
  version "4.0.7"
  resolved "http://registry.npm.qima-inc.com/eventemitter3/download/eventemitter3-4.0.7.tgz#2de9b68f6528d5644ef5c59526a1b4a07306169f"
  integrity sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8=

exec-sh@^0.3.2:
  version "0.3.4"
  resolved "http://registry.npm.qima-inc.com/exec-sh/download/exec-sh-0.3.4.tgz"
  integrity sha1-OgGM61JsxvbfK7UEsr/o46STTsU=

execa@^0.7.0:
  version "0.7.0"
  resolved "http://registry.npm.qima-inc.com/execa/download/execa-0.7.0.tgz"
  integrity sha1-lEvs00zEHuMqY6n68nrVpl/Fl3c=
  dependencies:
    cross-spawn "^5.0.1"
    get-stream "^3.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

execa@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/execa/download/execa-1.0.0.tgz"
  integrity sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg=
  dependencies:
    cross-spawn "^6.0.0"
    get-stream "^4.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

execa@^4.0.0, execa@^4.1.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/execa/download/execa-4.1.0.tgz"
  integrity sha1-TlSRrRVy8vF6d9OIxshXE1sihHo=
  dependencies:
    cross-spawn "^7.0.0"
    get-stream "^5.0.0"
    human-signals "^1.1.1"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.0"
    onetime "^5.1.0"
    signal-exit "^3.0.2"
    strip-final-newline "^2.0.0"

execa@^5.1.1:
  version "5.1.1"
  resolved "http://registry.npm.qima-inc.com/execa/download/execa-5.1.1.tgz#f80ad9cbf4298f7bd1d4c9555c21e93741c411dd"
  integrity sha1-+ArZy/Qpj3vR1MlVXCHpN0HEEd0=
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.0"
    human-signals "^2.1.0"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.1"
    onetime "^5.1.2"
    signal-exit "^3.0.3"
    strip-final-newline "^2.0.0"

exif-js@^2.3.0:
  version "2.3.0"
  resolved "http://registry.npm.qima-inc.com/exif-js/download/exif-js-2.3.0.tgz"
  integrity sha1-nRCBm/Vx+HOBPnZAJBJVq5zhqBQ=

exit@^0.1.2:
  version "0.1.2"
  resolved "http://registry.npm.qima-inc.com/exit/download/exit-0.1.2.tgz"
  integrity sha1-BjJjj42HfMghB9MKD/8aF8uhzQw=

expand-brackets@^2.1.4:
  version "2.1.4"
  resolved "http://registry.npm.qima-inc.com/expand-brackets/download/expand-brackets-2.1.4.tgz"
  integrity sha1-t3c14xXOMPa27/D4OwQVGiJEliI=
  dependencies:
    debug "^2.3.3"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    posix-character-classes "^0.1.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

expect@^26.6.2:
  version "26.6.2"
  resolved "http://registry.npm.qima-inc.com/expect/download/expect-26.6.2.tgz"
  integrity sha1-xrmWvya/P+GLZ7LQ9R/JgbqTRBc=
  dependencies:
    "@jest/types" "^26.6.2"
    ansi-styles "^4.0.0"
    jest-get-type "^26.3.0"
    jest-matcher-utils "^26.6.2"
    jest-message-util "^26.6.2"
    jest-regex-util "^26.0.0"

extend-shallow@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/extend-shallow/download/extend-shallow-2.0.1.tgz"
  integrity sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=
  dependencies:
    is-extendable "^0.1.0"

extend-shallow@^3.0.0, extend-shallow@^3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/extend-shallow/download/extend-shallow-3.0.2.tgz"
  integrity sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=
  dependencies:
    assign-symbols "^1.0.0"
    is-extendable "^1.0.1"

extend@^3.0.0, extend@^3.0.2, extend@~3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/extend/download/extend-3.0.2.tgz"
  integrity sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo=

external-editor@^3.0.3:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/external-editor/download/external-editor-3.1.0.tgz"
  integrity sha1-ywP3QL764D6k0oPK7SdBqD8zVJU=
  dependencies:
    chardet "^0.7.0"
    iconv-lite "^0.4.24"
    tmp "^0.0.33"

extglob@^2.0.4:
  version "2.0.4"
  resolved "http://registry.npm.qima-inc.com/extglob/download/extglob-2.0.4.tgz"
  integrity sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM=
  dependencies:
    array-unique "^0.3.2"
    define-property "^1.0.0"
    expand-brackets "^2.1.4"
    extend-shallow "^2.0.1"
    fragment-cache "^0.2.1"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

extsprintf@1.3.0:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/extsprintf/download/extsprintf-1.3.0.tgz"
  integrity sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=

extsprintf@^1.2.0:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/extsprintf/download/extsprintf-1.4.0.tgz"
  integrity sha1-4mifjzVvrWLMplo6kcXfX5VRaS8=

fast-deep-equal@^3.1.1:
  version "3.1.3"
  resolved "http://registry.npm.qima-inc.com/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-diff@^1.1.2:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/fast-diff/download/fast-diff-1.3.0.tgz#ece407fa550a64d638536cd727e129c61616e0f0"
  integrity sha1-7OQH+lUKZNY4U2zXJ+EpxhYW4PA=

fast-equals@^4.0.3:
  version "4.0.3"
  resolved "http://registry.npm.qima-inc.com/fast-equals/download/fast-equals-4.0.3.tgz#72884cc805ec3c6679b99875f6b7654f39f0e8c7"
  integrity sha1-cohMyAXsPGZ5uZh19rdlTznw6Mc=

fast-glob@2.2.6:
  version "2.2.6"
  resolved "http://registry.npm.qima-inc.com/fast-glob/download/fast-glob-2.2.6.tgz"
  integrity sha1-pdW2l+yN7aRo2Fp0A1KQoCWpUpU=
  dependencies:
    "@mrmlnc/readdir-enhanced" "^2.2.1"
    "@nodelib/fs.stat" "^1.1.2"
    glob-parent "^3.1.0"
    is-glob "^4.0.0"
    merge2 "^1.2.3"
    micromatch "^3.1.10"

fast-glob@^3.2.12, fast-glob@^3.2.2:
  version "3.3.2"
  resolved "http://registry.npm.qima-inc.com/fast-glob/download/fast-glob-3.3.2.tgz#a904501e57cfdd2ffcded45e99a54fef55e46129"
  integrity sha1-qQRQHlfP3S/83tRemaVP71XkYSk=
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fast-json-stable-stringify@2.x, fast-json-stable-stringify@^2.0.0, fast-json-stable-stringify@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fast-json-stringify@^2.2.3:
  version "2.3.1"
  resolved "http://registry.npm.qima-inc.com/fast-json-stringify/download/fast-json-stringify-2.3.1.tgz"
  integrity sha1-aUIhodKuCEFpA8Dt6TCUZiIM+y8=
  dependencies:
    ajv "^6.11.0"
    deepmerge "^4.2.2"
    string-similarity "^4.0.1"

fast-levenshtein@~2.0.6:
  version "2.0.6"
  resolved "http://registry.npm.qima-inc.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

fast-redact@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/fast-redact/download/fast-redact-2.1.0.tgz"
  integrity sha1-3+PBymk2f7Im8RCqTsEOyFRi/98=

fast-redact@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/fast-redact/download/fast-redact-3.0.0.tgz"
  integrity sha1-rC+eNsn0l29dufsYxv+68wjPMW0=

fast-safe-stringify@^2.0.7:
  version "2.0.7"
  resolved "http://registry.npm.qima-inc.com/fast-safe-stringify/download/fast-safe-stringify-2.0.7.tgz"
  integrity sha1-EkqohYmSYfaK7bQqfAgN6dpgh0M=

fast-text-encoding@^1.0.0:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/fast-text-encoding/download/fast-text-encoding-1.0.3.tgz"
  integrity sha1-7AKsjgGrijGa8YLa4mgSE8/pzlM=

fast-url-parser@^1.1.3:
  version "1.1.3"
  resolved "http://registry.npm.qima-inc.com/fast-url-parser/download/fast-url-parser-1.1.3.tgz"
  integrity sha1-9K8+qfNNiicc9YrSs3WfQx8LMY0=
  dependencies:
    punycode "^1.3.2"

fastq@^1.6.0:
  version "1.17.1"
  resolved "http://registry.npm.qima-inc.com/fastq/download/fastq-1.17.1.tgz#2a523f07a4e7b1e81a42b91b8bf2254107753b47"
  integrity sha1-KlI/B6TnsegaQrkbi/IlQQd1O0c=
  dependencies:
    reusify "^1.0.4"

fb-watchman@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/fb-watchman/download/fb-watchman-2.0.1.tgz"
  integrity sha1-/IT7OdJwnPP/bXQ3BhV7tXCKioU=
  dependencies:
    bser "2.1.1"

fclone@1.0.11, fclone@~1.0.11:
  version "1.0.11"
  resolved "http://registry.npm.qima-inc.com/fclone/download/fclone-1.0.11.tgz#10e85da38bfea7fc599341c296ee1d77266ee640"
  integrity sha1-EOhdo4v+p/xZk0HClu4ddyZu5kA=

fecha@^3.0.2:
  version "3.0.3"
  resolved "http://registry.npm.qima-inc.com/fecha/download/fecha-3.0.3.tgz"
  integrity sha1-+rvUFkl2SaQsJNNL+nJrV5IDoeI=

fecha@^4.2.0:
  version "4.2.1"
  resolved "http://registry.npm.qima-inc.com/fecha/download/fecha-4.2.1.tgz#0a83ad8f86ef62a091e22bb5a039cd03d23eecce"
  integrity sha1-CoOtj4bvYqCR4iu1oDnNA9I+7M4=

figures@^3.0.0:
  version "3.2.0"
  resolved "http://registry.npm.qima-inc.com/figures/download/figures-3.2.0.tgz"
  integrity sha1-YlwYvSk8YE3EqN2y/r8MiDQXRq8=
  dependencies:
    escape-string-regexp "^1.0.5"

file-entry-cache@^5.0.1:
  version "5.0.1"
  resolved "http://registry.npm.qima-inc.com/file-entry-cache/download/file-entry-cache-5.0.1.tgz"
  integrity sha1-yg9u+m3T1WEzP7FFFQZcL6/fQ5w=
  dependencies:
    flat-cache "^2.0.1"

file-entry-cache@^6.0.1:
  version "6.0.1"
  resolved "http://registry.npm.qima-inc.com/file-entry-cache/download/file-entry-cache-6.0.1.tgz#211b2dd9659cb0394b073e7323ac3c933d522027"
  integrity sha1-IRst2WWcsDlLBz5zI6w8kz1SICc=
  dependencies:
    flat-cache "^3.0.4"

file-uri-to-path@1, file-uri-to-path@1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/file-uri-to-path/download/file-uri-to-path-1.0.0.tgz"
  integrity sha1-VTp7hEb/b2hDWcRF8eN6BdrMM90=

file-uri-to-path@2:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/file-uri-to-path/download/file-uri-to-path-2.0.0.tgz#7b415aeba227d575851e0a5b0c640d7656403fba"
  integrity sha1-e0Fa66In1XWFHgpbDGQNdlZAP7o=

fill-range@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/fill-range/download/fill-range-4.0.0.tgz"
  integrity sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=
  dependencies:
    extend-shallow "^2.0.1"
    is-number "^3.0.0"
    repeat-string "^1.6.1"
    to-regex-range "^2.1.0"

fill-range@^7.0.1:
  version "7.0.1"
  resolved "http://registry.npm.qima-inc.com/fill-range/download/fill-range-7.0.1.tgz"
  integrity sha1-GRmmp8df44ssfHflGYU12prN2kA=
  dependencies:
    to-regex-range "^5.0.1"

fill-range@^7.1.1:
  version "7.1.1"
  resolved "http://registry.npm.qima-inc.com/fill-range/download/fill-range-7.1.1.tgz#44265d3cac07e3ea7dc247516380643754a05292"
  integrity sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=
  dependencies:
    to-regex-range "^5.0.1"

filter-obj@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/filter-obj/download/filter-obj-1.1.0.tgz#9b311112bc6c6127a16e016c6c5d7f19e0805c5b"
  integrity sha1-mzERErxsYSehbgFsbF1/GeCAXFs=

find-up@^2.0.0, find-up@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/find-up/download/find-up-2.1.0.tgz"
  integrity sha1-RdG35QbHF93UgndaK3eSCjwMV6c=
  dependencies:
    locate-path "^2.0.0"

find-up@^4.0.0, find-up@^4.1.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/find-up/download/find-up-4.1.0.tgz"
  integrity sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

find-up@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/find-up/download/find-up-5.0.0.tgz#4c92819ecb7083561e4f4a240a86be5198f536fc"
  integrity sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=
  dependencies:
    locate-path "^6.0.0"
    path-exists "^4.0.0"

find-versions@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/find-versions/download/find-versions-4.0.0.tgz#3c57e573bf97769b8cb8df16934b627915da4965"
  integrity sha512-wgpWy002tA+wgmO27buH/9KzyEOQnKsG/R0yrcjPT9BOFm0zRBVQbZ95nRGXWMywS8YR5knRbpohio0bcJABxQ==
  dependencies:
    semver-regex "^3.1.2"

flat-cache@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/flat-cache/download/flat-cache-2.0.1.tgz"
  integrity sha1-XSltbwS9pEpGMKMBQTvbwuwIXsA=
  dependencies:
    flatted "^2.0.0"
    rimraf "2.6.3"
    write "1.0.3"

flat-cache@^3.0.4:
  version "3.2.0"
  resolved "http://registry.npm.qima-inc.com/flat-cache/download/flat-cache-3.2.0.tgz#2c0c2d5040c99b1632771a9d105725c0115363ee"
  integrity sha1-LAwtUEDJmxYydxqdEFclwBFTY+4=
  dependencies:
    flatted "^3.2.9"
    keyv "^4.5.3"
    rimraf "^3.0.2"

flatstr@^1.0.12:
  version "1.0.12"
  resolved "http://registry.npm.qima-inc.com/flatstr/download/flatstr-1.0.12.tgz"
  integrity sha1-wrpqCBc+27bJZA4wVbleKHzrWTE=

flatted@^2.0.0:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/flatted/download/flatted-2.0.2.tgz"
  integrity sha1-RXWyHivO50NKqb5mL0t7X5wrUTg=

flatted@^3.2.9:
  version "3.3.1"
  resolved "http://registry.npm.qima-inc.com/flatted/download/flatted-3.3.1.tgz#21db470729a6734d4997002f439cb308987f567a"
  integrity sha1-IdtHBymmc01JlwAvQ5yzCJh/Vno=

flexbuffer@0.0.6:
  version "0.0.6"
  resolved "http://registry.npm.qima-inc.com/flexbuffer/download/flexbuffer-0.0.6.tgz"
  integrity sha1-A5/fI/iCPkQMOPMnfm/vEXQhWzA=

follow-redirects@1.5.10:
  version "1.5.10"
  resolved "http://registry.npm.qima-inc.com/follow-redirects/download/follow-redirects-1.5.10.tgz"
  integrity sha1-e3qfmuov3/NnhqlP9kPtB/T/Xio=
  dependencies:
    debug "=3.1.0"

follow-redirects@^1.14.0:
  version "1.15.1"
  resolved "http://registry.npm.qima-inc.com/follow-redirects/download/follow-redirects-1.15.1.tgz#0ca6a452306c9b276e4d3127483e29575e207ad5"
  integrity sha1-DKakUjBsmyduTTEnSD4pV14getU=

follow-redirects@^1.2.3, follow-redirects@^1.3.0:
  version "1.13.1"
  resolved "http://registry.npm.qima-inc.com/follow-redirects/download/follow-redirects-1.13.1.tgz"
  integrity sha1-X2m4Ezds7k/QR0o6uoNd8Eq3Y7c=

for-in@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/for-in/download/for-in-1.0.2.tgz"
  integrity sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=

foreach@^2.0.5:
  version "2.0.5"
  resolved "http://registry.npm.qima-inc.com/foreach/download/foreach-2.0.5.tgz"
  integrity sha1-C+4AUBiusmDQo6865ljdATbsG5k=

forever-agent@~0.6.1:
  version "0.6.1"
  resolved "http://registry.npm.qima-inc.com/forever-agent/download/forever-agent-0.6.1.tgz"
  integrity sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=

form-data@^2.3.1:
  version "2.5.1"
  resolved "http://registry.npm.qima-inc.com/form-data/download/form-data-2.5.1.tgz"
  integrity sha1-8svsV7XlniNxbhKP5E1OXdI4lfQ=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.6"
    mime-types "^2.1.12"

form-data@~2.3.2:
  version "2.3.3"
  resolved "http://registry.npm.qima-inc.com/form-data/download/form-data-2.3.3.tgz"
  integrity sha1-3M5SwF9kTymManq5Nr1yTO/786Y=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.6"
    mime-types "^2.1.12"

formidable@^1.1.1, formidable@^1.2.0:
  version "1.2.2"
  resolved "http://registry.npm.qima-inc.com/formidable/download/formidable-1.2.2.tgz"
  integrity sha1-v2muopcpgmdfAIZTQrmCmG9rjdk=

formstream@1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/formstream/download/formstream-1.1.0.tgz"
  integrity sha1-UfOXDyYTbrCtRDBN5M67UCB7RHk=
  dependencies:
    destroy "^1.0.4"
    mime "^1.3.4"
    pause-stream "~0.0.11"

formstream@^1.1.0:
  version "1.3.1"
  resolved "http://registry.npm.qima-inc.com/formstream/download/formstream-1.3.1.tgz#6c6f53c1c09f0ffc43231022b355b1d5feda3016"
  integrity sha1-bG9TwcCfD/xDIxAis1Wx1f7aMBY=
  dependencies:
    destroy "^1.0.4"
    mime "^2.5.2"
    pause-stream "~0.0.11"

fragment-cache@^0.2.1:
  version "0.2.1"
  resolved "http://registry.npm.qima-inc.com/fragment-cache/download/fragment-cache-0.2.1.tgz"
  integrity sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=
  dependencies:
    map-cache "^0.2.2"

fresh@^0.5.2:
  version "0.5.2"
  resolved "http://registry.npm.qima-inc.com/fresh/download/fresh-0.5.2.tgz"
  integrity sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=

fs-extra@9.0.1, fs-extra@^9.0.1:
  version "9.0.1"
  resolved "http://registry.npm.qima-inc.com/fs-extra/download/fs-extra-9.0.1.tgz"
  integrity sha1-kQ2gBiQ3ukw5/t2GPxZ1zP78ufw=
  dependencies:
    at-least-node "^1.0.0"
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^1.0.0"

fs-extra@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/fs-extra/download/fs-extra-5.0.0.tgz"
  integrity sha1-QU0BEM3QZwVzTQVWUsVBEmDDGr0=
  dependencies:
    graceful-fs "^4.1.2"
    jsonfile "^4.0.0"
    universalify "^0.1.0"

fs-extra@^8.1.0:
  version "8.1.0"
  resolved "http://registry.npm.qima-inc.com/fs-extra/download/fs-extra-8.1.0.tgz#49d43c45a88cd9677668cb7be1b46efdb8d2e1c0"
  integrity sha1-SdQ8RaiM2Wd2aMt74bRu/bjS4cA=
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^4.0.0"
    universalify "^0.1.0"

fs-extra@^9.0.0, fs-extra@^9.1.0:
  version "9.1.0"
  resolved "http://registry.npm.qima-inc.com/fs-extra/download/fs-extra-9.1.0.tgz#5954460c764a8da2094ba3554bf839e6b9a7c86d"
  integrity sha1-WVRGDHZKjaIJS6NVS/g55rmnyG0=
  dependencies:
    at-least-node "^1.0.0"
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/fs.realpath/download/fs.realpath-1.0.0.tgz"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fsevents@^1.2.7:
  version "1.2.13"
  resolved "http://registry.npm.qima-inc.com/fsevents/download/fsevents-1.2.13.tgz"
  integrity sha1-8yXLBFVZJCi88Rs4M3DvcOO/zDg=
  dependencies:
    bindings "^1.5.0"
    nan "^2.12.1"

fsevents@^2.1.2:
  version "2.3.1"
  resolved "http://registry.npm.qima-inc.com/fsevents/download/fsevents-2.3.1.tgz"
  integrity sha1-sgmrFMYQEmNsiGNQft9/tozFTp8=

fsevents@~2.3.2:
  version "2.3.2"
  resolved "http://registry.npm.qima-inc.com/fsevents/download/fsevents-2.3.2.tgz#8a526f78b8fdf4623b709e0b975c52c24c02fd1a"
  integrity sha1-ilJveLj99GI7cJ4Ll1xSwkwC/Ro=

ftp@^0.3.10, ftp@~0.3.10:
  version "0.3.10"
  resolved "http://registry.npm.qima-inc.com/ftp/download/ftp-0.3.10.tgz"
  integrity sha1-kZfYYa2BQvPmPVqDv+TFn3MwiF0=
  dependencies:
    readable-stream "1.1.x"
    xregexp "2.0.0"

function-bind@^1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/function-bind/download/function-bind-1.1.1.tgz"
  integrity sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0=

function-bind@^1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/function-bind/download/function-bind-1.1.2.tgz#2c02d864d97f3ea6c8830c464cbd11ab6eab7a1c"
  integrity sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=

functional-red-black-tree@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/functional-red-black-tree/download/functional-red-black-tree-1.0.1.tgz"
  integrity sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=

gaxios@^4.0.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/gaxios/download/gaxios-4.1.0.tgz"
  integrity sha1-6K1GbbWkODxwudY7/RTfqofrAJk=
  dependencies:
    abort-controller "^3.0.0"
    extend "^3.0.2"
    https-proxy-agent "^5.0.0"
    is-stream "^2.0.0"
    node-fetch "^2.3.0"

gcp-metadata@^4.2.0:
  version "4.2.1"
  resolved "http://registry.npm.qima-inc.com/gcp-metadata/download/gcp-metadata-4.2.1.tgz"
  integrity sha1-MYSfvPkCXvNMIpfDKomh5+nyzWI=
  dependencies:
    gaxios "^4.0.0"
    json-bigint "^1.0.0"

gensequence@^5.0.2:
  version "5.0.2"
  resolved "http://registry.npm.qima-inc.com/gensequence/download/gensequence-5.0.2.tgz#f065be2f9a5b2967b9cad7f33b2d79ce1f22dc82"
  integrity sha1-8GW+L5pbKWe5ytfzOy15zh8i3II=

gensync@^1.0.0-beta.1:
  version "1.0.0-beta.2"
  resolved "http://registry.npm.qima-inc.com/gensync/download/gensync-1.0.0-beta.2.tgz"
  integrity sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=

get-caller-file@^2.0.1:
  version "2.0.5"
  resolved "http://registry.npm.qima-inc.com/get-caller-file/download/get-caller-file-2.0.5.tgz"
  integrity sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=

get-intrinsic@^1.0.1, get-intrinsic@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/get-intrinsic/download/get-intrinsic-1.0.2.tgz"
  integrity sha1-aCDaIm5QskiU4IhZRp3Gg2FUXUk=
  dependencies:
    function-bind "^1.1.1"
    has "^1.0.3"
    has-symbols "^1.0.1"

get-intrinsic@^1.1.3, get-intrinsic@^1.2.4:
  version "1.2.4"
  resolved "http://registry.npm.qima-inc.com/get-intrinsic/download/get-intrinsic-1.2.4.tgz#e385f5a4b5227d449c3eabbad05494ef0abbeadd"
  integrity sha1-44X1pLUifUScPqu60FSU7wq76t0=
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    has-proto "^1.0.1"
    has-symbols "^1.0.3"
    hasown "^2.0.0"

get-own-enumerable-property-symbols@^3.0.0:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/get-own-enumerable-property-symbols/download/get-own-enumerable-property-symbols-3.0.2.tgz#b5fde77f22cbe35f390b4e089922c50bce6ef664"
  integrity sha1-tf3nfyLL4185C04ImSLFC85u9mQ=

get-package-type@^0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.qima-inc.com/get-package-type/download/get-package-type-0.1.0.tgz"
  integrity sha1-jeLYA8/0TfO8bEVuZmizbDkm4Ro=

get-port@^3.1.0:
  version "3.2.0"
  resolved "http://registry.npm.qima-inc.com/get-port/download/get-port-3.2.0.tgz"
  integrity sha1-3Xzn3hh8Bsi/NTeWrHHgmfCYDrw=

get-ready@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/get-ready/download/get-ready-2.0.1.tgz"
  integrity sha1-pIxBh1PjnPTQHzpCDPG3V93MZI8=
  dependencies:
    is-type-of "^1.0.0"

get-stdin@^6.0.0:
  version "6.0.0"
  resolved "http://registry.npm.qima-inc.com/get-stdin/download/get-stdin-6.0.0.tgz"
  integrity sha1-ngm/cSs2CrkiXoEgSPcf3pyJZXs=

get-stdin@^8.0.0:
  version "8.0.0"
  resolved "http://registry.npm.qima-inc.com/get-stdin/download/get-stdin-8.0.0.tgz#cbad6a73feb75f6eeb22ba9e01f89aa28aa97a53"
  integrity sha1-y61qc/63X27rIrqeAfiaooqpelM=

get-stream@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/get-stream/download/get-stream-3.0.0.tgz"
  integrity sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ=

get-stream@^4.0.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/get-stream/download/get-stream-4.1.0.tgz"
  integrity sha1-wbJVV189wh1Zv8ec09K0axw6VLU=
  dependencies:
    pump "^3.0.0"

get-stream@^5.0.0:
  version "5.2.0"
  resolved "http://registry.npm.qima-inc.com/get-stream/download/get-stream-5.2.0.tgz"
  integrity sha1-SWaheV7lrOZecGxLe+txJX1uItM=
  dependencies:
    pump "^3.0.0"

get-stream@^6.0.0:
  version "6.0.1"
  resolved "http://registry.npm.qima-inc.com/get-stream/download/get-stream-6.0.1.tgz#a262d8eef67aced57c2852ad6167526a43cbf7b7"
  integrity sha1-omLY7vZ6ztV8KFKtYWdSakPL97c=

get-uri@3:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/get-uri/download/get-uri-3.0.2.tgz#f0ef1356faabc70e1f9404fa3b66b2ba9bfc725c"
  integrity sha1-8O8TVvqrxw4flAT6O2ayupv8clw=
  dependencies:
    "@tootallnate/once" "1"
    data-uri-to-buffer "3"
    debug "4"
    file-uri-to-path "2"
    fs-extra "^8.1.0"
    ftp "^0.3.10"

get-uri@^2.0.0:
  version "2.0.4"
  resolved "http://registry.npm.qima-inc.com/get-uri/download/get-uri-2.0.4.tgz"
  integrity sha1-1JN6uBniGNTLWuGOT1livvFpzGo=
  dependencies:
    data-uri-to-buffer "1"
    debug "2"
    extend "~3.0.2"
    file-uri-to-path "1"
    ftp "~0.3.10"
    readable-stream "2"

get-value@^2.0.3, get-value@^2.0.6:
  version "2.0.6"
  resolved "http://registry.npm.qima-inc.com/get-value/download/get-value-2.0.6.tgz"
  integrity sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=

getpass@^0.1.1:
  version "0.1.7"
  resolved "http://registry.npm.qima-inc.com/getpass/download/getpass-0.1.7.tgz"
  integrity sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=
  dependencies:
    assert-plus "^1.0.0"

gitignore-to-glob@^0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.qima-inc.com/gitignore-to-glob/download/gitignore-to-glob-0.3.0.tgz#59f32ab3d9b66ce50299c3ed24cb0ef42a094ceb"
  integrity sha1-WfMqs9m2bOUCmcPtJMsO9CoJTOs=

gl-matrix@^2.1.0:
  version "2.8.1"
  resolved "http://registry.npm.qima-inc.com/gl-matrix/download/gl-matrix-2.8.1.tgz"
  integrity sha1-HHhzRI6sYdLNJYA6B06De9QlgaM=

glob-parent@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/glob-parent/download/glob-parent-3.1.0.tgz"
  integrity sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4=
  dependencies:
    is-glob "^3.1.0"
    path-dirname "^1.0.0"

glob-parent@^5.0.0:
  version "5.1.1"
  resolved "http://registry.npm.qima-inc.com/glob-parent/download/glob-parent-5.1.1.tgz"
  integrity sha1-tsHvQXxOVmPqSY8cRa+saRa7wik=
  dependencies:
    is-glob "^4.0.1"

glob-parent@^5.1.2, glob-parent@~5.1.2:
  version "5.1.2"
  resolved "http://registry.npm.qima-inc.com/glob-parent/download/glob-parent-5.1.2.tgz#869832c58034fe68a4093c17dc15e8340d8401c4"
  integrity sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=
  dependencies:
    is-glob "^4.0.1"

glob-to-regexp@^0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.qima-inc.com/glob-to-regexp/download/glob-to-regexp-0.3.0.tgz"
  integrity sha1-jFoUlNIGbFcMw7/kSWF1rMTVAqs=

glob@^7.0.0, glob@^7.1.1, glob@^7.1.2, glob@^7.1.3, glob@^7.1.4, glob@^7.1.6:
  version "7.1.6"
  resolved "http://registry.npm.qima-inc.com/glob/download/glob-7.1.6.tgz"
  integrity sha1-FB8zuBp8JJLhJVlDB0gMRmeSeKY=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

glob@^7.0.5:
  version "7.2.3"
  resolved "http://registry.npm.qima-inc.com/glob/download/glob-7.2.3.tgz#b8df0fb802bbfa8e89bd1d938b4e16578ed44f2b"
  integrity sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

global-dirs@^0.1.0, global-dirs@^0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/global-dirs/download/global-dirs-0.1.1.tgz"
  integrity sha1-sxnA3UYH81PzvpzKTHL8FIxJ9EU=
  dependencies:
    ini "^1.3.4"

globals@^11.1.0:
  version "11.12.0"
  resolved "http://registry.npm.qima-inc.com/globals/download/globals-11.12.0.tgz"
  integrity sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=

globals@^12.1.0:
  version "12.4.0"
  resolved "http://registry.npm.qima-inc.com/globals/download/globals-12.4.0.tgz"
  integrity sha1-oYgTV2pBsAokqX5/gVkYwuGZJfg=
  dependencies:
    type-fest "^0.8.1"

google-auth-library@^6.1.1:
  version "6.1.4"
  resolved "http://registry.npm.qima-inc.com/google-auth-library/download/google-auth-library-6.1.4.tgz"
  integrity sha1-vHDE87ZoGuUnM0NGa87zdXe37kQ=
  dependencies:
    arrify "^2.0.0"
    base64-js "^1.3.0"
    ecdsa-sig-formatter "^1.0.11"
    fast-text-encoding "^1.0.0"
    gaxios "^4.0.0"
    gcp-metadata "^4.2.0"
    gtoken "^5.0.4"
    jws "^4.0.0"
    lru-cache "^6.0.0"

google-p12-pem@^3.0.3:
  version "3.0.3"
  resolved "http://registry.npm.qima-inc.com/google-p12-pem/download/google-p12-pem-3.0.3.tgz"
  integrity sha1-ZzrDp105A6h/BYePPHXgb8FRZp4=
  dependencies:
    node-forge "^0.10.0"

gopd@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/gopd/download/gopd-1.0.1.tgz#29ff76de69dac7489b7c0918a5788e56477c332c"
  integrity sha1-Kf923mnax0ibfAkYpXiOVkd8Myw=
  dependencies:
    get-intrinsic "^1.1.3"

got@^6.7.1:
  version "6.7.1"
  resolved "http://registry.npm.qima-inc.com/got/download/got-6.7.1.tgz"
  integrity sha1-JAzQV4WpoY5WHcG0S0HHY+8ejbA=
  dependencies:
    create-error-class "^3.0.0"
    duplexer3 "^0.1.4"
    get-stream "^3.0.0"
    is-redirect "^1.0.0"
    is-retry-allowed "^1.0.0"
    is-stream "^1.0.0"
    lowercase-keys "^1.0.0"
    safe-buffer "^5.0.1"
    timed-out "^4.0.0"
    unzip-response "^2.0.1"
    url-parse-lax "^1.0.0"

graceful-config@0.0.1:
  version "0.0.1"
  resolved "http://registry.npm.qima-inc.com/graceful-config/download/graceful-config-0.0.1.tgz"
  integrity sha1-TErSHiDb9Rovvpwckby8cswGXZo=
  dependencies:
    debug "^4.1.1"
    lodash "^4.17.19"

graceful-config@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/graceful-config/download/graceful-config-1.0.0.tgz#2755b82d3010aa7c0b31577c81a2be5ab656a1f4"
  integrity sha1-J1W4LTAQqnwLMVd8gaK+WrZWofQ=
  dependencies:
    debug "^4.1.1"
    lodash "^4.17.19"

graceful-error@1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/graceful-error/download/graceful-error-1.1.1.tgz"
  integrity sha1-7d3BlTvZNOY4J4POOS3KKCFJ7Sk=

graceful-error@^1.1.5:
  version "1.1.5"
  resolved "http://registry.npm.qima-inc.com/graceful-error/download/graceful-error-1.1.5.tgz"
  integrity sha1-9u4p8fJ4/fkuHogbF2rqsDUoLLA=

graceful-fs@^4.1.11, graceful-fs@^4.1.2, graceful-fs@^4.1.6, graceful-fs@^4.2.0, graceful-fs@^4.2.4:
  version "4.2.4"
  resolved "http://registry.npm.qima-inc.com/graceful-fs/download/graceful-fs-4.2.4.tgz"
  integrity sha1-Ila94U02MpWMRl68ltxGfKB6Kfs=

graceful-json-parse@^0.0.2:
  version "0.0.2"
  resolved "http://registry.npm.qima-inc.com/graceful-json-parse/download/graceful-json-parse-0.0.2.tgz"
  integrity sha1-i30aIQnDJaNHSp2IC8prjk1/l3k=
  dependencies:
    graceful-error "1.1.1"

growly@^1.3.0:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/growly/download/growly-1.3.0.tgz"
  integrity sha1-8QdIy+dq+WS3yWyTxrzCivEgwIE=

gtoken@^5.0.4:
  version "5.2.0"
  resolved "http://registry.npm.qima-inc.com/gtoken/download/gtoken-5.2.0.tgz"
  integrity sha1-fx4Cn5Ryu4iZ1pEcA8ZvetmFyEk=
  dependencies:
    gaxios "^4.0.0"
    google-p12-pem "^3.0.3"
    jws "^4.0.0"
    mime "^2.2.0"

har-schema@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/har-schema/download/har-schema-2.0.0.tgz"
  integrity sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=

har-validator@~5.1.3:
  version "5.1.5"
  resolved "http://registry.npm.qima-inc.com/har-validator/download/har-validator-5.1.5.tgz"
  integrity sha1-HwgDufjLIMD6E4It8ezds2veHv0=
  dependencies:
    ajv "^6.12.3"
    har-schema "^2.0.0"

has-ansi@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/has-ansi/download/has-ansi-2.0.0.tgz"
  integrity sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=
  dependencies:
    ansi-regex "^2.0.0"

has-flag@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/has-flag/download/has-flag-3.0.0.tgz"
  integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/has-flag/download/has-flag-4.0.0.tgz"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

has-own-prop@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/has-own-prop/download/has-own-prop-2.0.0.tgz#f0f95d58f65804f5d218db32563bb85b8e0417af"
  integrity sha1-8PldWPZYBPXSGNsyVju4W44EF68=

has-property-descriptors@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/has-property-descriptors/download/has-property-descriptors-1.0.2.tgz#963ed7d071dc7bf5f084c5bfbe0d1b6222586854"
  integrity sha1-lj7X0HHce/XwhMW/vg0bYiJYaFQ=
  dependencies:
    es-define-property "^1.0.0"

has-proto@^1.0.1:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/has-proto/download/has-proto-1.0.3.tgz#b31ddfe9b0e6e9914536a6ab286426d0214f77fd"
  integrity sha1-sx3f6bDm6ZFFNqarKGQm0CFPd/0=

has-symbols@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/has-symbols/download/has-symbols-1.0.1.tgz"
  integrity sha1-n1IUdYpEGWxAbZvXbOv4HsLdMeg=

has-symbols@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/has-symbols/download/has-symbols-1.0.3.tgz#bb7b2c4349251dce87b125f7bdf874aa7c8b39f8"
  integrity sha1-u3ssQ0klHc6HsSX3vfh0qnyLOfg=

has-tostringtag@^1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/has-tostringtag/download/has-tostringtag-1.0.2.tgz#2cdc42d40bef2e5b4eeab7c01a73c54ce7ab5abc"
  integrity sha1-LNxC1AvvLltO6rfAGnPFTOerWrw=
  dependencies:
    has-symbols "^1.0.3"

has-value@^0.3.1:
  version "0.3.1"
  resolved "http://registry.npm.qima-inc.com/has-value/download/has-value-0.3.1.tgz"
  integrity sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=
  dependencies:
    get-value "^2.0.3"
    has-values "^0.1.4"
    isobject "^2.0.0"

has-value@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/has-value/download/has-value-1.0.0.tgz"
  integrity sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=
  dependencies:
    get-value "^2.0.6"
    has-values "^1.0.0"
    isobject "^3.0.0"

has-values@^0.1.4:
  version "0.1.4"
  resolved "http://registry.npm.qima-inc.com/has-values/download/has-values-0.1.4.tgz"
  integrity sha1-bWHeldkd/Km5oCCJrThL/49it3E=

has-values@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/has-values/download/has-values-1.0.0.tgz"
  integrity sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=
  dependencies:
    is-number "^3.0.0"
    kind-of "^4.0.0"

has@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/has/download/has-1.0.3.tgz"
  integrity sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y=
  dependencies:
    function-bind "^1.1.1"

hasown@^2.0.0, hasown@^2.0.2:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/hasown/download/hasown-2.0.2.tgz#003eaf91be7adc372e84ec59dc37252cedb80003"
  integrity sha1-AD6vkb563DcuhOxZ3DclLO24AAM=
  dependencies:
    function-bind "^1.1.2"

here@0.0.2:
  version "0.0.2"
  resolved "http://registry.npm.qima-inc.com/here/download/here-0.0.2.tgz"
  integrity sha1-acGvPwISHz2HiOAuhNyLOQXXEZU=

hessian.js@^2.9.0:
  version "2.10.0"
  resolved "http://registry.npm.qima-inc.com/hessian.js/download/hessian.js-2.10.0.tgz"
  integrity sha1-GZf8xALKbRRfLrTGHl4ICkxet/M=
  dependencies:
    byte "^1.4.1"
    debug "^3.2.6"
    is-type-of "^1.2.1"
    long "^4.0.0"
    utility "^1.16.1"

hosted-git-info@^2.1.4:
  version "2.8.8"
  resolved "http://registry.npm.qima-inc.com/hosted-git-info/download/hosted-git-info-2.8.8.tgz"
  integrity sha1-dTm9S8Hg4KiVgVouAmJCCxKFhIg=

html-encoding-sniffer@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/html-encoding-sniffer/download/html-encoding-sniffer-2.0.1.tgz"
  integrity sha1-QqbcT9M/ACgRduiyN1nKTk+hhfM=
  dependencies:
    whatwg-encoding "^1.0.5"

html-escaper@^2.0.0:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/html-escaper/download/html-escaper-2.0.2.tgz"
  integrity sha1-39YAJ9o2o238viNiYsAKWCJoFFM=

http-assert@^1.1.0:
  version "1.4.1"
  resolved "http://registry.npm.qima-inc.com/http-assert/download/http-assert-1.4.1.tgz"
  integrity sha1-xfcl1neqfoc+9zYZm4lobM6zeHg=
  dependencies:
    deep-equal "~1.0.1"
    http-errors "~1.7.2"

http-errors@1.7.3, http-errors@~1.7.2:
  version "1.7.3"
  resolved "http://registry.npm.qima-inc.com/http-errors/download/http-errors-1.7.3.tgz"
  integrity sha1-bGGeT5xgMIw4UZSYwU+7EKrOuwY=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.4"
    setprototypeof "1.1.1"
    statuses ">= 1.5.0 < 2"
    toidentifier "1.0.0"

http-errors@^1.2.8, http-errors@^1.3.1, http-errors@^1.6.1:
  version "1.8.0"
  resolved "http://registry.npm.qima-inc.com/http-errors/download/http-errors-1.8.0.tgz"
  integrity sha1-ddG75JfhBE9R5O6ecEpi8o0zZQc=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses ">= 1.5.0 < 2"
    toidentifier "1.0.0"

http-errors@~1.6.2:
  version "1.6.3"
  resolved "http://registry.npm.qima-inc.com/http-errors/download/http-errors-1.6.3.tgz"
  integrity sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.0"
    statuses ">= 1.4.0 < 2"

http-proxy-agent@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/http-proxy-agent/download/http-proxy-agent-2.1.0.tgz"
  integrity sha1-5IIb7vWyFCogJr1zkm/lN2McVAU=
  dependencies:
    agent-base "4"
    debug "3.1.0"

http-proxy-agent@^4.0.0, http-proxy-agent@^4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/http-proxy-agent/download/http-proxy-agent-4.0.1.tgz#8a8c8ef7f5932ccf953c296ca8291b95aa74aa3a"
  integrity sha1-ioyO9/WTLM+VPClsqCkblap0qjo=
  dependencies:
    "@tootallnate/once" "1"
    agent-base "6"
    debug "4"

http-signature@~1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/http-signature/download/http-signature-1.2.0.tgz"
  integrity sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=
  dependencies:
    assert-plus "^1.0.0"
    jsprim "^1.2.2"
    sshpk "^1.7.0"

https-proxy-agent@5:
  version "5.0.1"
  resolved "http://registry.npm.qima-inc.com/https-proxy-agent/download/https-proxy-agent-5.0.1.tgz#c59ef224a04fe8b754f3db0063a25ea30d0005d6"
  integrity sha1-xZ7yJKBP6LdU89sAY6Jeow0ABdY=
  dependencies:
    agent-base "6"
    debug "4"

https-proxy-agent@^3.0.0:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/https-proxy-agent/download/https-proxy-agent-3.0.1.tgz"
  integrity sha1-uMKGQz6HYCMRsByOo0QT2Fakr4E=
  dependencies:
    agent-base "^4.3.0"
    debug "^3.1.0"

https-proxy-agent@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/https-proxy-agent/download/https-proxy-agent-5.0.0.tgz"
  integrity sha1-4qkFQqu2inYuCghQ9sntrf2FBrI=
  dependencies:
    agent-base "6"
    debug "4"

human-signals@^1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/human-signals/download/human-signals-1.1.1.tgz"
  integrity sha1-xbHNFPUK6uCatsWf5jujOV/k36M=

human-signals@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/human-signals/download/human-signals-2.1.0.tgz#dc91fcba42e4d06e4abaed33b3e7a3c02f514ea0"
  integrity sha1-3JH8ukLk0G5Kuu0zs+ejwC9RTqA=

humanize-ms@^1.2.0, humanize-ms@^1.2.1:
  version "1.2.1"
  resolved "http://registry.npm.qima-inc.com/humanize-ms/download/humanize-ms-1.2.1.tgz"
  integrity sha1-xG4xWaKT9riW2ikxbYtv6Lt5u+0=
  dependencies:
    ms "^2.0.0"

husky@^4.3.0:
  version "4.3.8"
  resolved "http://registry.npm.qima-inc.com/husky/download/husky-4.3.8.tgz#31144060be963fd6850e5cc8f019a1dfe194296d"
  integrity sha512-LCqqsB0PzJQ/AlCgfrfzRe3e3+NvmefAdKQhRYpxS4u6clblBoDdzzvHi8fmxKRzvMxPY/1WZWzomPZww0Anow==
  dependencies:
    chalk "^4.0.0"
    ci-info "^2.0.0"
    compare-versions "^3.6.0"
    cosmiconfig "^7.0.0"
    find-versions "^4.0.0"
    opencollective-postinstall "^2.0.2"
    pkg-dir "^5.0.0"
    please-upgrade-node "^3.2.0"
    slash "^3.0.0"
    which-pm-runs "^1.0.0"

iconv-lite@0.4.24, iconv-lite@^0.4.15, iconv-lite@^0.4.24, iconv-lite@^0.4.4:
  version "0.4.24"
  resolved "http://registry.npm.qima-inc.com/iconv-lite/download/iconv-lite-0.4.24.tgz"
  integrity sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

ignore-by-default@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/ignore-by-default/download/ignore-by-default-1.0.1.tgz"
  integrity sha1-SMptcvbGo68Aqa1K5odr44ieKwk=

ignore@^4.0.6:
  version "4.0.6"
  resolved "http://registry.npm.qima-inc.com/ignore/download/ignore-4.0.6.tgz"
  integrity sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw=

import-fresh@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/import-fresh/download/import-fresh-2.0.0.tgz"
  integrity sha1-2BNVwVYS04bGH53dOSLUMEgipUY=
  dependencies:
    caller-path "^2.0.0"
    resolve-from "^3.0.0"

import-fresh@^3.0.0, import-fresh@^3.2.1, import-fresh@^3.3.0:
  version "3.3.0"
  resolved "http://registry.npm.qima-inc.com/import-fresh/download/import-fresh-3.3.0.tgz"
  integrity sha1-NxYsJfy566oublPVtNiM4X2eDCs=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-lazy@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/import-lazy/download/import-lazy-2.1.0.tgz"
  integrity sha1-BWmOPUXIjo1+nZLLBYTnfwlvPkM=

import-local@^3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/import-local/download/import-local-3.0.2.tgz"
  integrity sha1-qM/QQx0d5KIZlwPQA+PmI2T6bbY=
  dependencies:
    pkg-dir "^4.2.0"
    resolve-cwd "^3.0.0"

import-meta-resolve@^2.2.2:
  version "2.2.2"
  resolved "http://registry.npm.qima-inc.com/import-meta-resolve/download/import-meta-resolve-2.2.2.tgz#75237301e72d1f0fbd74dbc6cca9324b164c2cc9"
  integrity sha1-dSNzAectHw+9dNvGzKkySxZMLMk=

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "http://registry.npm.qima-inc.com/imurmurhash/download/imurmurhash-0.1.4.tgz"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

indent-string@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/indent-string/download/indent-string-4.0.0.tgz#624f8f4497d619b2d9768531d58f4122854d7251"
  integrity sha1-Yk+PRJfWGbLZdoUx1Y9BIoVNclE=

inflation@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/inflation/download/inflation-2.0.0.tgz"
  integrity sha1-i0F+R8KPklpFEz2RTKH9OJEH8w8=

inflight@^1.0.4:
  version "1.0.6"
  resolved "http://registry.npm.qima-inc.com/inflight/download/inflight-1.0.6.tgz"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@2.0.4, inherits@^2.0.3, inherits@~2.0.1, inherits@~2.0.3:
  version "2.0.4"
  resolved "http://registry.npm.qima-inc.com/inherits/download/inherits-2.0.4.tgz"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

inherits@2.0.3:
  version "2.0.3"
  resolved "http://registry.npm.qima-inc.com/inherits/download/inherits-2.0.3.tgz"
  integrity sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=

ini@^1.3.4, ini@~1.3.0:
  version "1.3.8"
  resolved "http://registry.npm.qima-inc.com/ini/download/ini-1.3.8.tgz"
  integrity sha1-op2kJbSIBvNHZ6Tvzjlyaa8oQyw=

inquirer@^7.0.0:
  version "7.3.3"
  resolved "http://registry.npm.qima-inc.com/inquirer/download/inquirer-7.3.3.tgz"
  integrity sha1-BNF2sq8Er8FXqD/XwQDpjuCq0AM=
  dependencies:
    ansi-escapes "^4.2.1"
    chalk "^4.1.0"
    cli-cursor "^3.1.0"
    cli-width "^3.0.0"
    external-editor "^3.0.3"
    figures "^3.0.0"
    lodash "^4.17.19"
    mute-stream "0.0.8"
    run-async "^2.4.0"
    rxjs "^6.6.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"
    through "^2.3.6"

interpret@^1.0.0:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/interpret/download/interpret-1.4.0.tgz"
  integrity sha1-Zlq4vE2iendKQFhOgS4+D6RbGh4=

invert-kv@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/invert-kv/download/invert-kv-1.0.0.tgz"
  integrity sha1-EEqOSqym09jNFXqO+L+rLXo//bY=

ioredis@4.3.0:
  version "4.3.0"
  resolved "http://registry.npm.qima-inc.com/ioredis/download/ioredis-4.3.0.tgz"
  integrity sha1-qShQ3YeU6u5POKJlyDDKgjoJ00U=
  dependencies:
    cluster-key-slot "^1.0.6"
    debug "^3.1.0"
    denque "^1.1.0"
    flexbuffer "0.0.6"
    lodash.defaults "^4.2.0"
    lodash.flatten "^4.4.0"
    redis-commands "1.4.0"
    redis-errors "^1.2.0"
    redis-parser "^3.0.0"
    standard-as-callback "^1.0.0"

ioredis@^4.26.0:
  version "4.28.5"
  resolved "http://registry.npm.qima-inc.com/ioredis/download/ioredis-4.28.5.tgz#5c149e6a8d76a7f8fa8a504ffc85b7d5b6797f9f"
  integrity sha1-XBSeao12p/j6ilBP/IW31bZ5f58=
  dependencies:
    cluster-key-slot "^1.1.0"
    debug "^4.3.1"
    denque "^1.1.0"
    lodash.defaults "^4.2.0"
    lodash.flatten "^4.4.0"
    lodash.isarguments "^3.1.0"
    p-map "^2.1.0"
    redis-commands "1.7.0"
    redis-errors "^1.2.0"
    redis-parser "^3.0.0"
    standard-as-callback "^2.1.0"

ip-regex@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/ip-regex/download/ip-regex-2.1.0.tgz"
  integrity sha1-+ni/XS5pE8kRzp+BnuUUa7bYROk=

ip@1.1.5, ip@^1.1.5:
  version "1.1.5"
  resolved "http://registry.npm.qima-inc.com/ip/download/ip-1.1.5.tgz"
  integrity sha1-vd7XARQpCCjAoDnnLvJfWq7ENUo=

ip@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/ip/download/ip-2.0.0.tgz#4cf4ab182fee2314c75ede1276f8c80b479936da"
  integrity sha1-TPSrGC/uIxTHXt4SdvjIC0eZNto=

is-accessor-descriptor@^0.1.6:
  version "0.1.6"
  resolved "http://registry.npm.qima-inc.com/is-accessor-descriptor/download/is-accessor-descriptor-0.1.6.tgz"
  integrity sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=
  dependencies:
    kind-of "^3.0.2"

is-accessor-descriptor@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz"
  integrity sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=
  dependencies:
    kind-of "^6.0.0"

is-arguments@^1.0.4:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/is-arguments/download/is-arguments-1.1.0.tgz"
  integrity sha1-YjUwMd++4HzrNGVqa95Z7+yujdk=
  dependencies:
    call-bind "^1.0.0"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "http://registry.npm.qima-inc.com/is-arrayish/download/is-arrayish-0.2.1.tgz"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-binary-path@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/is-binary-path/download/is-binary-path-1.0.1.tgz"
  integrity sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg=
  dependencies:
    binary-extensions "^1.0.0"

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/is-binary-path/download/is-binary-path-2.1.0.tgz#ea1f7f3b80f064236e83470f86c09c254fb45b09"
  integrity sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=
  dependencies:
    binary-extensions "^2.0.0"

is-buffer@^1.1.5:
  version "1.1.6"
  resolved "http://registry.npm.qima-inc.com/is-buffer/download/is-buffer-1.1.6.tgz"
  integrity sha1-76ouqdqg16suoTqXsritUf776L4=

is-buffer@^2.0.2:
  version "2.0.5"
  resolved "http://registry.npm.qima-inc.com/is-buffer/download/is-buffer-2.0.5.tgz"
  integrity sha1-68JS5ADSL/jXf6CYiIIaJKZYwZE=

is-callable@^1.1.4, is-callable@^1.2.2:
  version "1.2.2"
  resolved "http://registry.npm.qima-inc.com/is-callable/download/is-callable-1.2.2.tgz"
  integrity sha1-x8ZxXNItTdtI0+GZcCI6zquwgNk=

is-ci@^1.0.10:
  version "1.2.1"
  resolved "http://registry.npm.qima-inc.com/is-ci/download/is-ci-1.2.1.tgz"
  integrity sha1-43ecjuF/zPQoSI9uKBGH8uYyhBw=
  dependencies:
    ci-info "^1.5.0"

is-ci@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/is-ci/download/is-ci-2.0.0.tgz"
  integrity sha1-a8YzQYGBDgS1wis9WJ/cpVAmQEw=
  dependencies:
    ci-info "^2.0.0"

is-class-hotfix@~0.0.6:
  version "0.0.6"
  resolved "http://registry.npm.qima-inc.com/is-class-hotfix/download/is-class-hotfix-0.0.6.tgz"
  integrity sha1-pSfTH7IyeSgd3l84XHe13nCnJDU=

is-core-module@^2.1.0:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/is-core-module/download/is-core-module-2.2.0.tgz"
  integrity sha1-lwN+89UiJNhRY/VZeytj2a/tmBo=
  dependencies:
    has "^1.0.3"

is-core-module@^2.13.0:
  version "2.15.1"
  resolved "http://registry.npm.qima-inc.com/is-core-module/download/is-core-module-2.15.1.tgz#a7363a25bee942fefab0de13bf6aa372c82dcc37"
  integrity sha1-pzY6Jb7pQv76sN4Tv2qjcsgtzDc=
  dependencies:
    hasown "^2.0.2"

is-core-module@^2.9.0:
  version "2.10.0"
  resolved "http://registry.npm.qima-inc.com/is-core-module/download/is-core-module-2.10.0.tgz#9012ede0a91c69587e647514e1d5277019e728ed"
  integrity sha1-kBLt4KkcaVh+ZHUU4dUncBnnKO0=
  dependencies:
    has "^1.0.3"

is-data-descriptor@^0.1.4:
  version "0.1.4"
  resolved "http://registry.npm.qima-inc.com/is-data-descriptor/download/is-data-descriptor-0.1.4.tgz"
  integrity sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=
  dependencies:
    kind-of "^3.0.2"

is-data-descriptor@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz"
  integrity sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=
  dependencies:
    kind-of "^6.0.0"

is-date-object@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/is-date-object/download/is-date-object-1.0.2.tgz"
  integrity sha1-vac28s2P0G0yhE53Q7+nSUw7/X4=

is-descriptor@^0.1.0:
  version "0.1.6"
  resolved "http://registry.npm.qima-inc.com/is-descriptor/download/is-descriptor-0.1.6.tgz"
  integrity sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco=
  dependencies:
    is-accessor-descriptor "^0.1.6"
    is-data-descriptor "^0.1.4"
    kind-of "^5.0.0"

is-descriptor@^1.0.0, is-descriptor@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/is-descriptor/download/is-descriptor-1.0.2.tgz"
  integrity sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=
  dependencies:
    is-accessor-descriptor "^1.0.0"
    is-data-descriptor "^1.0.0"
    kind-of "^6.0.2"

is-directory@^0.3.1:
  version "0.3.1"
  resolved "http://registry.npm.qima-inc.com/is-directory/download/is-directory-0.3.1.tgz"
  integrity sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE=

is-docker@^2.0.0:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/is-docker/download/is-docker-2.1.1.tgz"
  integrity sha1-QSWojkTkUNOE4JBH7eca3C0UQVY=

is-expression@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/is-expression/download/is-expression-4.0.0.tgz#c33155962abf21d0afd2552514d67d2ec16fd2ab"
  integrity sha1-wzFVliq/IdCv0lUlFNZ9LsFv0qs=
  dependencies:
    acorn "^7.1.1"
    object-assign "^4.1.1"

is-extendable@^0.1.0, is-extendable@^0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/is-extendable/download/is-extendable-0.1.1.tgz"
  integrity sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=

is-extendable@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/is-extendable/download/is-extendable-1.0.1.tgz"
  integrity sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=
  dependencies:
    is-plain-object "^2.0.4"

is-extglob@^2.1.0, is-extglob@^2.1.1:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/is-extglob/download/is-extglob-2.1.1.tgz"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-fullwidth-code-point@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/is-fullwidth-code-point/download/is-fullwidth-code-point-1.0.0.tgz"
  integrity sha1-754xOG8DGn8NZDr4L95QxFfvAMs=
  dependencies:
    number-is-nan "^1.0.0"

is-fullwidth-code-point@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz"
  integrity sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-generator-fn@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/is-generator-fn/download/is-generator-fn-2.1.0.tgz"
  integrity sha1-fRQK3DiarzARqPKipM+m+q3/sRg=

is-generator-function@^1.0.3, is-generator-function@^1.0.7:
  version "1.0.8"
  resolved "http://registry.npm.qima-inc.com/is-generator-function/download/is-generator-function-1.0.8.tgz"
  integrity sha1-37XCsSDgKwqNnSxoBs1WIaqSL3s=

is-glob@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/is-glob/download/is-glob-3.1.0.tgz"
  integrity sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=
  dependencies:
    is-extglob "^2.1.0"

is-glob@^4.0.0, is-glob@^4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/is-glob/download/is-glob-4.0.1.tgz"
  integrity sha1-dWfb6fL14kZ7x3q4PEopSCQHpdw=
  dependencies:
    is-extglob "^2.1.1"

is-glob@~4.0.1:
  version "4.0.3"
  resolved "http://registry.npm.qima-inc.com/is-glob/download/is-glob-4.0.3.tgz#64f61e42cbbb2eec2071a9dac0b28ba1e65d5084"
  integrity sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=
  dependencies:
    is-extglob "^2.1.1"

is-installed-globally@^0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.qima-inc.com/is-installed-globally/download/is-installed-globally-0.1.0.tgz"
  integrity sha1-Df2Y9akRFxbdU13aZJL2e/PSWoA=
  dependencies:
    global-dirs "^0.1.0"
    is-path-inside "^1.0.0"

is-negative-zero@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/is-negative-zero/download/is-negative-zero-2.0.1.tgz"
  integrity sha1-PedGwY3aIxkkGlNnWQjY92bxHCQ=

is-npm@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/is-npm/download/is-npm-1.0.0.tgz"
  integrity sha1-8vtjpl5JBbQGyGBydloaTceTufQ=

is-number@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/is-number/download/is-number-3.0.0.tgz"
  integrity sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=
  dependencies:
    kind-of "^3.0.2"

is-number@^7.0.0:
  version "7.0.0"
  resolved "http://registry.npm.qima-inc.com/is-number/download/is-number-7.0.0.tgz"
  integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=

is-obj@^1.0.0, is-obj@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/is-obj/download/is-obj-1.0.1.tgz"
  integrity sha1-PkcprB9f3gJc19g6iW2rn09n2w8=

is-obj@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/is-obj/download/is-obj-2.0.0.tgz#473fb05d973705e3fd9620545018ca8e22ef4982"
  integrity sha1-Rz+wXZc3BeP9liBUUBjKjiLvSYI=

is-path-inside@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/is-path-inside/download/is-path-inside-1.0.1.tgz"
  integrity sha1-jvW33lBDej/cprToZe96pVy0gDY=
  dependencies:
    path-is-inside "^1.0.1"

is-plain-object@^2.0.3, is-plain-object@^2.0.4:
  version "2.0.4"
  resolved "http://registry.npm.qima-inc.com/is-plain-object/download/is-plain-object-2.0.4.tgz"
  integrity sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=
  dependencies:
    isobject "^3.0.1"

is-potential-custom-element-name@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/is-potential-custom-element-name/download/is-potential-custom-element-name-1.0.0.tgz"
  integrity sha1-DFLlS8yjkbssSUsh6GJtczbG45c=

is-promise@^2.0.0:
  version "2.2.2"
  resolved "http://registry.npm.qima-inc.com/is-promise/download/is-promise-2.2.2.tgz#39ab959ccbf9a774cf079f7b40c7a26f763135f1"
  integrity sha1-OauVnMv5p3TPB597QMeib3YxNfE=

is-redirect@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/is-redirect/download/is-redirect-1.0.0.tgz"
  integrity sha1-HQPd7VO9jbDzDCbk+V02/HyH3CQ=

is-regex@^1.0.3:
  version "1.1.4"
  resolved "http://registry.npm.qima-inc.com/is-regex/download/is-regex-1.1.4.tgz#eef5663cd59fa4c0ae339505323df6854bb15958"
  integrity sha1-7vVmPNWfpMCuM5UFMj32hUuxWVg=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-regex@^1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/is-regex/download/is-regex-1.1.1.tgz"
  integrity sha1-xvmKrMVG9s7FRooHt7FTq1ZKV7k=
  dependencies:
    has-symbols "^1.0.1"

is-regexp@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/is-regexp/download/is-regexp-1.0.0.tgz#fd2d883545c46bac5a633e7b9a09e87fa2cb5069"
  integrity sha1-/S2INUXEa6xaYz57mgnof6LLUGk=

is-retry-allowed@^1.0.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/is-retry-allowed/download/is-retry-allowed-1.2.0.tgz"
  integrity sha1-13hIi9CkZmo76KFIK58rqv7eqLQ=

is-stream@^1.0.0, is-stream@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/is-stream/download/is-stream-1.1.0.tgz"
  integrity sha1-EtSj3U5o4Lec6428hBc66A2RykQ=

is-stream@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/is-stream/download/is-stream-2.0.0.tgz"
  integrity sha1-venDJoDW+uBBKdasnZIc54FfeOM=

is-string@^1.0.5:
  version "1.0.5"
  resolved "http://registry.npm.qima-inc.com/is-string/download/is-string-1.0.5.tgz"
  integrity sha1-QEk+0ZjvP/R3uMf5L2ROyCpc06Y=

is-symbol@^1.0.2:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/is-symbol/download/is-symbol-1.0.3.tgz"
  integrity sha1-OOEBS55jKb4N6dJKQU/XRB7GGTc=
  dependencies:
    has-symbols "^1.0.1"

is-type-of@^1.0.0, is-type-of@^1.2.1:
  version "1.2.1"
  resolved "http://registry.npm.qima-inc.com/is-type-of/download/is-type-of-1.2.1.tgz"
  integrity sha1-4mPsOFes608oxHEw7HjbCakg+MU=
  dependencies:
    core-util-is "^1.0.2"
    is-class-hotfix "~0.0.6"
    isstream "~0.1.2"

is-typed-array@^1.1.3:
  version "1.1.4"
  resolved "http://registry.npm.qima-inc.com/is-typed-array/download/is-typed-array-1.1.4.tgz"
  integrity sha1-H2bzSig6PJSkM1Q0ZhylP/+AESA=
  dependencies:
    available-typed-arrays "^1.0.2"
    call-bind "^1.0.0"
    es-abstract "^1.18.0-next.1"
    foreach "^2.0.5"
    has-symbols "^1.0.1"

is-typedarray@^1.0.0, is-typedarray@~1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/is-typedarray/download/is-typedarray-1.0.0.tgz"
  integrity sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=

is-unicode-supported@^0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.qima-inc.com/is-unicode-supported/download/is-unicode-supported-0.1.0.tgz#3f26c76a809593b52bfa2ecb5710ed2779b522a7"
  integrity sha1-PybHaoCVk7Ur+i7LVxDtJ3m1Iqc=

is-windows@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/is-windows/download/is-windows-1.0.2.tgz"
  integrity sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=

is-wsl@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/is-wsl/download/is-wsl-1.1.0.tgz"
  integrity sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0=

is-wsl@^2.1.1, is-wsl@^2.2.0:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/is-wsl/download/is-wsl-2.2.0.tgz"
  integrity sha1-dKTHbnfKn9P5MvKQwX6jJs0VcnE=
  dependencies:
    is-docker "^2.0.0"

isarray@0.0.1:
  version "0.0.1"
  resolved "http://registry.npm.qima-inc.com/isarray/download/isarray-0.0.1.tgz"
  integrity sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=

isarray@1.0.0, isarray@^1.0.0, isarray@~1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/isarray/download/isarray-1.0.0.tgz"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isexe@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/isexe/download/isexe-2.0.0.tgz"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

isobject@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/isobject/download/isobject-2.1.0.tgz"
  integrity sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=
  dependencies:
    isarray "1.0.0"

isobject@^3.0.0, isobject@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/isobject/download/isobject-3.0.1.tgz"
  integrity sha1-TkMekrEalzFjaqH5yNHMvP2reN8=

isstream@~0.1.2:
  version "0.1.2"
  resolved "http://registry.npm.qima-inc.com/isstream/download/isstream-0.1.2.tgz"
  integrity sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=

istanbul-lib-coverage@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/istanbul-lib-coverage/download/istanbul-lib-coverage-3.0.0.tgz"
  integrity sha1-9ZRKN8cLVQsCp4pcOyBVsoDOyOw=

istanbul-lib-instrument@^4.0.0, istanbul-lib-instrument@^4.0.3:
  version "4.0.3"
  resolved "http://registry.npm.qima-inc.com/istanbul-lib-instrument/download/istanbul-lib-instrument-4.0.3.tgz"
  integrity sha1-hzxv/4l0UBGCIndGlqPyiQLXfB0=
  dependencies:
    "@babel/core" "^7.7.5"
    "@istanbuljs/schema" "^0.1.2"
    istanbul-lib-coverage "^3.0.0"
    semver "^6.3.0"

istanbul-lib-report@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/istanbul-lib-report/download/istanbul-lib-report-3.0.0.tgz"
  integrity sha1-dRj+UupE3jcvRgp2tezan/tz2KY=
  dependencies:
    istanbul-lib-coverage "^3.0.0"
    make-dir "^3.0.0"
    supports-color "^7.1.0"

istanbul-lib-source-maps@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/istanbul-lib-source-maps/download/istanbul-lib-source-maps-4.0.0.tgz"
  integrity sha1-dXQ85tlruG3H7kNSz2Nmoj8LGtk=
  dependencies:
    debug "^4.1.1"
    istanbul-lib-coverage "^3.0.0"
    source-map "^0.6.1"

istanbul-reports@^3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/istanbul-reports/download/istanbul-reports-3.0.2.tgz"
  integrity sha1-1ZMhDlAAaDdQywn8BkTktuJ/1Ts=
  dependencies:
    html-escaper "^2.0.0"
    istanbul-lib-report "^3.0.0"

jest-changed-files@^26.6.2:
  version "26.6.2"
  resolved "http://registry.npm.qima-inc.com/jest-changed-files/download/jest-changed-files-26.6.2.tgz"
  integrity sha1-9hmEeeHMZvIvmuHiKsqgtCnAQtA=
  dependencies:
    "@jest/types" "^26.6.2"
    execa "^4.0.0"
    throat "^5.0.0"

jest-cli@^26.6.3:
  version "26.6.3"
  resolved "http://registry.npm.qima-inc.com/jest-cli/download/jest-cli-26.6.3.tgz"
  integrity sha1-QxF8/vJLxM1pGhdKh5alMuE16So=
  dependencies:
    "@jest/core" "^26.6.3"
    "@jest/test-result" "^26.6.2"
    "@jest/types" "^26.6.2"
    chalk "^4.0.0"
    exit "^0.1.2"
    graceful-fs "^4.2.4"
    import-local "^3.0.2"
    is-ci "^2.0.0"
    jest-config "^26.6.3"
    jest-util "^26.6.2"
    jest-validate "^26.6.2"
    prompts "^2.0.1"
    yargs "^15.4.1"

jest-config@^26.6.3:
  version "26.6.3"
  resolved "http://registry.npm.qima-inc.com/jest-config/download/jest-config-26.6.3.tgz"
  integrity sha1-ZPQURO756wPcUdXFO3XIxx9kU0k=
  dependencies:
    "@babel/core" "^7.1.0"
    "@jest/test-sequencer" "^26.6.3"
    "@jest/types" "^26.6.2"
    babel-jest "^26.6.3"
    chalk "^4.0.0"
    deepmerge "^4.2.2"
    glob "^7.1.1"
    graceful-fs "^4.2.4"
    jest-environment-jsdom "^26.6.2"
    jest-environment-node "^26.6.2"
    jest-get-type "^26.3.0"
    jest-jasmine2 "^26.6.3"
    jest-regex-util "^26.0.0"
    jest-resolve "^26.6.2"
    jest-util "^26.6.2"
    jest-validate "^26.6.2"
    micromatch "^4.0.2"
    pretty-format "^26.6.2"

jest-diff@^26.0.0, jest-diff@^26.6.2:
  version "26.6.2"
  resolved "http://registry.npm.qima-inc.com/jest-diff/download/jest-diff-26.6.2.tgz"
  integrity sha1-GqdGi1LDpo19XF/c381eSb0WQ5Q=
  dependencies:
    chalk "^4.0.0"
    diff-sequences "^26.6.2"
    jest-get-type "^26.3.0"
    pretty-format "^26.6.2"

jest-docblock@^26.0.0:
  version "26.0.0"
  resolved "http://registry.npm.qima-inc.com/jest-docblock/download/jest-docblock-26.0.0.tgz"
  integrity sha1-Pi+iCJn8koyxO9D/aL03EaNoibU=
  dependencies:
    detect-newline "^3.0.0"

jest-each@^26.6.2:
  version "26.6.2"
  resolved "http://registry.npm.qima-inc.com/jest-each/download/jest-each-26.6.2.tgz"
  integrity sha1-AlJkOKd6Z0AcimOC3+WZmVLBZ8s=
  dependencies:
    "@jest/types" "^26.6.2"
    chalk "^4.0.0"
    jest-get-type "^26.3.0"
    jest-util "^26.6.2"
    pretty-format "^26.6.2"

jest-environment-jsdom@^26.6.2:
  version "26.6.2"
  resolved "http://registry.npm.qima-inc.com/jest-environment-jsdom/download/jest-environment-jsdom-26.6.2.tgz"
  integrity sha1-eNCf6c8BmjVwCbm34fEB0jvR2j4=
  dependencies:
    "@jest/environment" "^26.6.2"
    "@jest/fake-timers" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    jest-mock "^26.6.2"
    jest-util "^26.6.2"
    jsdom "^16.4.0"

jest-environment-node@^26.6.2:
  version "26.6.2"
  resolved "http://registry.npm.qima-inc.com/jest-environment-node/download/jest-environment-node-26.6.2.tgz"
  integrity sha1-gk5Mf7SURkY1bxGsdbIpsANfKww=
  dependencies:
    "@jest/environment" "^26.6.2"
    "@jest/fake-timers" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    jest-mock "^26.6.2"
    jest-util "^26.6.2"

jest-get-type@^26.3.0:
  version "26.3.0"
  resolved "http://registry.npm.qima-inc.com/jest-get-type/download/jest-get-type-26.3.0.tgz"
  integrity sha1-6X3Dw/U8K0Bsp6+u1Ek7HQmRmeA=

jest-haste-map@^26.6.2:
  version "26.6.2"
  resolved "http://registry.npm.qima-inc.com/jest-haste-map/download/jest-haste-map-26.6.2.tgz"
  integrity sha1-3X5g/n3A6fkRoj15xf9/tcLK/qo=
  dependencies:
    "@jest/types" "^26.6.2"
    "@types/graceful-fs" "^4.1.2"
    "@types/node" "*"
    anymatch "^3.0.3"
    fb-watchman "^2.0.0"
    graceful-fs "^4.2.4"
    jest-regex-util "^26.0.0"
    jest-serializer "^26.6.2"
    jest-util "^26.6.2"
    jest-worker "^26.6.2"
    micromatch "^4.0.2"
    sane "^4.0.3"
    walker "^1.0.7"
  optionalDependencies:
    fsevents "^2.1.2"

jest-jasmine2@^26.6.3:
  version "26.6.3"
  resolved "http://registry.npm.qima-inc.com/jest-jasmine2/download/jest-jasmine2-26.6.3.tgz"
  integrity sha1-rcPPkV3qy1ISyTufNUfNEpWPLt0=
  dependencies:
    "@babel/traverse" "^7.1.0"
    "@jest/environment" "^26.6.2"
    "@jest/source-map" "^26.6.2"
    "@jest/test-result" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    chalk "^4.0.0"
    co "^4.6.0"
    expect "^26.6.2"
    is-generator-fn "^2.0.0"
    jest-each "^26.6.2"
    jest-matcher-utils "^26.6.2"
    jest-message-util "^26.6.2"
    jest-runtime "^26.6.3"
    jest-snapshot "^26.6.2"
    jest-util "^26.6.2"
    pretty-format "^26.6.2"
    throat "^5.0.0"

jest-leak-detector@^26.6.2:
  version "26.6.2"
  resolved "http://registry.npm.qima-inc.com/jest-leak-detector/download/jest-leak-detector-26.6.2.tgz"
  integrity sha1-dxfPEYuSI48uumUFTIoMnGU6ka8=
  dependencies:
    jest-get-type "^26.3.0"
    pretty-format "^26.6.2"

jest-matcher-utils@^26.6.2:
  version "26.6.2"
  resolved "http://registry.npm.qima-inc.com/jest-matcher-utils/download/jest-matcher-utils-26.6.2.tgz"
  integrity sha1-jm/W6GPIstMaxkcu6yN7xZXlPno=
  dependencies:
    chalk "^4.0.0"
    jest-diff "^26.6.2"
    jest-get-type "^26.3.0"
    pretty-format "^26.6.2"

jest-message-util@^26.6.2:
  version "26.6.2"
  resolved "http://registry.npm.qima-inc.com/jest-message-util/download/jest-message-util-26.6.2.tgz"
  integrity sha1-WBc3RK1vwFBrXSEVC5vlbvABygc=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@jest/types" "^26.6.2"
    "@types/stack-utils" "^2.0.0"
    chalk "^4.0.0"
    graceful-fs "^4.2.4"
    micromatch "^4.0.2"
    pretty-format "^26.6.2"
    slash "^3.0.0"
    stack-utils "^2.0.2"

jest-mock@^26.6.2:
  version "26.6.2"
  resolved "http://registry.npm.qima-inc.com/jest-mock/download/jest-mock-26.6.2.tgz"
  integrity sha1-1stxKwQe1H/g2bb8NHS8ZUP+swI=
  dependencies:
    "@jest/types" "^26.6.2"
    "@types/node" "*"

jest-pnp-resolver@^1.2.2:
  version "1.2.2"
  resolved "http://registry.npm.qima-inc.com/jest-pnp-resolver/download/jest-pnp-resolver-1.2.2.tgz"
  integrity sha1-twSsCuAoqJEIpNBAs/kZ393I4zw=

jest-regex-util@^26.0.0:
  version "26.0.0"
  resolved "http://registry.npm.qima-inc.com/jest-regex-util/download/jest-regex-util-26.0.0.tgz"
  integrity sha1-0l5xhLNuOf1GbDvEG+CXHoIf7ig=

jest-resolve-dependencies@^26.6.3:
  version "26.6.3"
  resolved "http://registry.npm.qima-inc.com/jest-resolve-dependencies/download/jest-resolve-dependencies-26.6.3.tgz"
  integrity sha1-ZoCFnuXSLuXc2WH+SHH1n0x4T7Y=
  dependencies:
    "@jest/types" "^26.6.2"
    jest-regex-util "^26.0.0"
    jest-snapshot "^26.6.2"

jest-resolve@^26.6.2:
  version "26.6.2"
  resolved "http://registry.npm.qima-inc.com/jest-resolve/download/jest-resolve-26.6.2.tgz"
  integrity sha1-o6sVFyF/RptQTxtWYDxbtUH7tQc=
  dependencies:
    "@jest/types" "^26.6.2"
    chalk "^4.0.0"
    graceful-fs "^4.2.4"
    jest-pnp-resolver "^1.2.2"
    jest-util "^26.6.2"
    read-pkg-up "^7.0.1"
    resolve "^1.18.1"
    slash "^3.0.0"

jest-runner@^26.6.3:
  version "26.6.3"
  resolved "http://registry.npm.qima-inc.com/jest-runner/download/jest-runner-26.6.3.tgz"
  integrity sha1-LR/tPUbhDyM/0dvTv6o/6JJL4Vk=
  dependencies:
    "@jest/console" "^26.6.2"
    "@jest/environment" "^26.6.2"
    "@jest/test-result" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    chalk "^4.0.0"
    emittery "^0.7.1"
    exit "^0.1.2"
    graceful-fs "^4.2.4"
    jest-config "^26.6.3"
    jest-docblock "^26.0.0"
    jest-haste-map "^26.6.2"
    jest-leak-detector "^26.6.2"
    jest-message-util "^26.6.2"
    jest-resolve "^26.6.2"
    jest-runtime "^26.6.3"
    jest-util "^26.6.2"
    jest-worker "^26.6.2"
    source-map-support "^0.5.6"
    throat "^5.0.0"

jest-runtime@^26.6.3:
  version "26.6.3"
  resolved "http://registry.npm.qima-inc.com/jest-runtime/download/jest-runtime-26.6.3.tgz"
  integrity sha1-T2TvvPrDmDMbdLSzyC0n1AG4+is=
  dependencies:
    "@jest/console" "^26.6.2"
    "@jest/environment" "^26.6.2"
    "@jest/fake-timers" "^26.6.2"
    "@jest/globals" "^26.6.2"
    "@jest/source-map" "^26.6.2"
    "@jest/test-result" "^26.6.2"
    "@jest/transform" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/yargs" "^15.0.0"
    chalk "^4.0.0"
    cjs-module-lexer "^0.6.0"
    collect-v8-coverage "^1.0.0"
    exit "^0.1.2"
    glob "^7.1.3"
    graceful-fs "^4.2.4"
    jest-config "^26.6.3"
    jest-haste-map "^26.6.2"
    jest-message-util "^26.6.2"
    jest-mock "^26.6.2"
    jest-regex-util "^26.0.0"
    jest-resolve "^26.6.2"
    jest-snapshot "^26.6.2"
    jest-util "^26.6.2"
    jest-validate "^26.6.2"
    slash "^3.0.0"
    strip-bom "^4.0.0"
    yargs "^15.4.1"

jest-serializer@^26.6.2:
  version "26.6.2"
  resolved "http://registry.npm.qima-inc.com/jest-serializer/download/jest-serializer-26.6.2.tgz"
  integrity sha1-0Tmq/UaVfTpEjzps2r4pGboHQtE=
  dependencies:
    "@types/node" "*"
    graceful-fs "^4.2.4"

jest-snapshot@^26.6.2:
  version "26.6.2"
  resolved "http://registry.npm.qima-inc.com/jest-snapshot/download/jest-snapshot-26.6.2.tgz"
  integrity sha1-87CvGssiMxaFC9FOG+6pg3+znIQ=
  dependencies:
    "@babel/types" "^7.0.0"
    "@jest/types" "^26.6.2"
    "@types/babel__traverse" "^7.0.4"
    "@types/prettier" "^2.0.0"
    chalk "^4.0.0"
    expect "^26.6.2"
    graceful-fs "^4.2.4"
    jest-diff "^26.6.2"
    jest-get-type "^26.3.0"
    jest-haste-map "^26.6.2"
    jest-matcher-utils "^26.6.2"
    jest-message-util "^26.6.2"
    jest-resolve "^26.6.2"
    natural-compare "^1.4.0"
    pretty-format "^26.6.2"
    semver "^7.3.2"

jest-util@^26.1.0, jest-util@^26.6.2:
  version "26.6.2"
  resolved "http://registry.npm.qima-inc.com/jest-util/download/jest-util-26.6.2.tgz"
  integrity sha1-kHU12+TVpstMR6ybkm9q8pV2y8E=
  dependencies:
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    chalk "^4.0.0"
    graceful-fs "^4.2.4"
    is-ci "^2.0.0"
    micromatch "^4.0.2"

jest-validate@^26.6.2:
  version "26.6.2"
  resolved "http://registry.npm.qima-inc.com/jest-validate/download/jest-validate-26.6.2.tgz"
  integrity sha1-I9OAlxWHFQRnNCkRw9e0rFerIOw=
  dependencies:
    "@jest/types" "^26.6.2"
    camelcase "^6.0.0"
    chalk "^4.0.0"
    jest-get-type "^26.3.0"
    leven "^3.1.0"
    pretty-format "^26.6.2"

jest-watcher@^26.6.2:
  version "26.6.2"
  resolved "http://registry.npm.qima-inc.com/jest-watcher/download/jest-watcher-26.6.2.tgz"
  integrity sha1-pbaDuPnWjbyx19rjIXLSzKBZKXU=
  dependencies:
    "@jest/test-result" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    ansi-escapes "^4.2.1"
    chalk "^4.0.0"
    jest-util "^26.6.2"
    string-length "^4.0.1"

jest-worker@^26.6.2:
  version "26.6.2"
  resolved "http://registry.npm.qima-inc.com/jest-worker/download/jest-worker-26.6.2.tgz"
  integrity sha1-f3LLxNZDw2Xie5/XdfnQ6qnHqO0=
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^7.0.0"

jest@^26.6.3:
  version "26.6.3"
  resolved "http://registry.npm.qima-inc.com/jest/download/jest-26.6.3.tgz#40e8fdbe48f00dfa1f0ce8121ca74b88ac9148ef"
  integrity sha1-QOj9vkjwDfofDOgSHKdLiKyRSO8=
  dependencies:
    "@jest/core" "^26.6.3"
    import-local "^3.0.2"
    jest-cli "^26.6.3"

jmespath@^0.15.0:
  version "0.15.0"
  resolved "http://registry.npm.qima-inc.com/jmespath/download/jmespath-0.15.0.tgz"
  integrity sha1-o/Iiqarp+Wb10nx5ZRDigJF2Qhc=

joycon@^2.2.5:
  version "2.2.5"
  resolved "http://registry.npm.qima-inc.com/joycon/download/joycon-2.2.5.tgz"
  integrity sha1-jUz0y7JUTXt1g8IW/N/sGfa+FhU=

js-stringify@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/js-stringify/download/js-stringify-1.0.2.tgz#1736fddfd9724f28a3682adc6230ae7e4e9679db"
  integrity sha1-Fzb939lyTyijaCrcYjCufk6Weds=

js-tokens@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/js-tokens/download/js-tokens-4.0.0.tgz"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

js-yaml@^3.13.1, js-yaml@^3.9.0:
  version "3.14.1"
  resolved "http://registry.npm.qima-inc.com/js-yaml/download/js-yaml-3.14.1.tgz"
  integrity sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

js-yaml@^4.1.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/js-yaml/download/js-yaml-4.1.0.tgz#c1fb65f8f5017901cdd2c951864ba18458a10602"
  integrity sha1-wftl+PUBeQHN0slRhkuhhFihBgI=
  dependencies:
    argparse "^2.0.1"

jsbn@~0.1.0:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/jsbn/download/jsbn-0.1.1.tgz"
  integrity sha1-peZUwuWi3rXyAdls77yoDA7y9RM=

jscpd@^3.4.5:
  version "3.5.9"
  resolved "http://registry.npm.qima-inc.com/jscpd/download/jscpd-3.5.9.tgz#44588cf23d1c5afbb09ea45ce15e478dae4ea5cd"
  integrity sha1-RFiM8j0cWvuwnqRc4V5Hja5Opc0=
  dependencies:
    "@jscpd/core" "^3.5.4"
    "@jscpd/finder" "^3.5.5"
    "@jscpd/html-reporter" "^3.5.9"
    "@jscpd/tokenizer" "^3.5.4"
    colors "1.4.0"
    commander "^5.0.0"
    fs-extra "^9.1.0"
    gitignore-to-glob "^0.3.0"

jsdom@^16.4.0:
  version "16.4.0"
  resolved "http://registry.npm.qima-inc.com/jsdom/download/jsdom-16.4.0.tgz"
  integrity sha1-NgBb3i0Tb3Pu4agwxtReVUCO3ds=
  dependencies:
    abab "^2.0.3"
    acorn "^7.1.1"
    acorn-globals "^6.0.0"
    cssom "^0.4.4"
    cssstyle "^2.2.0"
    data-urls "^2.0.0"
    decimal.js "^10.2.0"
    domexception "^2.0.1"
    escodegen "^1.14.1"
    html-encoding-sniffer "^2.0.1"
    is-potential-custom-element-name "^1.0.0"
    nwsapi "^2.2.0"
    parse5 "5.1.1"
    request "^2.88.2"
    request-promise-native "^1.0.8"
    saxes "^5.0.0"
    symbol-tree "^3.2.4"
    tough-cookie "^3.0.1"
    w3c-hr-time "^1.0.2"
    w3c-xmlserializer "^2.0.0"
    webidl-conversions "^6.1.0"
    whatwg-encoding "^1.0.5"
    whatwg-mimetype "^2.3.0"
    whatwg-url "^8.0.0"
    ws "^7.2.3"
    xml-name-validator "^3.0.0"

jsesc@^2.5.1:
  version "2.5.2"
  resolved "http://registry.npm.qima-inc.com/jsesc/download/jsesc-2.5.2.tgz"
  integrity sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q=

json-bigint@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/json-bigint/download/json-bigint-1.0.0.tgz"
  integrity sha1-rlR4I6wMrYOYZn+M2e9HMPWwH/E=
  dependencies:
    bignumber.js "^9.0.0"

json-buffer@3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/json-buffer/download/json-buffer-3.0.1.tgz#9338802a30d3b6605fbe0613e094008ca8c05a13"
  integrity sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=

json-parse-better-errors@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz#bb867cfb3450e69107c131d1c514bab3dc8bcaa9"
  integrity sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=

json-parse-even-better-errors@^2.3.0:
  version "2.3.1"
  resolved "http://registry.npm.qima-inc.com/json-parse-even-better-errors/download/json-parse-even-better-errors-2.3.1.tgz"
  integrity sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "http://registry.npm.qima-inc.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-schema@0.2.3:
  version "0.2.3"
  resolved "http://registry.npm.qima-inc.com/json-schema/download/json-schema-0.2.3.tgz"
  integrity sha1-tIDIkuWaLwWVTOcnvT8qTogvnhM=

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

json-stringify-safe@~5.0.1:
  version "5.0.1"
  resolved "http://registry.npm.qima-inc.com/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz"
  integrity sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=

json5@2.x, json5@^2.1.2:
  version "2.1.3"
  resolved "http://registry.npm.qima-inc.com/json5/download/json5-2.1.3.tgz"
  integrity sha1-ybD3+pIzv+WAf+ZvzzpWF+1ZfUM=
  dependencies:
    minimist "^1.2.5"

json5@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/json5/download/json5-1.0.1.tgz"
  integrity sha1-d5+wAYYE+oVOrL9iUhgNg1Q+Pb4=
  dependencies:
    minimist "^1.2.0"

jsonfile@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/jsonfile/download/jsonfile-4.0.0.tgz"
  integrity sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonfile@^6.0.1:
  version "6.1.0"
  resolved "http://registry.npm.qima-inc.com/jsonfile/download/jsonfile-6.1.0.tgz"
  integrity sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonp-body@1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/jsonp-body/download/jsonp-body-1.0.0.tgz"
  integrity sha1-5hD7b86nnPDMnye6p7Vjd9SwuzY=

jsprim@^1.2.2:
  version "1.4.1"
  resolved "http://registry.npm.qima-inc.com/jsprim/download/jsprim-1.4.1.tgz"
  integrity sha1-MT5mvB5cwG5Di8G3SZwuXFastqI=
  dependencies:
    assert-plus "1.0.0"
    extsprintf "1.3.0"
    json-schema "0.2.3"
    verror "1.10.0"

jstransformer@1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/jstransformer/download/jstransformer-1.0.0.tgz#ed8bf0921e2f3f1ed4d5c1a44f68709ed24722c3"
  integrity sha1-7Yvwkh4vPx7U1cGkT2hwntJHIsM=
  dependencies:
    is-promise "^2.0.0"
    promise "^7.0.1"

jwa@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/jwa/download/jwa-2.0.0.tgz"
  integrity sha1-p+nD8p2ulAJ+vK9Jl1yTRVk0EPw=
  dependencies:
    buffer-equal-constant-time "1.0.1"
    ecdsa-sig-formatter "1.0.11"
    safe-buffer "^5.0.1"

jws@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/jws/download/jws-4.0.0.tgz"
  integrity sha1-LU6M9qMY/6oSYV6d7H6G5slzEPQ=
  dependencies:
    jwa "^2.0.0"
    safe-buffer "^5.0.1"

keygrip@~1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/keygrip/download/keygrip-1.0.3.tgz"
  integrity sha1-OZ1wnwrtK6sKBZ4M3TpQI6BT4dw=

keygrip@~1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/keygrip/download/keygrip-1.1.0.tgz"
  integrity sha1-hxsWgdXhWcYqRFsMdLYV4JF+ciY=
  dependencies:
    tsscmp "1.0.6"

keyv@^4.5.3:
  version "4.5.4"
  resolved "http://registry.npm.qima-inc.com/keyv/download/keyv-4.5.4.tgz#a879a99e29452f942439f2a405e3af8b31d4de93"
  integrity sha1-qHmpnilFL5QkOfKkBeOvizHU3pM=
  dependencies:
    json-buffer "3.0.1"

kind-of@^3.0.2, kind-of@^3.0.3, kind-of@^3.2.0:
  version "3.2.2"
  resolved "http://registry.npm.qima-inc.com/kind-of/download/kind-of-3.2.2.tgz"
  integrity sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/kind-of/download/kind-of-4.0.0.tgz"
  integrity sha1-IIE989cSkosgc3hpGkUGb65y3Vc=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^5.0.0:
  version "5.1.0"
  resolved "http://registry.npm.qima-inc.com/kind-of/download/kind-of-5.1.0.tgz"
  integrity sha1-cpyR4thXt6QZofmqZWhcTDP1hF0=

kind-of@^6.0.0, kind-of@^6.0.2:
  version "6.0.3"
  resolved "http://registry.npm.qima-inc.com/kind-of/download/kind-of-6.0.3.tgz"
  integrity sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=

kleur@^3.0.3:
  version "3.0.3"
  resolved "http://registry.npm.qima-inc.com/kleur/download/kleur-3.0.3.tgz"
  integrity sha1-p5yezIbuHOP6YgbRIWxQHxR/wH4=

ko-sleep@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/ko-sleep/download/ko-sleep-1.0.3.tgz"
  integrity sha1-KKKgoUhei39BX/SI3uF9JHiKsII=
  dependencies:
    ms "^2.0.0"

koa-body@2.5.0:
  version "2.5.0"
  resolved "http://registry.npm.qima-inc.com/koa-body/download/koa-body-2.5.0.tgz"
  integrity sha1-hOj82NUimozBy5ipJuk5Bp5xaRU=
  dependencies:
    co-body "^5.1.1"
    formidable "^1.1.1"

koa-bodyparser@4.2.1:
  version "4.2.1"
  resolved "http://registry.npm.qima-inc.com/koa-bodyparser/download/koa-bodyparser-4.2.1.tgz"
  integrity sha1-TX2stebbEQZkm1ldnlzLFYtvOyk=
  dependencies:
    co-body "^6.0.0"
    copy-to "^2.0.1"

koa-compose@4.1.0, koa-compose@^4.0.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/koa-compose/download/koa-compose-4.1.0.tgz"
  integrity sha1-UHMGuTcZAdtBEhyBLpI9DWfT6Hc=

koa-compose@^3.0.0:
  version "3.2.1"
  resolved "http://registry.npm.qima-inc.com/koa-compose/download/koa-compose-3.2.1.tgz"
  integrity sha1-qFzLQLfZhtjlo0Wzoazo6rz1Tec=
  dependencies:
    any-promise "^1.1.0"

koa-convert@^1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/koa-convert/download/koa-convert-1.2.0.tgz"
  integrity sha1-2kCHXfSd4FOQmNFwC1CCDOvNIdA=
  dependencies:
    co "^4.6.0"
    koa-compose "^3.0.0"

koa-is-json@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/koa-is-json/download/koa-is-json-1.0.0.tgz"
  integrity sha1-JzwH7c3Ljfaiwat9We52SRRR7BQ=

koa-router@7.4.0:
  version "7.4.0"
  resolved "http://registry.npm.qima-inc.com/koa-router/download/koa-router-7.4.0.tgz"
  integrity sha1-ruH3rcAtXLMdfWdGXJ6syCXoxeA=
  dependencies:
    debug "^3.1.0"
    http-errors "^1.3.1"
    koa-compose "^3.0.0"
    methods "^1.0.1"
    path-to-regexp "^1.1.1"
    urijs "^1.19.0"

koa-send@^4.1.0:
  version "4.1.3"
  resolved "http://registry.npm.qima-inc.com/koa-send/download/koa-send-4.1.3.tgz"
  integrity sha1-CCIge79SU6QUyPF2Xrwp+kE1PLY=
  dependencies:
    debug "^2.6.3"
    http-errors "^1.6.1"
    mz "^2.6.0"
    resolve-path "^1.4.0"

koa-static@4.0.2:
  version "4.0.2"
  resolved "http://registry.npm.qima-inc.com/koa-static/download/koa-static-4.0.2.tgz"
  integrity sha1-bNqS2I13Hcqtnw2CXNlKYxyGGho=
  dependencies:
    debug "^2.6.8"
    koa-send "^4.1.0"

koa@2.5.0:
  version "2.5.0"
  resolved "http://registry.npm.qima-inc.com/koa/download/koa-2.5.0.tgz"
  integrity sha1-sPvh4ZXkOydYigT9C+DdrsosFUw=
  dependencies:
    accepts "^1.2.2"
    content-disposition "~0.5.0"
    content-type "^1.0.0"
    cookies "~0.7.0"
    debug "*"
    delegates "^1.0.0"
    depd "^1.1.0"
    destroy "^1.0.3"
    error-inject "~1.0.0"
    escape-html "~1.0.1"
    fresh "^0.5.2"
    http-assert "^1.1.0"
    http-errors "^1.2.8"
    is-generator-function "^1.0.3"
    koa-compose "^4.0.0"
    koa-convert "^1.2.0"
    koa-is-json "^1.0.0"
    mime-types "^2.0.7"
    on-finished "^2.1.0"
    only "0.0.2"
    parseurl "^1.3.0"
    statuses "^1.2.0"
    type-is "^1.5.5"
    vary "^1.0.0"

latest-version@^3.0.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/latest-version/download/latest-version-3.1.0.tgz"
  integrity sha1-ogU4P+oyKzO1rjsYq+4NwvNW7hU=
  dependencies:
    package-json "^4.0.0"

lazy@~1.0.11:
  version "1.0.11"
  resolved "http://registry.npm.qima-inc.com/lazy/download/lazy-1.0.11.tgz#daa068206282542c088288e975c297c1ae77b690"
  integrity sha1-2qBoIGKCVCwIgojpdcKXwa53tpA=

lcid@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/lcid/download/lcid-1.0.0.tgz"
  integrity sha1-MIrMr6C8SDo4Z7S28rlQYlHRuDU=
  dependencies:
    invert-kv "^1.0.0"

leven@2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/leven/download/leven-2.1.0.tgz"
  integrity sha1-wuep93IJTe6dNCAq6KzORoeHVYA=

leven@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/leven/download/leven-3.1.0.tgz"
  integrity sha1-d4kd6DQGTMy6gq54QrtrFKE+1/I=

levn@^0.3.0, levn@~0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.qima-inc.com/levn/download/levn-0.3.0.tgz"
  integrity sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=
  dependencies:
    prelude-ls "~1.1.2"
    type-check "~0.3.2"

lightning-request@0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.qima-inc.com/lightning-request/download/lightning-request-0.2.0.tgz"
  integrity sha1-ZBj5FccOUqNi0rUkZ0XB3wmJU60=

lightning-request@0.2.1:
  version "0.2.1"
  resolved "http://registry.npm.qima-inc.com/lightning-request/download/lightning-request-0.2.1.tgz#2cc2d7760cdc8fe4137287da553f978b60836b46"
  integrity sha512-e75vjCPaj2BLDQSMQkF1CJ6odMrYdaMJcI1Vn8nq6Nk2Bm6KpTejJ8RVxbBwzzrr1dYznpVkHe8KKrHYkU+OIQ==
  dependencies:
    sync-rpc "^1.3.6"

lightning-request@1.0.0, lightning-request@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/lightning-request/download/lightning-request-1.0.0.tgz#b36d5a9714045781b04a7d4e8ef37017b95d0ff6"
  integrity sha1-s21alxQEV4GwSn1OjvNwF7ldD/Y=
  dependencies:
    sync-rpc "^1.3.6"

lines-and-columns@^1.1.6:
  version "1.1.6"
  resolved "http://registry.npm.qima-inc.com/lines-and-columns/download/lines-and-columns-1.1.6.tgz"
  integrity sha1-HADHQ7QzzQpOgHWPe2SldEDZ/wA=

lint-staged@^10.5.3:
  version "10.5.4"
  resolved "http://registry.npm.qima-inc.com/lint-staged/download/lint-staged-10.5.4.tgz#cd153b5f0987d2371fc1d2847a409a2fe705b665"
  integrity sha1-zRU7XwmH0jcfwdKEekCaL+cFtmU=
  dependencies:
    chalk "^4.1.0"
    cli-truncate "^2.1.0"
    commander "^6.2.0"
    cosmiconfig "^7.0.0"
    debug "^4.2.0"
    dedent "^0.7.0"
    enquirer "^2.3.6"
    execa "^4.1.0"
    listr2 "^3.2.2"
    log-symbols "^4.0.0"
    micromatch "^4.0.2"
    normalize-path "^3.0.0"
    please-upgrade-node "^3.2.0"
    string-argv "0.3.1"
    stringify-object "^3.3.0"

listr2@^3.2.2:
  version "3.14.0"
  resolved "http://registry.npm.qima-inc.com/listr2/download/listr2-3.14.0.tgz#23101cc62e1375fd5836b248276d1d2b51fdbe9e"
  integrity sha1-IxAcxi4Tdf1YNrJIJ20dK1H9vp4=
  dependencies:
    cli-truncate "^2.1.0"
    colorette "^2.0.16"
    log-update "^4.0.0"
    p-map "^4.0.0"
    rfdc "^1.3.0"
    rxjs "^7.5.1"
    through "^2.3.8"
    wrap-ansi "^7.0.0"

load-json-file@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/load-json-file/download/load-json-file-2.0.0.tgz"
  integrity sha1-eUfkIUmvgNaWy/eXvKq8/h/inKg=
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^2.2.0"
    pify "^2.0.0"
    strip-bom "^3.0.0"

locate-path@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/locate-path/download/locate-path-2.0.0.tgz"
  integrity sha1-K1aLJl7slExtnA3pw9u7ygNUzY4=
  dependencies:
    p-locate "^2.0.0"
    path-exists "^3.0.0"

locate-path@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/locate-path/download/locate-path-5.0.0.tgz"
  integrity sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=
  dependencies:
    p-locate "^4.1.0"

locate-path@^6.0.0:
  version "6.0.0"
  resolved "http://registry.npm.qima-inc.com/locate-path/download/locate-path-6.0.0.tgz#55321eb309febbc59c4801d931a72452a681d286"
  integrity sha1-VTIeswn+u8WcSAHZMackUqaB0oY=
  dependencies:
    p-locate "^5.0.0"

lodash.camelcase@^4.3.0:
  version "4.3.0"
  resolved "http://registry.npm.qima-inc.com/lodash.camelcase/download/lodash.camelcase-4.3.0.tgz"
  integrity sha1-soqmKIorn8ZRA1x3EfZathkDMaY=

lodash.clone@^4.5.0:
  version "4.5.0"
  resolved "http://registry.npm.qima-inc.com/lodash.clone/download/lodash.clone-4.5.0.tgz"
  integrity sha1-GVhwRQ9aExkkeN9Lw9I9LeoZB7Y=

lodash.defaults@^4.2.0:
  version "4.2.0"
  resolved "http://registry.npm.qima-inc.com/lodash.defaults/download/lodash.defaults-4.2.0.tgz"
  integrity sha1-0JF4cW/+pN3p5ft7N/bwgCJ0WAw=

lodash.flatten@^4.4.0:
  version "4.4.0"
  resolved "http://registry.npm.qima-inc.com/lodash.flatten/download/lodash.flatten-4.4.0.tgz"
  integrity sha1-8xwiIlqWMtK7+OSt2+8kCqdlph8=

lodash.isarguments@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/lodash.isarguments/download/lodash.isarguments-3.1.0.tgz#2f573d85c6a24289ff00663b491c1d338ff3458a"
  integrity sha1-L1c9hcaiQon/AGY7SRwdM4/zRYo=

lodash.memoize@4.x:
  version "4.1.2"
  resolved "http://registry.npm.qima-inc.com/lodash.memoize/download/lodash.memoize-4.1.2.tgz"
  integrity sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4=

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "http://registry.npm.qima-inc.com/lodash.merge/download/lodash.merge-4.6.2.tgz#558aa53b43b661e1925a0afdfa36a9a1085fe57a"
  integrity sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=

lodash.sortby@^4.7.0:
  version "4.7.0"
  resolved "http://registry.npm.qima-inc.com/lodash.sortby/download/lodash.sortby-4.7.0.tgz"
  integrity sha1-7dFMgk4sycHgsKG0K7UhBRakJDg=

lodash.toarray@^4.4.0:
  version "4.4.0"
  resolved "http://registry.npm.qima-inc.com/lodash.toarray/download/lodash.toarray-4.4.0.tgz"
  integrity sha1-JMS/zWsvuji/0FlNsRedjptlZWE=

lodash@4.17.10:
  version "4.17.10"
  resolved "http://registry.npm.qima-inc.com/lodash/download/lodash-4.17.10.tgz"
  integrity sha1-G3eTz3JZ6jj7NmHU04syYK+K5Oc=

lodash@4.17.11:
  version "4.17.11"
  resolved "http://registry.npm.qima-inc.com/lodash/download/lodash-4.17.11.tgz"
  integrity sha1-s56mIp72B+zYniyN8SU2iRysm40=

lodash@4.17.19:
  version "4.17.19"
  resolved "http://registry.npm.qima-inc.com/lodash/download/lodash-4.17.19.tgz"
  integrity sha1-5I3e2+MLMyF4PFtDAfvTU7weSks=

lodash@^4.17.11, lodash@^4.17.14, lodash@^4.17.15, lodash@^4.17.19, lodash@^4.17.5, lodash@~>=4.17.11:
  version "4.17.20"
  resolved "http://registry.npm.qima-inc.com/lodash/download/lodash-4.17.20.tgz"
  integrity sha1-tEqbYpe8tpjxxRo1RaKzs2jVnFI=

lodash@^4.17.21:
  version "4.17.21"
  resolved "http://registry.npm.qima-inc.com/lodash/download/lodash-4.17.21.tgz#679591c564c3bffaae8454cf0b3df370c3d6911c"
  integrity sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=

log-driver@^1.2.7:
  version "1.2.7"
  resolved "http://registry.npm.qima-inc.com/log-driver/download/log-driver-1.2.7.tgz#63b95021f0702fedfa2c9bb0a24e7797d71871d8"
  integrity sha1-Y7lQIfBwL+36LJuwok53l9cYcdg=

log-symbols@^2.2.0:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/log-symbols/download/log-symbols-2.2.0.tgz"
  integrity sha1-V0Dhxdbw39pK2TI7UzIQfva0xAo=
  dependencies:
    chalk "^2.0.1"

log-symbols@^4.0.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/log-symbols/download/log-symbols-4.1.0.tgz#3fbdbb95b4683ac9fc785111e792e558d4abd503"
  integrity sha1-P727lbRoOsn8eFER55LlWNSr1QM=
  dependencies:
    chalk "^4.1.0"
    is-unicode-supported "^0.1.0"

log-update@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/log-update/download/log-update-4.0.0.tgz#589ecd352471f2a1c0c570287543a64dfd20e0a1"
  integrity sha1-WJ7NNSRx8qHAxXAodUOmTf0g4KE=
  dependencies:
    ansi-escapes "^4.3.0"
    cli-cursor "^3.1.0"
    slice-ansi "^4.0.0"
    wrap-ansi "^6.2.0"

long@^3.2.0:
  version "3.2.0"
  resolved "http://registry.npm.qima-inc.com/long/download/long-3.2.0.tgz"
  integrity sha1-2CG3E4yhy1gcFymQ7xTbIAtcR0s=

long@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/long/download/long-4.0.0.tgz"
  integrity sha1-mntxz7fTYaGU6lVSQckvdGjVvyg=

lowercase-keys@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/lowercase-keys/download/lowercase-keys-1.0.1.tgz"
  integrity sha1-b54wtHCE2XGnyCD/FabFFnt0wm8=

lru-cache@5.1.1, lru-cache@^5.1.1:
  version "5.1.1"
  resolved "http://registry.npm.qima-inc.com/lru-cache/download/lru-cache-5.1.1.tgz"
  integrity sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=
  dependencies:
    yallist "^3.0.2"

lru-cache@^4.0.1:
  version "4.1.5"
  resolved "http://registry.npm.qima-inc.com/lru-cache/download/lru-cache-4.1.5.tgz"
  integrity sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80=
  dependencies:
    pseudomap "^1.0.2"
    yallist "^2.1.2"

lru-cache@^6.0.0:
  version "6.0.0"
  resolved "http://registry.npm.qima-inc.com/lru-cache/download/lru-cache-6.0.0.tgz"
  integrity sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ=
  dependencies:
    yallist "^4.0.0"

make-dir@^1.0.0:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/make-dir/download/make-dir-1.3.0.tgz"
  integrity sha1-ecEDO4BRW9bSTsmTPoYMp17ifww=
  dependencies:
    pify "^3.0.0"

make-dir@^3.0.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/make-dir/download/make-dir-3.1.0.tgz"
  integrity sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8=
  dependencies:
    semver "^6.0.0"

make-error@1.x, make-error@^1.1.1:
  version "1.3.6"
  resolved "http://registry.npm.qima-inc.com/make-error/download/make-error-1.3.6.tgz"
  integrity sha1-LrLjfqm2fEiR9oShOUeZr0hM96I=

makeerror@1.0.x:
  version "1.0.11"
  resolved "http://registry.npm.qima-inc.com/makeerror/download/makeerror-1.0.11.tgz"
  integrity sha1-4BpckQnyr3lmDk6LlYd5AYT1qWw=
  dependencies:
    tmpl "1.0.x"

map-cache@^0.2.2:
  version "0.2.2"
  resolved "http://registry.npm.qima-inc.com/map-cache/download/map-cache-0.2.2.tgz"
  integrity sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=

map-canvas@>=0.1.5:
  version "0.1.5"
  resolved "http://registry.npm.qima-inc.com/map-canvas/download/map-canvas-0.1.5.tgz"
  integrity sha1-i+a63gvz6fmotW6INqHR0TPKsYY=
  dependencies:
    drawille-canvas-blessed-contrib ">=0.0.1"
    xml2js "^0.4.5"

map-visit@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/map-visit/download/map-visit-1.0.0.tgz"
  integrity sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=
  dependencies:
    object-visit "^1.0.0"

markdown-table@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/markdown-table/download/markdown-table-2.0.0.tgz#194a90ced26d31fe753d8b9434430214c011865b"
  integrity sha1-GUqQztJtMf51PYuUNEMCFMARhls=
  dependencies:
    repeat-string "^1.0.0"

marked-terminal@^4.0.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/marked-terminal/download/marked-terminal-4.1.0.tgz"
  integrity sha1-AQhzctNjbcfLKGR1odYUcYf1AOA=
  dependencies:
    ansi-escapes "^4.3.1"
    cardinal "^2.1.1"
    chalk "^4.0.0"
    cli-table "^0.3.1"
    node-emoji "^1.10.0"
    supports-hyperlinks "^2.1.0"

marked@^0.7.0:
  version "0.7.0"
  resolved "http://registry.npm.qima-inc.com/marked/download/marked-0.7.0.tgz"
  integrity sha1-tkIB8FHScbHtwQoE0a6bdLuOXA4=

media-typer@0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.qima-inc.com/media-typer/download/media-typer-0.3.0.tgz"
  integrity sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=

memory-streams@^0.1.0:
  version "0.1.3"
  resolved "http://registry.npm.qima-inc.com/memory-streams/download/memory-streams-0.1.3.tgz"
  integrity sha1-2bABe0uH8dkvVfJ0XJyqyx3JPOs=
  dependencies:
    readable-stream "~1.0.2"

memorystream@^0.3.1:
  version "0.3.1"
  resolved "http://registry.npm.qima-inc.com/memorystream/download/memorystream-0.3.1.tgz"
  integrity sha1-htcJCzDORV1j+64S3aUaR93K+bI=

merge-descriptors@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/merge-descriptors/download/merge-descriptors-1.0.1.tgz"
  integrity sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E=

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/merge-stream/download/merge-stream-2.0.0.tgz"
  integrity sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=

merge2@^1.2.3, merge2@^1.3.0:
  version "1.4.1"
  resolved "http://registry.npm.qima-inc.com/merge2/download/merge2-1.4.1.tgz"
  integrity sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=

methods@^1.0.1, methods@^1.1.1, methods@^1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/methods/download/methods-1.1.2.tgz"
  integrity sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=

micromatch@^3.1.10, micromatch@^3.1.4:
  version "3.1.10"
  resolved "http://registry.npm.qima-inc.com/micromatch/download/micromatch-3.1.10.tgz"
  integrity sha1-cIWbyVyYQJUvNZoGij/En57PrCM=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    braces "^2.3.1"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    extglob "^2.0.4"
    fragment-cache "^0.2.1"
    kind-of "^6.0.2"
    nanomatch "^1.2.9"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.2"

micromatch@^4.0.2:
  version "4.0.2"
  resolved "http://registry.npm.qima-inc.com/micromatch/download/micromatch-4.0.2.tgz"
  integrity sha1-T8sJmb+fvC/L3SEvbWKbmlbDklk=
  dependencies:
    braces "^3.0.1"
    picomatch "^2.0.5"

micromatch@^4.0.4:
  version "4.0.7"
  resolved "http://registry.npm.qima-inc.com/micromatch/download/micromatch-4.0.7.tgz#33e8190d9fe474a9895525f5618eee136d46c2e5"
  integrity sha1-M+gZDZ/kdKmJVSX1YY7uE21GwuU=
  dependencies:
    braces "^3.0.3"
    picomatch "^2.3.1"

micromatch@^4.0.5:
  version "4.0.8"
  resolved "http://registry.npm.qima-inc.com/micromatch/download/micromatch-4.0.8.tgz#d66fa18f3a47076789320b9b1af32bd86d9fa202"
  integrity sha1-1m+hjzpHB2eJMgubGvMr2G2fogI=
  dependencies:
    braces "^3.0.3"
    picomatch "^2.3.1"

microtime@3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/microtime/download/microtime-3.0.0.tgz"
  integrity sha1-0UCRS96Iqom0+f0qGGILQ1rw85s=
  dependencies:
    node-addon-api "^1.2.0"
    node-gyp-build "^3.8.0"

mime-db@1.45.0:
  version "1.45.0"
  resolved "http://registry.npm.qima-inc.com/mime-db/download/mime-db-1.45.0.tgz"
  integrity sha1-zO7aIczXw6dF66LezVXUtz54eeo=

mime-types@^2.0.7, mime-types@^2.1.12, mime-types@~2.1.19, mime-types@~2.1.24:
  version "2.1.28"
  resolved "http://registry.npm.qima-inc.com/mime-types/download/mime-types-2.1.28.tgz"
  integrity sha1-EWDEdX6rLFNjiI4AUnPs950qDs0=
  dependencies:
    mime-db "1.45.0"

mime@2.3.1:
  version "2.3.1"
  resolved "http://registry.npm.qima-inc.com/mime/download/mime-2.3.1.tgz"
  integrity sha1-sWIcVNY7l8R9PP5/chX31kUXw2k=

mime@^1.3.4, mime@^1.4.1:
  version "1.6.0"
  resolved "http://registry.npm.qima-inc.com/mime/download/mime-1.6.0.tgz"
  integrity sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=

mime@^2.2.0:
  version "2.5.0"
  resolved "http://registry.npm.qima-inc.com/mime/download/mime-2.5.0.tgz"
  integrity sha1-K0r5NEAXeYBu6YAmu0Lowa4YdrE=

mime@^2.4.6, mime@^2.5.2:
  version "2.6.0"
  resolved "http://registry.npm.qima-inc.com/mime/download/mime-2.6.0.tgz#a2a682a95cd4d0cb1d6257e28f83da7e35800367"
  integrity sha1-oqaCqVzU0MsdYlfij4PafjWAA2c=

mimic-fn@^1.0.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/mimic-fn/download/mimic-fn-1.2.0.tgz"
  integrity sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI=

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/mimic-fn/download/mimic-fn-2.1.0.tgz"
  integrity sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=

minimatch@^3.0.4:
  version "3.0.4"
  resolved "http://registry.npm.qima-inc.com/minimatch/download/minimatch-3.0.4.tgz"
  integrity sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM=
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^3.1.1:
  version "3.1.2"
  resolved "http://registry.npm.qima-inc.com/minimatch/download/minimatch-3.1.2.tgz#19cd194bfd3e428f049a70817c038d89ab4be35b"
  integrity sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=
  dependencies:
    brace-expansion "^1.1.7"

minimist@1.2.5, minimist@^1.1.0, minimist@^1.1.1, minimist@^1.2.0, minimist@^1.2.5:
  version "1.2.5"
  resolved "http://registry.npm.qima-inc.com/minimist/download/minimist-1.2.5.tgz"
  integrity sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI=

miniprogram-api-typings@2.x:
  version "2.12.0"
  resolved "http://registry.npm.qima-inc.com/miniprogram-api-typings/download/miniprogram-api-typings-2.12.0.tgz#7a29c90f3e5efa36588422d1f01e22d3394aaaa1"
  integrity sha1-einJDz5e+jZYhCLR8B4i0zlKqqE=

mixin-deep@^1.2.0:
  version "1.3.2"
  resolved "http://registry.npm.qima-inc.com/mixin-deep/download/mixin-deep-1.3.2.tgz"
  integrity sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY=
  dependencies:
    for-in "^1.0.2"
    is-extendable "^1.0.1"

mkdirp@1.0.4, mkdirp@1.x:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/mkdirp/download/mkdirp-1.0.4.tgz"
  integrity sha1-PrXtYmInVteaXw4qIh3+utdcL34=

mkdirp@^0.5.1:
  version "0.5.5"
  resolved "http://registry.npm.qima-inc.com/mkdirp/download/mkdirp-0.5.5.tgz"
  integrity sha1-2Rzv1i0UNsoPQWIOJRKI1CAJne8=
  dependencies:
    minimist "^1.2.5"

mm@^3.2.0:
  version "3.2.0"
  resolved "http://registry.npm.qima-inc.com/mm/download/mm-3.2.0.tgz"
  integrity sha1-UJzCR3wkN29MRBIp3fjd09gLX8w=
  dependencies:
    is-type-of "^1.2.1"
    ko-sleep "^1.0.3"
    muk-prop "^1.2.1"
    thenify "^3.3.0"

module-details-from-path@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/module-details-from-path/download/module-details-from-path-1.0.3.tgz#114c949673e2a8a35e9d35788527aa37b679da2b"
  integrity sha1-EUyUlnPiqKNenTV4hSeqN7Z52is=

moment-timezone@^0.5.x:
  version "0.5.37"
  resolved "http://registry.npm.qima-inc.com/moment-timezone/download/moment-timezone-0.5.37.tgz#adf97f719c4e458fdb12e2b4e87b8bec9f4eef1e"
  integrity sha1-rfl/cZxORY/bEuK06HuL7J9O7x4=
  dependencies:
    moment ">= 2.9.0"

"moment@>= 2.9.0":
  version "2.29.4"
  resolved "http://registry.npm.qima-inc.com/moment/download/moment-2.29.4.tgz#3dbe052889fe7c1b2ed966fcb3a77328964ef108"
  integrity sha1-Pb4FKIn+fBsu2Wb8s6dzKJZO8Qg=

mri@1.1.4:
  version "1.1.4"
  resolved "http://registry.npm.qima-inc.com/mri/download/mri-1.1.4.tgz"
  integrity sha1-fLHdG5tAkF8frAU6viW2cg9EdEo=

ms@2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/ms/download/ms-2.0.0.tgz"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

ms@2.1.2:
  version "2.1.2"
  resolved "http://registry.npm.qima-inc.com/ms/download/ms-2.1.2.tgz"
  integrity sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=

ms@^2.0.0, ms@^2.1.1, ms@^2.1.3:
  version "2.1.3"
  resolved "http://registry.npm.qima-inc.com/ms/download/ms-2.1.3.tgz"
  integrity sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=

muk-prop@^1.2.1:
  version "1.2.1"
  resolved "http://registry.npm.qima-inc.com/muk-prop/download/muk-prop-1.2.1.tgz"
  integrity sha1-QPo9bpNVOyAWqft32JGFaMV64U0=

mute-stream@0.0.8, mute-stream@~0.0.4:
  version "0.0.8"
  resolved "http://registry.npm.qima-inc.com/mute-stream/download/mute-stream-0.0.8.tgz"
  integrity sha1-FjDEKyJR/4HiooPelqVJfqkuXg0=

mz@^2.6.0, mz@^2.7.0:
  version "2.7.0"
  resolved "http://registry.npm.qima-inc.com/mz/download/mz-2.7.0.tgz"
  integrity sha1-lQCAV6Vsr63CvGPd5/n/aVWUjjI=
  dependencies:
    any-promise "^1.0.0"
    object-assign "^4.0.1"
    thenify-all "^1.0.0"

nan@^2.12.1:
  version "2.14.2"
  resolved "http://registry.npm.qima-inc.com/nan/download/nan-2.14.2.tgz"
  integrity sha1-9TdkAGlRaPTMaUrJOT0MlYXu6hk=

nanomatch@^1.2.9:
  version "1.2.13"
  resolved "http://registry.npm.qima-inc.com/nanomatch/download/nanomatch-1.2.13.tgz"
  integrity sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    fragment-cache "^0.2.1"
    is-windows "^1.0.2"
    kind-of "^6.0.2"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/natural-compare/download/natural-compare-1.4.0.tgz"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

needle@2.4.0:
  version "2.4.0"
  resolved "http://registry.npm.qima-inc.com/needle/download/needle-2.4.0.tgz#6833e74975c444642590e15a750288c5f939b57c"
  integrity sha1-aDPnSXXERGQlkOFadQKIxfk5tXw=
  dependencies:
    debug "^3.2.6"
    iconv-lite "^0.4.4"
    sax "^1.2.4"

negotiator@0.6.2:
  version "0.6.2"
  resolved "http://registry.npm.qima-inc.com/negotiator/download/negotiator-0.6.2.tgz"
  integrity sha1-/qz3zPUlp3rpY0Q2pkiD/+yjRvs=

netmask@^1.0.6:
  version "1.0.6"
  resolved "http://registry.npm.qima-inc.com/netmask/download/netmask-1.0.6.tgz"
  integrity sha1-ICl+idhvb2QA8lDZ9Pa0wZRfzTU=

netmask@^2.0.1:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/netmask/download/netmask-2.0.2.tgz#8b01a07644065d536383835823bc52004ebac5e7"
  integrity sha1-iwGgdkQGXVNjg4NYI7xSAE66xec=

nice-try@^1.0.4:
  version "1.0.5"
  resolved "http://registry.npm.qima-inc.com/nice-try/download/nice-try-1.0.5.tgz"
  integrity sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y=

node-addon-api@^1.2.0:
  version "1.7.2"
  resolved "http://registry.npm.qima-inc.com/node-addon-api/download/node-addon-api-1.7.2.tgz"
  integrity sha1-PfMLlXILU8JOWZSLSVMrZiRE9U0=

node-emoji@^1.10.0:
  version "1.10.0"
  resolved "http://registry.npm.qima-inc.com/node-emoji/download/node-emoji-1.10.0.tgz"
  integrity sha1-iIar0l2ce7YYAqZYUj0fjSqJsto=
  dependencies:
    lodash.toarray "^4.4.0"

node-fetch@^2.3.0:
  version "2.6.1"
  resolved "http://registry.npm.qima-inc.com/node-fetch/download/node-fetch-2.6.1.tgz"
  integrity sha1-BFvTI2Mfdu0uK1VXM5RBa2OaAFI=

node-fetch@^2.6.9:
  version "2.7.0"
  resolved "http://registry.npm.qima-inc.com/node-fetch/download/node-fetch-2.7.0.tgz#d0f0fa6e3e2dc1d27efcd8ad99d550bda94d187d"
  integrity sha1-0PD6bj4twdJ+/NitmdVQvalNGH0=
  dependencies:
    whatwg-url "^5.0.0"

node-forge@^0.10.0:
  version "0.10.0"
  resolved "http://registry.npm.qima-inc.com/node-forge/download/node-forge-0.10.0.tgz"
  integrity sha1-Mt6ir7Ppkm8C7lzoeUkCaRpna/M=

node-gyp-build@^3.8.0:
  version "3.9.0"
  resolved "http://registry.npm.qima-inc.com/node-gyp-build/download/node-gyp-build-3.9.0.tgz"
  integrity sha1-U6NQGH3U1SdnUNohYF0ctoHQniU=

node-int64@^0.4.0:
  version "0.4.0"
  resolved "http://registry.npm.qima-inc.com/node-int64/download/node-int64-0.4.0.tgz"
  integrity sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=

node-modules-regexp@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/node-modules-regexp/download/node-modules-regexp-1.0.0.tgz"
  integrity sha1-jZ2+KJZKSsVxLpExZCEHxx6Q7EA=

node-notifier@^8.0.0:
  version "8.0.1"
  resolved "http://registry.npm.qima-inc.com/node-notifier/download/node-notifier-8.0.1.tgz"
  integrity sha1-+G6Ju8kl8rBoeEsx84Kv3Gyla+E=
  dependencies:
    growly "^1.3.0"
    is-wsl "^2.2.0"
    semver "^7.3.2"
    shellwords "^0.1.1"
    uuid "^8.3.0"
    which "^2.0.2"

nodemon@^1.17.3:
  version "1.19.4"
  resolved "http://registry.npm.qima-inc.com/nodemon/download/nodemon-1.19.4.tgz"
  integrity sha1-VttcYHQI4P34kg0rREgZrxquCXE=
  dependencies:
    chokidar "^2.1.8"
    debug "^3.2.6"
    ignore-by-default "^1.0.1"
    minimatch "^3.0.4"
    pstree.remy "^1.1.7"
    semver "^5.7.1"
    supports-color "^5.5.0"
    touch "^3.1.0"
    undefsafe "^2.0.2"
    update-notifier "^2.5.0"

nopt@~1.0.10:
  version "1.0.10"
  resolved "http://registry.npm.qima-inc.com/nopt/download/nopt-1.0.10.tgz"
  integrity sha1-bd0hvSoxQXuScn3Vhfim83YI6+4=
  dependencies:
    abbrev "1"

nopt@~2.1.2:
  version "2.1.2"
  resolved "http://registry.npm.qima-inc.com/nopt/download/nopt-2.1.2.tgz"
  integrity sha1-bMzZd7gBMqB3MdbozljCyDA8+a8=
  dependencies:
    abbrev "1"

normalize-package-data@^2.3.2, normalize-package-data@^2.5.0:
  version "2.5.0"
  resolved "http://registry.npm.qima-inc.com/normalize-package-data/download/normalize-package-data-2.5.0.tgz"
  integrity sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=
  dependencies:
    hosted-git-info "^2.1.4"
    resolve "^1.10.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-path@^2.1.1:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/normalize-path/download/normalize-path-2.1.1.tgz"
  integrity sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=
  dependencies:
    remove-trailing-separator "^1.0.1"

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/normalize-path/download/normalize-path-3.0.0.tgz"
  integrity sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=

npm-run-path@^2.0.0:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/npm-run-path/download/npm-run-path-2.0.2.tgz"
  integrity sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=
  dependencies:
    path-key "^2.0.0"

npm-run-path@^4.0.0, npm-run-path@^4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/npm-run-path/download/npm-run-path-4.0.1.tgz"
  integrity sha1-t+zR5e1T2o43pV4cImnguX7XSOo=
  dependencies:
    path-key "^3.0.0"

nssocket@0.6.0:
  version "0.6.0"
  resolved "http://registry.npm.qima-inc.com/nssocket/download/nssocket-0.6.0.tgz#59f96f6ff321566f33c70f7dbeeecdfdc07154fa"
  integrity sha1-Wflvb/MhVm8zxw99vu7N/cBxVPo=
  dependencies:
    eventemitter2 "~0.4.14"
    lazy "~1.0.11"

number-is-nan@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/number-is-nan/download/number-is-nan-1.0.1.tgz"
  integrity sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0=

nunjucks@3.1.6:
  version "3.1.6"
  resolved "http://registry.npm.qima-inc.com/nunjucks/download/nunjucks-3.1.6.tgz"
  integrity sha1-bjo0IMd86uk3rjI+nimVOD1kEPs=
  dependencies:
    a-sync-waterfall "^1.0.0"
    asap "^2.0.3"
    postinstall-build "^5.0.1"
    yargs "^3.32.0"
  optionalDependencies:
    chokidar "^2.0.0"

nwsapi@^2.2.0:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/nwsapi/download/nwsapi-2.2.0.tgz"
  integrity sha1-IEh5qePQaP8qVROcLHcngGgaOLc=

oauth-sign@~0.9.0:
  version "0.9.0"
  resolved "http://registry.npm.qima-inc.com/oauth-sign/download/oauth-sign-0.9.0.tgz"
  integrity sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU=

object-assign@^4.0.1, object-assign@^4.1.0, object-assign@^4.1.1:
  version "4.1.1"
  resolved "http://registry.npm.qima-inc.com/object-assign/download/object-assign-4.1.1.tgz"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-copy@^0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.qima-inc.com/object-copy/download/object-copy-0.1.0.tgz"
  integrity sha1-fn2Fi3gb18mRpBupde04EnVOmYw=
  dependencies:
    copy-descriptor "^0.1.0"
    define-property "^0.2.5"
    kind-of "^3.0.3"

object-inspect@^1.8.0:
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/object-inspect/download/object-inspect-1.9.0.tgz"
  integrity sha1-yQUh104RJ7ZyZt7TOUrWEWmGUzo=

object-keys@^1.0.12, object-keys@^1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/object-keys/download/object-keys-1.1.1.tgz"
  integrity sha1-HEfyct8nfzsdrwYWd9nILiMixg4=

object-visit@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/object-visit/download/object-visit-1.0.1.tgz"
  integrity sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=
  dependencies:
    isobject "^3.0.0"

object.assign@^4.1.1, object.assign@^4.1.2:
  version "4.1.2"
  resolved "http://registry.npm.qima-inc.com/object.assign/download/object.assign-4.1.2.tgz"
  integrity sha1-DtVKNC7Os3s4/3brgxoOeIy2OUA=
  dependencies:
    call-bind "^1.0.0"
    define-properties "^1.1.3"
    has-symbols "^1.0.1"
    object-keys "^1.1.1"

object.entries@^1.1.2:
  version "1.1.3"
  resolved "http://registry.npm.qima-inc.com/object.entries/download/object.entries-1.1.3.tgz"
  integrity sha1-xgHH8Wi2I3RUGgfdvT4tXk93EaY=
  dependencies:
    call-bind "^1.0.0"
    define-properties "^1.1.3"
    es-abstract "^1.18.0-next.1"
    has "^1.0.3"

object.pick@^1.3.0:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/object.pick/download/object.pick-1.3.0.tgz"
  integrity sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=
  dependencies:
    isobject "^3.0.1"

object.values@^1.1.1:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/object.values/download/object.values-1.1.2.tgz"
  integrity sha1-eiAV4G/LD1Rr1lJIbOhYOkcxxzE=
  dependencies:
    call-bind "^1.0.0"
    define-properties "^1.1.3"
    es-abstract "^1.18.0-next.1"
    has "^1.0.3"

on-finished@^2.1.0:
  version "2.3.0"
  resolved "http://registry.npm.qima-inc.com/on-finished/download/on-finished-2.3.0.tgz"
  integrity sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=
  dependencies:
    ee-first "1.1.1"

once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/once/download/once-1.4.0.tgz"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/onetime/download/onetime-2.0.1.tgz"
  integrity sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ=
  dependencies:
    mimic-fn "^1.0.0"

onetime@^5.1.0, onetime@^5.1.2:
  version "5.1.2"
  resolved "http://registry.npm.qima-inc.com/onetime/download/onetime-5.1.2.tgz"
  integrity sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=
  dependencies:
    mimic-fn "^2.1.0"

only@0.0.2:
  version "0.0.2"
  resolved "http://registry.npm.qima-inc.com/only/download/only-0.0.2.tgz"
  integrity sha1-Kv3oTQPlC5qO3EROMGEKcCle37Q=

open@7.1.0:
  version "7.1.0"
  resolved "http://registry.npm.qima-inc.com/open/download/open-7.1.0.tgz"
  integrity sha1-aIZffTyyOFIPoSJaY88ovPg2ihw=
  dependencies:
    is-docker "^2.0.0"
    is-wsl "^2.1.1"

opencollective-postinstall@^2.0.2:
  version "2.0.3"
  resolved "http://registry.npm.qima-inc.com/opencollective-postinstall/download/opencollective-postinstall-2.0.3.tgz#7a0fff978f6dbfa4d006238fbac98ed4198c3259"
  integrity sha512-8AV/sCtuzUeTo8gQK5qDZzARrulB3egtLzFgteqB2tcT4Mw7B8Kt7JcDHmltjz6FOAHsvTevk70gZEbhM4ZS9Q==

opn@^5.3.0:
  version "5.5.0"
  resolved "http://registry.npm.qima-inc.com/opn/download/opn-5.5.0.tgz"
  integrity sha1-/HFk+rVtI1kExRw7J9pnWMo7m/w=
  dependencies:
    is-wsl "^1.1.0"

optimist@0.2:
  version "0.2.8"
  resolved "http://registry.npm.qima-inc.com/optimist/download/optimist-0.2.8.tgz"
  integrity sha1-6YGrfiaLRXlIWTtVZ0wJmoFcrDE=
  dependencies:
    wordwrap ">=0.0.1 <0.1.0"

optimist@~0.3.4:
  version "0.3.7"
  resolved "http://registry.npm.qima-inc.com/optimist/download/optimist-0.3.7.tgz"
  integrity sha1-yQlBrVnkJzMokjB00s8ufLxuwNk=
  dependencies:
    wordwrap "~0.0.2"

optionator@^0.8.1, optionator@^0.8.3:
  version "0.8.3"
  resolved "http://registry.npm.qima-inc.com/optionator/download/optionator-0.8.3.tgz"
  integrity sha1-hPodA2/p08fiHZmIS2ARZ+yPtJU=
  dependencies:
    deep-is "~0.1.3"
    fast-levenshtein "~2.0.6"
    levn "~0.3.0"
    prelude-ls "~1.1.2"
    type-check "~0.3.2"
    word-wrap "~1.2.3"

ora@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/ora/download/ora-2.1.0.tgz"
  integrity sha1-bK8oMOuSSUGGHsU6FzeZ4Ai1Hls=
  dependencies:
    chalk "^2.3.1"
    cli-cursor "^2.1.0"
    cli-spinners "^1.1.0"
    log-symbols "^2.2.0"
    strip-ansi "^4.0.0"
    wcwidth "^1.0.1"

os-locale@^1.4.0:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/os-locale/download/os-locale-1.4.0.tgz"
  integrity sha1-IPnxeuKe00XoveWDsT0gCYA8FNk=
  dependencies:
    lcid "^1.0.0"

os-name@~1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/os-name/download/os-name-1.0.3.tgz"
  integrity sha1-GzefZINa98Wn9JizV8uVIVwVnt8=
  dependencies:
    osx-release "^1.0.0"
    win-release "^1.0.0"

os-tmpdir@~1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/os-tmpdir/download/os-tmpdir-1.0.2.tgz"
  integrity sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=

osx-release@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/osx-release/download/osx-release-1.1.0.tgz"
  integrity sha1-8heRGigTaUmvG/kwiyQeJzfTzWw=
  dependencies:
    minimist "^1.1.0"

p-each-series@^2.1.0:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/p-each-series/download/p-each-series-2.2.0.tgz"
  integrity sha1-EFqwNXznKyAqiouUkzZyZXteKpo=

p-finally@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/p-finally/download/p-finally-1.0.0.tgz"
  integrity sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=

p-limit@^1.1.0:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/p-limit/download/p-limit-1.3.0.tgz"
  integrity sha1-uGvV8MJWkJEcdZD8v8IBDVSzzLg=
  dependencies:
    p-try "^1.0.0"

p-limit@^2.2.0:
  version "2.3.0"
  resolved "http://registry.npm.qima-inc.com/p-limit/download/p-limit-2.3.0.tgz"
  integrity sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=
  dependencies:
    p-try "^2.0.0"

p-limit@^3.0.2:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/p-limit/download/p-limit-3.1.0.tgz#e1daccbe78d0d1388ca18c64fea38e3e57e3706b"
  integrity sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=
  dependencies:
    yocto-queue "^0.1.0"

p-locate@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/p-locate/download/p-locate-2.0.0.tgz"
  integrity sha1-IKAQOyIqcMj9OcwuWAaA893l7EM=
  dependencies:
    p-limit "^1.1.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/p-locate/download/p-locate-4.1.0.tgz"
  integrity sha1-o0KLtwiLOmApL2aRkni3wpetTwc=
  dependencies:
    p-limit "^2.2.0"

p-locate@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/p-locate/download/p-locate-5.0.0.tgz#83c8315c6785005e3bd021839411c9e110e6d834"
  integrity sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=
  dependencies:
    p-limit "^3.0.2"

p-map@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/p-map/download/p-map-2.1.0.tgz#310928feef9c9ecc65b68b17693018a665cea175"
  integrity sha1-MQko/u+cnsxltosXaTAYpmXOoXU=

p-map@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/p-map/download/p-map-4.0.0.tgz#bb2f95a5eda2ec168ec9274e06a747c3e2904d2b"
  integrity sha1-uy+Vpe2i7BaOySdOBqdHw+KQTSs=
  dependencies:
    aggregate-error "^3.0.0"

p-try@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/p-try/download/p-try-1.0.0.tgz"
  integrity sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M=

p-try@^2.0.0:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/p-try/download/p-try-2.2.0.tgz"
  integrity sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=

pac-proxy-agent@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/pac-proxy-agent/download/pac-proxy-agent-3.0.1.tgz"
  integrity sha1-EVseWPkldsrC66cYWTynsON94q0=
  dependencies:
    agent-base "^4.2.0"
    debug "^4.1.1"
    get-uri "^2.0.0"
    http-proxy-agent "^2.1.0"
    https-proxy-agent "^3.0.0"
    pac-resolver "^3.0.0"
    raw-body "^2.2.0"
    socks-proxy-agent "^4.0.1"

pac-proxy-agent@^4.1.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/pac-proxy-agent/download/pac-proxy-agent-4.1.0.tgz#66883eeabadc915fc5e95457324cb0f0ac78defb"
  integrity sha1-Zog+6rrckV/F6VRXMkyw8Kx43vs=
  dependencies:
    "@tootallnate/once" "1"
    agent-base "6"
    debug "4"
    get-uri "3"
    http-proxy-agent "^4.0.1"
    https-proxy-agent "5"
    pac-resolver "^4.1.0"
    raw-body "^2.2.0"
    socks-proxy-agent "5"

pac-resolver@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/pac-resolver/download/pac-resolver-3.0.0.tgz"
  integrity sha1-auoweH2wqJFwTet4AKcip2FabyY=
  dependencies:
    co "^4.6.0"
    degenerator "^1.0.4"
    ip "^1.1.5"
    netmask "^1.0.6"
    thunkify "^2.1.2"

pac-resolver@^4.1.0:
  version "4.2.0"
  resolved "http://registry.npm.qima-inc.com/pac-resolver/download/pac-resolver-4.2.0.tgz#b82bcb9992d48166920bc83c7542abb454bd9bdd"
  integrity sha1-uCvLmZLUgWaSC8g8dUKrtFS9m90=
  dependencies:
    degenerator "^2.2.0"
    ip "^1.1.5"
    netmask "^2.0.1"

package-json@^4.0.0:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/package-json/download/package-json-4.0.1.tgz"
  integrity sha1-iGmgQBJTZhxMTKPabCEh7VVfXu0=
  dependencies:
    got "^6.7.1"
    registry-auth-token "^3.0.1"
    registry-url "^3.0.3"
    semver "^5.1.0"

parent-module@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/parent-module/download/parent-module-1.0.1.tgz"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

parent-module@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/parent-module/download/parent-module-2.0.0.tgz#fa71f88ff1a50c27e15d8ff74e0e3a9523bf8708"
  integrity sha1-+nH4j/GlDCfhXY/3Tg46lSO/hwg=
  dependencies:
    callsites "^3.1.0"

parse-json@^2.2.0:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/parse-json/download/parse-json-2.2.0.tgz"
  integrity sha1-9ID0BDTvgHQfhGkJn43qGPVaTck=
  dependencies:
    error-ex "^1.2.0"

parse-json@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/parse-json/download/parse-json-4.0.0.tgz"
  integrity sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=
  dependencies:
    error-ex "^1.3.1"
    json-parse-better-errors "^1.0.1"

parse-json@^5.0.0:
  version "5.1.0"
  resolved "http://registry.npm.qima-inc.com/parse-json/download/parse-json-5.1.0.tgz"
  integrity sha1-+WCIzfJKj6qa6poAny2dlCyZlkY=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

parse5@5.1.1:
  version "5.1.1"
  resolved "http://registry.npm.qima-inc.com/parse5/download/parse5-5.1.1.tgz"
  integrity sha1-9o5OW6GFKsLK3AD0VV//bCq7YXg=

parseurl@^1.3.0:
  version "1.3.3"
  resolved "http://registry.npm.qima-inc.com/parseurl/download/parseurl-1.3.3.tgz"
  integrity sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=

pascalcase@^0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/pascalcase/download/pascalcase-0.1.1.tgz"
  integrity sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=

path-dirname@^1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/path-dirname/download/path-dirname-1.0.2.tgz"
  integrity sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA=

path-exists@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/path-exists/download/path-exists-3.0.0.tgz"
  integrity sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=

path-exists@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/path-exists/download/path-exists-4.0.0.tgz"
  integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=

path-is-absolute@1.0.1, path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-is-inside@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/path-is-inside/download/path-is-inside-1.0.2.tgz"
  integrity sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM=

path-key@^2.0.0, path-key@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/path-key/download/path-key-2.0.1.tgz"
  integrity sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "http://registry.npm.qima-inc.com/path-key/download/path-key-3.1.1.tgz"
  integrity sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=

path-matching@0.0.2:
  version "0.0.2"
  resolved "http://registry.npm.qima-inc.com/path-matching/download/path-matching-0.0.2.tgz"
  integrity sha1-LZTEe4G1nkjMQbOw49WI5NNC8/w=
  dependencies:
    path-to-regexp "^2.2.0"

path-parse@^1.0.6:
  version "1.0.6"
  resolved "http://registry.npm.qima-inc.com/path-parse/download/path-parse-1.0.6.tgz"
  integrity sha1-1i27VnlAXXLEc37FhgDp3c8G0kw=

path-parse@^1.0.7:
  version "1.0.7"
  resolved "http://registry.npm.qima-inc.com/path-parse/download/path-parse-1.0.7.tgz#fbc114b60ca42b30d9daf5858e4bd68bbedb6735"
  integrity sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=

path-to-regexp@^1.1.1:
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/path-to-regexp/download/path-to-regexp-1.9.0.tgz#5dc0753acbf8521ca2e0f137b4578b917b10cf24"
  integrity sha1-XcB1Osv4Uhyi4PE3tFeLkXsQzyQ=
  dependencies:
    isarray "0.0.1"

path-to-regexp@^2.2.0:
  version "2.4.0"
  resolved "http://registry.npm.qima-inc.com/path-to-regexp/download/path-to-regexp-2.4.0.tgz#35ce7f333d5616f1c1e1bfe266c3aba2e5b2e704"
  integrity sha1-Nc5/Mz1WFvHB4b/iZsOrouWy5wQ=

path-type@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/path-type/download/path-type-2.0.0.tgz"
  integrity sha1-8BLMuEFbcJb8LaoQVMPXI4lZTHM=
  dependencies:
    pify "^2.0.0"

path-type@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/path-type/download/path-type-4.0.0.tgz#84ed01c0a7ba380afe09d90a8c180dcd9d03043b"
  integrity sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=

pause-stream@~0.0.11:
  version "0.0.11"
  resolved "http://registry.npm.qima-inc.com/pause-stream/download/pause-stream-0.0.11.tgz"
  integrity sha1-/lo0sMvOErWqaitAPuLnO2AvFEU=
  dependencies:
    through "~2.3"

performance-now@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/performance-now/download/performance-now-2.1.0.tgz"
  integrity sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=

picomatch@^2.0.4, picomatch@^2.0.5, picomatch@^2.2.1:
  version "2.2.2"
  resolved "http://registry.npm.qima-inc.com/picomatch/download/picomatch-2.2.2.tgz"
  integrity sha1-IfMz6ba46v8CRo9RRupAbTRfTa0=

picomatch@^2.3.1:
  version "2.3.1"
  resolved "http://registry.npm.qima-inc.com/picomatch/download/picomatch-2.3.1.tgz#3ba3833733646d9d3e4995946c1365a67fb07a42"
  integrity sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=

picture-tuber@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/picture-tuber/download/picture-tuber-1.0.2.tgz"
  integrity sha1-L28CSogvvSiGnQt4qNGrRZUObL8=
  dependencies:
    buffers "~0.1.1"
    charm "~0.1.0"
    event-stream "~0.9.8"
    optimist "~0.3.4"
    png-js "~0.1.0"
    x256 "~0.0.1"

pidusage@2.0.18:
  version "2.0.18"
  resolved "http://registry.npm.qima-inc.com/pidusage/download/pidusage-2.0.18.tgz#9ccef35df5508a5a4b0838c712ea9b79609aff34"
  integrity sha1-nM7zXfVQilpLCDjHEuqbeWCa/zQ=
  dependencies:
    safe-buffer "^5.1.2"

pify@^2.0.0:
  version "2.3.0"
  resolved "http://registry.npm.qima-inc.com/pify/download/pify-2.3.0.tgz"
  integrity sha1-7RQaasBDqEnqWISY59yosVMw6Qw=

pify@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/pify/download/pify-3.0.0.tgz"
  integrity sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=

pino-http@5.2.0:
  version "5.2.0"
  resolved "http://registry.npm.qima-inc.com/pino-http/download/pino-http-5.2.0.tgz"
  integrity sha1-1R2nmBq26nyp8XxNskpCfkupxUY=
  dependencies:
    fast-url-parser "^1.1.3"
    pino "^6.0.0"
    pino-std-serializers "^2.4.0"

pino-pretty@4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/pino-pretty/download/pino-pretty-4.0.0.tgz"
  integrity sha1-r7/4H5RjQrnW6rxDSUL+SQ4C+qk=
  dependencies:
    "@hapi/bourne" "^2.0.0"
    args "^5.0.1"
    chalk "^3.0.0"
    dateformat "^3.0.3"
    fast-safe-stringify "^2.0.7"
    jmespath "^0.15.0"
    joycon "^2.2.5"
    pump "^3.0.0"
    readable-stream "^3.6.0"
    split2 "^3.1.1"
    strip-json-comments "^3.0.1"

pino-std-serializers@^2.4.0, pino-std-serializers@^2.4.2:
  version "2.5.0"
  resolved "http://registry.npm.qima-inc.com/pino-std-serializers/download/pino-std-serializers-2.5.0.tgz"
  integrity sha1-QOrXgcZaDOfs2cHDP0CdMf5xIxU=

pino-std-serializers@^3.1.0:
  version "3.1.1"
  resolved "http://registry.npm.qima-inc.com/pino-std-serializers/download/pino-std-serializers-3.1.1.tgz"
  integrity sha1-WpoAcVyf80KNVVXUsCOj+KWJPlQ=

pino@6.3.2:
  version "6.3.2"
  resolved "http://registry.npm.qima-inc.com/pino/download/pino-6.3.2.tgz"
  integrity sha1-Vfc6phWEd0ylmEBo/7eOjVGc4Z4=
  dependencies:
    fast-redact "^2.0.0"
    fast-safe-stringify "^2.0.7"
    flatstr "^1.0.12"
    pino-std-serializers "^2.4.2"
    quick-format-unescaped "^4.0.1"
    sonic-boom "^1.0.0"

pino@^6.0.0:
  version "6.11.0"
  resolved "http://registry.npm.qima-inc.com/pino/download/pino-6.11.0.tgz"
  integrity sha1-xHRxLUYvHeUkz4hSV9IScdGrfBY=
  dependencies:
    fast-redact "^3.0.0"
    fast-safe-stringify "^2.0.7"
    flatstr "^1.0.12"
    pino-std-serializers "^3.1.0"
    quick-format-unescaped "^4.0.1"
    sonic-boom "^1.0.2"

pirates@^4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/pirates/download/pirates-4.0.1.tgz"
  integrity sha1-ZDqSyviUVm+RsrmG0sZpUKji+4c=
  dependencies:
    node-modules-regexp "^1.0.0"

pkg-dir@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/pkg-dir/download/pkg-dir-2.0.0.tgz"
  integrity sha1-9tXREJ4Z1j7fQo4L1X4Sd3YVM0s=
  dependencies:
    find-up "^2.1.0"

pkg-dir@^4.2.0:
  version "4.2.0"
  resolved "http://registry.npm.qima-inc.com/pkg-dir/download/pkg-dir-4.2.0.tgz"
  integrity sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=
  dependencies:
    find-up "^4.0.0"

pkg-dir@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/pkg-dir/download/pkg-dir-5.0.0.tgz#a02d6aebe6ba133a928f74aec20bafdfe6b8e760"
  integrity sha512-NPE8TDbzl/3YQYY7CSS228s3g2ollTFnc+Qi3tqmqJp9Vg2ovUpixcJEo2HJScN2Ez+kEaal6y70c0ehqJBJeA==
  dependencies:
    find-up "^5.0.0"

please-upgrade-node@^3.2.0:
  version "3.2.0"
  resolved "http://registry.npm.qima-inc.com/please-upgrade-node/download/please-upgrade-node-3.2.0.tgz#aeddd3f994c933e4ad98b99d9a556efa0e2fe942"
  integrity sha1-rt3T+ZTJM+StmLmdmlVu+g4v6UI=
  dependencies:
    semver-compare "^1.0.0"

pm2-axon-rpc@0.5.1:
  version "0.5.1"
  resolved "http://registry.npm.qima-inc.com/pm2-axon-rpc/download/pm2-axon-rpc-0.5.1.tgz#ad3c43c43811c71f13e5eee2821194d03ceb03fe"
  integrity sha1-rTxDxDgRxx8T5e7ighGU0DzrA/4=
  dependencies:
    debug "^3.0"

pm2-axon-rpc@~0.7.0:
  version "0.7.1"
  resolved "http://registry.npm.qima-inc.com/pm2-axon-rpc/download/pm2-axon-rpc-0.7.1.tgz#2daec5383a63135b3f18babb70266dacdcbc429a"
  integrity sha1-La7FODpjE1s/GLq7cCZtrNy8Qpo=
  dependencies:
    debug "^4.3.1"

pm2-axon@3.3.0:
  version "3.3.0"
  resolved "http://registry.npm.qima-inc.com/pm2-axon/download/pm2-axon-3.3.0.tgz#a9badfdb8e083fbd5d7d24317b4a21eb708f0735"
  integrity sha1-qbrf244IP71dfSQxe0oh63CPBzU=
  dependencies:
    amp "~0.3.1"
    amp-message "~0.1.1"
    debug "^3.0"
    escape-regexp "0.0.1"

pm2-axon@~4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/pm2-axon/download/pm2-axon-4.0.1.tgz#a7b4bb586e9aeb35b1042b488cde15b60cabafd2"
  integrity sha1-p7S7WG6a6zWxBCtIjN4Vtgyrr9I=
  dependencies:
    amp "~0.3.1"
    amp-message "~0.1.1"
    debug "^4.3.1"
    escape-string-regexp "^4.0.0"

pm2-deploy@~1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/pm2-deploy/download/pm2-deploy-1.0.2.tgz#98d8385553a3a4dca11c7b3116deb519bc5961a7"
  integrity sha1-mNg4VVOjpNyhHHsxFt61GbxZYac=
  dependencies:
    run-series "^1.1.8"
    tv4 "^1.3.0"

pm2-multimeter@^0.1.2:
  version "0.1.2"
  resolved "http://registry.npm.qima-inc.com/pm2-multimeter/download/pm2-multimeter-0.1.2.tgz#1a1e55153d41a05534cea23cfe860abaa0eb4ace"
  integrity sha1-Gh5VFT1BoFU0zqI8/oYKuqDrSs4=
  dependencies:
    charm "~0.1.1"

pm2@4.4.0:
  version "4.4.0"
  resolved "http://registry.npm.qima-inc.com/pm2/download/pm2-4.4.0.tgz#85ad86da2d3c6618881714e885c85a96d77ee379"
  integrity sha1-ha2G2i08ZhiIFxTohchaltd+43k=
  dependencies:
    "@pm2/agent" "~1.0.2"
    "@pm2/io" "~4.3.5"
    "@pm2/js-api" "~0.6.0"
    "@pm2/pm2-version-check" latest
    async "~3.2.0"
    blessed "0.1.81"
    chalk "3.0.0"
    chokidar "^3.3.0"
    cli-tableau "^2.0.0"
    commander "2.15.1"
    cron "1.8.2"
    dayjs "~1.8.25"
    debug "4.1.1"
    enquirer "2.3.5"
    eventemitter2 "5.0.1"
    fclone "1.0.11"
    mkdirp "1.0.4"
    needle "2.4.0"
    pidusage "2.0.18"
    pm2-axon "3.3.0"
    pm2-axon-rpc "0.5.1"
    pm2-deploy "~1.0.2"
    pm2-multimeter "^0.1.2"
    promptly "^2"
    ps-list "6.3.0"
    semver "^7.2"
    source-map-support "0.5.16"
    sprintf-js "1.1.2"
    vizion "0.2.13"
    yamljs "0.3.0"
  optionalDependencies:
    systeminformation "^4.23.3"

png-js@~0.1.0:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/png-js/download/png-js-0.1.1.tgz"
  integrity sha1-HMfCEjA6yr50Jj7DrHgAlYAkLZM=

posix-character-classes@^0.1.0:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/posix-character-classes/download/posix-character-classes-0.1.1.tgz"
  integrity sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=

postinstall-build@^5.0.1:
  version "5.0.3"
  resolved "http://registry.npm.qima-inc.com/postinstall-build/download/postinstall-build-5.0.3.tgz"
  integrity sha1-I4aS9xKkgdj1vIlg6UeGA2JB78c=

prelude-ls@~1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/prelude-ls/download/prelude-ls-1.1.2.tgz"
  integrity sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ=

prepend-http@^1.0.1:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/prepend-http/download/prepend-http-1.0.4.tgz"
  integrity sha1-1PRWKwzjaW5BrFLQ4ALlemNdxtw=

prettier-linter-helpers@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/prettier-linter-helpers/download/prettier-linter-helpers-1.0.0.tgz#d23d41fe1375646de2d0104d3454a3008802cf7b"
  integrity sha1-0j1B/hN1ZG3i0BBNNFSjAIgCz3s=
  dependencies:
    fast-diff "^1.1.2"

prettier@^2.0.0:
  version "2.2.1"
  resolved "http://registry.npm.qima-inc.com/prettier/download/prettier-2.2.1.tgz"
  integrity sha1-eVoaeN1S8HPaDNQrIfnJE4GSP/U=

pretty-format@^26.0.0, pretty-format@^26.6.2:
  version "26.6.2"
  resolved "http://registry.npm.qima-inc.com/pretty-format/download/pretty-format-26.6.2.tgz"
  integrity sha1-41wnBfFMt/4v6U+geDRbREEg/JM=
  dependencies:
    "@jest/types" "^26.6.2"
    ansi-regex "^5.0.0"
    ansi-styles "^4.0.0"
    react-is "^17.0.1"

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/process-nextick-args/download/process-nextick-args-2.0.1.tgz"
  integrity sha1-eCDZsWEgzFXKmud5JoCufbptf+I=

progress@^2.0.0:
  version "2.0.3"
  resolved "http://registry.npm.qima-inc.com/progress/download/progress-2.0.3.tgz#7e8cf8d8f5b8f239c1bc68beb4eb78567d572ef8"
  integrity sha1-foz42PW48jnBvGi+tOt4Vn1XLvg=

promise@^7.0.1:
  version "7.3.1"
  resolved "http://registry.npm.qima-inc.com/promise/download/promise-7.3.1.tgz#064b72602b18f90f29192b8b1bc418ffd1ebd3bf"
  integrity sha1-BktyYCsY+Q8pGSuLG8QY/9Hr078=
  dependencies:
    asap "~2.0.3"

promptly@^2:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/promptly/download/promptly-2.2.0.tgz#2a13fa063688a2a5983b161fff0108a07d26fc74"
  integrity sha1-KhP6BjaIoqWYOxYf/wEIoH0m/HQ=
  dependencies:
    read "^1.0.4"

prompts@^2.0.1:
  version "2.4.0"
  resolved "http://registry.npm.qima-inc.com/prompts/download/prompts-2.4.0.tgz"
  integrity sha1-SqXeByOiMdHukSHED99mPfc/Ydc=
  dependencies:
    kleur "^3.0.3"
    sisteransi "^1.0.5"

properties-parser@0.3.1:
  version "0.3.1"
  resolved "http://registry.npm.qima-inc.com/properties-parser/download/properties-parser-0.3.1.tgz"
  integrity sha1-ExbpU5/7/ZOEXjabIRAiq9R4dxo=
  dependencies:
    string.prototype.codepointat "^0.2.0"

protobufjs@^6.8.6:
  version "6.10.2"
  resolved "http://registry.npm.qima-inc.com/protobufjs/download/protobufjs-6.10.2.tgz"
  integrity sha1-uctr2OyPh1FFkro/39KOk/M6Rps=
  dependencies:
    "@protobufjs/aspromise" "^1.1.2"
    "@protobufjs/base64" "^1.1.2"
    "@protobufjs/codegen" "^2.0.4"
    "@protobufjs/eventemitter" "^1.1.0"
    "@protobufjs/fetch" "^1.1.0"
    "@protobufjs/float" "^1.0.2"
    "@protobufjs/inquire" "^1.1.0"
    "@protobufjs/path" "^1.1.2"
    "@protobufjs/pool" "^1.1.0"
    "@protobufjs/utf8" "^1.1.0"
    "@types/long" "^4.0.1"
    "@types/node" "^13.7.0"
    long "^4.0.0"

proxy-agent@^3.0.3:
  version "3.1.1"
  resolved "http://registry.npm.qima-inc.com/proxy-agent/download/proxy-agent-3.1.1.tgz"
  integrity sha1-fgTga/Nq+mJKFUC+JHtHyXC9MBQ=
  dependencies:
    agent-base "^4.2.0"
    debug "4"
    http-proxy-agent "^2.1.0"
    https-proxy-agent "^3.0.0"
    lru-cache "^5.1.1"
    pac-proxy-agent "^3.0.1"
    proxy-from-env "^1.0.0"
    socks-proxy-agent "^4.0.1"

proxy-agent@~4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/proxy-agent/download/proxy-agent-4.0.1.tgz#326c3250776c7044cd19655ccbfadf2e065a045c"
  integrity sha1-MmwyUHdscETNGWVcy/rfLgZaBFw=
  dependencies:
    agent-base "^6.0.0"
    debug "4"
    http-proxy-agent "^4.0.0"
    https-proxy-agent "^5.0.0"
    lru-cache "^5.1.1"
    pac-proxy-agent "^4.1.0"
    proxy-from-env "^1.0.0"
    socks-proxy-agent "^5.0.0"

proxy-from-env@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/proxy-from-env/download/proxy-from-env-1.1.0.tgz"
  integrity sha1-4QLxbKNVQkhldV0sno6k8k1Yw+I=

ps-list@6.3.0:
  version "6.3.0"
  resolved "http://registry.npm.qima-inc.com/ps-list/download/ps-list-6.3.0.tgz#a2b775c2db7d547a28fbaa3a05e4c281771259be"
  integrity sha1-ord1wtt9VHoo+6o6BeTCgXcSWb4=

pseudomap@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/pseudomap/download/pseudomap-1.0.2.tgz"
  integrity sha1-8FKijacOYYkX7wqKw0wa5aaChrM=

psl@^1.1.28:
  version "1.8.0"
  resolved "http://registry.npm.qima-inc.com/psl/download/psl-1.8.0.tgz"
  integrity sha1-kyb4vPsBOtzABf3/BWrM4CDlHCQ=

pstree.remy@^1.1.7:
  version "1.1.8"
  resolved "http://registry.npm.qima-inc.com/pstree.remy/download/pstree.remy-1.1.8.tgz"
  integrity sha1-wkIiT0pnwh9oaDm720rCgrg3PTo=

pug-attrs@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/pug-attrs/download/pug-attrs-3.0.0.tgz#b10451e0348165e31fad1cc23ebddd9dc7347c41"
  integrity sha1-sQRR4DSBZeMfrRzCPr3dncc0fEE=
  dependencies:
    constantinople "^4.0.1"
    js-stringify "^1.0.2"
    pug-runtime "^3.0.0"

pug-code-gen@^3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/pug-code-gen/download/pug-code-gen-3.0.2.tgz#ad190f4943133bf186b60b80de483100e132e2ce"
  integrity sha1-rRkPSUMTO/GGtguA3kgxAOEy4s4=
  dependencies:
    constantinople "^4.0.1"
    doctypes "^1.1.0"
    js-stringify "^1.0.2"
    pug-attrs "^3.0.0"
    pug-error "^2.0.0"
    pug-runtime "^3.0.0"
    void-elements "^3.1.0"
    with "^7.0.0"

pug-error@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/pug-error/download/pug-error-2.0.0.tgz#5c62173cb09c34de2a2ce04f17b8adfec74d8ca5"
  integrity sha1-XGIXPLCcNN4qLOBPF7it/sdNjKU=

pug-filters@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/pug-filters/download/pug-filters-4.0.0.tgz#d3e49af5ba8472e9b7a66d980e707ce9d2cc9b5e"
  integrity sha1-0+Sa9bqEcum3pm2YDnB86dLMm14=
  dependencies:
    constantinople "^4.0.1"
    jstransformer "1.0.0"
    pug-error "^2.0.0"
    pug-walk "^2.0.0"
    resolve "^1.15.1"

pug-lexer@^5.0.1:
  version "5.0.1"
  resolved "http://registry.npm.qima-inc.com/pug-lexer/download/pug-lexer-5.0.1.tgz#ae44628c5bef9b190b665683b288ca9024b8b0d5"
  integrity sha1-rkRijFvvmxkLZlaDsojKkCS4sNU=
  dependencies:
    character-parser "^2.2.0"
    is-expression "^4.0.0"
    pug-error "^2.0.0"

pug-linker@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/pug-linker/download/pug-linker-4.0.0.tgz#12cbc0594fc5a3e06b9fc59e6f93c146962a7708"
  integrity sha1-EsvAWU/Fo+Brn8Web5PBRpYqdwg=
  dependencies:
    pug-error "^2.0.0"
    pug-walk "^2.0.0"

pug-load@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/pug-load/download/pug-load-3.0.0.tgz#9fd9cda52202b08adb11d25681fb9f34bd41b662"
  integrity sha1-n9nNpSICsIrbEdJWgfufNL1BtmI=
  dependencies:
    object-assign "^4.1.1"
    pug-walk "^2.0.0"

pug-parser@^6.0.0:
  version "6.0.0"
  resolved "http://registry.npm.qima-inc.com/pug-parser/download/pug-parser-6.0.0.tgz#a8fdc035863a95b2c1dc5ebf4ecf80b4e76a1260"
  integrity sha1-qP3ANYY6lbLB3F6/Ts+AtOdqEmA=
  dependencies:
    pug-error "^2.0.0"
    token-stream "1.0.0"

pug-runtime@^3.0.0, pug-runtime@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/pug-runtime/download/pug-runtime-3.0.1.tgz#f636976204723f35a8c5f6fad6acda2a191b83d7"
  integrity sha1-9jaXYgRyPzWoxfb61qzaKhkbg9c=

pug-strip-comments@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/pug-strip-comments/download/pug-strip-comments-2.0.0.tgz#f94b07fd6b495523330f490a7f554b4ff876303e"
  integrity sha1-+UsH/WtJVSMzD0kKf1VLT/h2MD4=
  dependencies:
    pug-error "^2.0.0"

pug-walk@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/pug-walk/download/pug-walk-2.0.0.tgz#417aabc29232bb4499b5b5069a2b2d2a24d5f5fe"
  integrity sha1-QXqrwpIyu0SZtbUGmistKiTV9f4=

pug@^3.0.1, pug@^3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/pug/download/pug-3.0.2.tgz#f35c7107343454e43bc27ae0ff76c731b78ea535"
  integrity sha1-81xxBzQ0VOQ7wnrg/3bHMbeOpTU=
  dependencies:
    pug-code-gen "^3.0.2"
    pug-filters "^4.0.0"
    pug-lexer "^5.0.1"
    pug-linker "^4.0.0"
    pug-load "^3.0.0"
    pug-parser "^6.0.0"
    pug-runtime "^3.0.1"
    pug-strip-comments "^2.0.0"

pump@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/pump/download/pump-3.0.0.tgz"
  integrity sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ=
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

punycode@^1.3.2:
  version "1.4.1"
  resolved "http://registry.npm.qima-inc.com/punycode/download/punycode-1.4.1.tgz"
  integrity sha1-wNWmOycYgArY4esPpSachN1BhF4=

punycode@^2.1.0, punycode@^2.1.1:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/punycode/download/punycode-2.1.1.tgz"
  integrity sha1-tYsBCsQMIsVldhbI0sLALHv0eew=

qs@^6.4.0, qs@^6.5.1, qs@^6.5.2:
  version "6.9.4"
  resolved "http://registry.npm.qima-inc.com/qs/download/qs-6.9.4.tgz"
  integrity sha1-kJCykNH5FyjTwi5UhDykSupatoc=

qs@~6.5.2:
  version "6.5.2"
  resolved "http://registry.npm.qima-inc.com/qs/download/qs-6.5.2.tgz"
  integrity sha1-yzroBuh0BERYTvFUzo7pjUA/PjY=

query-string@5:
  version "5.1.1"
  resolved "http://registry.npm.qima-inc.com/query-string/download/query-string-5.1.1.tgz"
  integrity sha1-p4wBK3HBfgXy4/ojGd0zBoLvs8s=
  dependencies:
    decode-uri-component "^0.2.0"
    object-assign "^4.1.0"
    strict-uri-encode "^1.0.0"

query-string@6.3.0:
  version "6.3.0"
  resolved "http://registry.npm.qima-inc.com/query-string/download/query-string-6.3.0.tgz"
  integrity sha1-Qa6KYeEhPICxgtXbbPEp4Fr4n8U=
  dependencies:
    decode-uri-component "^0.2.0"
    strict-uri-encode "^2.0.0"

query-string@^6.13.7:
  version "6.14.1"
  resolved "http://registry.npm.qima-inc.com/query-string/download/query-string-6.14.1.tgz#7ac2dca46da7f309449ba0f86b1fd28255b0c86a"
  integrity sha1-esLcpG2n8wlEm6D4ax/SglWwyGo=
  dependencies:
    decode-uri-component "^0.2.0"
    filter-obj "^1.1.0"
    split-on-first "^1.0.0"
    strict-uri-encode "^2.0.0"

querystringify@^2.1.1:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/querystringify/download/querystringify-2.2.0.tgz"
  integrity sha1-M0WUG0FTy50ILY7uTNogFqmu9/Y=

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "http://registry.npm.qima-inc.com/queue-microtask/download/queue-microtask-1.2.3.tgz#4929228bbc724dfac43e0efb058caf7b6cfb6243"
  integrity sha1-SSkii7xyTfrEPg77BYyve2z7YkM=

quick-format-unescaped@^4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/quick-format-unescaped/download/quick-format-unescaped-4.0.1.tgz"
  integrity sha1-Q3peoaC2Het2Bfirao/ThY2+twE=

raf@^3.4.1:
  version "3.4.1"
  resolved "http://registry.npm.qima-inc.com/raf/download/raf-3.4.1.tgz"
  integrity sha1-B0LpmkplUvRF1z4+4DKK8P8e3jk=
  dependencies:
    performance-now "^2.1.0"

raw-body@^2.2.0, raw-body@^2.3.3:
  version "2.4.1"
  resolved "http://registry.npm.qima-inc.com/raw-body/download/raw-body-2.4.1.tgz"
  integrity sha1-MKyC+Yu1rowVLmcUnayNVRU7Fow=
  dependencies:
    bytes "3.1.0"
    http-errors "1.7.3"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

rc@^1.0.1, rc@^1.1.6:
  version "1.2.8"
  resolved "http://registry.npm.qima-inc.com/rc/download/rc-1.2.8.tgz"
  integrity sha1-zZJL9SAKB1uDwYjNa54hG3/A0+0=
  dependencies:
    deep-extend "^0.6.0"
    ini "~1.3.0"
    minimist "^1.2.0"
    strip-json-comments "~2.0.1"

react-is@^17.0.1:
  version "17.0.1"
  resolved "http://registry.npm.qima-inc.com/react-is/download/react-is-17.0.1.tgz"
  integrity sha1-WzUxvXamRaTJ+25pPtNkGeMwEzk=

read-pkg-up@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/read-pkg-up/download/read-pkg-up-2.0.0.tgz"
  integrity sha1-a3KoBImE4MQeeVEP1en6mbO1Sb4=
  dependencies:
    find-up "^2.0.0"
    read-pkg "^2.0.0"

read-pkg-up@^7.0.1:
  version "7.0.1"
  resolved "http://registry.npm.qima-inc.com/read-pkg-up/download/read-pkg-up-7.0.1.tgz"
  integrity sha1-86YTV1hFlzOuK5VjgFbhhU5+9Qc=
  dependencies:
    find-up "^4.1.0"
    read-pkg "^5.2.0"
    type-fest "^0.8.1"

read-pkg@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/read-pkg/download/read-pkg-2.0.0.tgz"
  integrity sha1-jvHAYjxqbbDcZxPEv6xGMysjaPg=
  dependencies:
    load-json-file "^2.0.0"
    normalize-package-data "^2.3.2"
    path-type "^2.0.0"

read-pkg@^5.2.0:
  version "5.2.0"
  resolved "http://registry.npm.qima-inc.com/read-pkg/download/read-pkg-5.2.0.tgz"
  integrity sha1-e/KVQ4yloz5WzTDgU7NO5yUMk8w=
  dependencies:
    "@types/normalize-package-data" "^2.4.0"
    normalize-package-data "^2.5.0"
    parse-json "^5.0.0"
    type-fest "^0.6.0"

read@^1.0.4:
  version "1.0.7"
  resolved "http://registry.npm.qima-inc.com/read/download/read-1.0.7.tgz#b3da19bd052431a97671d44a42634adf710b40c4"
  integrity sha1-s9oZvQUkMal2cdRKQmNK33ELQMQ=
  dependencies:
    mute-stream "~0.0.4"

readable-stream@1.1.x:
  version "1.1.14"
  resolved "http://registry.npm.qima-inc.com/readable-stream/download/readable-stream-1.1.14.tgz"
  integrity sha1-fPTFTvZI44EwhMY23SB54WbAgdk=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.1"
    isarray "0.0.1"
    string_decoder "~0.10.x"

readable-stream@2, readable-stream@^2.0.2, readable-stream@^2.3.5:
  version "2.3.7"
  resolved "http://registry.npm.qima-inc.com/readable-stream/download/readable-stream-2.3.7.tgz"
  integrity sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^3.0.0, readable-stream@^3.6.0:
  version "3.6.0"
  resolved "http://registry.npm.qima-inc.com/readable-stream/download/readable-stream-3.6.0.tgz"
  integrity sha1-M3u9o63AcGvT4CRCaihtS0sskZg=
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readable-stream@~1.0.2:
  version "1.0.34"
  resolved "http://registry.npm.qima-inc.com/readable-stream/download/readable-stream-1.0.34.tgz"
  integrity sha1-Elgg40vIQtLyqq+v5MKRbuMsFXw=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.1"
    isarray "0.0.1"
    string_decoder "~0.10.x"

readdirp@^2.2.1:
  version "2.2.1"
  resolved "http://registry.npm.qima-inc.com/readdirp/download/readdirp-2.2.1.tgz"
  integrity sha1-DodiKjMlqjPokihcr4tOhGUppSU=
  dependencies:
    graceful-fs "^4.1.11"
    micromatch "^3.1.10"
    readable-stream "^2.0.2"

readdirp@~3.6.0:
  version "3.6.0"
  resolved "http://registry.npm.qima-inc.com/readdirp/download/readdirp-3.6.0.tgz#74a370bd857116e245b29cc97340cd431a02a6c7"
  integrity sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=
  dependencies:
    picomatch "^2.2.1"

rechoir@^0.6.2:
  version "0.6.2"
  resolved "http://registry.npm.qima-inc.com/rechoir/download/rechoir-0.6.2.tgz"
  integrity sha1-hSBLVNuoLVdC4oyWdW70OvUOM4Q=
  dependencies:
    resolve "^1.1.6"

redeyed@~2.1.0:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/redeyed/download/redeyed-2.1.1.tgz"
  integrity sha1-iYS1gV2ZyyIEacme7v/jiRPmzAs=
  dependencies:
    esprima "~4.0.0"

redis-commands@1.4.0:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/redis-commands/download/redis-commands-1.4.0.tgz"
  integrity sha1-UvnPmRU+/M5WqPhq+Ya9BOmIYC8=

redis-commands@1.7.0:
  version "1.7.0"
  resolved "http://registry.npm.qima-inc.com/redis-commands/download/redis-commands-1.7.0.tgz#15a6fea2d58281e27b1cd1acfb4b293e278c3a89"
  integrity sha1-Fab+otWCgeJ7HNGs+0spPieMOok=

redis-errors@^1.0.0, redis-errors@^1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/redis-errors/download/redis-errors-1.2.0.tgz"
  integrity sha1-62LSrbFeTq9GEMBK/hUpOEJQq60=

redis-parser@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/redis-parser/download/redis-parser-3.0.0.tgz"
  integrity sha1-tm2CjNyv5rS4pCin3vTGvKwxyLQ=
  dependencies:
    redis-errors "^1.0.0"

regenerator-runtime@^0.13.4:
  version "0.13.7"
  resolved "http://registry.npm.qima-inc.com/regenerator-runtime/download/regenerator-runtime-0.13.7.tgz#cac2dacc8a1ea675feaabaeb8ae833898ae46f55"
  integrity sha1-ysLazIoepnX+qrrriugziYrkb1U=

regex-not@^1.0.0, regex-not@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/regex-not/download/regex-not-1.0.2.tgz"
  integrity sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw=
  dependencies:
    extend-shallow "^3.0.2"
    safe-regex "^1.1.0"

regexpp@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/regexpp/download/regexpp-2.0.1.tgz"
  integrity sha1-jRnTHPYySCtYkEn4KB+T28uk0H8=

regexpp@^3.0.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/regexpp/download/regexpp-3.1.0.tgz"
  integrity sha1-IG0K0KVkjP+9uK5GQ489xRyfeOI=

registry-auth-token@^3.0.1:
  version "3.4.0"
  resolved "http://registry.npm.qima-inc.com/registry-auth-token/download/registry-auth-token-3.4.0.tgz"
  integrity sha1-10RoFUM/XV7WQxzV3KIQSPZrOX4=
  dependencies:
    rc "^1.1.6"
    safe-buffer "^5.0.1"

registry-url@^3.0.3:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/registry-url/download/registry-url-3.1.0.tgz"
  integrity sha1-PU74cPc93h138M+aOBQyRE4XSUI=
  dependencies:
    rc "^1.0.1"

remove-trailing-separator@^1.0.1:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/remove-trailing-separator/download/remove-trailing-separator-1.1.0.tgz"
  integrity sha1-wkvOKig62tW8P1jg1IJJuSN52O8=

repeat-element@^1.1.2:
  version "1.1.3"
  resolved "http://registry.npm.qima-inc.com/repeat-element/download/repeat-element-1.1.3.tgz"
  integrity sha1-eC4NglwMWjuzlzH4Tv7mt0Lmsc4=

repeat-string@^1.0.0, repeat-string@^1.6.1:
  version "1.6.1"
  resolved "http://registry.npm.qima-inc.com/repeat-string/download/repeat-string-1.6.1.tgz"
  integrity sha1-jcrkcOHIirwtYA//Sndihtp15jc=

reprism@^0.0.11:
  version "0.0.11"
  resolved "http://registry.npm.qima-inc.com/reprism/download/reprism-0.0.11.tgz#e760b85e0ae241722032cb8942a2bcab992a9083"
  integrity sha1-52C4XgriQXIgMsuJQqK8q5kqkIM=

request-promise-core@1.1.4:
  version "1.1.4"
  resolved "http://registry.npm.qima-inc.com/request-promise-core/download/request-promise-core-1.1.4.tgz"
  integrity sha1-Pu3UIjII1BmGe3jOgVFn0QWToi8=
  dependencies:
    lodash "^4.17.19"

request-promise-native@^1.0.8:
  version "1.0.9"
  resolved "http://registry.npm.qima-inc.com/request-promise-native/download/request-promise-native-1.0.9.tgz"
  integrity sha1-5AcSBSal79yaObKKVnm/R7nZ3Cg=
  dependencies:
    request-promise-core "1.1.4"
    stealthy-require "^1.1.1"
    tough-cookie "^2.3.3"

request@^2.88.2:
  version "2.88.2"
  resolved "http://registry.npm.qima-inc.com/request/download/request-2.88.2.tgz"
  integrity sha1-1zyRhzHLWofaBH4gcjQUb2ZNErM=
  dependencies:
    aws-sign2 "~0.7.0"
    aws4 "^1.8.0"
    caseless "~0.12.0"
    combined-stream "~1.0.6"
    extend "~3.0.2"
    forever-agent "~0.6.1"
    form-data "~2.3.2"
    har-validator "~5.1.3"
    http-signature "~1.2.0"
    is-typedarray "~1.0.0"
    isstream "~0.1.2"
    json-stringify-safe "~5.0.1"
    mime-types "~2.1.19"
    oauth-sign "~0.9.0"
    performance-now "^2.1.0"
    qs "~6.5.2"
    safe-buffer "^5.1.2"
    tough-cookie "~2.5.0"
    tunnel-agent "^0.6.0"
    uuid "^3.3.2"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/require-directory/download/require-directory-2.1.1.tgz"
  integrity sha1-jGStX9MNqxyXbiNE/+f3kqam30I=

require-in-the-middle@^5.0.0:
  version "5.1.0"
  resolved "http://registry.npm.qima-inc.com/require-in-the-middle/download/require-in-the-middle-5.1.0.tgz#b768f800377b47526d026bbf5a7f727f16eb412f"
  integrity sha1-t2j4ADd7R1JtAmu/Wn9yfxbrQS8=
  dependencies:
    debug "^4.1.1"
    module-details-from-path "^1.0.3"
    resolve "^1.12.0"

require-main-filename@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/require-main-filename/download/require-main-filename-2.0.0.tgz"
  integrity sha1-0LMp7MfMD2Fkn2IhW+aa9UqomJs=

requireindex@~1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/requireindex/download/requireindex-1.1.0.tgz"
  integrity sha1-5UBLgVV+91225JxacgBIk/4D4WI=

requires-port@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/requires-port/download/requires-port-1.0.0.tgz"
  integrity sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=

resolve-cwd@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/resolve-cwd/download/resolve-cwd-3.0.0.tgz"
  integrity sha1-DwB18bslRHZs9zumpuKt/ryxPy0=
  dependencies:
    resolve-from "^5.0.0"

resolve-from@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/resolve-from/download/resolve-from-3.0.0.tgz"
  integrity sha1-six699nWiBvItuZTM17rywoYh0g=

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/resolve-from/download/resolve-from-4.0.0.tgz"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

resolve-from@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/resolve-from/download/resolve-from-5.0.0.tgz"
  integrity sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=

resolve-global@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/resolve-global/download/resolve-global-1.0.0.tgz#a2a79df4af2ca3f49bf77ef9ddacd322dad19255"
  integrity sha1-oqed9K8so/Sb93753azTItrRklU=
  dependencies:
    global-dirs "^0.1.1"

resolve-path@^1.4.0:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/resolve-path/download/resolve-path-1.4.0.tgz"
  integrity sha1-xL2p9e+y/OZSR4c6s2u02DT+Fvc=
  dependencies:
    http-errors "~1.6.2"
    path-is-absolute "1.0.1"

resolve-url@^0.2.1:
  version "0.2.1"
  resolved "http://registry.npm.qima-inc.com/resolve-url/download/resolve-url-0.2.1.tgz"
  integrity sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=

resolve@^1.1.6, resolve@^1.10.0, resolve@^1.13.1, resolve@^1.17.0, resolve@^1.18.1:
  version "1.19.0"
  resolved "http://registry.npm.qima-inc.com/resolve/download/resolve-1.19.0.tgz"
  integrity sha1-GvW/YwQJc0oGfK4pMYqsf6KaJnw=
  dependencies:
    is-core-module "^2.1.0"
    path-parse "^1.0.6"

resolve@^1.12.0:
  version "1.22.1"
  resolved "http://registry.npm.qima-inc.com/resolve/download/resolve-1.22.1.tgz#27cb2ebb53f91abb49470a928bba7558066ac177"
  integrity sha1-J8suu1P5GrtJRwqSi7p1WAZqwXc=
  dependencies:
    is-core-module "^2.9.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

resolve@^1.15.1:
  version "1.22.8"
  resolved "http://registry.npm.qima-inc.com/resolve/download/resolve-1.22.8.tgz#b6c87a9f2aa06dfab52e3d70ac8cde321fa5a48d"
  integrity sha1-tsh6nyqgbfq1Lj1wrIzeMh+lpI0=
  dependencies:
    is-core-module "^2.13.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

restore-cursor@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/restore-cursor/download/restore-cursor-2.0.0.tgz"
  integrity sha1-n37ih/gv0ybU/RYpI9YhKe7g368=
  dependencies:
    onetime "^2.0.0"
    signal-exit "^3.0.2"

restore-cursor@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/restore-cursor/download/restore-cursor-3.1.0.tgz"
  integrity sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

ret@~0.1.10:
  version "0.1.15"
  resolved "http://registry.npm.qima-inc.com/ret/download/ret-0.1.15.tgz"
  integrity sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w=

reusify@^1.0.4:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/reusify/download/reusify-1.0.4.tgz#90da382b1e126efc02146e90845a88db12925d76"
  integrity sha1-kNo4Kx4SbvwCFG6QhFqI2xKSXXY=

rfdc@^1.3.0:
  version "1.4.1"
  resolved "http://registry.npm.qima-inc.com/rfdc/download/rfdc-1.4.1.tgz#778f76c4fb731d93414e8f925fbecf64cce7f6ca"
  integrity sha1-d492xPtzHZNBTo+SX77PZMzn9so=

rimraf@2.6.3:
  version "2.6.3"
  resolved "http://registry.npm.qima-inc.com/rimraf/download/rimraf-2.6.3.tgz"
  integrity sha1-stEE/g2Psnz54KHNqCYt04M8bKs=
  dependencies:
    glob "^7.1.3"

rimraf@^3.0.0, rimraf@^3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/rimraf/download/rimraf-3.0.2.tgz"
  integrity sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=
  dependencies:
    glob "^7.1.3"

rsvp@^4.8.4:
  version "4.8.5"
  resolved "http://registry.npm.qima-inc.com/rsvp/download/rsvp-4.8.5.tgz"
  integrity sha1-yPFVMR0Wf2jyHhaN9x7FsIMRNzQ=

run-async@^2.4.0:
  version "2.4.1"
  resolved "http://registry.npm.qima-inc.com/run-async/download/run-async-2.4.1.tgz"
  integrity sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU=

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/run-parallel/download/run-parallel-1.2.0.tgz#66d1368da7bdf921eb9d95bd1a9229e7f21a43ee"
  integrity sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=
  dependencies:
    queue-microtask "^1.2.2"

run-series@^1.1.8:
  version "1.1.9"
  resolved "http://registry.npm.qima-inc.com/run-series/download/run-series-1.1.9.tgz#15ba9cb90e6a6c054e67c98e1dc063df0ecc113a"
  integrity sha1-FbqcuQ5qbAVOZ8mOHcBj3w7METo=

rxjs@^6.6.0:
  version "6.6.3"
  resolved "http://registry.npm.qima-inc.com/rxjs/download/rxjs-6.6.3.tgz"
  integrity sha1-jKhGNcTaqQDA05Z6buesYCce5VI=
  dependencies:
    tslib "^1.9.0"

rxjs@^7.5.1:
  version "7.8.1"
  resolved "http://registry.npm.qima-inc.com/rxjs/download/rxjs-7.8.1.tgz#6f6f3d99ea8044291efd92e7c7fcf562c4057543"
  integrity sha1-b289meqARCke/ZLnx/z1YsQFdUM=
  dependencies:
    tslib "^2.1.0"

safe-buffer@5.1.2, safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "http://registry.npm.qima-inc.com/safe-buffer/download/safe-buffer-5.1.2.tgz"
  integrity sha1-mR7GnSluAxN0fVm9/St0XDX4go0=

safe-buffer@^5.0.1, safe-buffer@^5.1.2, safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "http://registry.npm.qima-inc.com/safe-buffer/download/safe-buffer-5.2.1.tgz"
  integrity sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=

safe-regex@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/safe-regex/download/safe-regex-1.1.0.tgz"
  integrity sha1-QKNmnzsHfR6UPURinhV91IAjvy4=
  dependencies:
    ret "~0.1.10"

"safer-buffer@>= 2.1.2 < 3", safer-buffer@^2.0.2, safer-buffer@^2.1.0, safer-buffer@~2.1.0:
  version "2.1.2"
  resolved "http://registry.npm.qima-inc.com/safer-buffer/download/safer-buffer-2.1.2.tgz"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

sane@^4.0.3:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/sane/download/sane-4.1.0.tgz"
  integrity sha1-7Ygf2SJzOmxGG8GJ3CtsAG8//e0=
  dependencies:
    "@cnakazawa/watch" "^1.0.3"
    anymatch "^2.0.0"
    capture-exit "^2.0.0"
    exec-sh "^0.3.2"
    execa "^1.0.0"
    fb-watchman "^2.0.0"
    micromatch "^3.1.4"
    minimist "^1.1.1"
    walker "~1.0.5"

sax@>=0.6.0, sax@^1.2.4:
  version "1.2.4"
  resolved "http://registry.npm.qima-inc.com/sax/download/sax-1.2.4.tgz"
  integrity sha1-KBYjTiN4vdxOU1T6tcqold9xANk=

saxes@^5.0.0:
  version "5.0.1"
  resolved "http://registry.npm.qima-inc.com/saxes/download/saxes-5.0.1.tgz"
  integrity sha1-7rq5U/o7dgjb6U5drbFciI+maW0=
  dependencies:
    xmlchars "^2.2.0"

semver-compare@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/semver-compare/download/semver-compare-1.0.0.tgz#0dee216a1c941ab37e9efb1788f6afc5ff5537fc"
  integrity sha1-De4hahyUGrN+nvsXiPavxf9VN/w=

semver-diff@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/semver-diff/download/semver-diff-2.1.0.tgz"
  integrity sha1-S7uEN8jTfksM8aaP1ybsbWRdbTY=
  dependencies:
    semver "^5.0.3"

semver-regex@^3.1.2:
  version "3.1.4"
  resolved "http://registry.npm.qima-inc.com/semver-regex/download/semver-regex-3.1.4.tgz#13053c0d4aa11d070a2f2872b6b1e3ae1e1971b4"
  integrity sha512-6IiqeZNgq01qGf0TId0t3NvKzSvUsjcpdEO3AQNeIjR6A2+ckTnQlDpl4qu1bjRv0RzN3FP9hzFmws3lKqRWkA==

"semver@2 || 3 || 4 || 5", semver@^5.0.1, semver@^5.0.3, semver@^5.1.0, semver@^5.3.0, semver@^5.4.1, semver@^5.5.0, semver@^5.6.0, semver@^5.7.1:
  version "5.7.1"
  resolved "http://registry.npm.qima-inc.com/semver/download/semver-5.7.1.tgz"
  integrity sha1-qVT5Ma66UI0we78Gnv8MAclhFvc=

semver@6.3.0, semver@^6.0.0, semver@^6.1.2, semver@^6.2.0, semver@^6.3.0:
  version "6.3.0"
  resolved "http://registry.npm.qima-inc.com/semver/download/semver-6.3.0.tgz"
  integrity sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=

semver@7.x, semver@^7.3.2:
  version "7.3.4"
  resolved "http://registry.npm.qima-inc.com/semver/download/semver-7.3.4.tgz"
  integrity sha1-J6qn0uTKdkUvmNOt0JOnLJQ+3Jc=
  dependencies:
    lru-cache "^6.0.0"

semver@^7.2:
  version "7.3.7"
  resolved "http://registry.npm.qima-inc.com/semver/download/semver-7.3.7.tgz#12c5b649afdbf9049707796e22a4028814ce523f"
  integrity sha1-EsW2Sa/b+QSXB3luIqQCiBTOUj8=
  dependencies:
    lru-cache "^6.0.0"

semver@^7.3.8:
  version "7.6.3"
  resolved "http://registry.npm.qima-inc.com/semver/download/semver-7.6.3.tgz#980f7b5550bc175fb4dc09403085627f9eb33143"
  integrity sha1-mA97VVC8F1+03AlAMIVif56zMUM=

semver@~7.2.0:
  version "7.2.3"
  resolved "http://registry.npm.qima-inc.com/semver/download/semver-7.2.3.tgz#3641217233c6382173c76bf2c7ecd1e1c16b0d8a"
  integrity sha1-NkEhcjPGOCFzx2vyx+zR4cFrDYo=

set-blocking@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/set-blocking/download/set-blocking-2.0.0.tgz"
  integrity sha1-BF+XgtARrppoA93TgrJDkrPYkPc=

set-function-length@^1.2.1:
  version "1.2.2"
  resolved "http://registry.npm.qima-inc.com/set-function-length/download/set-function-length-1.2.2.tgz#aac72314198eaed975cf77b2c3b6b880695e5449"
  integrity sha1-qscjFBmOrtl1z3eyw7a4gGleVEk=
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"

set-value@^2.0.0, set-value@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/set-value/download/set-value-2.0.1.tgz"
  integrity sha1-oY1AUw5vB95CKMfe/kInr4ytAFs=
  dependencies:
    extend-shallow "^2.0.1"
    is-extendable "^0.1.1"
    is-plain-object "^2.0.3"
    split-string "^3.0.1"

setprototypeof@1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/setprototypeof/download/setprototypeof-1.1.0.tgz"
  integrity sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY=

setprototypeof@1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/setprototypeof/download/setprototypeof-1.1.1.tgz"
  integrity sha1-fpWsskqpL1iF4KvvW6ExMw1K5oM=

setprototypeof@1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/setprototypeof/download/setprototypeof-1.2.0.tgz"
  integrity sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ=

shebang-command@^1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/shebang-command/download/shebang-command-1.2.0.tgz"
  integrity sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=
  dependencies:
    shebang-regex "^1.0.0"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/shebang-command/download/shebang-command-2.0.0.tgz"
  integrity sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/shebang-regex/download/shebang-regex-1.0.0.tgz"
  integrity sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/shebang-regex/download/shebang-regex-3.0.0.tgz"
  integrity sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=

shelljs@^0.8.2:
  version "0.8.4"
  resolved "http://registry.npm.qima-inc.com/shelljs/download/shelljs-0.8.4.tgz"
  integrity sha1-3naE/ut2f4cWsyYHiooAh1iQ48I=
  dependencies:
    glob "^7.0.0"
    interpret "^1.0.0"
    rechoir "^0.6.2"

shellwords@^0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/shellwords/download/shellwords-0.1.1.tgz"
  integrity sha1-1rkYHBpI05cyTISHHvvPxz/AZUs=

shimmer@^1.1.0, shimmer@^1.2.0:
  version "1.2.1"
  resolved "http://registry.npm.qima-inc.com/shimmer/download/shimmer-1.2.1.tgz#610859f7de327b587efebf501fb43117f9aff337"
  integrity sha1-YQhZ994ye1h+/r9QH7QxF/mv8zc=

signal-exit@^3.0.0, signal-exit@^3.0.2:
  version "3.0.3"
  resolved "http://registry.npm.qima-inc.com/signal-exit/download/signal-exit-3.0.3.tgz"
  integrity sha1-oUEMLt2PB3sItOJTyOrPyvBXRhw=

signal-exit@^3.0.3:
  version "3.0.7"
  resolved "http://registry.npm.qima-inc.com/signal-exit/download/signal-exit-3.0.7.tgz#a9a1767f8af84155114eaabd73f99273c8f59ad9"
  integrity sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=

sisteransi@^1.0.5:
  version "1.0.5"
  resolved "http://registry.npm.qima-inc.com/sisteransi/download/sisteransi-1.0.5.tgz"
  integrity sha1-E01oEpd1ZDfMBcoBNw06elcQde0=

slash@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/slash/download/slash-3.0.0.tgz"
  integrity sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=

slice-ansi@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/slice-ansi/download/slice-ansi-2.1.0.tgz"
  integrity sha1-ys12k0YaY3pXiNkqfdT7oGjoFjY=
  dependencies:
    ansi-styles "^3.2.0"
    astral-regex "^1.0.0"
    is-fullwidth-code-point "^2.0.0"

slice-ansi@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/slice-ansi/download/slice-ansi-3.0.0.tgz#31ddc10930a1b7e0b67b08c96c2f49b77a789787"
  integrity sha1-Md3BCTCht+C2ewjJbC9Jt3p4l4c=
  dependencies:
    ansi-styles "^4.0.0"
    astral-regex "^2.0.0"
    is-fullwidth-code-point "^3.0.0"

slice-ansi@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/slice-ansi/download/slice-ansi-4.0.0.tgz#500e8dd0fd55b05815086255b3195adf2a45fe6b"
  integrity sha1-UA6N0P1VsFgVCGJVsxla3ypF/ms=
  dependencies:
    ansi-styles "^4.0.0"
    astral-regex "^2.0.0"
    is-fullwidth-code-point "^3.0.0"

smart-buffer@^4.1.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/smart-buffer/download/smart-buffer-4.1.0.tgz"
  integrity sha1-kWBcJdkWUvRmHqacz0XxszHKIbo=

smart-buffer@^4.2.0:
  version "4.2.0"
  resolved "http://registry.npm.qima-inc.com/smart-buffer/download/smart-buffer-4.2.0.tgz#6e1d71fa4f18c05f7d0ff216dd16a481d0e8d9ae"
  integrity sha1-bh1x+k8YwF99D/IW3RakgdDo2a4=

snapdragon-node@^2.0.1:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/snapdragon-node/download/snapdragon-node-2.1.1.tgz"
  integrity sha1-bBdfhv8UvbByRWPo88GwIaKGhTs=
  dependencies:
    define-property "^1.0.0"
    isobject "^3.0.0"
    snapdragon-util "^3.0.1"

snapdragon-util@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/snapdragon-util/download/snapdragon-util-3.0.1.tgz"
  integrity sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI=
  dependencies:
    kind-of "^3.2.0"

snapdragon@^0.8.1:
  version "0.8.2"
  resolved "http://registry.npm.qima-inc.com/snapdragon/download/snapdragon-0.8.2.tgz"
  integrity sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0=
  dependencies:
    base "^0.11.1"
    debug "^2.2.0"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    map-cache "^0.2.2"
    source-map "^0.5.6"
    source-map-resolve "^0.5.0"
    use "^3.1.0"

socks-proxy-agent@5, socks-proxy-agent@^5.0.0:
  version "5.0.1"
  resolved "http://registry.npm.qima-inc.com/socks-proxy-agent/download/socks-proxy-agent-5.0.1.tgz#032fb583048a29ebffec2e6a73fca0761f48177e"
  integrity sha1-Ay+1gwSKKev/7C5qc/ygdh9IF34=
  dependencies:
    agent-base "^6.0.2"
    debug "4"
    socks "^2.3.3"

socks-proxy-agent@^4.0.1:
  version "4.0.2"
  resolved "http://registry.npm.qima-inc.com/socks-proxy-agent/download/socks-proxy-agent-4.0.2.tgz"
  integrity sha1-PImR8xRbJ5nnDhG9X7yLGWMRY4Y=
  dependencies:
    agent-base "~4.2.1"
    socks "~2.3.2"

socks@^2.3.3:
  version "2.7.0"
  resolved "http://registry.npm.qima-inc.com/socks/download/socks-2.7.0.tgz#f9225acdb841e874dca25f870e9130990f3913d0"
  integrity sha1-+SJazbhB6HTcol+HDpEwmQ85E9A=
  dependencies:
    ip "^2.0.0"
    smart-buffer "^4.2.0"

socks@~2.3.2:
  version "2.3.3"
  resolved "http://registry.npm.qima-inc.com/socks/download/socks-2.3.3.tgz"
  integrity sha1-ARKfCl1TTSuJdxLtis6rfuZdeOM=
  dependencies:
    ip "1.1.5"
    smart-buffer "^4.1.0"

sonic-boom@^1.0.0, sonic-boom@^1.0.2:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/sonic-boom/download/sonic-boom-1.3.0.tgz"
  integrity sha1-XHfIRs5sOV3d8uuOjmX5zFdvLnY=
  dependencies:
    atomic-sleep "^1.0.0"
    flatstr "^1.0.12"

source-map-resolve@^0.5.0:
  version "0.5.3"
  resolved "http://registry.npm.qima-inc.com/source-map-resolve/download/source-map-resolve-0.5.3.tgz"
  integrity sha1-GQhmvs51U+H48mei7oLGBrVQmho=
  dependencies:
    atob "^2.1.2"
    decode-uri-component "^0.2.0"
    resolve-url "^0.2.1"
    source-map-url "^0.4.0"
    urix "^0.1.0"

source-map-support@0.5.16:
  version "0.5.16"
  resolved "http://registry.npm.qima-inc.com/source-map-support/download/source-map-support-0.5.16.tgz#0ae069e7fe3ba7538c64c98515e35339eac5a042"
  integrity sha1-CuBp5/47p1OMZMmFFeNTOerFoEI=
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map-support@^0.5.6:
  version "0.5.19"
  resolved "http://registry.npm.qima-inc.com/source-map-support/download/source-map-support-0.5.19.tgz"
  integrity sha1-qYti+G3K9PZzmWSMCFKRq56P7WE=
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map-url@^0.4.0:
  version "0.4.0"
  resolved "http://registry.npm.qima-inc.com/source-map-url/download/source-map-url-0.4.0.tgz"
  integrity sha1-PpNdfd1zYxuXZZlW1VEo6HtQhKM=

source-map@^0.5.0, source-map@^0.5.6:
  version "0.5.7"
  resolved "http://registry.npm.qima-inc.com/source-map/download/source-map-0.5.7.tgz"
  integrity sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=

source-map@^0.6.0, source-map@^0.6.1, source-map@~0.6.1:
  version "0.6.1"
  resolved "http://registry.npm.qima-inc.com/source-map/download/source-map-0.6.1.tgz"
  integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=

source-map@^0.7.3:
  version "0.7.3"
  resolved "http://registry.npm.qima-inc.com/source-map/download/source-map-0.7.3.tgz"
  integrity sha1-UwL4FpAxc1ImVECS5kmB91F1A4M=

spark-md5@^3.0.1:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/spark-md5/download/spark-md5-3.0.2.tgz#7952c4a30784347abcee73268e473b9c0167e3fc"
  integrity sha1-eVLEoweENHq87nMmjkc7nAFn4/w=

sparkline@^0.1.1:
  version "0.1.2"
  resolved "http://registry.npm.qima-inc.com/sparkline/download/sparkline-0.1.2.tgz"
  integrity sha1-w73kYlKxNU5xDEsgDVSBa9nwejI=
  dependencies:
    here "0.0.2"
    nopt "~2.1.2"

spdx-correct@^3.0.0:
  version "3.1.1"
  resolved "http://registry.npm.qima-inc.com/spdx-correct/download/spdx-correct-3.1.1.tgz"
  integrity sha1-3s6BrJweZxPl99G28X1Gj6U9iak=
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.3.0"
  resolved "http://registry.npm.qima-inc.com/spdx-exceptions/download/spdx-exceptions-2.3.0.tgz"
  integrity sha1-PyjOGnegA3JoPq3kpDMYNSeiFj0=

spdx-expression-parse@^3.0.0:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/spdx-expression-parse/download/spdx-expression-parse-3.0.1.tgz"
  integrity sha1-z3D1BILu/cmOPOCmgz5KU87rpnk=
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.7"
  resolved "http://registry.npm.qima-inc.com/spdx-license-ids/download/spdx-license-ids-3.0.7.tgz"
  integrity sha1-6cGKQQ5e1+EkQqVJ+9ivp2cDjWU=

split-on-first@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/split-on-first/download/split-on-first-1.1.0.tgz#f610afeee3b12bce1d0c30425e76398b78249a5f"
  integrity sha1-9hCv7uOxK84dDDBCXnY5i3gkml8=

split-string@^3.0.1, split-string@^3.0.2:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/split-string/download/split-string-3.1.0.tgz"
  integrity sha1-fLCd2jqGWFcFxks5pkZgOGguj+I=
  dependencies:
    extend-shallow "^3.0.0"

split2@^3.1.1:
  version "3.2.2"
  resolved "http://registry.npm.qima-inc.com/split2/download/split2-3.2.2.tgz"
  integrity sha1-vyzyo32DgxLCSciSBv16F90SNl8=
  dependencies:
    readable-stream "^3.0.0"

sprintf-js@1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/sprintf-js/download/sprintf-js-1.1.2.tgz#da1765262bf8c0f571749f2ad6c26300207ae673"
  integrity sha1-2hdlJiv4wPVxdJ8q1sJjACB65nM=

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/sprintf-js/download/sprintf-js-1.0.3.tgz"
  integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=

sshpk@^1.7.0:
  version "1.16.1"
  resolved "http://registry.npm.qima-inc.com/sshpk/download/sshpk-1.16.1.tgz"
  integrity sha1-+2YcC+8ps520B2nuOfpwCT1vaHc=
  dependencies:
    asn1 "~0.2.3"
    assert-plus "^1.0.0"
    bcrypt-pbkdf "^1.0.0"
    dashdash "^1.12.0"
    ecc-jsbn "~0.1.1"
    getpass "^0.1.1"
    jsbn "~0.1.0"
    safer-buffer "^2.0.2"
    tweetnacl "~0.14.0"

stack-utils@^2.0.2:
  version "2.0.3"
  resolved "http://registry.npm.qima-inc.com/stack-utils/download/stack-utils-2.0.3.tgz"
  integrity sha1-zV8DASb/EWt4zLPAJ/4wJxO2Enc=
  dependencies:
    escape-string-regexp "^2.0.0"

standard-as-callback@^1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/standard-as-callback/download/standard-as-callback-1.0.2.tgz"
  integrity sha1-0IEyidsA+L1eDynnR0TLY3BnB8g=

standard-as-callback@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/standard-as-callback/download/standard-as-callback-2.1.0.tgz#8953fc05359868a77b5b9739a665c5977bb7df45"
  integrity sha1-iVP8BTWYaKd7W5c5pmXFl3u330U=

static-extend@^0.1.1:
  version "0.1.2"
  resolved "http://registry.npm.qima-inc.com/static-extend/download/static-extend-0.1.2.tgz"
  integrity sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=
  dependencies:
    define-property "^0.2.5"
    object-copy "^0.1.0"

"statuses@>= 1.4.0 < 2", "statuses@>= 1.5.0 < 2", statuses@^1.2.0, statuses@^1.3.1:
  version "1.5.0"
  resolved "http://registry.npm.qima-inc.com/statuses/download/statuses-1.5.0.tgz"
  integrity sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=

stealthy-require@^1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/stealthy-require/download/stealthy-require-1.1.1.tgz"
  integrity sha1-NbCYdbT/SfJqd35QmzCQoyJr8ks=

strict-uri-encode@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/strict-uri-encode/download/strict-uri-encode-1.1.0.tgz"
  integrity sha1-J5siXfHVgrH1TmWt3UNS4Y+qBxM=

strict-uri-encode@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/strict-uri-encode/download/strict-uri-encode-2.0.0.tgz"
  integrity sha1-ucczDHBChi9rFC3CdLvMWGbONUY=

string-argv@0.3.1:
  version "0.3.1"
  resolved "http://registry.npm.qima-inc.com/string-argv/download/string-argv-0.3.1.tgz#95e2fbec0427ae19184935f816d74aaa4c5c19da"
  integrity sha1-leL77AQnrhkYSTX4FtdKqkxcGdo=

string-length@^4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/string-length/download/string-length-4.0.1.tgz"
  integrity sha1-Spc78x73fE7bzq3WryYRmWmF+KE=
  dependencies:
    char-regex "^1.0.2"
    strip-ansi "^6.0.0"

string-similarity@^4.0.1:
  version "4.0.3"
  resolved "http://registry.npm.qima-inc.com/string-similarity/download/string-similarity-4.0.3.tgz"
  integrity sha1-71LW/FnIoPyTtjB/u8CMxuGM3iE=

string-width@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/string-width/download/string-width-1.0.2.tgz"
  integrity sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=
  dependencies:
    code-point-at "^1.0.0"
    is-fullwidth-code-point "^1.0.0"
    strip-ansi "^3.0.0"

string-width@^2.0.0, string-width@^2.1.1:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/string-width/download/string-width-2.1.1.tgz"
  integrity sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=
  dependencies:
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^4.0.0"

string-width@^3.0.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/string-width/download/string-width-3.1.0.tgz"
  integrity sha1-InZ74htirxCBV0MG9prFG2IgOWE=
  dependencies:
    emoji-regex "^7.0.1"
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^5.1.0"

string-width@^4.1.0, string-width@^4.2.0:
  version "4.2.0"
  resolved "http://registry.npm.qima-inc.com/string-width/download/string-width-4.2.0.tgz"
  integrity sha1-lSGCxGzHssMT0VluYjmSvRY7crU=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.0"

string.prototype.codepointat@^0.2.0:
  version "0.2.1"
  resolved "http://registry.npm.qima-inc.com/string.prototype.codepointat/download/string.prototype.codepointat-0.2.1.tgz"
  integrity sha1-AErUTIr8cnUnsQjNRitNlxzUabw=

string.prototype.trimend@^1.0.1:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/string.prototype.trimend/download/string.prototype.trimend-1.0.3.tgz"
  integrity sha1-oivVPMpcfPRNfJ1ccyEYhz1s0Ys=
  dependencies:
    call-bind "^1.0.0"
    define-properties "^1.1.3"

string.prototype.trimstart@^1.0.1:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/string.prototype.trimstart/download/string.prototype.trimstart-1.0.3.tgz"
  integrity sha1-m0y1kOEjuzZWRAHVmCQpjeUP1ao=
  dependencies:
    call-bind "^1.0.0"
    define-properties "^1.1.3"

string_decoder@^1.1.1:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/string_decoder/download/string_decoder-1.3.0.tgz"
  integrity sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=
  dependencies:
    safe-buffer "~5.2.0"

string_decoder@~0.10.x:
  version "0.10.31"
  resolved "http://registry.npm.qima-inc.com/string_decoder/download/string_decoder-0.10.31.tgz"
  integrity sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ=

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/string_decoder/download/string_decoder-1.1.1.tgz"
  integrity sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=
  dependencies:
    safe-buffer "~5.1.0"

stringify-object@^3.3.0:
  version "3.3.0"
  resolved "http://registry.npm.qima-inc.com/stringify-object/download/stringify-object-3.3.0.tgz#703065aefca19300d3ce88af4f5b3956d7556629"
  integrity sha1-cDBlrvyhkwDTzoivT1s5VtdVZik=
  dependencies:
    get-own-enumerable-property-symbols "^3.0.0"
    is-obj "^1.0.1"
    is-regexp "^1.0.0"

strip-ansi@^3.0.0, strip-ansi@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/strip-ansi/download/strip-ansi-3.0.1.tgz"
  integrity sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=
  dependencies:
    ansi-regex "^2.0.0"

strip-ansi@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/strip-ansi/download/strip-ansi-4.0.0.tgz"
  integrity sha1-qEeQIusaw2iocTibY1JixQXuNo8=
  dependencies:
    ansi-regex "^3.0.0"

strip-ansi@^5.1.0, strip-ansi@^5.2.0:
  version "5.2.0"
  resolved "http://registry.npm.qima-inc.com/strip-ansi/download/strip-ansi-5.2.0.tgz"
  integrity sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=
  dependencies:
    ansi-regex "^4.1.0"

strip-ansi@^6.0.0:
  version "6.0.0"
  resolved "http://registry.npm.qima-inc.com/strip-ansi/download/strip-ansi-6.0.0.tgz"
  integrity sha1-CxVx3XZpzNTz4G4U7x7tJiJa5TI=
  dependencies:
    ansi-regex "^5.0.0"

strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "http://registry.npm.qima-inc.com/strip-ansi/download/strip-ansi-6.0.1.tgz#9e26c63d30f53443e9489495b2105d37b67a85d9"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-bom@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/strip-bom/download/strip-bom-3.0.0.tgz"
  integrity sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=

strip-bom@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/strip-bom/download/strip-bom-4.0.0.tgz"
  integrity sha1-nDUFwdtFvO3KPZz3oW9cWqOQGHg=

strip-eof@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/strip-eof/download/strip-eof-1.0.0.tgz"
  integrity sha1-u0P/VZim6wXYm1n80SnJgzE2Br8=

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/strip-final-newline/download/strip-final-newline-2.0.0.tgz"
  integrity sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=

strip-json-comments@^3.0.1:
  version "3.1.1"
  resolved "http://registry.npm.qima-inc.com/strip-json-comments/download/strip-json-comments-3.1.1.tgz"
  integrity sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=

strip-json-comments@~2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/strip-json-comments/download/strip-json-comments-2.0.1.tgz"
  integrity sha1-PFMZQukIwml8DsNEhYwobHygpgo=

superagent@^3.8.3:
  version "3.8.3"
  resolved "http://registry.npm.qima-inc.com/superagent/download/superagent-3.8.3.tgz"
  integrity sha1-Rg6g29t9WxG8T3jeulZfhqF44Sg=
  dependencies:
    component-emitter "^1.2.0"
    cookiejar "^2.1.0"
    debug "^3.1.0"
    extend "^3.0.0"
    form-data "^2.3.1"
    formidable "^1.2.0"
    methods "^1.1.1"
    mime "^1.4.1"
    qs "^6.5.1"
    readable-stream "^2.3.5"

supertest@^4.0.2:
  version "4.0.2"
  resolved "http://registry.npm.qima-inc.com/supertest/download/supertest-4.0.2.tgz"
  integrity sha1-wiNNvdbcebbxW5nI1ld7kOTOPzY=
  dependencies:
    methods "^1.1.2"
    superagent "^3.8.3"

supports-color@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/supports-color/download/supports-color-2.0.0.tgz"
  integrity sha1-U10EXOa2Nj+kARcIRimZXp3zJMc=

supports-color@^5.3.0, supports-color@^5.5.0:
  version "5.5.0"
  resolved "http://registry.npm.qima-inc.com/supports-color/download/supports-color-5.5.0.tgz"
  integrity sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.0.0, supports-color@^7.1.0:
  version "7.2.0"
  resolved "http://registry.npm.qima-inc.com/supports-color/download/supports-color-7.2.0.tgz"
  integrity sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=
  dependencies:
    has-flag "^4.0.0"

supports-hyperlinks@^2.0.0, supports-hyperlinks@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/supports-hyperlinks/download/supports-hyperlinks-2.1.0.tgz"
  integrity sha1-9mPfJSr183xdSbvX7u+p4Lnlnkc=
  dependencies:
    has-flag "^4.0.0"
    supports-color "^7.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/supports-preserve-symlinks-flag/download/supports-preserve-symlinks-flag-1.0.0.tgz#6eda4bd344a3c94aea376d4cc31bc77311039e09"
  integrity sha1-btpL00SjyUrqN21MwxvHcxEDngk=

symbol-tree@^3.2.4:
  version "3.2.4"
  resolved "http://registry.npm.qima-inc.com/symbol-tree/download/symbol-tree-3.2.4.tgz"
  integrity sha1-QwY30ki6d+B4iDlR+5qg7tfGP6I=

sync-rpc@^1.3.6:
  version "1.3.6"
  resolved "http://registry.npm.qima-inc.com/sync-rpc/download/sync-rpc-1.3.6.tgz"
  integrity sha1-suiyVQoSzLxx34ZEgQUp3raGZac=
  dependencies:
    get-port "^3.1.0"

systeminformation@^4.23.3:
  version "4.34.23"
  resolved "http://registry.npm.qima-inc.com/systeminformation/download/systeminformation-4.34.23.tgz#54c54ced5adc49c27cda953b73c0819ed56edd45"
  integrity sha1-VMVM7VrcScJ82pU7c8CBntVu3UU=

table@^5.2.3:
  version "5.4.6"
  resolved "http://registry.npm.qima-inc.com/table/download/table-5.4.6.tgz"
  integrity sha1-EpLRlQDOP4YFOwXw6Ofko7shB54=
  dependencies:
    ajv "^6.10.2"
    lodash "^4.17.14"
    slice-ansi "^2.1.0"
    string-width "^3.0.0"

term-canvas@0.0.5:
  version "0.0.5"
  resolved "http://registry.npm.qima-inc.com/term-canvas/download/term-canvas-0.0.5.tgz"
  integrity sha1-WXr6wvpjaabxeGC86cX2bW6gypY=

term-size@^1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/term-size/download/term-size-1.2.0.tgz"
  integrity sha1-RYuDiH8oj8Vtb/+/rSYuJmOO+mk=
  dependencies:
    execa "^0.7.0"

terminal-link@^2.0.0:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/terminal-link/download/terminal-link-2.1.1.tgz"
  integrity sha1-FKZKJ6s8Dfkz6lRvulXy0HjtyZQ=
  dependencies:
    ansi-escapes "^4.2.1"
    supports-hyperlinks "^2.0.0"

test-exclude@^6.0.0:
  version "6.0.0"
  resolved "http://registry.npm.qima-inc.com/test-exclude/download/test-exclude-6.0.0.tgz"
  integrity sha1-BKhphmHYBepvopO2y55jrARO8V4=
  dependencies:
    "@istanbuljs/schema" "^0.1.2"
    glob "^7.1.4"
    minimatch "^3.0.4"

text-table@^0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.qima-inc.com/text-table/download/text-table-0.2.0.tgz"
  integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=

thenify-all@^1.0.0:
  version "1.6.0"
  resolved "http://registry.npm.qima-inc.com/thenify-all/download/thenify-all-1.6.0.tgz"
  integrity sha1-GhkY1ALY/D+Y+/I02wvMjMEOlyY=
  dependencies:
    thenify ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4", thenify@^3.3.0:
  version "3.3.1"
  resolved "http://registry.npm.qima-inc.com/thenify/download/thenify-3.3.1.tgz"
  integrity sha1-iTLmhqQGYDigFt2eLKRq3Zg4qV8=
  dependencies:
    any-promise "^1.0.0"

throat@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/throat/download/throat-5.0.0.tgz"
  integrity sha1-xRmSNYA6rRh1SmZ9ZZtecs4Wdks=

through@^2.3.6, through@^2.3.8, through@~2.3:
  version "2.3.8"
  resolved "http://registry.npm.qima-inc.com/through/download/through-2.3.8.tgz"
  integrity sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=

thunkify@^2.1.2:
  version "2.1.2"
  resolved "http://registry.npm.qima-inc.com/thunkify/download/thunkify-2.1.2.tgz"
  integrity sha1-+qDp0jDFGsyVyhOjYawFyn4EVT0=

timed-out@^4.0.0:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/timed-out/download/timed-out-4.0.1.tgz"
  integrity sha1-8y6srFoXW+ol1/q1Zas+2HQe9W8=

tmp@^0.0.33:
  version "0.0.33"
  resolved "http://registry.npm.qima-inc.com/tmp/download/tmp-0.0.33.tgz"
  integrity sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=
  dependencies:
    os-tmpdir "~1.0.2"

tmpl@1.0.x:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/tmpl/download/tmpl-1.0.4.tgz"
  integrity sha1-I2QN17QtAEM5ERQIIOXPRA5SHdE=

to-fast-properties@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/to-fast-properties/download/to-fast-properties-2.0.0.tgz"
  integrity sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=

to-object-path@^0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.qima-inc.com/to-object-path/download/to-object-path-0.3.0.tgz"
  integrity sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=
  dependencies:
    kind-of "^3.0.2"

to-regex-range@^2.1.0:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/to-regex-range/download/to-regex-range-2.1.1.tgz"
  integrity sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=
  dependencies:
    is-number "^3.0.0"
    repeat-string "^1.6.1"

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "http://registry.npm.qima-inc.com/to-regex-range/download/to-regex-range-5.0.1.tgz"
  integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
  dependencies:
    is-number "^7.0.0"

to-regex@^3.0.1, to-regex@^3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/to-regex/download/to-regex-3.0.2.tgz"
  integrity sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4=
  dependencies:
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    regex-not "^1.0.2"
    safe-regex "^1.1.0"

toidentifier@1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/toidentifier/download/toidentifier-1.0.0.tgz"
  integrity sha1-fhvjRw8ed5SLxD2Uo8j013UrpVM=

token-stream@1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/token-stream/download/token-stream-1.0.0.tgz#cc200eab2613f4166d27ff9afc7ca56d49df6eb4"
  integrity sha1-zCAOqyYT9BZtJ/+a/HylbUnfbrQ=

touch@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/touch/download/touch-3.1.0.tgz"
  integrity sha1-/jZfX3XsntTlaCXgu3bSSrdK+Ds=
  dependencies:
    nopt "~1.0.10"

tough-cookie@^2.3.3, tough-cookie@~2.5.0:
  version "2.5.0"
  resolved "http://registry.npm.qima-inc.com/tough-cookie/download/tough-cookie-2.5.0.tgz"
  integrity sha1-zZ+yoKodWhK0c72fuW+j3P9lreI=
  dependencies:
    psl "^1.1.28"
    punycode "^2.1.1"

tough-cookie@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/tough-cookie/download/tough-cookie-3.0.1.tgz"
  integrity sha1-nfT1fnOcJpMKAYGEiH9K233Kc7I=
  dependencies:
    ip-regex "^2.1.0"
    psl "^1.1.28"
    punycode "^2.1.1"

tr46@^2.0.2:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/tr46/download/tr46-2.0.2.tgz"
  integrity sha1-Ayc1ht7xWVrgj+2zjXczzukdJHk=
  dependencies:
    punycode "^2.1.1"

tr46@~0.0.3:
  version "0.0.3"
  resolved "http://registry.npm.qima-inc.com/tr46/download/tr46-0.0.3.tgz#8184fd347dac9cdc185992f3a6622e14b9d9ab6a"
  integrity sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o=

ts-jest@^26.1.4:
  version "26.4.4"
  resolved "http://registry.npm.qima-inc.com/ts-jest/download/ts-jest-26.4.4.tgz"
  integrity sha1-YfE/shq0AIU8UyJw5SzA7X5QLEk=
  dependencies:
    "@types/jest" "26.x"
    bs-logger "0.x"
    buffer-from "1.x"
    fast-json-stable-stringify "2.x"
    jest-util "^26.1.0"
    json5 "2.x"
    lodash.memoize "4.x"
    make-error "1.x"
    mkdirp "1.x"
    semver "7.x"
    yargs-parser "20.x"

ts-node@^7.0.0:
  version "7.0.1"
  resolved "http://registry.npm.qima-inc.com/ts-node/download/ts-node-7.0.1.tgz"
  integrity sha1-lWLcLR5tJI0kvFX3c+P2FDN9m68=
  dependencies:
    arrify "^1.0.0"
    buffer-from "^1.1.0"
    diff "^3.1.0"
    make-error "^1.1.1"
    minimist "^1.2.0"
    mkdirp "^0.5.1"
    source-map-support "^0.5.6"
    yn "^2.0.0"

tsconfig-paths@^3.4.2, tsconfig-paths@^3.9.0:
  version "3.9.0"
  resolved "http://registry.npm.qima-inc.com/tsconfig-paths/download/tsconfig-paths-3.9.0.tgz"
  integrity sha1-CYVHpsREiAfo/Ljq4IEGTumjyQs=
  dependencies:
    "@types/json5" "^0.0.29"
    json5 "^1.0.1"
    minimist "^1.2.0"
    strip-bom "^3.0.0"

tslib@1.10.0:
  version "1.10.0"
  resolved "http://registry.npm.qima-inc.com/tslib/download/tslib-1.10.0.tgz"
  integrity sha1-w8GflZc/sKYpc/sJ2Q2WHuQ+XIo=

tslib@1.9.3:
  version "1.9.3"
  resolved "http://registry.npm.qima-inc.com/tslib/download/tslib-1.9.3.tgz"
  integrity sha1-1+TdeSRdhUKMTX5IIqeZF5VMooY=

tslib@1.x, tslib@^1.10.0, tslib@^1.8.1, tslib@^1.9.0, tslib@^1.9.3:
  version "1.14.1"
  resolved "http://registry.npm.qima-inc.com/tslib/download/tslib-1.14.1.tgz"
  integrity sha1-zy04vcNKE0vK8QkcQfZhni9nLQA=

tslib@^2.0.1:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/tslib/download/tslib-2.1.0.tgz"
  integrity sha1-2mCGDxwuyqVwOrfTm8Bba/mIuXo=

tslib@^2.0.3, tslib@^2.2.0:
  version "2.3.1"
  resolved "http://registry.npm.qima-inc.com/tslib/download/tslib-2.3.1.tgz#e8a335add5ceae51aa261d32a490158ef042ef01"
  integrity sha1-6KM1rdXOrlGqJh0ypJAVjvBC7wE=

tslib@^2.1.0:
  version "2.8.0"
  resolved "http://registry.npm.qima-inc.com/tslib/download/tslib-2.8.0.tgz#d124c86c3c05a40a91e6fdea4021bd31d377971b"
  integrity sha1-0STIbDwFpAqR5v3qQCG9MdN3lxs=

tslib@^2.5.3:
  version "2.8.1"
  resolved "http://registry.npm.qima-inc.com/tslib/download/tslib-2.8.1.tgz#612efe4ed235d567e8aba5f2a5fab70280ade83f"
  integrity sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8=

tsscmp@1.0.6:
  version "1.0.6"
  resolved "http://registry.npm.qima-inc.com/tsscmp/download/tsscmp-1.0.6.tgz"
  integrity sha1-hbmVg6w1iexL/vgltQAKqRHWBes=

tsutils@^3.17.1:
  version "3.19.1"
  resolved "http://registry.npm.qima-inc.com/tsutils/download/tsutils-3.19.1.tgz"
  integrity sha1-2FZuDFHILzL5wlpNNnzWJAmlR6k=
  dependencies:
    tslib "^1.8.1"

tunnel-agent@0.6.0, tunnel-agent@^0.6.0:
  version "0.6.0"
  resolved "http://registry.npm.qima-inc.com/tunnel-agent/download/tunnel-agent-0.6.0.tgz"
  integrity sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=
  dependencies:
    safe-buffer "^5.0.1"

tv4@^1.3.0:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/tv4/download/tv4-1.3.0.tgz#d020c846fadd50c855abb25ebaecc68fc10f7963"
  integrity sha1-0CDIRvrdUMhVq7JeuuzGj8EPeWM=

tweetnacl@^0.14.3, tweetnacl@~0.14.0:
  version "0.14.5"
  resolved "http://registry.npm.qima-inc.com/tweetnacl/download/tweetnacl-0.14.5.tgz"
  integrity sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=

type-check@~0.3.2:
  version "0.3.2"
  resolved "http://registry.npm.qima-inc.com/type-check/download/type-check-0.3.2.tgz"
  integrity sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=
  dependencies:
    prelude-ls "~1.1.2"

type-detect@4.0.8:
  version "4.0.8"
  resolved "http://registry.npm.qima-inc.com/type-detect/download/type-detect-4.0.8.tgz"
  integrity sha1-dkb7XxiHHPu3dJ5pvTmmOI63RQw=

type-fest@^0.11.0:
  version "0.11.0"
  resolved "http://registry.npm.qima-inc.com/type-fest/download/type-fest-0.11.0.tgz"
  integrity sha1-l6vwhyMQ/tiKXEZrJWgVdhReM/E=

type-fest@^0.21.3:
  version "0.21.3"
  resolved "http://registry.npm.qima-inc.com/type-fest/download/type-fest-0.21.3.tgz#d260a24b0198436e133fa26a524a6d65fa3b2e37"
  integrity sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc=

type-fest@^0.6.0:
  version "0.6.0"
  resolved "http://registry.npm.qima-inc.com/type-fest/download/type-fest-0.6.0.tgz"
  integrity sha1-jSojcNPfiG61yQraHFv2GIrPg4s=

type-fest@^0.8.1:
  version "0.8.1"
  resolved "http://registry.npm.qima-inc.com/type-fest/download/type-fest-0.8.1.tgz"
  integrity sha1-CeJJ696FHTseSNJ8EFREZn8XuD0=

type-is@^1.5.5, type-is@^1.6.14, type-is@^1.6.16:
  version "1.6.18"
  resolved "http://registry.npm.qima-inc.com/type-is/download/type-is-1.6.18.tgz"
  integrity sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

typedarray-to-buffer@^3.1.5:
  version "3.1.5"
  resolved "http://registry.npm.qima-inc.com/typedarray-to-buffer/download/typedarray-to-buffer-3.1.5.tgz"
  integrity sha1-qX7nqf9CaRufeD/xvFES/j/KkIA=
  dependencies:
    is-typedarray "^1.0.0"

typescript@3.7.5:
  version "3.7.5"
  resolved "http://registry.npm.qima-inc.com/typescript/download/typescript-3.7.5.tgz"
  integrity sha1-BpLiH2X9QQi5MwI4qsEd0uF3oa4=

typescript@^3.8.2:
  version "3.9.9"
  resolved "http://registry.npm.qima-inc.com/typescript/download/typescript-3.9.9.tgz#e69905c54bc0681d0518bd4d587cc6f2d0b1a674"
  integrity sha1-5pkFxUvAaB0FGL1NWHzG8tCxpnQ=

typescript@^3.8.3:
  version "3.9.7"
  resolved "http://registry.npm.qima-inc.com/typescript/download/typescript-3.9.7.tgz"
  integrity sha1-mNYApevcOPQMsndSLxLcgA6eJfo=

ua-parser-js@^0.7.39:
  version "0.7.39"
  resolved "http://registry.npm.qima-inc.com/ua-parser-js/-/ua-parser-js-0.7.39.tgz#c71efb46ebeabc461c4612d22d54f88880fabe7e"
  integrity sha512-IZ6acm6RhQHNibSt7+c09hhvsKy9WUr4DVbeq9U8o71qxyYtJpQeDxQnMrVqnIFMLcQjHO0I9wgfO2vIahht4w==

undefsafe@^2.0.2:
  version "2.0.3"
  resolved "http://registry.npm.qima-inc.com/undefsafe/download/undefsafe-2.0.3.tgz"
  integrity sha1-axZucJStRjE7IgLafsws18xueq4=
  dependencies:
    debug "^2.2.0"

unescape@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/unescape/download/unescape-1.0.1.tgz"
  integrity sha1-lW5DD2HK2KTVfYLFGPXmzF0N2pY=
  dependencies:
    extend-shallow "^2.0.1"

union-value@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/union-value/download/union-value-1.0.1.tgz"
  integrity sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc=
  dependencies:
    arr-union "^3.1.0"
    get-value "^2.0.6"
    is-extendable "^0.1.1"
    set-value "^2.0.1"

unique-string@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/unique-string/download/unique-string-1.0.0.tgz"
  integrity sha1-nhBXzKhRq7kzmPizOuGHuZyuwRo=
  dependencies:
    crypto-random-string "^1.0.0"

unique-string@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/unique-string/download/unique-string-2.0.0.tgz#****************************************"
  integrity sha1-OcZFH4GvsnSd4rIz4/fF6IQ72J0=
  dependencies:
    crypto-random-string "^2.0.0"

universalify@^0.1.0:
  version "0.1.2"
  resolved "http://registry.npm.qima-inc.com/universalify/download/universalify-0.1.2.tgz"
  integrity sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY=

universalify@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/universalify/download/universalify-1.0.0.tgz"
  integrity sha1-thodoXPoQ1sv48Z9Kbmt+FlL0W0=

universalify@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/universalify/download/universalify-2.0.0.tgz"
  integrity sha1-daSYTv7cSwiXXFrrc/Uw0C3yVxc=

unpipe@1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/unpipe/download/unpipe-1.0.0.tgz"
  integrity sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=

unset-value@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/unset-value/download/unset-value-1.0.0.tgz"
  integrity sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=
  dependencies:
    has-value "^0.3.1"
    isobject "^3.0.0"

unzip-response@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/unzip-response/download/unzip-response-2.0.1.tgz"
  integrity sha1-0vD3N9FrBhXnKmk17QQhRXLVb5c=

upath@^1.1.1:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/upath/download/upath-1.2.0.tgz"
  integrity sha1-j2bbzVWog6za5ECK+LA1pQRMGJQ=

update-notifier@^2.5.0:
  version "2.5.0"
  resolved "http://registry.npm.qima-inc.com/update-notifier/download/update-notifier-2.5.0.tgz"
  integrity sha1-0HRFk+E/Fh5AassdlAi3LK0Ir/Y=
  dependencies:
    boxen "^1.2.1"
    chalk "^2.0.1"
    configstore "^3.0.0"
    import-lazy "^2.1.0"
    is-ci "^1.0.10"
    is-installed-globally "^0.1.0"
    is-npm "^1.0.0"
    latest-version "^3.0.0"
    semver-diff "^2.0.0"
    xdg-basedir "^3.0.0"

uri-js@^4.2.2:
  version "4.4.1"
  resolved "http://registry.npm.qima-inc.com/uri-js/download/uri-js-4.4.1.tgz"
  integrity sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=
  dependencies:
    punycode "^2.1.0"

urijs@^1.19.0:
  version "1.19.5"
  resolved "http://registry.npm.qima-inc.com/urijs/download/urijs-1.19.5.tgz"
  integrity sha1-EZaDq0svsL1jfl6m3ZEXvKxo0+Q=

urix@^0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.qima-inc.com/urix/download/urix-0.1.0.tgz"
  integrity sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=

url-parse-lax@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/url-parse-lax/download/url-parse-lax-1.0.0.tgz"
  integrity sha1-evjzA2Rem9eaJy56FKxovAYJ2nM=
  dependencies:
    prepend-http "^1.0.1"

url-parse@^1.4.4:
  version "1.4.7"
  resolved "http://registry.npm.qima-inc.com/url-parse/download/url-parse-1.4.7.tgz"
  integrity sha1-qKg1NejACjFuQDpdtKwbm4U64ng=
  dependencies:
    querystringify "^2.1.1"
    requires-port "^1.0.0"

url-parse@^1.4.7:
  version "1.5.4"
  resolved "http://registry.npm.qima-inc.com/url-parse/download/url-parse-1.5.4.tgz#e4f645a7e2a0852cc8a66b14b292a3e9a11a97fd"
  integrity sha1-5PZFp+KghSzIpmsUspKj6aEal/0=
  dependencies:
    querystringify "^2.1.1"
    requires-port "^1.0.0"

urllib@2.22.0:
  version "2.22.0"
  resolved "http://registry.npm.qima-inc.com/urllib/download/urllib-2.22.0.tgz"
  integrity sha1-KWXcSuEnpvtpW32yfTGE8X2Cy0I=
  dependencies:
    any-promise "^1.3.0"
    content-type "^1.0.2"
    debug "^2.6.0"
    default-user-agent "^1.0.0"
    digest-header "^0.0.1"
    ee-first "~1.1.1"
    humanize-ms "^1.2.0"
    iconv-lite "^0.4.15"
    qs "^6.4.0"
    statuses "^1.3.1"

urllib@^2.36.1:
  version "2.41.0"
  resolved "http://registry.npm.qima-inc.com/urllib/download/urllib-2.41.0.tgz#be15705e62af4eadcafcfeadda1014b526ae2d0d"
  integrity sha1-vhVwXmKvTq3K/P6t2hAUtSauLQ0=
  dependencies:
    any-promise "^1.3.0"
    content-type "^1.0.2"
    debug "^2.6.9"
    default-user-agent "^1.0.0"
    digest-header "^1.0.0"
    ee-first "~1.1.1"
    formstream "^1.1.0"
    humanize-ms "^1.2.0"
    iconv-lite "^0.4.15"
    ip "^1.1.5"
    pump "^3.0.0"
    qs "^6.4.0"
    statuses "^1.3.1"
    utility "^1.16.1"

use@^3.1.0:
  version "3.1.1"
  resolved "http://registry.npm.qima-inc.com/use/download/use-3.1.1.tgz"
  integrity sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8=

util-deprecate@^1.0.1, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/util-deprecate/download/util-deprecate-1.0.2.tgz"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

util@^0.12.2:
  version "0.12.3"
  resolved "http://registry.npm.qima-inc.com/util/download/util-0.12.3.tgz"
  integrity sha1-lxuwKS0swMiS2rfGpdN8K+xweIg=
  dependencies:
    inherits "^2.0.3"
    is-arguments "^1.0.4"
    is-generator-function "^1.0.7"
    is-typed-array "^1.1.3"
    safe-buffer "^5.1.2"
    which-typed-array "^1.1.2"

utility-types@^3.7.0:
  version "3.10.0"
  resolved "http://registry.npm.qima-inc.com/utility-types/download/utility-types-3.10.0.tgz"
  integrity sha1-6kFI+adBAV8F7XT9YV4dIOa+2Cs=

utility@0.1.11:
  version "0.1.11"
  resolved "http://registry.npm.qima-inc.com/utility/download/utility-0.1.11.tgz"
  integrity sha1-/eYM+bTkdRlHoM9dEEzik2ciZxU=
  dependencies:
    address ">=0.0.1"

utility@^1.12.0, utility@^1.13.1, utility@^1.16.1:
  version "1.17.0"
  resolved "http://registry.npm.qima-inc.com/utility/download/utility-1.17.0.tgz"
  integrity sha1-YIGfcSpuDOd09S+x1pGZKl9Z02I=
  dependencies:
    copy-to "^2.0.1"
    escape-html "^1.0.3"
    mkdirp "^0.5.1"
    mz "^2.7.0"
    unescape "^1.0.1"

uuid@^3.2.1, uuid@^3.3.2:
  version "3.4.0"
  resolved "http://registry.npm.qima-inc.com/uuid/download/uuid-3.4.0.tgz"
  integrity sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4=

uuid@^8.3.0, uuid@^8.3.2:
  version "8.3.2"
  resolved "http://registry.npm.qima-inc.com/uuid/download/uuid-8.3.2.tgz"
  integrity sha1-gNW1ztJxu5r2xEXyGhoExgbO++I=

v8-compile-cache@^2.0.3:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/v8-compile-cache/download/v8-compile-cache-2.2.0.tgz"
  integrity sha1-lHHvo++RKNL3xqfKOcTda1BVsTI=

v8-to-istanbul@^7.0.0:
  version "7.1.0"
  resolved "http://registry.npm.qima-inc.com/v8-to-istanbul/download/v8-to-istanbul-7.1.0.tgz"
  integrity sha1-W5XO9FwPgyF+x5+Px+4ci0hq7gc=
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.1"
    convert-source-map "^1.6.0"
    source-map "^0.7.3"

validate-npm-package-license@^3.0.1:
  version "3.0.4"
  resolved "http://registry.npm.qima-inc.com/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz"
  integrity sha1-/JH2uce6FchX9MssXe/uw51PQQo=
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

vary@^1.0.0:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/vary/download/vary-1.1.2.tgz"
  integrity sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=

verror@1.10.0:
  version "1.10.0"
  resolved "http://registry.npm.qima-inc.com/verror/download/verror-1.10.0.tgz"
  integrity sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=
  dependencies:
    assert-plus "^1.0.0"
    core-util-is "1.0.2"
    extsprintf "^1.2.0"

vizion@0.2.13:
  version "0.2.13"
  resolved "http://registry.npm.qima-inc.com/vizion/download/vizion-0.2.13.tgz#1314cdee2b34116f9f5b1248536f95dbfcd6ef5f"
  integrity sha1-ExTN7is0EW+fWxJIU2+V2/zW718=
  dependencies:
    async "1.5"

void-elements@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/void-elements/download/void-elements-3.1.0.tgz#614f7fbf8d801f0bb5f0661f5b2f5785750e4f09"
  integrity sha1-YU9/v42AHwu18GYfWy9XhXUOTwk=

vscode-languageserver-textdocument@^1.0.8:
  version "1.0.12"
  resolved "http://registry.npm.qima-inc.com/vscode-languageserver-textdocument/download/vscode-languageserver-textdocument-1.0.12.tgz#457ee04271ab38998a093c68c2342f53f6e4a631"
  integrity sha1-RX7gQnGrOJmKCTxowjQvU/bkpjE=

vscode-uri@^3.0.7:
  version "3.0.8"
  resolved "http://registry.npm.qima-inc.com/vscode-uri/download/vscode-uri-3.0.8.tgz#1770938d3e72588659a172d0fd4642780083ff9f"
  integrity sha1-F3CTjT5yWIZZoXLQ/UZCeACD/58=

vue-eslint-parser@^7.0.0:
  version "7.3.0"
  resolved "http://registry.npm.qima-inc.com/vue-eslint-parser/download/vue-eslint-parser-7.3.0.tgz"
  integrity sha1-iUCFg52Z2BKW+ggdGWQ3M/I9dVk=
  dependencies:
    debug "^4.1.1"
    eslint-scope "^5.0.0"
    eslint-visitor-keys "^1.1.0"
    espree "^6.2.1"
    esquery "^1.0.1"
    lodash "^4.17.15"

w3c-hr-time@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/w3c-hr-time/download/w3c-hr-time-1.0.2.tgz"
  integrity sha1-ConN9cwVgi35w2BUNnaWPgzDCM0=
  dependencies:
    browser-process-hrtime "^1.0.0"

w3c-xmlserializer@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/w3c-xmlserializer/download/w3c-xmlserializer-2.0.0.tgz"
  integrity sha1-PnEEoFt1FGzGD1ZDgLf2g6zxAgo=
  dependencies:
    xml-name-validator "^3.0.0"

walker@^1.0.7, walker@~1.0.5:
  version "1.0.7"
  resolved "http://registry.npm.qima-inc.com/walker/download/walker-1.0.7.tgz"
  integrity sha1-L3+bj9ENZ3JisYqITijRlhjgKPs=
  dependencies:
    makeerror "1.0.x"

wcwidth@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/wcwidth/download/wcwidth-1.0.1.tgz"
  integrity sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=
  dependencies:
    defaults "^1.0.3"

webidl-conversions@^3.0.0:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/webidl-conversions/download/webidl-conversions-3.0.1.tgz#24534275e2a7bc6be7bc86611cc16ae0a5654871"
  integrity sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE=

webidl-conversions@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/webidl-conversions/download/webidl-conversions-5.0.0.tgz"
  integrity sha1-rlnIoAsSFUOirMZcBDT1ew/BGv8=

webidl-conversions@^6.1.0:
  version "6.1.0"
  resolved "http://registry.npm.qima-inc.com/webidl-conversions/download/webidl-conversions-6.1.0.tgz"
  integrity sha1-kRG01+qArNQPUnDWZmIa+ni2lRQ=

whatwg-encoding@^1.0.5:
  version "1.0.5"
  resolved "http://registry.npm.qima-inc.com/whatwg-encoding/download/whatwg-encoding-1.0.5.tgz"
  integrity sha1-WrrPd3wyFmpR0IXWtPPn0nET3bA=
  dependencies:
    iconv-lite "0.4.24"

whatwg-mimetype@^2.3.0:
  version "2.3.0"
  resolved "http://registry.npm.qima-inc.com/whatwg-mimetype/download/whatwg-mimetype-2.3.0.tgz"
  integrity sha1-PUseAxLSB5h5+Cav8Y2+7KWWD78=

whatwg-url@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/whatwg-url/download/whatwg-url-5.0.0.tgz#966454e8765462e37644d3626f6742ce8b70965d"
  integrity sha1-lmRU6HZUYuN2RNNib2dCzotwll0=
  dependencies:
    tr46 "~0.0.3"
    webidl-conversions "^3.0.0"

whatwg-url@^8.0.0:
  version "8.4.0"
  resolved "http://registry.npm.qima-inc.com/whatwg-url/download/whatwg-url-8.4.0.tgz"
  integrity sha1-UPuWFbBUaVkdKyvW367SlC7XKDc=
  dependencies:
    lodash.sortby "^4.7.0"
    tr46 "^2.0.2"
    webidl-conversions "^6.1.0"

which-module@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/which-module/download/which-module-2.0.0.tgz"
  integrity sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho=

which-pm-runs@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/which-pm-runs/download/which-pm-runs-1.1.0.tgz#35ccf7b1a0fce87bd8b92a478c9d045785d3bf35"
  integrity sha512-n1brCuqClxfFfq/Rb0ICg9giSZqCS+pLtccdag6C2HyufBrh3fBOiy9nb6ggRMvWOVH5GrdJskj5iGTZNxd7SA==

which-typed-array@^1.1.2:
  version "1.1.4"
  resolved "http://registry.npm.qima-inc.com/which-typed-array/download/which-typed-array-1.1.4.tgz"
  integrity sha1-j8t9PuWt8tdxBm+6fPN+Mv6HEf8=
  dependencies:
    available-typed-arrays "^1.0.2"
    call-bind "^1.0.0"
    es-abstract "^1.18.0-next.1"
    foreach "^2.0.5"
    function-bind "^1.1.1"
    has-symbols "^1.0.1"
    is-typed-array "^1.1.3"

which@^1.2.9:
  version "1.3.1"
  resolved "http://registry.npm.qima-inc.com/which/download/which-1.3.1.tgz"
  integrity sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=
  dependencies:
    isexe "^2.0.0"

which@^2.0.1, which@^2.0.2:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/which/download/which-2.0.2.tgz"
  integrity sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=
  dependencies:
    isexe "^2.0.0"

widest-line@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/widest-line/download/widest-line-2.0.1.tgz"
  integrity sha1-dDh2RzDsfvQ4HOTfgvuYpTFCo/w=
  dependencies:
    string-width "^2.1.1"

win-release@^1.0.0:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/win-release/download/win-release-1.1.1.tgz"
  integrity sha1-X6VeAr58qTTt/BJmVjLoSbcuUgk=
  dependencies:
    semver "^5.0.1"

window-size@^0.1.4:
  version "0.1.4"
  resolved "http://registry.npm.qima-inc.com/window-size/download/window-size-0.1.4.tgz"
  integrity sha1-+OGqHuWlPsW/FR/6CXQqatdpeHY=

with@^7.0.0:
  version "7.0.2"
  resolved "http://registry.npm.qima-inc.com/with/download/with-7.0.2.tgz#ccee3ad542d25538a7a7a80aad212b9828495bac"
  integrity sha1-zO461ULSVTinp6gKrSErmChJW6w=
  dependencies:
    "@babel/parser" "^7.9.6"
    "@babel/types" "^7.9.6"
    assert-never "^1.2.1"
    babel-walk "3.0.0-canary-5"

word-wrap@~1.2.3:
  version "1.2.3"
  resolved "http://registry.npm.qima-inc.com/word-wrap/download/word-wrap-1.2.3.tgz"
  integrity sha1-YQY29rH3A4kb00dxzLF/uTtHB5w=

"wordwrap@>=0.0.1 <0.1.0", wordwrap@~0.0.2:
  version "0.0.3"
  resolved "http://registry.npm.qima-inc.com/wordwrap/download/wordwrap-0.0.3.tgz"
  integrity sha1-o9XabNXAvAAI03I0u68b7WMFkQc=

wrap-ansi@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/wrap-ansi/download/wrap-ansi-2.1.0.tgz"
  integrity sha1-2Pw9KE3QV5T+hJc8rs3Rz4JP3YU=
  dependencies:
    string-width "^1.0.1"
    strip-ansi "^3.0.1"

wrap-ansi@^6.2.0:
  version "6.2.0"
  resolved "http://registry.npm.qima-inc.com/wrap-ansi/download/wrap-ansi-6.2.0.tgz"
  integrity sha1-6Tk7oHEC5skaOyIUePAlfNKFblM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^7.0.0:
  version "7.0.0"
  resolved "http://registry.npm.qima-inc.com/wrap-ansi/download/wrap-ansi-7.0.0.tgz#67e145cff510a6a6984bdf1152911d69d2eb9e43"
  integrity sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrappy@1:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/wrappy/download/wrappy-1.0.2.tgz"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

write-file-atomic@^2.0.0:
  version "2.4.3"
  resolved "http://registry.npm.qima-inc.com/write-file-atomic/download/write-file-atomic-2.4.3.tgz"
  integrity sha1-H9Lprh3z51uNjDZ0Q8aS1MqB9IE=
  dependencies:
    graceful-fs "^4.1.11"
    imurmurhash "^0.1.4"
    signal-exit "^3.0.2"

write-file-atomic@^3.0.0:
  version "3.0.3"
  resolved "http://registry.npm.qima-inc.com/write-file-atomic/download/write-file-atomic-3.0.3.tgz"
  integrity sha1-Vr1cWlxwSBzRnFcb05q5ZaXeVug=
  dependencies:
    imurmurhash "^0.1.4"
    is-typedarray "^1.0.0"
    signal-exit "^3.0.2"
    typedarray-to-buffer "^3.1.5"

write@1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/write/download/write-1.0.3.tgz"
  integrity sha1-CADhRSO5I6OH5BUSPIZWFqrg9cM=
  dependencies:
    mkdirp "^0.5.1"

ws@^6.0.0:
  version "6.2.2"
  resolved "http://registry.npm.qima-inc.com/ws/download/ws-6.2.2.tgz#dd5cdbd57a9979916097652d78f1cc5faea0c32e"
  integrity sha1-3Vzb1XqZeZFgl2UtePHMX66gwy4=
  dependencies:
    async-limiter "~1.0.0"

ws@^7.0.0:
  version "7.5.9"
  resolved "http://registry.npm.qima-inc.com/ws/download/ws-7.5.9.tgz#54fa7db29f4c7cec68b1ddd3a89de099942bb591"
  integrity sha1-VPp9sp9MfOxosd3TqJ3gmZQrtZE=

ws@^7.2.3:
  version "7.4.2"
  resolved "http://registry.npm.qima-inc.com/ws/download/ws-7.4.2.tgz"
  integrity sha1-eCEABI5U6zb+mEM2OrHGhnKyYd0=

ws@~7.2.0:
  version "7.2.5"
  resolved "http://registry.npm.qima-inc.com/ws/download/ws-7.2.5.tgz#abb1370d4626a5a9cd79d8de404aa18b3465d10d"
  integrity sha1-q7E3DUYmpanNedjeQEqhizRl0Q0=

x256@>=0.0.1, x256@~0.0.1:
  version "0.0.2"
  resolved "http://registry.npm.qima-inc.com/x256/download/x256-0.0.2.tgz"
  integrity sha1-ya8Yh296F1gB1WT+cK2egxd4STQ=

xdg-basedir@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/xdg-basedir/download/xdg-basedir-3.0.0.tgz"
  integrity sha1-SWsswQnsqNus/i3HK2A8F8WHCtQ=

xdg-basedir@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/xdg-basedir/download/xdg-basedir-4.0.0.tgz#4bc8d9984403696225ef83a1573cbbcb4e79db13"
  integrity sha1-S8jZmEQDaWIl74OhVzy7y0552xM=

xml-name-validator@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/xml-name-validator/download/xml-name-validator-3.0.0.tgz"
  integrity sha1-auc+Bt5NjG5H+fsYH3jWSK1FfGo=

xml2js@^0.4.23, xml2js@^0.4.5:
  version "0.4.23"
  resolved "http://registry.npm.qima-inc.com/xml2js/download/xml2js-0.4.23.tgz#a0c69516752421eb2ac758ee4d4ccf58843eac66"
  integrity sha1-oMaVFnUkIesqx1juTUzPWIQ+rGY=
  dependencies:
    sax ">=0.6.0"
    xmlbuilder "~11.0.0"

xmlbuilder@~11.0.0:
  version "11.0.1"
  resolved "http://registry.npm.qima-inc.com/xmlbuilder/download/xmlbuilder-11.0.1.tgz"
  integrity sha1-vpuuHIoEbnazESdyY0fQrXACvrM=

xmlchars@^2.2.0:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/xmlchars/download/xmlchars-2.2.0.tgz"
  integrity sha1-Bg/hvLf5x2/ioX24apvDq4lCEMs=

xregexp@2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/xregexp/download/xregexp-2.0.0.tgz"
  integrity sha1-UqY+VsoLhKfzpfPWGHLxJq16WUM=

xss@0.3.7:
  version "0.3.7"
  resolved "http://registry.npm.qima-inc.com/xss/download/xss-0.3.7.tgz"
  integrity sha1-HfbchcAkC0VbXl8EKL3szXOatO4=
  dependencies:
    commander "^2.9.0"
    cssfilter "0.0.10"

y18n@^3.2.0:
  version "3.2.2"
  resolved "http://registry.npm.qima-inc.com/y18n/download/y18n-3.2.2.tgz"
  integrity sha1-hckBvWRwznH8S7cjrSCbcPfyhpY=

y18n@^4.0.0:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/y18n/download/y18n-4.0.1.tgz"
  integrity sha1-jbK4PDHF11CZu4kLI/MJSJHiR9Q=

yallist@^2.1.2:
  version "2.1.2"
  resolved "http://registry.npm.qima-inc.com/yallist/download/yallist-2.1.2.tgz"
  integrity sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI=

yallist@^3.0.2:
  version "3.1.1"
  resolved "http://registry.npm.qima-inc.com/yallist/download/yallist-3.1.1.tgz"
  integrity sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=

yallist@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/yallist/download/yallist-4.0.0.tgz"
  integrity sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=

yaml@^1.10.0:
  version "1.10.2"
  resolved "http://registry.npm.qima-inc.com/yaml/download/yaml-1.10.2.tgz#2301c5ffbf12b467de8da2333a459e29e7920e4b"
  integrity sha1-IwHF/78StGfejaIzOkWeKeeSDks=

yamljs@0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.qima-inc.com/yamljs/download/yamljs-0.3.0.tgz#dc060bf267447b39f7304e9b2bfbe8b5a7ddb03b"
  integrity sha1-3AYL8mdEezn3ME6bK/votafdsDs=
  dependencies:
    argparse "^1.0.7"
    glob "^7.0.5"

yargs-parser@20.x:
  version "20.2.4"
  resolved "http://registry.npm.qima-inc.com/yargs-parser/download/yargs-parser-20.2.4.tgz"
  integrity sha1-tCiQ8UVmeW+Fro46JSkNIF8VSlQ=

yargs-parser@^18.1.2:
  version "18.1.3"
  resolved "http://registry.npm.qima-inc.com/yargs-parser/download/yargs-parser-18.1.3.tgz"
  integrity sha1-vmjEl1xrKr9GkjawyHA2L6sJp7A=
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs@^15.4.1:
  version "15.4.1"
  resolved "http://registry.npm.qima-inc.com/yargs/download/yargs-15.4.1.tgz"
  integrity sha1-DYehbeAa7p2L7Cv7909nhRcw9Pg=
  dependencies:
    cliui "^6.0.0"
    decamelize "^1.2.0"
    find-up "^4.1.0"
    get-caller-file "^2.0.1"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^4.2.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^18.1.2"

yargs@^3.32.0:
  version "3.32.0"
  resolved "http://registry.npm.qima-inc.com/yargs/download/yargs-3.32.0.tgz"
  integrity sha1-AwiOnr+edWtpdRYR0qXvWRSCyZU=
  dependencies:
    camelcase "^2.0.1"
    cliui "^3.0.3"
    decamelize "^1.1.1"
    os-locale "^1.4.0"
    string-width "^1.0.1"
    window-size "^0.1.4"
    y18n "^3.2.0"

yn@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/yn/download/yn-2.0.0.tgz"
  integrity sha1-5a2ryKz0CPY4X8dklWhMiOavaJo=

yocto-queue@^0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.qima-inc.com/yocto-queue/download/yocto-queue-0.1.0.tgz#0294eb3dee05028d31ee1a5fa2c556a6aaf10a1b"
  integrity sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=

zan-ajax@*, zan-ajax@3.0.0, zan-ajax@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/zan-ajax/download/zan-ajax-3.0.0.tgz"
  integrity sha1-mN10FJUXec4Xl6mY1fVbj6wdjik=
  dependencies:
    axios "0.19.0"
    zan-json-parse "^1.0.0"

zan-ajax@2.1.0, zan-ajax@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/zan-ajax/download/zan-ajax-2.1.0.tgz"
  integrity sha1-tHKffI9oC+G7QqNep4hm5pUsh+Y=
  dependencies:
    axios "0.18.0"
    zan-json-parse "^1.0.0"

zan-ajax@^1.1.7:
  version "1.2.2"
  resolved "http://registry.npm.qima-inc.com/zan-ajax/download/zan-ajax-1.2.2.tgz"
  integrity sha1-zC+rteVRx9wHHbM6WNqMCQmTbI4=
  dependencies:
    axios "0.16.2"
    zan-json-parse "^1.0.0"

zan-jquery@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/zan-jquery/download/zan-jquery-1.0.2.tgz"
  integrity sha1-2aRQiNDRUs/kFymspwtmguMM5c8=

zan-json-parse@1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/zan-json-parse/download/zan-json-parse-1.0.2.tgz"
  integrity sha1-RX2QF/M8C0Nh/nPr3V5h74GBjzw=

zan-json-parse@^1.0.0:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/zan-json-parse/download/zan-json-parse-1.0.3.tgz"
  integrity sha1-0D/vn5FDE8vwQ5fsDjhCw1LNReQ=

zod@^1.11.17:
  version "1.11.17"
  resolved "http://registry.npm.qima-inc.com/zod/download/zod-1.11.17.tgz#2aae9e91fc66128116ae9844e8f416a95f453f8e"
  integrity sha1-Kq6ekfxmEoEWrphE6PQWqV9FP44=
