<template>
  <div
    class="cart-goods"
    :cart-id="goods.cart_id"
    :class="{
      'cart-goods--sub': isSub,
    }"
  >
    <van-swipe-cell :right-width="!isCanDelete ? 0 : 64">
      <div class="cart-goods__card cart-goods__card--invalid">
        <div
          class="cart-goods__img-wrap"
          @click="goToGoodsPage"
        >
          <img alt="商品图片" class="cart-goods__img" :src="imgUrl" />
          <div class="cart-goods__img-mask"></div>
          <div class="cart-goods__img-tip">
            <span>失效商品</span>
          </div>
        </div>

        <!-- 如果有其他商品推荐，则不显示失效商品的标题及描述 -->
        <div ref="desc-wrap" class="cart-goods__desc-wrap" v-if="!recommendList.length">
          <div
            class="cart-goods__title"
            :class="{
              'cart-goods__title-not-exist': !goods.title,
            }"
          >
            <span class="cart-goods__title-text cart-goods__title-text_invalid">
              {{ goods.title | unescape }}
            </span>
          </div>

          <template>
            <div :style="priceStyle">
              <div
                v-if="goods.errorMsg && isInvalid"
                class="cart-goods__err-msg"
                :class="{
                  'cart-goods__err-msg--invalid': isInvalid,
                }"
              >
                {{ goods.errorMsg }}
              </div>
            </div>
          </template>
        </div>
        <!-- 其他商品推荐 -->
        <recommend-goods
          class="recommend-goods"
          v-if="recommendList.length"
          :recommend-list="recommendList"
        />
      </div>
      
      <template slot="right">
        <div
          v-if="isCanDelete"
          class="cart-goods__swipe-delete"
          @click="deleteGoods"
        >
          <span>删除</span>
        </div>
      </template>
    </van-swipe-cell>
  </div>
</template>

<script>
import urlHelper from '@youzan/utils/url/helper';
import get from 'utils/get';
import image from '@youzan/utils/browser/image';
import { SwipeCell } from 'vant';
import { action } from '@youzan/zan-jsbridge';
import ZNB from '@youzan/znb';
import { jumpLink, isTTApp } from '@shared/common/helper';
import RecommendGoods from './RecommendGoods';
import {
  GOODS_LINK,
  IS_APP_CART,
  IS_OPEN_NEW,
} from '../../constant';

export default {
  name: 'goods',

  components: {
    [SwipeCell.name]: SwipeCell,
    RecommendGoods,
  },

  props: {
    goods: Object,
    kdtId: Number,
    // 是否是无效商品
    isInvalid: {
      type: Boolean,
      default: false,
    },
    // 是否是附属商品
    isSub: {
      type: Boolean,
      default: false,
    },
    // 是否可以删除
    isCanDelete: {
      type: Boolean,
      default: true,
    },
  },

  data() {
    return {
      cartProcess: null,
      priceStyle: {}, // 计算当右侧高度不足时，让价格落底
      recommendList: [],
    };
  },

  computed: {
    imgUrl() {
      return image.toWebp(
        urlHelper.getCdnImageUrl(this.goods.attachmentUrl, '!200x200.jpg')
      );
    },

    isSevenDayUnconditionalReturn() {
      return this.goods.isSevenDayUnconditionalReturn;
    },
  },

  watch: {
    goods: {
      handler(val) {
        if (val.recommendListObj) {
          const recommendListTemp = val.recommendListObj.recommendList || [];
          this.recommendList = recommendListTemp.slice(0,3);
        }
        this.priceStyle = {
          paddingTop: '',
        };
        if (!this.recommendList.length) {
          this.$nextTick(() => {
            const descWrap = this.$refs['desc-wrap'];
            if (descWrap.offsetHeight < 96) {
              this.priceStyle = {
                paddingTop: `${96 - descWrap.offsetHeight}px`,
              };
            }
          });
        }
      },
      immediate: true
    },
  },

  methods: {
    deleteGoods() {
      this.$emit('deleteCartItem', { goods: this.goods });
    },

    goToGoodsPage() {
      // 点击商品图片时所做的操作
      const goodsLink = `${GOODS_LINK}?alias=${this.goods.alias}`;
      const isAlipayApp = get(window, '_global.miniprogram.isAlipayApp', false);
      const isQQApp = get(window, '_global.miniprogram.isQQApp', false);
      const isXhsApp = get(window, '_global.miniprogram.isXhsApp', false);
    
      // 头条小程序内使用jumpLink，内有特殊逻辑
      if (isTTApp) {
        jumpLink(goodsLink);
      }

      if (isAlipayApp || isQQApp || isXhsApp) {
        ZNB.navigate({
          aliappUrl: `/packages/goods/detail/index?alias=${this.goods.alias}`,
          qqUrl: `/packages/goods/detail/index?alias=${this.goods.alias}`,
          xhsUrl: `/packages/goods/detail/index?alias=${this.goods.alias}`,
        });
        return;
      }

      if (!IS_APP_CART) {
        const options = {
          url: goodsLink,
        };
        // 如果页面 url 上有 __openNew__ 参数，说明需要用新页面打开链接
        options.fallback = IS_OPEN_NEW ? 'open' : 'redirect';
        ZNB.navigate(options);
        return;
      }

      action.gotoWebview({
        url: goodsLink,
        page: 'web',
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.cart-goods {
  position: relative;
  background: #fff;

  &:last-child {
    margin-bottom: 0;
  }

  &--sub {
    .cart-goods__title {
      -webkit-line-clamp: 1;
    }
  }

  &__img-wrap {
    width: 96px;
    height: 96px;
    float: left;
    position: relative;
    margin-left: auto;
    margin-right: auto;
    border-radius: 8px;
    overflow: hidden;
    background: #fff;
    background-size: cover;
  }

  &__img {
    position: absolute;
    margin: auto;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: auto;
    height: auto;
    max-width: 100%;
    max-height: 100%;
  }

  &__img-mask {
    position: absolute;
    margin: auto;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: auto;
    height: auto;
    max-width: 100%;
    max-height: 100%;
    background: #fff;
    opacity: 0.6;
  }

  &__img-tip {
    width: 56px;
    height: 56px;
    position: absolute;
    left: 20px;
    top: 20px;
    border-radius: 50%;
    opacity: 0.6;
    background: #000000;

    span {
      width: 24px;
      height: 32px;
      position: absolute;
      left: 16px;
      top: 12px;
      font-family: PingFangSC-Regular;
      font-size: 12px;
      color: #FFFFFF;
      text-align: center;
      line-height: 16px;
    }
  }

  &__card {
    box-sizing: content-box;
    min-height: 96px;
    padding: 12px 12px 12px 0;
    margin-left: 44px;
    background: #fff;

    a {
      display: block;
    }
  }

  &__card--invalid {
    margin-left: 12px;
  }

  &__title {
    margin-bottom: 8px;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;

    &-not-exist {
      margin-bottom: 0;
    }
  }

  &__title-text {
    color: #323233;
    font-size: 14px;
    line-height: 20px;
    vertical-align: middle;

    &_invalid {
      color: #969799;
    }
  }

  &__desc-wrap {
    margin-left: 104px;
  }

  &__err-msg {
    font-size: 14px;
    color: #323233;
    line-height: 16px;
    margin-top: 3px;

    &--invalid {
      margin-top: 0;
    }
  }

  &__swipe-delete {
    height: 100%;
    width: 64px;
    text-align: center;
    background: #ff4343;
    color: #fff;
    line-height: 1;

    span {
      font-size: 14px;
      left: 20px;
      position: absolute;
      top: 50%;
      margin-top: -6px;
    }
  }

  &__seven-return {
    font-size: 12px;
    color: #faab0c;
    letter-spacing: 0;
    line-height: 16px;
    margin-bottom: 8px;
  }
}
.recommend-goods {
  float: left;
  margin-left: 8px;
}
</style>
