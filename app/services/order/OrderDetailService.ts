/* eslint-disable @typescript-eslint/no-explicit-any */
import get from 'lodash/get';
import { IShop } from '@youzan/utils-shop';
import { IOrderDetailReq, IOrderDetailDto } from 'definitions/order/OrderDetail';
import { IOrderInfoDto } from 'definitions/order/detail/OrderInfo';
import { IShopInfoDto } from 'definitions/order/detail/ShopInfo';
import { IPaymentInfoDto } from 'definitions/order/detail/PaymentInfo';
import { ISourceInfoDto } from 'definitions/order/detail/SourceInfo';
import { IOrderBizExtraDto } from 'definitions/order/detail/OrderBizExtra';
import BaseService from '../base/BaseService';
import OrderInfoFormatter from './format/detail/OrderInfoFormatter';
import AddressFormatter from './format/detail/AddressFormatter';
import SourceInfoFormatter from './format/detail/SourceInfoFormatter';
import ItemInfoFormatter from './format/detail/ItemInfoFormatter';
import CityOrderFormatter from './format/detail/CityOrderFormatter';
import UmpHandler from './format/detail/UmpHandler';
import DeliveryHandler from './format/detail/DeliveryHandler';
import HaitaoOrderHandler from './format/detail/HaitaoOrderHandler';
import MultiStoreHandler from './format/detail/MultiStoreHandler';
import EducationOrderHandler from './format/detail/EducationOrderHandler';
import GroupBuyOrderHandler from './format/detail/GroupBuyOrderHandler';
import PeriodBuyOrderHandler from './format/detail/PeriodBuyOrderHandler';
import WeappHandler from './format/detail/WeappHandler';
import CrmOfflineOrderHandler from './format/detail/CrmOfflineOrderHandler';
import { formatPackingFee } from '../../utils';

class OrderDetailService extends BaseService {
  get ORDER_DETAIL_SERVICE(): string {
    return 'com.youzan.ebiz.trade.api.manage.detail.DetailService';
  }

  get DELIVERY_OPERATE_SERVICE(): string {
    return 'com.youzan.trade.dc.api.service.operate.DeliveryOperateService';
  }

  get ORDER_OFFLINEGATHER_SERVICE(): string {
    return 'com.youzan.owl.api.offlineenrollment.OfflineGiveawayFacade';
  }

  get ORDER_INFO_SERVICE(): string {
    return 'com.youzan.trade.detail.api.service.OrderInfoService';
  }

  get VERIFY_TOOL_FACADE_SERVICE(): string {
    return 'com.youzan.ump.voucher.front.api.facade.service.voucher.VerifyToolFacadeService';
  }

  get ORDER_DETAIL_SERVICE_TRADE(): string {
    return 'com.youzan.trade.detail.api.service.OrderDetailService';
  }

  /**
   * 确认收货
   * @param params
   */
  public orderConfirmRecvive(params: any) {
    return this.invoke(this.DELIVERY_OPERATE_SERVICE, 'orderConfirmReceive', [params]);
  }

  public orderDelayReceive(params: any) {
    return this.invoke(this.DELIVERY_OPERATE_SERVICE, 'orderDelayReceive', [params]);
  }

  public checkOrderDelayReceive(params: any) {
    return this.invoke(this.DELIVERY_OPERATE_SERVICE, 'checkOrderDelayReceive', [params]);
  }

  public getGoodsGiftsByOrderNo(kdtId: number, orderNo: string) {
    return this.invoke(this.ORDER_OFFLINEGATHER_SERVICE, 'findGiveAwayByOrderNo', [kdtId, orderNo]);
  }

  public async detailByOrderNo(params: IOrderDetailReq, clientType = ''): Promise<IOrderDetailDto> {
    let method = 'getOrderInfoFormat';
    if (params.from === 'IVR') {
      method = 'getOrderInfoIVR';
      delete params.from;
    }

    const order = await this.callOrderInfo(method, params);
    if (!order) {
      return {} as any;
    }
    return this._formatOrder(order, clientType);
  }

  /**
   * 订单详情轻量级请求
   */
  public async lightDetailByOrderNo(params: IOrderDetailReq): Promise<any> {
    const order = await this.callOrderInfo('getOrderInfoFormat', params);

    if (!order) {
      return {};
    }
    const itemInfosDto = ItemInfoFormatter.format(order.itemInfo || [], order.mainOrderInfo);
    order.itemInfo = itemInfosDto;
    return order;
  }

  private async callOrderInfo(method = 'getOrderInfoFormat', params: IOrderDetailReq): Promise<any> {
    /**
     * tether调用
     */
    return await this.invoke(this.ORDER_DETAIL_SERVICE, method, [params], {
      allowBigNumberInJSON: true,
    });
  }

  private async _formatOrder(data: any, clientType: string): Promise<IOrderDetailDto> {
    const isWeapp = clientType === 'weapp';

    // 主订单数据
    const mainOrderInfo = data.mainOrderInfo || {};
    const itemProcessInfo = data.itemProcessInfo || {}; // 商品制作进度
    const refundDTO = data.refundDTO || {}; // 退差价信息
    const orderExtra = mainOrderInfo.extra || {};
    const customInfoMap = mainOrderInfo.customInfoMap || {};
    const orderBizExtra: IOrderBizExtraDto = get(data, 'orderBizExtra', {}); // 订单扩展字段（业务扩展，前端展示逻辑等）
    const orderBizUrl = get(data, 'orderBizUrl', {});
    const orderAddressInfo = data.orderAddressInfo || {};
    const shopInfo: IShopInfoDto = data.shopInfo || {};
    const { hotelInfoDTO } = data;

    // 格式化主订单信息
    const orderInfoDto: IOrderInfoDto = OrderInfoFormatter.format(mainOrderInfo, orderExtra, data.remarkInfo);
    // 处理支付信息
    const paymentInfoDto: IPaymentInfoDto = data.paymentInfo || {};
    const sourceInfo: ISourceInfoDto = SourceInfoFormatter.format(data.sourceInfo || {});
    const addressInfoDto = AddressFormatter.format(orderAddressInfo);
    // 处理订单扩展字段
    OrderInfoFormatter.formatOrderBiz(
      orderInfoDto,
      orderBizExtra,
      orderBizUrl,
      mainOrderInfo,
      orderExtra,
      sourceInfo,
      this.ctx
    );

    // 同城订单处理
    CityOrderFormatter.format(addressInfoDto, orderInfoDto, orderBizExtra, orderAddressInfo, mainOrderInfo);
    // 海淘订单处理
    HaitaoOrderHandler.handle(orderInfoDto, orderExtra);

    // 格式化商品信息
    const itemInfosDto = ItemInfoFormatter.format(data.itemInfo || [], refundDTO, itemProcessInfo, orderBizExtra, orderInfoDto?.activityType);

    // 电子卡券跨网点核销
    MultiStoreHandler.handle(orderInfoDto, shopInfo, orderExtra);

    // 课程商品信息
    EducationOrderHandler.handle(orderInfoDto, orderExtra, customInfoMap);

    // 拼团订单处理
    GroupBuyOrderHandler.handle(orderInfoDto, addressInfoDto, mainOrderInfo);

    // 订单优惠
    UmpHandler.handle(orderInfoDto, mainOrderInfo, data.ump, paymentInfoDto);

    // 处理周期购
    PeriodBuyOrderHandler.handle(orderInfoDto, itemInfosDto, mainOrderInfo);

    // 格式化商品包裹信息
    DeliveryHandler.handle(orderInfoDto, data.deliveryInfo || {}, mainOrderInfo);

    // 小程序环境专用处理
    if (isWeapp) {
      WeappHandler.handleCompatible({
        orderInfoDto,
        orderBizExtra,
        paymentInfoDto,
        orderInfoDo: mainOrderInfo,
        ctx: this.ctx,
      });
      WeappHandler.handleItemInfoCompatible(itemInfosDto, this.ctx.weappVersion);

      // 打包费处理
      formatPackingFee(paymentInfoDto, 'orderExtraPrices', this.ctx.mpVersion, shopInfo as IShop);
    }

    // 线下门店订单处理
    CrmOfflineOrderHandler.handle(orderInfoDto);

    const res: IOrderDetailDto = {
      orderExtra,
      orderBizExtra,
      orderBizUrl,
      mainOrderInfo: orderInfoDto,
      itemInfo: itemInfosDto,
      paymentInfo: paymentInfoDto,
      orderAddressInfo: addressInfoDto,
      hotelInfo: hotelInfoDTO,
      shopInfo,
      ump: data.ump,
      buyerInfo: data.buyerInfo || {},
      invoiceInfo: data.invoiceInfo || {},
      gift: data.gift,
      marketingInfo: data.marketingInfo || {},
      sourceInfo,
      goodsGift: [],
      microTransferInfo: data.microTransfer,
      privacyWaybill: data.privacyWaybillDTO,
      outerPromotion: data.outerPromotion,
    };
    return res;
  }

  /**
   *  * 获取订单及订单相关信息(判断是否为 crm 线下订单及 root_kdt_id)
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1257
   *
   *  @param {Object} param - 请求参数
   *  @param {string} param.app - 调用的应用
   *  @param {string} param.bizGroup - 调用的业务组
   *  @param {string} param.orderNo -
   *  @param {number} param.kdtId -
   *  @param {Object} param.options -
   *  @param {string} param.source -
   *  @return {Promise}
   */
  async getOrderInfo(param: any) {
    return this.invoke(this.ORDER_INFO_SERVICE, 'get', [param]);
  }

  /**
   *  分页查询分销单可用优惠券核销码
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1451541
   *  天网: https://ops.qima-inc.com/v3/skynet/#/main/prod/log/search/all-log?appName=wsc-h5-trade&order=DESC&queryString=&tags=methodName%*****************************&tags=serviceName%3DVerifyToolFacadeService
   *
   *  @param {Object} request -
   *  @param {string} request.sendSource - 发放来源
   *  @param {number} request.kdtId - 店铺id
   *  @param {string} request.sendNo - 发放单号
   *  @param {number} request.pageSize - 每页数量
   *  @param {string} request.buyerId - 用户ID
   *  @param {number} request.pageNum - 页码
   *  @return {Promise}
   */
  async pageQueryVerifyCodeBySendNo(request: any) {
    return this.invoke(this.VERIFY_TOOL_FACADE_SERVICE, 'pageQueryVerifyCodeBySendNo', [request]);
  }

  /**
   *  订单详情非实时接口
   *  NOTE: 完全从Hbase取数据，不从DB取数据，新非实时详情业务接入此接口
   *
   * 设计文档:
   * https://doc.qima-inc.com/pages/viewpage.action?pageId=85918522
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/260165
   *
   *  @param {Object} param - 请求参数
   *  @param {string} param.app - 调用的应用
   *  @param {string} param.bizGroup - 调用的业务组
   *  @param {Array.<Array>} param.orderNos[] -
   *  @param {number} param.kdtId -
   *  @param {string} param.requestId - 唯一请求id，必填
   *  @param {Object} param.options -
   *  @param {string} param.source -
   *  @return {Promise}
   */
  async getTradeOrdersDetail(param: any) {
    return this.invoke(this.ORDER_DETAIL_SERVICE_TRADE, 'getOrders', [param]);
  }
}

export = OrderDetailService;
