import { app, mock } from '@youzan/astroboy-mock/bootstrap';
import BaseController from '../../../controllers/base/BaseController';
import CustomerQueryService from '../../../services/trade/CustomerQueryService';
import TradeService from '../../../services/trade/TradeService';

jest.mock('../../../controllers/base/BaseController', () =>
  jest.requireActual('../../__mocks__/BaseController')
);

jest.mock('../../../services/trade/CustomerQueryService');
jest.mock('../../../services/trade/TradeService');

afterEach(() => {
  BaseController.mockClear();
  CustomerQueryService.mockClear();
  TradeService.mockClear();
});

test('getReceiverIdentityList', async () => {
  app.mockContext({
    buyerId: 8000035538,
    kdtId: 491391,
  });

  CustomerQueryService.mockImplementation(() => {
    return {
      getReceiverIdentityList: jest.fn().mockResolvedValue([]),
    };
  });

  const res = await app
    .httpRequest()
    .get('/pay/wsctrade/order/buy/getReceiverIdentityList.json');

  expect(res.body).toEqual({
    code: 0,
    data: [],
    msg: 'ok',
  });
});

test('getIndexHtml', async () => {
  app.mockContext({
    buyerId: 8106728201,
    kdtId: 56416603,
    ABTestClient: {
      getTest: () => {}
    }
  });

  const data = {};

  TradeService.mockImplementation(() => {
    return {
      prepareFast: jest.fn().mockResolvedValue(data),
    };
  });

  app.mockApollox({
    appId: 'wsc-h5-trade',
    namespace: 'wsc-h5-trade.youzanyun',
    key: 'sandboxShopList',
  }, '56416603')

  app.mockApollox({
    appId: 'wsc-h5-trade',
    namespace: 'wsc-h5-trade.application',
    key: 'overseaRegulationWhiteList',
  }, '56416603')

  const res = await app.httpRequest().get('/pay/wsctrade_buy');

  const global = mock.getViewGlobal(res.text);
  expect(res.body).toEqual({});
  expect(BaseController.mockInitZanPay.mock.calls.length).toBe(1);
  expect(global.prepare).toEqual({});
  expect(global.useCloudGuard).toBeTruthy();
  expect(global.overseaRegulationWhiteList).toEqual(['56416603']);
});

test('getPayHtml', async () => {
  app.mockContext({
    buyerId: 8106728201,
    kdtId: 56416603,
  });

  app.mockLocalSession({
    youzan_user_id: 8106728201
  })

  const data = {};

  TradeService.mockImplementation(() => {
    return {
      preparePaymentV2: jest.fn().mockResolvedValue(data),
    };
  });
  

  const res = await app.httpRequest().get('/pay/wsctrade_pay');

  expect(res.body).toEqual({});
  expect(BaseController.mockInitZanPay.mock.calls.length).toBe(1);
});

test('getPrepareByBookKeyJson', async () => {
  app.mockContext({
    buyerId: 8106728201,
    kdtId: 56416603,
    xExtraData: {
      version: '2.50.1',
    }
  });

  const data = {};

  TradeService.mockImplementation(() => {
    return {
      prepareV2: jest.fn().mockResolvedValue(data),
    };
  });

  app.mockApollox({
    appId: 'wsc-h5-trade',
    namespace: 'wsc-h5-trade.application',
    key: 'overseaRegulationWhiteList',
  }, '56416603')

  const res = await app.httpRequest().get('/pay/wsctrade/order/buy/prepare-by-book-key.json');

  expect(res.body).toEqual({
    code: 0,
    msg: '',
    data:{
      pointsName: '积分',
      version: 1,
      orderKeepApply: false,
      showWxSubscribe: false,
      tradeConfirmation: { orderPayment: { wechatPayParams: { wxSubOpenId: '', wxSelfOpenId: '' } } },
      displayConfig: { riskWarnShopPrompt: '', copyrightPicUrl: '' },
      address: { list: [] },
      agreement: {},
      ignoreIdBinding: true
    }
  });
});
/**
 * 小程序接口
 * 同城预约的Apollo开关配置
 * 2020年11月26日
 */
test('checkIsNewProcess', async () => {
  app.mockContext({
    kdtId: 123456,
  });

  TradeService.mockImplementation(() => {
    return {
      getDeliveryApolloConfig: jest.fn().mockResolvedValue(true),
    }
  })

  const res = await app.httpRequest().get('/pay/wsctrade/order/buy/checkIsNewProcess.json');

  expect(res.body).toEqual({
      code: 0,
      data: true,
      msg: 'ok',
  })
})
