<template>
  <div v-if="hasGoods" ref="shop-title" class="shop-header">
    <div class="shop-header__title" @click="goNative">
      <van-icon name="shop-o" size="16px" color="#323233" style="min-width: 1em" class="shop-o-icon" />
      <div class="cart-shop-name">
        <span class="name">{{ shopTitle }}</span>
        <van-icon name="arrow" size="16px" color="#969799" style="min-width: 1em" class="arrow-icon" />
        <span class="switch-shop" @click="switchShop" v-if="isChainShop && isSwitchOpen">[切换]</span>
      </div>
    </div>
    <span v-if="isWholesaler" class="go-wholesale" @click="goWholesalePurchase">查看进货单</span>
    <span class="edit" @click="edit">{{ editText }}</span>
  </div>
</template>

<script>
import ZNB from '@youzan/znb';
import { Icon } from 'vant';
import { action } from '@youzan/zan-jsbridge';
import { IS_MAIJIABAN, HAS_EMPTY_LINK } from '@biz/cart/constant';
import { getShopConfig, getIsWholesaler } from '@biz/cart/api';
import * as SafeLink from '@youzan/safe-link';

export default {
  props: {
    ctx: Object,
  },

  components: {
    [Icon.name]: Icon,
  },

  data() {
    const { shopTitle, editMode, shopCart } = this.ctx.data;
    const { offlineData = {}, shopMetaInfo = {} } = window._global;
    // 不是单店，并且没有开启校区隔离
    const isChainShop = [1, 2].includes(+shopMetaInfo.shopRole) && +shopMetaInfo.chainOnlineShopMode === 2;
    return {
      shopTitle,
      editMode,
      shopCart,
      editText: '编辑',
      hasGoods: true,
      offlineData,
      isChainShop,
      isSwitchOpen: false,
      isWholesaler: false,
    };
  },

  created() {
    getShopConfig().then((res) => {
      this.isSwitchOpen = +res?.data?.subshop_online_shop_switch_show;
    });
    this.unwatchShopTitle = this.ctx.watch('shopTitle', (val) => {
      this.shopTitle = val;
    });

    this.unwatchShopCart = this.ctx.watch('shopCart', (val) => {
      this.shopCart = val;
      // 可用商品 || 失效商品
      this.hasGoods =
        (this.shopCart.goodsGroupList || []).some((goodsGroup) => !!goodsGroup.goodsList.length) ||
        (this.shopCart.unavailableItems && this.shopCart.unavailableItems.length > 0);
    });

    getIsWholesaler().then((res) => {
      this.isWholesaler = res.data || false;
    });
  },

  destroyed() {
    this.unwatchShopTitle && this.unwatchShopTitle();
    this.unwatchShopCart && this.unwatchShopCart();
  },

  methods: {
    edit() {
      if (this.ctx.data.editMode === 'submit') {
        this.editText = '完成';
        this.ctx.data.editMode = 'edit';
      } else if (this.ctx.data.editMode === 'edit') {
        this.editText = '编辑';
        this.ctx.data.editMode = 'submit';
      }
    },

    goNative() {
      if (IS_MAIJIABAN) {
        // 跳转原生APP首页
        action.doAction({ action: 'goHome' });
      } else {
        let link = '';
        if (HAS_EMPTY_LINK) {
          link = `${window._global.url.wap}/showcase/homepage?kdt_id=${this.shopCart.kdtId}`;
        }
        ZNB.getEnv()
          .then((env) => {
            const shouldSwitchTab =
              env.platform === 'weapp' ||
              env.platform === 'swan' ||
              env.platform === 'ttapp' ||
              env.platform === 'qqapp' ||
              env.platform === 'aliapp' ||
              env.platform === 'xhsapp';

            ZNB.navigate({
              type: shouldSwitchTab ? 'switchTab' : '',
              url: link,
              ttUrl: '/pages/home/<USER>/main',
              swanUrl: '/pages/home/<USER>/index',
              aliappUrl: '/pages/home/<USER>/index',
              qqUrl: '/pages/home/<USER>/index',
              xhsUrl: '/pages/home/<USER>/index',
            });
          })
          .catch(() => {});
      }
    },

    switchShop(e) {
      e.stopPropagation();
      SafeLink.redirect({
        url: this.offlineData.url,
      });
    },

    goWholesalePurchase() {
      ZNB.navigate({
        url: '/wsctrade/wholesale/purchaseOrder',
      });
    },
  },
};
</script>

<style scoped lang="scss">
.shop-header {
  color: #999;
  font-size: 18px;
  height: 48px;
  padding-left: 16px;
  line-height: 48px;
  position: relative;
  background: #fff;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &__title {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-grow: 1;
  }

  .shop-o-icon {
    margin-right: 8px;
  }

  .cart-shop-name {
    display: flex;
    align-items: center;
    flex: 1;
    width: 0;
    font-size: 16px;
    color: #323233;

    .name {
      vertical-align: middle;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }

    .switch-shop {
      vertical-align: middle;
      color: #323233;
    }
  }

  .edit,
  .go-wholesale {
    color: #323233;
    font-size: 14px;
    padding: 0 12px;
    flex-shrink: 0;
  }

  .go-wholesale {
    position: relative;

    &::after {
      content: '';
      position: absolute;
      width: 1px;
      background-color: #d8d8d8;
      top: 17px;
      bottom: 16px;
      right: 0;
    }
  }
}
</style>
