<template>
  <div v-if="showEmptyCart" class="empty-list">
    <i class="empty-list__icon" />
    <h2>{{ getCustomCartName }}还是空的</h2>
    <div class="desc" v-if="showDesc">赶紧买点宝贝慰劳下自己吧</div>
    <div v-if="!hideStroll" class="go-homepage-btn" @click="goNative">
      <a>{{ goBuyText }}</a>
    </div>
  </div>
</template>

<script>
import { action } from '@youzan/zan-jsbridge';
import ZNB from '@youzan/znb';
import { EMPTY_CART_LINK, IS_MAIJIABAN, HIDE_STROLL, IS_EDU_STORE } from '@biz/cart/constant';

const { customCartName } = window._global || {};

export default {
  name: 'empty-cart',

  props: {
    ctx: Object,
  },

  data() {
    return {
      emptyCartLink: EMPTY_CART_LINK,
      hideStroll: HIDE_STROLL,
      shopCart: [],
    };
  },

  computed: {
    showEmptyCart() {
      // 没有可用商品 && 没有失效商品
      return (
        !(this.shopCart.goodsGroupList || []).some((goodsGroup) => !!goodsGroup.goodsList.length) &&
        (!this.shopCart.unavailableItems || this.shopCart.unavailableItems.length === 0)
      );
    },
    getCustomCartName() {
      let parse = {};
      try {
        parse = JSON.parse(customCartName.value);
      } catch (error) {
        parse = {};
      }
      return parse.label || '购物车';
    },
    goBuyText() {
      return IS_EDU_STORE ? '去选购' : '去逛逛';
    },
    // 教育店铺不展示
    showDesc() {
      return !IS_EDU_STORE;
    },
  },

  created() {
    this.watchShopCart();
  },

  destroyed() {
    this.unwatchShopCart && this.unwatchShopCart();
  },

  methods: {
    watchShopCart() {
      this.unwatchShopCart = this.ctx.watch('shopCart', (val = []) => {
        this.shopCart = val;
      });
    },
    goNative() {
      if (IS_MAIJIABAN) {
        // 跳转原生APP首页
        action.doAction({ action: 'goHome' });
      } else {
        ZNB.getEnv()
          .then((env) => {
            const shouldSwitchTab =
              env.platform === 'weapp' ||
              env.platform === 'swan' ||
              env.platform === 'aliapp' ||
              env.platform === 'qqapp' ||
              env.platform === 'xhsapp' ||
              env.platform === 'ttapp';

            ZNB.navigate({
              type: shouldSwitchTab ? 'switchTab' : '',
              url: this.emptyCartLink,
              /* weappUrl: '/pages/home/<USER>/index', */
              ttUrl: '/pages/home/<USER>/main',
              swanUrl: '/pages/home/<USER>/index',
              aliappUrl: '/pages/home/<USER>/index',
              qqUrl: '/pages/home/<USER>/index',
              xhsUrl: '/pages/home/<USER>/index',
            });
          })
          .catch(() => {});
      }
    },
  },
};
</script>

<style scoped lang="scss">
.empty-list {
  padding: 32px 0;
  text-align: center;

  &__icon {
    display: block;
    width: 150px;
    height: 150px;
    margin: 0 auto;
    background: url('//img01.yzcdn.cn/public_files/da9aa300df22f24aae85fc8eb690bf1c.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }

  h2 {
    font-size: 14px;
    color: #323233;
    margin-bottom: 8px;
    margin-top: 24px;
    font-weight: normal;
  }

  .desc {
    font-size: 14px;
    color: #969799;
  }

  .go-homepage-btn {
    box-sizing: border-box;
    width: 74px;
    height: 32px;
    line-height: 30px;
    font-size: 14px;
    margin: 16px auto 0;
    border: 1px solid var(--icon, #323233);;
    color: var(--icon, #323233);
    border-radius: 16px;

    a {
      display: block;
      color: var(--icon, #323233);
    }
  }
}
</style>
