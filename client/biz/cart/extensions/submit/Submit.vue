<template>
  <div v-if="isShopCartHasGoods" class="cart-bottom">
    <upper-bar v-show="!discountDetailVisible" />
    <div class="van-hairline--top">
      <div
        class="select-all"
        :class="{
          'discount-height': isShowDiscountBar,
        }"
      >
        <van-checkbox :value="isCheckedAll" @click="chooseAll" />
        <span @click="chooseAll">全选</span>
      </div>
      <div
        class="button-wrap"
        :class="{
          'discount-height': isShowDiscountBar,
        }"
      >
        <div v-if="!currentEditKdtId" class="total-price">
          <div class="price-desc">
            <!-- 有优惠明细时与合计在同一行显示 -->
            <span class="desc" v-show="isShowDiscountBar">
              {{ cartBottomData.desc }}
            </span>
            <span class="total-price__inner">
              <span class="price-label">合计：</span>
              <Price
                class="price-inner"
                :class="{
                  active: cartBottomData.totalPrice > 0,
                }"
                :price="cartBottomData.totalPrice"
              />
            </span>
          </div>
          <div class="discount-desc">
            <span class="desc" v-show="isShowDiscountBar">
              共优惠：￥{{ (cartBottomData.totalDiscount / 100).toFixed(2) }}
              <discount-detail
                style="display: inline-block"
                ref="discountDetail"
                :goods-total-price="cartBottomData.goodsTotalPrice"
                :total-price="cartBottomData.totalPrice"
                :total-discount="cartBottomData.totalDiscount"
                :promotion-details="cartBottomData.promotionDetails"
                @click-discount-detail="clickDiscountDetail"
              />
            </span>
            <span class="desc" v-show="!isShowDiscountBar">
              {{ cartBottomData.desc }}
            </span>
          </div>
        </div>
        <van-button
          square
          round
          class="bottom-button"
          :disabled="buttonDisabled"
          :loading="submitLoading"
          @click="onClickButton"
        >
          {{ buttonText }}
        </van-button>
      </div>
      <separate-buy
        v-if="separateBuy.show"
        :separate-buy="separateBuy"
        :buy-text="SUBMIT_SETTLE"
        @buy="buyWithGoods"
        @setSeparateBuy="setSeparateBuy"
      ></separate-buy>
    </div>
  </div>
</template>

<script>
import get from 'utils/get';
import each from 'utils/each';
import { Checkbox, Button, Toast } from 'vant';
import Price from '@biz/cart/components/Price';
import { jumpLink } from '@shared/common/helper';
import SeparateBuy from './components/separate-buy-popup/index';
import { SEPARATE_BUY_TYPE } from '@biz/cart/constant';
import DiscountDetail from './components/DiscountDetail';
import userInfoAuthorize from '@youzan/user-info-authorize';
import { action as ZanJSBridgeAction } from '@youzan/zan-jsbridge';
import { DRUG_QUANLITY_LIMIT } from '../../constant';
import { getVoucher } from '@biz/cart/api';
import { goBuy, separateGoodsForLogistics, separateGoodsForMixType } from './buy';
import ZNB from '@youzan/znb';
import { Logger } from 'common/log/logger';
import { appSkdSettleEvent } from '@biz/cart/utils';

const global = window._global;
const SUBMIT_DELETE = '删除';
const SUBMIT_RESERVE = '立即预约';
const SUBMIT_SETTLE = '去结算';
const SUBMIT_SETTLE_COUPON = '领券结算';

export default {
  name: 'submit',

  components: {
    [Button.name]: Button,
    [Checkbox.name]: Checkbox,
    Price,
    SeparateBuy,
    DiscountDetail,
  },

  props: {
    ctx: Object,
  },

  data() {
    return {
      shopCart: {},
      editMode: 'submit',
      isCheckedAll: false,
      checkedGoodsList: [],
      separateBuy: {},
      presentData: {
        IS_SELECT_PRESENT: 0,
        SELECTED_PRESENTS: [],
      },
      cartBottomData: {},
      // 是否已领取最优优惠券
      isReceiveBestCoupon: true,
      /* 不展示共优惠+优惠明细 */
      isShowDiscountBar: false,
      /* 优惠明细面板是否可见 */
      discountDetailVisible: false,
      submitLoading: false,
    };
  },

  computed: {
    isDrugShop() {
      return get(window, '_global.isDrugShop');
    },
    isShopCartHasGoods() {
      return (this.shopCart.goodsGroupList || []).some((goodsGroup) => {
        return !!goodsGroup.goodsList.length;
      });
    },

    currentEditKdtId() {
      return this.editMode === 'edit' ? this.shopCart.kdtId : 0;
    },
    buttonText() {
      return this.currentEditKdtId
        ? SUBMIT_DELETE
        : this.isDrugShop === true
        ? SUBMIT_RESERVE
        : this.isReceiveBestCoupon
        ? SUBMIT_SETTLE
        : SUBMIT_SETTLE_COUPON;
    },

    buttonDisabled() {
      const { currentEditKdtId, checkedGoodsList = [] } = this;
      return currentEditKdtId ? !checkedGoodsList.length : !this.cartBottomData.num;
    },

    isAlipayApp() {
      const isAlipayApp = get(window, '_global.miniprogram.isAlipayApp', false);
      return isAlipayApp;
    },

    isQQApp() {
      const isQQApp = get(window, '_global.miniprogram.isQQApp', false);
      return isQQApp;
    },

    isXhsApp() {
      const isXhsApp = get(window, '_global.miniprogram.isXhsApp', false);
      return isXhsApp;
    },

    hasSelectOverseaGoods() {
      return this.checkedGoodsList.some((item) => item.settlementRule?.settlementMark === 'HAITAO');
    },
  },
  watch: {
    shopCart(shopCart) {
      const result = {
        // 选中了几种规格
        num: 0,
        // 合计优惠后价格
        totalPrice: 0,
        desc: '不含运费',
        // 共优惠
        totalDiscount: 0,
        // 商品总价
        goodsTotalPrice: 0,
        // 优惠明细数组
        promotionDetails: [],
        // 是否优惠明细--使用新算法
        isShowPromotionDetail: false,
      };
      // 外部改变勾选商品，此时在validGoods组件里已进行请求，这边只需要取即可
      // 获取当前优惠金额
      const currentShop = shopCart;
      const selectedPreferencePrice = get(currentShop, 'selectedPreferencePrice', 0);
      // 兼容老版本 新增字段 仅当isShowPromotionDetail为true且优惠明细数组不为空时有用
      const selectedDiscountFee = get(currentShop, 'selectedDiscountFee', 0);

      result.promotionDetails = currentShop.promotionDetails || [];
      //    如果promotionDetails为空数组 则表示该订单没有任何优惠活动
      result.isShowPromotionDetail = currentShop.showPromotionDetail;
      // 值得注意的是，这里需要判断用户对于勾选的商品，是否已经领取最优的优惠券（单个订单只能用一张，不能叠加）
      this.calcBestCoupon(currentShop.promotionDetails || [], result.isShowPromotionDetail);
      // ---为了兼容老版本,根据isShowPromotionDetail来判断 但是比如限购一件 用户购了两件 却不返回优惠明细的情况，此时前端应该再走老逻辑
      result.totalDiscount =
        result.isShowPromotionDetail && !!result.promotionDetails.length
          ? selectedDiscountFee
          : selectedPreferencePrice;
      // 是否显示优惠明细按钮
      this.isShowDiscountBar = !!result.promotionDetails.length && result.isShowPromotionDetail;
      each(this.checkedGoodsList, (goods) => {
        result.num += 1;
        // 餐饮商品需要加上加料的价格
        const feedList = goods.ingredientInfoList || [];
        let feedsPrice = 0;
        if (feedList.length > 0) {
          feedsPrice = feedList.reduce((pre, feedItem) => {
            return pre + feedItem.payPrice;
          }, 0);
        }
        // 改动 有优惠明细后 使用originPrice算总价 为了兼容老版本
        if (this.isShowDiscountBar) {
          result.goodsTotalPrice += (goods.originPrice + feedsPrice) * goods.num;
        } else {
          result.goodsTotalPrice += (goods.payPrice + feedsPrice) * goods.num;
        }
        // 选择不含税海淘商品
        if (goods.tariffRule === 0) result.desc = '不含运费和进口税';
      });
      // 减掉优惠价格，并防止出现负数
      result.totalPrice = Math.max(result.goodsTotalPrice - result.totalDiscount, 0);
      this.cartBottomData = result;
    },
    // 优惠明细按钮曝光次数 埋点
    isShowDiscountBar(val) {
      val &&
        Logger.log({
          et: 'view', // 事件类型
          ei: 'promotion_detail_button_view', // 事件标识
          en: '优惠明细按钮曝光次数', // 事件名称
          params: {}, // 事件参数
        });
    },
  },
  // 领券结算按钮曝光次数 埋点
  isReceiveBestCoupon(val) {
    val &&
      Logger.log({
        et: 'view', // 事件类型
        ei: 'get_coupons_button_view', // 事件标识
        en: '领券结算按钮曝光次数', // 事件名称
        params: {}, // 事件参数
      });
  },
  created() {
    this.unwatchShopCart = this.ctx.watch('shopCart', (val = []) => {
      this.shopCart = val;
    });
    this.unwatchEditMode = this.ctx.watch('editMode', (val = []) => {
      this.editMode = val;
    });
    this.unwatchIsCheckedAll = this.ctx.watch('isCheckedAll', (val = []) => {
      this.isCheckedAll = val;
    });
    this.unwatchCheckedGoodsList = this.ctx.watch('checkedGoodsList', (val = []) => {
      this.checkedGoodsList = val;
    });
    this.unwatchPresentData = this.ctx.watch('presentData', (val = {}) => {
      this.presentData = val;
    });
  },

  mounted() {
    document.body.style.paddingBottom = `${this.$el.clientHeight}px`;
  },

  destroyed() {
    this.unwatchShopCart && this.unwatchShopCart();
    this.unwatchEditMode && this.unwatchEditMode();
    this.unwatchIsCheckedAll && this.unwatchIsCheckedAll();
    this.unwatchCheckedGoodsList && this.unwatchCheckedGoodsList();
    this.unwatchPresentData && this.unwatchPresentData();
  },

  methods: {
    setSeparateBuy(params) {
      this.separateBuy = params;
    },
    /* promotionDetails优惠明细字段list是根据appType优惠类型进行区分，
    优惠券只会返回最优的优惠券，比如优惠券和满减活动，那么list里就两个元素。
    PS:如果用户未领取且无可领取的优惠券的时候，不返回优惠券对象
    然后根据优惠券对象的benefitId判断是否已领取最优优惠券，未领取则benefitId为null
    如果领了，则用户已领取最优的优惠券，底部显示【去结算】否则显示【领券结算】
    拆单情况下 均显示’去结算‘  因为拆单不好算哪个商品对应哪个优惠券
     */
    calcBestCoupon(promotionDetails = [], isShowPromotionDetail = false) {
      const coupon = promotionDetails.filter((item) => item.appType === 105);
      if (coupon.length) {
        this.isReceiveBestCoupon = coupon[0].benefitId || !isShowPromotionDetail;
      } else {
        this.isReceiveBestCoupon = true;
      }
    },
    async onClickButton() {
      if (!this.isReceiveBestCoupon) {
        try {
          await userInfoAuthorize.open({
            scene: 'get_coupon', // 领取优惠券
          });
          // 授权成功后
        } catch (e) {
          // 授权失败，直接返回
          return;
        }
      }
      if (this.currentEditKdtId) {
        this.ctx.event.emit('deleteCartItems');
      } else {
        // 结算
        // 支付宝 QQ 小程序中存在 周期购商品就拦截
        if (this.isAlipayApp || this.isQQApp || this.isXhsApp) {
          let hasPeriodBuyGoods = false;
          this.checkedGoodsList.forEach((goods) => {
            if (+goods.goodsType === 24) {
              hasPeriodBuyGoods = true;
            }
          });
          if (hasPeriodBuyGoods) {
            return Toast('此渠道暂不支持结算周期购商品');
          }
        }
        // 判断处方药数量是否超出标准
        const isDrugGoodsValid = this.isDrugGoodsValid();
        if (isDrugGoodsValid === false) return;

        try {
          const isGuarantee = this.checkedGoodsList.some((goods) => goods.yzGuarantee);

          if (this.isDrugShop) {
            Logger?.log({
              et: 'click', // 事件类型
              ei: 'click_appointment', // 事件标识
              en: '去预约', // 事件名称
              pt: 'cart', // 页面类型
            });
          } else {
            Logger?.log({
              et: 'click', // 事件类型
              ei: 'click_settlement', // 事件标识
              en: '结算_点击', // 事件名称
              pt: 'cart', // 页面类型
              params: {
                guarantee_on: !!isGuarantee,
              },
            });
          }
          if (this.buttonText === SUBMIT_SETTLE_COUPON) {
            Logger &&
              Logger.log({
                et: 'click', // 事件类型
                ei: 'get_coupons_button_click', // 事件标识
                en: '领券结算按钮点击次数', // 事件名称
                params: {}, // 事件参数
              });
          }
        } catch (error) {
          console.log(error);
        }
        this.buyWithGoods({
          list: this.checkedGoodsList,
        });
      }
    },

    isDrugGoodsValid() {
      let isValid = true;
      const drugMap = {}; // spu级别药品的数量
      this.checkedGoodsList.forEach((item) => {
        const isPrescriptionDrug = item?.bizExtension?.cartBizMark?.IS_PRESCRIPTION_DRUG === '1';
        if (isPrescriptionDrug) {
          drugMap[item.goodsId] = (drugMap[item.goodsId] || 0) + item.num;
          if (drugMap[item.goodsId] > DRUG_QUANLITY_LIMIT) {
            Toast(`为保障用药安全，每种处方药不得超过${DRUG_QUANLITY_LIMIT}件`);
            isValid = false;
          }
        }
      });
      if (Object.keys(drugMap).length > DRUG_QUANLITY_LIMIT) {
        Toast(`一笔订单不得超过${DRUG_QUANLITY_LIMIT}种处方药`);
        isValid = false;
      }
      return isValid;
    },

    hasMemberGoods(){
      return !!this.checkedGoodsList.find(item => item.activityTag === '会员折扣')
    },

    chooseAll() {
      // 只通知
      this.ctx.event.emit('toggleCheckedAll');
      // 点击全选的时候 关闭优惠明细弹窗
      if (this.$refs.discountDetail) {
        this.$refs.discountDetail.visible = false;
      }
    },

    async buyWithGoods({ list = [], expressType } = {}) {
      this.submitLoading = true;
      let tmp = separateGoodsForMixType(list);
      if (tmp.hasCourse) {
        Logger &&
          Logger.log({
            et: 'click', // 事件类型
            ei: 'cart_settlement_click', // 事件标识
            en: '教育商品购物车结算', // 事件名称
          });
      }
      if (tmp.needSeparate) {
        this.separateBuy = {
          show: true,
          type: tmp.hasCourse ? SEPARATE_BUY_TYPE.COURSE_MIX_TYPE : SEPARATE_BUY_TYPE.MIX_TYPE,
          data: tmp.data,
        };
        this.submitLoading = false;
        return;
      }
      tmp = separateGoodsForLogistics(list);
      if (tmp.needSeparate) {
        this.separateBuy = {
          show: true,
          type: SEPARATE_BUY_TYPE.LOGISTICS,
          data: tmp.data,
        };
        this.submitLoading = false;
        return;
      }
      let preToastDesc = await this.sendBestCoupon();
      this.hasMemberGoods() ? ( preToastDesc = '已默认选择最佳优惠' ) : null; 
      goBuy(list, {
        kdtId: global.kdtId,
        isNewHope: global.isNewhopeKdt,
        // 用户选中分开结算的物流方式
        expressType,
        presentData: this.presentData,
        preToastDesc,
      })
        .then((res) => {
          appSkdSettleEvent(this.checkedGoodsList, this.shopCart.goodsGroupList);
          if (this.isProcess) {
            if (this.separateBuy && this.separateBuy.show) {
              this.separateBuy.show = false;
            }
          } else if (window._global.platform === 'youzanmars') {
            ZanJSBridgeAction.gotoWebview({
              url: res.payUrl,
              page: 'web',
            });
          } else if (this.isAlipayApp || this.isQQApp || this.isXhsApp) {
            ZNB.navigate({
              aliappUrl: `/pages/web-view/index?src=${encodeURIComponent(res.payUrl)}`,
              qqUrl: `/pages/web-view/index?src=${encodeURIComponent(res.payUrl)}`,
              xhsUrl: `/pages/web-view/index?src=${encodeURIComponent(res.payUrl)}`,
            });
          } else {
            jumpLink(res.payUrl);
          }
          setTimeout(() => this.submitLoading = false, 1500);
        })
        .catch((e) => {
          console.log(e);
          this.submitLoading = false;
        });
    },
    // 如果未领券 需要领券
    async sendBestCoupon() {
      if (!this.isReceiveBestCoupon) {
        return new Promise((resolve) => {
          const coupon = this.cartBottomData.promotionDetails.filter((item) => item.appType === 105);
          getVoucher({
            activityId: coupon[0].activityIds[0],
            bizName: 'shopping_cart',
            source: 'shopping_cart_auto_take',
          })
            .then(() => {
              resolve('已为你领取1张优惠券，下单享优惠');
            })
            .catch((res) => {
              resolve(res.msg || '领券失败');
            });
        });
      }
    },
    // 优惠明细按钮点击次数 埋点
    clickDiscountDetail(visible) {
      this.discountDetailVisible = visible;
      visible &&
        Logger.log({
          et: 'click', // 事件类型
          ei: 'promotion_detail_button_click', // 事件标识
          en: '优惠明细按钮点击次数', // 事件名称
          params: {}, // 事件参数
        });
    },
  },
};
</script>

<style scoped lang="scss">
.cart-bottom {
  position: fixed;
  z-index: 100;
  bottom: 0;
  width: 100%;
  box-sizing: border-box;
  background: #fff;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);

  .warning-bar {
    width: 100%;
    height: 35px;
    background: #fff7cc;
    color: #f60;
    font-size: 12px;
    text-align: center;
    line-height: 35px;
  }

  .select-all {
    display: inline-block;
    height: 50px;
    line-height: 50px;
    font-size: 14px;
    color: #333;
    margin-left: 10px;
  }

  .button-wrap {
    float: right;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 50px;
    padding-right: 12px;

    .van-button--disabled {
      color: #fff;
      opacity: 1;
      background: #c8c9cc !important;
    }
  }
  .discount-height {
    height: 56px;
    line-height: 56px;
  }
  .total-price {
    box-sizing: border-box;
    font-size: 12px;
    line-height: 16px;
    color: #999;
    text-align: right;

    .desc {
      font-size: 12px;
      color: #969799;
      margin: 0;
    }
  }

  .price-label {
    font-size: 14px;
    color: #323233;
  }

  .price-inner {
    line-height: 20px;
    color: #999;

    &.active {
      color: var(--price, #323233);
    }
  }

  .bottom-button {
    height: 36px;
    line-height: 36px;
    min-width: 96px;
    border-radius: 18px;
    margin-left: 12px;
    border: none;
    background: var(--main-bg, #323233);
    color: var(--main-text, #fff);
  }

  .van-checkbox {
    float: left;
    height: 100%;
    margin-right: 5px;
  }
}

.clearfix::after {
  height: 0;
  content: '';
  visibility: hidden;
  clear: both;
  display: block;
}
</style>
