import buildUrl from '@youzan/utils/url/buildUrl';
import cloneDeep from '@youzan/utils/object/clone-deep';
import { TAG_CONF, CRM_OFFLINE_TYPE } from './constant';

// 计算标签
const computeTagList = (orderFlag = {}, goodsItem = {}) => {
  const tagList = [];
  orderFlag.isEnjoyBuy && tagList.push(TAG_CONF.enjoyBuy);
  orderFlag.isFcode && tagList.push(TAG_CONF.fcode);
  orderFlag.isKnowledgeGift && tagList.push(TAG_CONF.knowledgeGift);
  (orderFlag.isPresaleOrder || orderFlag.isPresale) && goodsItem?.preSale === '1' && tagList.push(TAG_CONF.presale);
  orderFlag.isGroup && tagList.push(TAG_CONF.group);
  orderFlag.isPeerpay && tagList.push(TAG_CONF.peerpay);
  orderFlag.isLotteryGroup && tagList.push(TAG_CONF.lotteryGroup);
  orderFlag.isPoints && tagList.push(TAG_CONF.points);
  orderFlag.isGiftOrder && tagList.push(TAG_CONF.giftOrder);

  goodsItem.isPresent && tagList.push(TAG_CONF.present);
  goodsItem.isPlusBuy && tagList.push(TAG_CONF.plusBuy);
  goodsItem.isUseGoodsExchangeCoupon && tagList.push(TAG_CONF.goodsExchange);
  goodsItem.isRecommendGift && tagList.push(TAG_CONF.recommendGift);

  return tagList;
};

export const lambdaFormatter = (ctx, listItem = {}, { openAppConfig = {} }) => {
  const listItemClone = cloneDeep(listItem);
  const firstOrderItem =
    listItemClone.orderItems && listItemClone.orderItems[0];

  listItemClone.orderItems.forEach((orderItem) => {
    // 订单详情url扩展点
    if (ctx.lambdas.detailUrlFormatter) {
      orderItem.detailUrl = ctx.lambdas.detailUrlFormatter(orderItem.detailUrl);
    }

    // 初始化商品标签
    (orderItem.items || []).forEach((goods) => {
      goods.tagList = computeTagList(firstOrderItem.orderExtra, goods);
    });
  });

  // 非CRM线下门店订单支持点击店铺跳转
  if (listItemClone.crmOfflineType !== CRM_OFFLINE_TYPE.OFFLINE) {
    // 点击店铺名称跳转url扩展点
    listItemClone.homeUrl = buildUrl(
      `${_global.url.wap}/showcase/homepage?kdt_id=${listItemClone.kdtId}${_global.orderEnterShopPolicy ? '&shopAutoEnter=1' : ''}`,
      '',
      listItemClone.kdtId
    );
    if (ctx.lambdas.shopNameFormatter) {
      listItemClone.homeUrl = ctx.lambdas.shopHomeUrlFormatter(
        listItemClone.homeUrl
      );
    }
  }

  // 店铺名称格式化扩展点
  if (ctx.lambdas.shopNameFormatter) {
    listItemClone.shopName = ctx.lambdas.shopNameFormatter(
      listItemClone.shopName
    );
  } else if (openAppConfig && openAppConfig.hideShopLink) {
    listItemClone.shopName = '';
  }

  return listItemClone;
};
