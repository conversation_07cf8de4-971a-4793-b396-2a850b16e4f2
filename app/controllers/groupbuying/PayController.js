const _ = require('lodash');
const BaseController = require('../base/BaseController');
const BuyerTradeService = require('../../services/groupbuying/BuyerTradeService');
const MyProfitService = require('../../services/groupbuying/MyProfitService');
const GroupBuyJoinSettingService = require('../../services/groupbuying/GroupBuyJoinSettingService');

class PayController extends BaseController {
  async init() {
    const options = {
      initCopyrightFooter: true,
    };
    await super.init(options);
  }

  async getGroupBuySettings(ctx) {
    const groupBuySettingService = new GroupBuyJoinSettingService(ctx);
    const groupBuySettings = await groupBuySettingService.getSettingV2(ctx.kdtId);
    ctx.json(0, '', groupBuySettings);
  }

  async getIndexHtml(ctx) {
    const { kdtId, bookKey, activityId, alias, headerBuyerId } = ctx.query;
    ctx.kdtId = kdtId;
    const mpData = ctx.getState('mpData') || {};
    const buyerTradeService = new BuyerTradeService(ctx);
    const myProfitService = new MyProfitService(ctx);
    const groupBuySettingService = new GroupBuyJoinSettingService(ctx);
    const [prepareData, groupHeaderDetail, groupBuySettings] = await Promise.all([
      buyerTradeService.prepare({
        bookKey,
        kdtId,
        ...this.buyer,
      }),
      myProfitService.headerInfo({
        headerBuyerId,
        kdtId,
      }),
      groupBuySettingService.getSettingV2(ctx.kdtId),
    ]);
    const security = {
      isYzSecured: _.get(prepareData, 'displayConfig.isSecuredTransActions', false),
    };
    ctx.setShare({
      notShare: true,
    });
    ctx.setGlobal({
      kdtId,
      shopName: mpData.shopName,
      prepareData: escape(JSON.stringify(prepareData)),
      groupHeaderDetail,
      groupBuySettings,
      activityId,
      alias,
      headerBuyerId,
      security,
      spm: {
        logType: 'cgroupon',
        logId: activityId,
      },
    });
    await ctx.render('groupbuying/pay.html');
  }
}

module.exports = PayController;
