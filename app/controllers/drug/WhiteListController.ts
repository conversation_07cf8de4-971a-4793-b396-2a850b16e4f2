/* eslint-disable @typescript-eslint/no-explicit-any */
import BaseController from '../base/BaseController';
import { isInGrayReleaseByKdtId } from '../../lib/WhiteListUtils';

import { Context } from 'astroboy';

class WhiteListController extends BaseController {
  async isInWhiteList(ctx: Context) {
    const result = await isInGrayReleaseByKdtId(
      ctx,
      { namespace: 'wsc-h5-trade.gray-release', key: 'isDrugShopUseWebview' },
      ctx.kdtId
    );
    ctx.json(0, '', result);
  }

  // 不需要实名认证商家白名单
  async isInRealNameWhiteList(ctx: Context) {
    const shopMetaInfo = await this.callService('iron-base/shop.ShopMetaReadService', 'getShopMetaInfo', ctx.kdtId);
    const { rootKdtId = ctx.kdtId } = shopMetaInfo;
    const result = await isInGrayReleaseByKdtId(
      ctx,
      { namespace: 'wsc-h5-trade.gray-release', key: 'isNoNeedRealNameWhiteList' },
      rootKdtId
    );
    ctx.json(0, '', result);
  }
}

export = WhiteListController;
