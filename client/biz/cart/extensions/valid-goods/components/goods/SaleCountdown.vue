<template>
  <div class="sale-base">
    <span v-for="item in showCountdown" :key="item.type">
      <span class="sale-base__countdown" :style="`width: ${item.width}`">
        {{item.value}}
      </span>
      <span>{{ item.text }}</span>
    </span>
    <span>后开售</span>
  </div>
</template>

<script>
import CountDown from 'common/count-down';
import padZero from 'utils/string/pad-zero';

const TIME_MAP = {
  DAY: 'day',
  HOUR: 'hour',
  MIN: 'min',
  SEC: 'sec',
};
export default {
  name:"sale-countdown",

  props: {
    startSoldTime: Number,
  },

  data(){
    return {
      countdownData: [
        {
          type: TIME_MAP.DAY,
          value: '',
          text: '天',
          width: '0',
        },
        {
          type: TIME_MAP.HOUR,
          value: '',
          text: '时',
          width: '0',
        },
        {
          type: TIME_MAP.MIN,
          value: '',
          text: '分',
          width: '0',
        },
        {
          type: TIME_MAP.SEC,
          value: '',
          text: '秒',
          width: '0',
        },
      ],
      showCountdown:[],
    }
  },

  mounted(){
    const nowTime = new Date().getTime() / 1000;
    const successTime = this.startSoldTime;

    new CountDown({
      seconds: successTime > nowTime ? successTime - nowTime : 0,
      onTimeChange: ({ day = 0, hour = 0, min = 0, sec = 0 }) => {
        this.countdownData = this.countdownData.map((item) => {
          let value = '';
          switch (item.type) {
            case TIME_MAP.DAY:
              value = String(day);
              break;
            case TIME_MAP.HOUR:
              value = String(padZero(hour));
              break;
            case TIME_MAP.MIN:
              value = String(padZero(min));
              break;
            case TIME_MAP.SEC:
              value = String(padZero(sec));
              break;
          }
          return {
            ...item,
            width: `${value.length * 8}px`,
            value,
          };
        });
        if (day > 0) {
          this.showCountdown = this.countdownData.slice(0);
        } else if (hour === 0 && min === 0 && sec === 0) {
          this.$emit('hideCountdown');
        } else {
          const countdown = [hour, min, sec];
          for (let i = 0; i < 3; i++) {
            if (countdown[i] !== 0) {
              this.showCountdown = this.countdownData.slice(i + 1);
              break;
            }
          }
        }
      },
    }).run();
  },
}
</script>

<style scoped lang="scss">
.sale-base {
  font-family: PingFangSC-Regular;
  color: #FF720D;
  font-size: 10px;
  line-height: 14px;
  letter-spacing: 0;

  &__countdown {
    width: 16px;
    text-align: center;
    display: inline-block;
  }
}
</style>