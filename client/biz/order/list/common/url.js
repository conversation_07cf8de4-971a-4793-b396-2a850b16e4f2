import get from 'utils/get';
import args from 'utils/browser/args';
import queryString from 'query-string';
import { Logger } from 'common/log/logger';

// 在 URL 上添加统计参数
export function addAnalyticsParams(url, query = {}) {
  const context = (Logger && get(Logger.getGlobal(), 'context')) || {};
  return url
    ? args.add(url, {
        dc_ps: context.dc_ps || '',
        from_source: context.from_source || '',
        from_params: context.from_params || '',
        ...query,
      })
    : '';
}

export function isDrugList() {
  const search = window.location.search;
  const { type } = queryString.parse(search);
  return type === 'drug';
}
