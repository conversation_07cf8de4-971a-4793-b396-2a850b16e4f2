/**
 * 零售订单进度查询接口请求参数
 */
export interface IShelfOrderProgressRequest {
  /** 订单号 */
  orderNo?: string;
  /** 请求来源 */
  retailSource?: string;
  /** 当前店铺id */
  kdtId?: number;
  /** 当前用户id */
  adminId?: number;
}

/**
 * 零售订单进度查询接口响应数据
 */
export interface IShelfOrderProgressResponse {
  /** 订单号 */
  orderNo?: string;
  /** 0:快递,1:自提,2:同城 */
  bizType?: number;
  /** 是否需要展示进度 */
  isNeedDisplay?: boolean;
  /** 当前订单待制作商品数 */
  currentGoodsNum?: number;
  /** 待制作订单数 */
  orderNum?: number;
  /** 商品制作状态：1，等待制作 2，制作中 3，制作完成 4，已发货 5，已收货  1，2，3是自提时的状态；1，4，5是外送的状态 */
  goodsMakingStatus?: number;
  /** 制作总时长 */
  productTime?: number;
  /** 待制作商品数 */
  goodsNum?: number;
  /**  是否展示待制作杯数 */
  queueProcessNumSwitch?: boolean;
  /** 是否展示预计等待时间 */
  queueProcessTimeSwitch?: boolean;
  /** 单位 */
  queueShowUnit?: string;
}

/**
 * 订单进度数据
 */
export interface IOrderProgressData {
  /** 订单号 */
  orderNo?: string;
  /** 订单状态 */
  orderStatus?: string;
  /** 进度状态 */
  progressStatus?: string;
  /** 进度描述 */
  progressDesc?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 扩展信息 */
  extInfo?: Record<string, any>;
}
