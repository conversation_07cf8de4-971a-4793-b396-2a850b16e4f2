<template>
  <div v-if="showCouponBar">
    <div class="coupon-wrapper" @click="setPopupVisible(true)">
      <span class="coupon-wrapper__title">领券</span>
      <div>
        <CouponTag v-for="coupon in topCouponList" :key="coupon.id">{{
          coupon.use_threshold_and_value_copywriting
        }}</CouponTag>
      </div>
      <van-icon name="arrow" />
    </div>
    <CouponPopup
      :show-popup="showPopup"
      :coupon-list="avlCouponList"
      @close="setPopupVisible(false)"
      @sendCoupon="handleSendCoupon"
    />
  </div>
</template>

<script>
import userInfoAuthorize from '@youzan/user-info-authorize';
import { Toast, Icon } from 'vant';
import CouponTag from './components/CouponTag';
import CouponPopup from './components/CouponPopup';
import { getAvlCouponList, sendCoupon } from '@biz/cart/api';
import { IS_LOGIN, BUYER_ID } from '@biz/cart/constant';
import { Logger } from 'common/log/logger';

// 匹配双字节 一个中文顶2个英文字符
// eslint-disable-next-line
const getRealLen = (str) => str.replace(/[^\x00-\xff]/g, '__').length;

export default {
  name: 'GoodsCoupon',

  components: {
    [Icon.name]: Icon,
    [CouponTag.name]: CouponTag,
    [CouponPopup.name]: CouponPopup,
    /* CouponPopup: () => import('./components/CouponPopup') */
  },

  props: ['ctx'],

  data() {
    return {
      kdtId: null,
      shopCart: {},
      couponList: [],
      showPopup: false,
      sendedCoupons: [],
      showCouponBar: false,
      isCouponFirstPopup: true,
      isCounponBarLogged: false,
      canUseCouponAddOn: true,
    };
  },

  computed: {
    avlCouponList() {
      return this.couponList.map((coupon) => ({
        ...coupon,
        isAvailable: !this.sendedCoupons.every((activityId) => activityId !== coupon.id),
      }));
    },
    topCouponList() {
      const MAX_COUPON_COUNT = 3;

      let displayCouponList = this.couponList.slice(0, MAX_COUPON_COUNT);
      // 3个的长度如果超过32的话，就隐藏第3个优惠券; 一个中文顶2个英文
      if (this.couponList.length >= MAX_COUPON_COUNT) {
        const cnt = displayCouponList.reduce((pre, current) => {
          return pre + current.use_threshold_and_value_copywriting;
        }, '');

        const MAX_DISPLAY_LENGTH = 32;
        if (getRealLen(cnt) > MAX_DISPLAY_LENGTH) {
          displayCouponList = displayCouponList.slice(0, MAX_COUPON_COUNT - 1);
        }
      }
      return displayCouponList;
    },
  },

  watch: {
    kdtId(kdtId) {
      getAvlCouponList({ kdtId, limit: 20 }).then((res) => {
        this.couponList = res.data;
      });
    },
    shopCart() {
      this.computeShowCouponBar();
    },
    couponList() {
      this.computeShowCouponBar();
    },
  },

  mounted() {
    this.unwatch = this.ctx.watch('kdtId', (val) => {
      this.kdtId = val;
    });

    this.unwatchShopCart = this.ctx.watch('shopCart', (val) => {
      this.shopCart = val;
    });
    this.unwatchCanUseCouponAddOn = this.ctx.watch('canUseCouponAddOn', (val) => {
      this.canUseCouponAddOn = val;
    });
  },

  destroyed() {
    this.unwatch && this.unwatch();
    this.unwatchShopCart && this.unwatchShopCart();
    this.unwatchCanUseCouponAddOn && this.unwatchCanUseCouponAddOn();
  },

  methods: {
    setPopupVisible(visible) {
      if (!visible && this.sendedCoupons.length === 0) {
        Toast('你有可用优惠券还未领取');
      }

      if (visible && this.isCouponFirstPopup) {
        this.isCouponFirstPopup = false;
        Logger.log({
          et: 'click', // 事件类型
          ei: 'take_coupon_at_cart_click', // 事件标识
          en: '领券bar点击', // 事件名称
          params: {
            component: 'takeCouponBar',
            kdt_id: this.kdtId,
            buyer_id: BUYER_ID,
            activity_type: 'coupon',
            activity_id: this.couponList.map((coupon) => coupon.id).join(','),
          }, // 事件参数
        });
      }
      this.showPopup = visible;
    },
    handleSendCoupon(coupon) {
      if (!IS_LOGIN) {
        return userInfoAuthorize
          .open({
            authTypeList: ['mobile'], // 只手机号授权
          })
          .then(() => {
            window.location.reload();
          });
      }
      sendCoupon({ activityId: coupon.id })
        .then((res) => {
          if (res.code === 0) {
            Toast('领取成功');
            this.sendedCoupons = [...this.sendedCoupons, coupon.id];
          } else {
            Toast(res.msg || '领取失败');
          }
        })
        .catch((err) => {
          Toast(err.msg || '领取失败');
        });
    },
    computeShowCouponBar() {
      const hasValidGoods = (this.shopCart.goodsGroupList || []).some((goodsGroup) => !!goodsGroup.goodsList.length);
      this.showCouponBar = hasValidGoods && this.couponList.length > 0;

      if (this.showCouponBar && !this.isCounponBarLogged) {
        this.isCounponBarLogged = true;

        Logger.log({
          et: 'view', // 事件类型
          ei: 'component_view', // 事件标识
          en: '领券bar曝光', // 事件名称
          params: {
            component: 'takeCouponBar',
            kdt_id: this.kdtId,
            buyer_id: BUYER_ID,
            activity_type: 'coupon',
            activity_id: this.couponList.map((coupon) => coupon.id).join(','),
          },
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.coupon-wrapper {
  background: #fff;
  margin: 0 12px 12px;
  padding: 0 12px;
  height: 45px;
  line-height: 45px;
  border-radius: 8px;
  font-size: 0;

  &__title {
    font-size: 14px;
    color: #323233;
    margin-right: 8px;
    vertical-align: middle;
  }

  & > div {
    display: inline-block;
  }

  .van-icon {
    font-size: 14px;
    float: right;
    top: 50%;
    transform: translateY(-50%);
  }

  .coupon-tag {
    margin-right: 8px;
    vertical-align: middle;

    &:last-child {
      margin-right: 0;
    }
  }
}
</style>
