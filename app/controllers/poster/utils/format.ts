import { COLOR_MAP } from '../../../constants/global-theme';

// 还需要对透明度
export function formatPosterThemeColor(
  themeType = 'default-theme',
  opacity = 1
) {
  let color = COLOR_MAP[themeType];

  if (typeof color === 'object') {
    color = color.mainColor;
  }

  return hexToRgba(color, opacity);
}

// hex -> rgba
function hexToRgba(hex: string, opacity: number) {
  return (
    'rgba(' +
    parseInt('0x' + hex.slice(1, 3)) +
    ',' +
    parseInt('0x' + hex.slice(3, 5)) +
    ',' +
    parseInt('0x' + hex.slice(5, 7)) +
    ',' +
    opacity +
    ')'
  );
}