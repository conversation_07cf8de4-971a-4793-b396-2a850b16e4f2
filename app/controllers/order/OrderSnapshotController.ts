import BaseController from '../base/BaseController';
import OrderSnapshotService from '../../services/order/OrderSnapshotService';

class OrderSnapshotController extends BaseController {
  async init() {
    await super.init({
      validKdtId: true,
      initCopyrightFooter: true,
    });
  }

  async getIndexHtml() {
    const { ctx } = this;
    const { order_no: orderNo = 0, kdt_id: kdtId } = ctx.query;

    const orderSnapshotService = new OrderSnapshotService(ctx);

    const orderSnapshotList = await orderSnapshotService.orderSnapshot({
      kdtId,
      orderNo,
    });

    ctx.setGlobal({
      orderSnapshotList,
    });

    // 日志埋点模型
    this.setSpm('goodsHistoryList', kdtId);

    await ctx.render('order/snapshot.html');
  }
}

export = OrderSnapshotController;
