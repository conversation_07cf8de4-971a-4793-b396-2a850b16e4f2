<template>
  <div :style="themeCss" v-if="status == 'ALL_PAID'">
    <!-- 返现和返储值金 -->
    <van-cell
      v-if="isNotNull(cash)"
      :border="false"
      title-class="award__item-left"
      value-class="award__item-right"
      class="award__item"
      @click="gotoUrl('cash', cash.url)"
    >
      <template slot="title">
        <span class="award__icon-wrap">
          <i class="award__icon-bg award__theme-background" />
          <i class="award__icon award__theme-color">返</i>
        </span>
        <span>
          恭喜获得返现
          <span v-if="cash.money > 0" class="award__num">
            <b>{{cash.money | formatMoney }}元</b>
          </span>
          <span v-if="cash.money > 0 && cash.valueCard > 0">
            以及
          </span>
          <span v-if="cash.valueCard > 0" class="award__num">
            <b>{{ cash.valueCard | formatMoney }}元储值金</b>
          </span>
        </span>
      </template>
      <template slot="default">
        <button
          v-if="cash.needActivated"
          class="award__btn award__theme-color"
          @click="doActivate('cash', cash.url)"
        >
          立即激活
        </button>
        <van-icon v-else name="arrow" />
      </template>
    </van-cell>
    <!-- 积分奖励 -->
    <van-cell
      v-if="isNotNull(creditInfo)"
      :border="false"
      title-class="award__item-left"
      value-class="award__item-right"
      class="award__item"
      @click="gotoUrl('creditInfo',creditInfo.url)"
    >
      <template slot="title">
        <span class="award__icon-wrap">
          <i class="award__icon-bg award__theme-background" />
          <i class="award__icon award__theme-color">积</i>
        </span>
        <span>
          恭喜获得
          <span class="award__num">
            {{ creditInfo.num }}{{ creditInfo.name || '积分' }}
          </span>
        </span>
      </template>
      <template slot="default">
        <van-icon name="arrow" />
      </template>
    </van-cell>
    <!-- 权益卡奖励 -->
    <van-cell
      v-if="isNotNull(memberCard)"
      :border="false"
      title-class="award__item-left"
      value-class="award__item-right"
      class="award__item"
      @click="gotoUrl('memberCard', memberCard.url)"
    >
      <template slot="title">
        <span class="award__icon-wrap">
          <i class="award__icon-bg award__theme-background" />
          <i class="award__icon award__theme-color">卡</i>
        </span>
        <span>
          恭喜获得
          <span class="award__blod">
            {{ joinNames(memberCard.names, '权益卡') }}
          </span>
        </span>
      </template>
      <template slot="default">
        <button
          v-if="memberCard.needActivated"
          class="award__btn award__theme-color"
          @click="doActivate('memberCard', memberCard.url)"
        >
          立即激活
        </button>
        <van-icon v-else name="arrow" />
      </template>
    </van-cell>
  </div>
</template>

<script>
import { mapData } from '@youzan/ranta-helper-vue';
import { Cell, Icon } from 'vant';
import format from '@youzan/utils/money/format';
import { isNotNull } from '../../util';
import buildUrl from '@youzan/utils/url/buildUrl';
import get from 'lodash/get';

export default {
  data() {
    return {
      payResult: {},
      themeColors: {},
      kdtId: '',
      themeCss: '',
      isLogView: false,
      clickLoggerMsg: [
        { type: 'creditInfo', ei: 'click_fanjifen_xiangqing', en: '点击返积分'},
        { type: 'memberCard', ei: 'click_detail_quanyika', en: '点击返权益卡'},
        { type: 'cash', ei: 'click_detail_fanchuzhi', en: '点击返储值金/返现'},
        // { type: 'salesman', ei: 'click_salesmancenter', en: '点击成为销售'}
      ]
    };
  },

  filters: {
    formatMoney(value) {
      return format(value, true);
    },
  },

  components: {
    [Cell.name]: Cell,
    [Icon.name]: Icon
  },

  watch: {
    hasAward(val) {
      this.$nextTick(() => {
        this.ctx.data.paidAwardHeight = (this.$refs['paid-award'] && this.$refs['paid-award'].offsetHeight) || 0;
     })
    }
  },

  computed: {
    status() {
      return get(this.payResult, 'payResultVO.payState');
    },
    award() {
      return { cashInfo: {}, credit: {}, hasMemberCard: false, memberCards: [], ...this.payResult.shopPayResult };
    },
    cash() {
      const { cashInfo } = this.award;
      const cash = {}
      // 返现
      if (isNotNull(cashInfo)) {
        // 返现金
        if (cashInfo.cash && typeof cashInfo.cash === 'number') {
          if (typeof cash.money === 'undefined') cash.money = 0;
          cash.money += +cashInfo.cash;
          cash.url = buildUrl(
            `https://h5.youzan.com/wscump/cashback/detail?kdt_id=${this.kdtId}&orderNo=${this.orderNo}`,
            'h5',
            this.kdtId
          );
        }
        // 返储值金
        if (cashInfo.valueCard && typeof cashInfo.valueCard === 'number') {
          if (typeof cash.money === 'undefined') cash.valueCard = 0;
          cash.valueCard += +cashInfo.valueCard;
        }
      }
      return cash;
    },
    creditInfo() {
      const { credit } = this.award;
      const creditInfo = {};
      // 积分
      if (isNotNull(credit)) {
        if (typeof creditInfo.num === 'undefined') creditInfo.num = 0;
        creditInfo.num += +credit.credit;
        creditInfo.name = creditInfo.name || credit.creditName;
        creditInfo.url = buildUrl(
          `https://h5.youzan.com/wscump/pointstore/pointcenter?kdt_id=${this.kdtId}`,
          'h5',
          this.kdtId
        );
      }
      return creditInfo;
    },
    memberCard() {
      const { hasMemberCard, memberCards = [] } = this.award;
      const memberCard = {};
      // 权益卡信息
      if (isNotNull(hasMemberCard)) {
        if (typeof memberCard.names === 'undefined') memberCard.names = [];
        memberCard.names.push(memberCards.map((item) => item.name).join('、'));
        memberCard.url = memberCard.url || memberCards[0] ? memberCards[0].url : '';
        memberCard.needActivated =
          memberCard.needActivated || memberCards[0]
            ? memberCards[0].needActivated
            : false;
      }
      return memberCard;
    },
    hasAward() {
      const result =  (
        isNotNull(this.cash) ||
        isNotNull(this.creditInfo) ||
        isNotNull(this.memberCard)
      );
      this.ctx.data.hasAward = result;
      return result;
    },
  },

  methods: {
    isNotNull(obj) {
      return !!obj && JSON.stringify(obj) !== "{}";
    },
    gotoUrl(type, url) {
      this.clickLoggerMsg.map(item => {
        if (type === item.type) {
          this.ctx.process.invoke('logger',
            'click',
            item.ei,
            item.en
          );
        }
      })
      this.ctx.process.invoke('navigateTo', url);
    },
    logView() {
      if (this.isLogView || this.payResult.shopPayResult === undefined) return;
      const { cashInfo = {}, credit = {}, hasMemberCard, memberCards = [] } = this.payResult.shopPayResult;
      if (isNotNull(cashInfo)) {
        this.ctx.process.invoke('logger',
          'view',
          'show_fanxian',
          '储值金/返现曝光'
        );
        if (cashInfo.needActivated) {
          this.ctx.process.invoke('logger',
            'view',
            'show_fanchuzhi_jihuo',
            '返储值金“立即激活”按钮曝光'
          );
        }
      }
      if (isNotNull(credit)) {
        this.ctx.process.invoke('logger',
          'view',
          'show_fanjifen',
          '返积分曝光'
        );
      }
      if (hasMemberCard && memberCards.length) {
        this.ctx.process.invoke('logger',
            'view',
            'show_quanyika',
            '返权益卡曝光'
          );
        if (memberCards[0].needActivated) {
          this.ctx.process.invoke('logger',
            'view',
            'show_quanyika_jihuo',
            '返权益卡“立即激活”曝光'
          );
        }
      }
      this.isLogView = true;
    },
    // 激活按钮
    doActivate(type, url) {
      if (type === 'memberCard') {
        this.ctx.process.invoke('logger',
          'click',
          'click_quanyika_jihuo',
          '点击返权益卡“立即激活”'
        );
      } else if (type === 'cash') {
        this.ctx.process.invoke('logger',
          'click',
          'click_fanchuzhi_jihuo',
          '点击返储值金“立即激活”'
        );
      }
      this.ctx.process.invoke('navigateTo', url);
    },

    joinNames(names, defaultValue) {
      return names ? names.join('、') : defaultValue;
    },
  },
  
  created() {
    mapData(this, ['kdtId', 'themeColors', 'themeCss', 'orderNo']);
    this.unwatchPayResult = this.ctx.watch('payResult', val => {
      this.payResult = val;
      this.logView();
    });
  },
  
  beforeDestory() {
    this.unwatchPayResult && this.unwatchPayResult();
  }
}
</script>

<style lang="scss">
$red-color: #ee0d27;
$text-color: #323233;
$red-color-active: #eb7784;
$cell-bg-active: #f2f3f5;

.award {

  &__item {
    justify-content: space-between;
    align-items: center;
    color: $text-color;
    padding: 12px 8px;
    height: 34px;
    line-height: 34px;
    background: transparent;

    &:active {
      background: $cell-bg-active;
    }

    &-left {
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      display: flex;
      align-items: center;
      color: $text-color;
    }

    &-right {
      flex: none;
    }

    &-html {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  &__icon-wrap {
    position: relative;
    display: inline-block;
    width: 16px;
    height: 16px;
    line-height: 16px;
    margin-right: 6px;
    text-align: center;
    flex-shrink: 0;
  }

  &__icon-bg {
    position: absolute;
    display: inline-block;
    width: 16px;
    height: 16px;
    top: 0;
    left: 0;
    border-radius: 2px;
    opacity: 0.2;
  }

  &__icon {
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px;
    color: $red-color;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  &__btn {
    color: $red-color;
    font-size: 12px;
    padding: 0 6px;
    border: none;
    background: none;
    outline: none;
    vertical-align: top;

    &:active {
      color: $red-color-active;
    }
  }

  &__num {
    font-weight: 600;
    margin-right: 4px;
  }

  &__bold {
    font-weight: 500;
    padding: 0 3px;
  }

  &__theme-color {
    color: var(--general,#f44);
  }

  &__theme-background {
    background-color: var(--main-bg,#f44);;
  }
}


</style>