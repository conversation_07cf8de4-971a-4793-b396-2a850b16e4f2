import PayBaseService from './PayBaseService';

class MerchantPayService extends PayBaseService {
  public async getPayChannels(partnerId: string, data: any) {
    return await this.invokePay(
      {
        service: 'youzan.pay.mer.pre',
        method: 'query',
        version: '1.0.0',
        partnerId
      },
      data
    );
  }

  /**
   * 支付
   * @param {*} data
   * @param {*} partnerId
   */
  public async getCashierInfo(partnerId: string, data: any) {
    return await this.invokePay(
      {
        service: 'youzan.pay.cashier.search',
        method: 'info',
        version: '1.0.0',
        partnerId
      },
      data
    );
  }

  public async getPayStatus(partnerId: string, data: any) {
    return await this.invokePay(
      {
        service: 'youzan.pay.cashier',
        method: 'queryorder',
        version: '1.0.0',
        partnerId
      },
      data
    );
  }
}

export = MerchantPayService;
