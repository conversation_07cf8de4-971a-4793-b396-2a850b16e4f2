import { ORDER_TYPE } from '../constants/state';

interface OrderUser {
  orderType: number;
  buyerId: number;
  customerId: number;
  customerType: number;
}

class OrderPermission {
  public static isNotPremission(orderType: ORDER_TYPE) {
    if (
      [
        ORDER_TYPE.TYPE_TEAM_TRUENAME,
        ORDER_TYPE.TYPE_QRCODE_FENXIAO_SELLER,
        ORDER_TYPE.TYPE_ENTERPRISE_PURCHASE,
        ORDER_TYPE.TYPE_FENXIAO_DEOPSIT
      ].indexOf(orderType) >= 0
    ) {
      return true;
    }
    return false;
  }

  public static isUserValid(order: OrderUser, buyerId = 0, fansId = 0, fansType = 0) {
    // 空对象判断
    if (!order || Object.keys(order).length === 0) {
      return false;
    }

    if (order.orderType === ORDER_TYPE.TYPE_WISH) {
      return true;
    }

    if (order.buyerId <= 0 && order.customerId <= 0) {
      return true; // nobody无法校验
    }

    if (buyerId > 0 && order.buyerId === buyerId) {
      return true;
    }

    // 个别店铺存在更换公众号的情况，这里不再做判断
    /* if (
      fansId > 0 &&
      (order.customer_id === fansId && order.customer_type === fansType)
    ) {
      return true;
    } */

    if (buyerId <= 0 && fansId <= 0) {
      return true; // 没有登录的情况下,后面要 false
    }

    return false;
  }
}

export default OrderPermission;
