<template>
  <theme class="evaluate-create" :type="globalTheme" :template="themeTemplate">
    <van-loading v-if="loading" type="spinner" vertical> </van-loading>
    <div v-else>
      <template v-if="showItemEvaluation || isEdit">
        <div>
          <evaluate-card
            v-if="isEdit"
            ref="evaluateCard"
            :goods="goods"
            :is-hotel="isHotel"
            :evaluation="evaluation"
          />
          <template v-else>
            <evaluate-card
              v-for="(item, index) in goods"
              :key="index"
              ref="evaluateCard"
              :goods="item"
              :is-hotel="isHotel"
              :evaluation="evaluation"
            />
          </template>
        </div>
      </template>

      <crm-service-card
        v-if="isOfflineOrder"
        ref="crmServiceCard"
        :order-scores="orderScores"
        :is-force-show-content-and-img="isMockGoodsWithGoodsEvaluation"
      />
      <service-card
        v-else
        ref="serviceCard"
        :is-show="isShow"
        :is-create="true"
        :is-hotel="isHotel"
        :show-delivery-evaluate="showDeliveryEvaluate"
        :order-scores="orderScores"
      />

      <div class="evaluate-create__submit-btn-wrapper">
        <van-button
          round
          block
          text="提交"
          type="danger"
          :loading="posting"
          class="evaluate-create__submit-btn"
          color="linear-gradient(to right, #ff6034, #ee0a24)"
          @click="beforeSubmit"
        />
      </div>
    </div>
  </theme>
</template>

<script>
import { ZNB } from '@youzan/znb';
import { Rate, Toast, Loading } from 'vant';
import args from '@youzan/utils/url/args';
import get from 'lodash/get';
import api from './api';
import Theme from 'components/Theme';
import { URL, ANONYMOUS_TYPE } from '../common/constant';
import { themeTemplate } from '../common/theme-template';
import ServiceCard from '../common/components/ServiceCard';
import CrmServiceCard from '../common/components/CrmServiceCard';
import EvaluateCard from '../common/components/EvaluateCard';
import {
  formatGoodsData,
  formatEvaluateData,
  formatItemEvaluationData,
} from '../common/utils/formatData';
import { logger } from '../common/logger';

import isWeappWebview from 'utils/browser/is-weapp-webview';

const isEduOrder = get(_global, 'isEduOrder', false);
const isEdit = args.get('is_edit') === '1';

const isOfflineOrder =
  get(_global, 'isCrmOfflineOrder', false) ||
  get(_global, 'isCrmYouzanOfflineSupportEvaluateOrder', false);
const enableItemEvaluation = get(_global, 'enableItemEvaluation', true); // * 是否开启商品评价
// 线下订单判断所有商品是否都有alias，只要有一个没有，就不展示商品评价
const itemEvaluationValid =
  !isOfflineOrder || _global.goods?.every((item) => !!item.goodsInfo.alias);
// 开启商品评价 && 商品数据满足条件
const showItemEvaluation = enableItemEvaluation && itemEvaluationValid;

// 商品评价下，因为有mock商品（即缺少alias的商品）而降级成订单评价(此时预期评价里展示文字输入框和图片上传)
const isMockGoodsWithGoodsEvaluation = enableItemEvaluation && !itemEvaluationValid;

export default {
  components: {
    Theme,
    [Rate.name]: Rate,
    [ServiceCard.name]: ServiceCard,
    [CrmServiceCard.name]: CrmServiceCard,
    [EvaluateCard.name]: EvaluateCard,
    [Loading.name]: Loading,
  },

  data() {
    return {
      isEdit,
      isOfflineOrder,
      showItemEvaluation,
      themeTemplate,
      isMockGoodsWithGoodsEvaluation,
      globalTheme: _global.globalTheme,
      isHotel: _global.isHotel,
      showDeliveryEvaluate: _global.showDeliveryEvaluate,
      posting: false,
      successed: false,
      observer: null,
      loading: true,
      goods: {},
    };
  },
  computed: {
    isShow() {
      return !isEduOrder;
    },
  },

  beforeMount() {
    this.redirectComment();
  },

  mounted() {
    this.setTagViewLogger();

    if (isEdit) {
      const { itemEvaluation } = _global;
      const evaluation = formatItemEvaluationData(itemEvaluation);
      this.evaluation = evaluation;
      this.goods = evaluation.goods;
      this.orderNo = evaluation.orderNo;
      this.orderScores = evaluation.orderScores;
      if (isOfflineOrder) {
        // * 线下订单需要获取维度评价分值
        this.orderDemissionValue = evaluation.orderDemissionValue;
      }
    } else {
      this.evaluation = undefined;
      this.orderScores = undefined;
      this.orderDemissionValue = []; // * 默认值待确认
      this.orderNo = args.get('order_no');
      this.goods = formatGoodsData(_global.goods);
    }

    if (isOfflineOrder) {
      // * 线下订单需要初始化 CRM 订单评价维度
      this.crmOrderEvaluationDimension = _global.crmOrderEvaluationDimension || {};
    }

    this.fetchTags();
  },

  destroyed() {
    this.observer?.disconnect();
  },

  methods: {
    async fetchTags() {
      // 修改评论的时候，只有单个商品
      const goods = JSON.parse(JSON.stringify(isEdit ? [this.goods] : this.goods));
      const shopShowEvaluateTags = get(_global, 'shopShowEvaluateTags', false);
      const tags = get(_global, 'guessTags', {});
      const items = goods.map((item) => ({ itemId: item.id, skuId: item.skuId }));
      let guessTags = [];
      // 标签店铺配置如果开启
      if (shopShowEvaluateTags) {
        // 如果老版本标签不存在，那就需要去请求智能标签
        // 老版本标签目前只存在非单店店铺
        if (Object.keys(tags).length === 0) {
          try {
            const res = await api.getItemsEvaluationLabels({ items });
            const { itemLabels = [] } = res.data || {};
            guessTags = itemLabels;
          } catch (err) {
            console.error('获取getItemsEvaluationLabels失败', err);
          }
        } else {
          // 老版本标签不区分skuId
          guessTags = items.map((item) => ({ ...item, labelsDesc: tags?.[item.itemId] || [] }));
        }

        // 把每个商品的标签塞一下
        const goodsWithTags = goods.map((item) => {
          const { id, skuId } = item || {};
          const current = guessTags.find((tag) => tag.itemId === id && tag.skuId === skuId) || {};
          item.guessTags = current.labelsDesc || [];
          return item;
        });

        // 修改评价的时候，只展示一个商品的标签
        this.goods = isEdit ? goodsWithTags[0] : goodsWithTags;
      }
    },

    beforeSubmit() {
      if (!isEdit && isOfflineOrder) {
        // * 创建评价加入超时校验
        api
          .getEvaluateState({ order_no: this.orderNo })
          .then((res) => {
            const { data: canEvaluate } = res;
            if (!canEvaluate) {
              Toast.fail('CRM 线下订单评价已超时，无法继续评价');
              this.redirectToClosePage();
            } else {
              this.submit();
            }
          })
          .catch(() => {
            Toast.fail('判断 CRM 线下订单是否可评价失败');
            // * 判断接口请求失败不阻塞评价提交
            this.submit();
          });
      } else {
        this.submit();
      }
    },
    // 提交评价
    submit() {
      if (this.successed) {
        return;
      }

      // * crm 线下订单不走评价埋点
      if (!isOfflineOrder) {
        logger.logEvaluationCreate();
      }

      const orderScores = this.$refs.serviceCard?.getOrderScores();

      let goodsEvaluations = [];

      if (showItemEvaluation || this.isEdit) {
        goodsEvaluations = this.isEdit
          ? [this.$refs.evaluateCard.getEvaluation()]
          : this.$refs.evaluateCard.map((item) => item.getEvaluation());
      }

      // 生成提交请求内容
      const queryData = formatEvaluateData(this.orderNo, orderScores, goodsEvaluations, isEdit);

      const crmOrderDemissionValue = this.$refs.crmServiceCard?.getValue();

      if (isOfflineOrder) {
        // * crm 线下订单，处理快捷评语/图/文/匿名开关
        const { evaluateCardValue, orderScores: crmOrderScores } = crmOrderDemissionValue;

        // * 确保每一个维度都完成了星级评价
        const notAllScore = crmOrderScores.some((score) => {
          return !score.starNum;
        });
        if (notAllScore) {
          Toast.fail('请完成所有星级评分');
          return;
        }

        queryData.evaluationValueParams = crmOrderScores.map((item) => ({
          score: item.starNum * 20,
          quickComments: item.tags,
          name: item.title,
        }));

        const { content, images, isAnonymous } = evaluateCardValue;
        queryData.orderEvaluation = { content, pictures: images };
        const anonymousTypeValue = isAnonymous
          ? ANONYMOUS_TYPE.ANONYMOUS
          : ANONYMOUS_TYPE.REAL_NAME;
        if (isEdit) {
          queryData.orderEvaluationAnonymousType = anonymousTypeValue;
        } else {
          queryData.anonymousType = anonymousTypeValue;
        }
      }

      // webview 回传 kdtId
      queryData.kdtId = window._global.orderKdtId;

      this.posting = true;

      const method = isEdit ? api.updateEvaluation : api.createEvaluation;
      method(queryData)
        .then(() => {
          this.posting = false;
          this.successed = true;
          this.handleCreateSuccess();
        })
        .catch((e) => {
          this.posting = false;
          this.handleCreateFail(e);
        });
    },

    // 处理追评成功后的逻辑
    handleCreateSuccess() {
      Toast.success('评价成功');

      // 通知小程序
      ZNB.postMessage({
        data: {
          'trade:evaluate:create': true,
        },
      });

      // 重定向到查看评价页面
      this.redirectToDetailPage();
    },

    //
    redirectComment() {
      const { isRedirectWxCommentPlugin } = _global;
      const orderNo = args.get('order_no');
      const { kdtId } = _global;
      // 微信环境 && 可以跳转评价页面
      if (isWeappWebview() && isRedirectWxCommentPlugin && !this.isEdit) {
        logger.logEvaluationCreateToWX({
          orderNo,
        });
        ZNB.init({ kdtId })
          .then(() => {
            ZNB.navigate({
              weappUrl: `/packages/wxcomment/index?type=create&order_no=${orderNo}&kdt_id=${kdtId}`,
            });
          })
          .catch((error) => {
            logger.logEvaluationCreateZNBInit({
              error,
            });
            this.loading = false;
          });
      } else {
        this.loading = false;
      }
    },

    // 处理追评失败后的逻辑
    handleCreateFail(err) {
      // 评价已存在
      if (err.code === 1210110501) {
        Toast('评价已存在');
        this.redirectToDetailPage();
      } else {
        this.posting = false;
        Toast.fail(err.msg || '评价失败');
      }
    },

    // * 跳转到评价关闭页
    redirectToClosePage() {
      const url = `${URL.close}?order_no=${this.orderNo}&kdt_id=${_global.kdtId}`;
      ZNB.navigate({
        url,
        type: 'redirectTo',
      });
    },

    redirectToDetailPage() {
      let url = `${URL.detail}?order_no=${this.orderNo}&kdt_id=${_global.kdtId}`;
      if (this.isEdit) {
        url = `${URL.detail}?evaluation_alias=${this.evaluation.alias}&order_no=${this.orderNo}`;
      }
      ZNB.navigate({
        url,
        type: 'redirectTo',
      });
    },

    setTagViewLogger() {
      const tagItems = document.querySelectorAll('.evaluate-guess-tags__item');
      if (!tagItems.length || !window.IntersectionObserver) {
        return;
      }
      this.observer = new IntersectionObserver((entries, observer) => {
        entries.forEach((entry) => {
          const { intersectionRatio, target } = entry;
          if (intersectionRatio > 0) {
            logger.logEvaluationTagView(JSON.parse(target.dataset.viewlog));
            // 上报过后取消观察
            observer.unobserve(target);
          }
        });
      });
      tagItems.forEach((item) => {
        this.observer.observe(item);
      });
    },
  },
};
</script>

<style lang="scss">
.evaluate-create {
  padding-bottom: 80px;
  &__submit-btn {
    font-weight: 500;
    font-size: 16px;
    height: 40px;
    line-height: 38px;
    margin-bottom: constant(safe-area-inset-bottom);
    margin-bottom: env(safe-area-inset-bottom);

    &-wrapper {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 5px 16px;
      background-color: #fff;
    }
  }
  .van-loading {
    margin: 200px auto 0;
  }
}
</style>
