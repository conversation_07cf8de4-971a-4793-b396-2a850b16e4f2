import BaseService from '../base/BaseService';

class OrderQueryService extends BaseService {
  SERVICE_NAME = 'com.youzan.trade.core.service.query.OrderQueryService';

  /**
   *  主订单维度基本信息查询 减少json解析以提高效率
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/125775
   *
   *  @param {string} orderNo - 订单No
   *  @return {Promise}
   */
  async querySimpleOrderByOrderNo(orderNo: string) {
    return this.invoke(this.SERVICE_NAME, 'querySimpleOrderByOrderNo', [orderNo]);
  }
}

export = OrderQueryService;
