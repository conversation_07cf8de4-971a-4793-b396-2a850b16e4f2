/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/camelcase */
import BaseController from '../base/BaseController';
import GoodsRecommendService from '../../services/goods/GoodsRecommendService';
import ShopConfigReadService from '../../services/shop/ShopConfigReadService';
import { RecommendGoodsItem } from 'definitions/other/RecommendGoods';
import { get, isUndefined, isNumber } from 'lodash';
import WapUrl from '@youzan/iron-base/app/lib/WapUrl';
import { Context } from 'astroboy';
import {
  checkRetailShop,
  checkWscChainStore,
  checkEduChainStore,
} from '@youzan/utils-shop';

const setupCdn = require('@youzan/iron-base/app/lib/set-up-cdn');

// 推荐栏总开关
const MAIN_SWITCH_KEY = 'goods_recommend';

// 推荐栏每个页面独立开关
const BIZ_NAME_SHOP_CONFIG_MAP: any = {
  cart: {
    key: 'goods_recommend_cart',
    scene: 'cart',
  },
  order_detail: {
    key: 'goods_recommend_order_detail',
    scene: 'order_detail',
  },
  order_search: {
    key: 'goods_recommend_order_search',
    scene: 'order_search',
  },
  order_detail_add_cart: {
    key: 'goods_recommend_order_detail_add_cart',
    scene: 'wsc~od~sg',
  },
  'od~sg': {
    key: 'goods_recommend_order_detail_add_cart',
    scene: 'wsc~od~sg',
  },
  order_list: {
    key: 'goods_recommend_order_list',
    scene: 'order_list',
  },
  pay_success: {
    key: 'goods_recommend_pay',
    scene: 'pay_success',
  },
  logistics_info: {
    key: 'goods_recommend_delivery',
    scene: 'logistics_info',
  },
  refund: {
    key: 'goods_recommend_refund',
    scene: 'refund',
  },
};

class GoodsRecommendController extends BaseController {
  async init() {
    await super.init({
      initShopMetaInfo: true,
    });
  }

  // 获取店铺是否开启推荐配置
  async getShopRecommendIsOpen(ctx: Context) {
    const postData = ctx.getPostData() || {};
    // @ts-ignore
    const { key: currentBizNameKey = 'goods_recommend' } =
      BIZ_NAME_SHOP_CONFIG_MAP[postData.bizName] || {};

    const checkConfigKeys = [MAIN_SWITCH_KEY];
    currentBizNameKey && checkConfigKeys.push(currentBizNameKey);

    const shopConfig = await ctx
        .getShopConfigs(checkConfigKeys)
        .catch(() => {});

    // 根据店铺的推荐开关 检测总开关或页面开关
    const isOpen = checkConfigKeys.every((key) => +get(shopConfig, key) === 1);

    setupCdn(ctx, 600);

    ctx.json(0, '', {
      isOpen,
    });
  }

  // 获取店铺是否开启推荐配置(get请求)
  async getShopRecommendIsOpenV2(ctx: Context) {
    const { bizName } = ctx.query || {};
    // @ts-ignore
    const { key: currentBizNameKey = 'goods_recommend' } =
      BIZ_NAME_SHOP_CONFIG_MAP[bizName] || {};

    const checkConfigKeys = [MAIN_SWITCH_KEY];
    currentBizNameKey && checkConfigKeys.push(currentBizNameKey);

    const shopConfig = await ctx
        .getShopConfigs(checkConfigKeys)
        .catch(() => {});

    // 根据店铺的推荐开关 检测总开关或页面开关
    const isOpen = checkConfigKeys.every((key) => +get(shopConfig, key) === 1);

    setupCdn(ctx, 600);

    ctx.json(0, '', {
      isOpen,
    });
  }

  async postRecommendGoodsJson(ctx: Context) {
    const { kdtId, buyerId, isWeapp } = ctx;
    const postData = ctx.getPostData() || {};
    const goodsRecommendService = new GoodsRecommendService(ctx);
    // @ts-ignore
    const homePageUrl = new WapUrl(ctx).getHomePageUrlByKdtId(kdtId);
    const { key: currentBizNameKey = 'goods_recommend' } =
      BIZ_NAME_SHOP_CONFIG_MAP[postData.bizName] || {};
    let goodsList: RecommendGoodsItem[] = [];

    const checkConfigKeys = [MAIN_SWITCH_KEY];
    currentBizNameKey && checkConfigKeys.push(currentBizNameKey);
    const shopConfig = ctx.getShopConfigsWithKdtId(kdtId, checkConfigKeys)
        .catch(() => {});
    // 根据店铺的推荐开关 检测总开关或页面开关
    const isOpen = checkConfigKeys.every((key) => +get(shopConfig, key) === 1);

    // 店铺开关打开
    if (isOpen) {
      if (isUndefined(postData.storeId)) {
        // @ts-ignore
        postData.storeId = await this.detectStoreId({
          needRedirect: false,
          justGet: true,
        });
      }

      // @ts-ignore
      const shopMetaInfo = await this.callService(
        'iron-base/shop.ShopMetaReadService',
        'getShopMetaInfo',
        ctx.kdtId
      );
      const isWscChainStore = checkWscChainStore(shopMetaInfo);
      const isRetailShop = checkRetailShop(shopMetaInfo);
      const isEduChainShop = checkEduChainStore(shopMetaInfo);
      let channel = ctx.platform;
      let { scene = 'plugin' } =
        BIZ_NAME_SHOP_CONFIG_MAP[postData.bizName] || {};
      /**
       * 普通微商城调用getCommonRecommend
       * 微商城连锁、教育连锁、零售店铺调用getChainAndRetailRecommend
       */
      if (!isWscChainStore && !isRetailShop && !isEduChainShop) {
        goodsList = await goodsRecommendService.getCommonRecommend({
          kdtId,
          yzUid: buyerId,
          isMini: isWeapp,
          ...postData,
        });
      } else {
        // 区分不同场景的连锁店铺，分别使用不同的scene前缀
        if (isEduChainShop) {
          channel = 'edu';
          scene = `edu_${scene}`;
        } else if (isWscChainStore) {
          channel = 'chain';
          scene = `chain_${scene}`;
        } else if (isRetailShop) {
          channel = 'retail';
          scene = `retail_${scene}`;
        }
        goodsList = await goodsRecommendService.getChainAndRetailRecommend({
          appName: 'wsc-h5-trade',
          channel,
          devUser: 'ziling',
          scene,
          yzUid: buyerId,
          extraParams: {
            params: {
              kdtId,
              yzUid: buyerId,
              bizName: scene,
              isMini: isWeapp,
              itemSize: +postData.itemSize || 10,
              ...get(postData, ['storeId', 'goodsIds', 'categoryIds']),
            },
          },
        });
      }

      // 格式化商品价格
      if (goodsList && goodsList instanceof Array) {
        goodsList.forEach((item) => {
          item.price = isNumber(item.price)
            ? (item.price / 100).toFixed(2)
            : '';
        });
      }
    }

    ctx.json(0, '', {
      isOpen,
      homePageUrl,
      goodsList,
    });
  }

  async recommendGoodsJsonOrderDetailAddCart(ctx: Context) {
    const { kdtId, buyerId, isWeapp } = ctx;
    const postData = ctx.getPostData() || {};

    const goodsRecommendService = new GoodsRecommendService(ctx);
    let goodsList: RecommendGoodsItem[] = [];

    const res = await goodsRecommendService.getRecommandGoodsOrderDetailAddToCart({
      kdtId,
      yzUid: buyerId,
      isMini: isWeapp,
      pageNum: 1,
      pageSize: 6,
      extraParams: {
        kdtId,
        isMini: isWeapp,
      },
      ...postData,
      scene: BIZ_NAME_SHOP_CONFIG_MAP[postData.bizName]?.scene,
    });

    goodsList = res.list;
    if (goodsList && goodsList instanceof Array) {
      goodsList.forEach((item) => {
        item.price = isNumber(item.price)
          ? (item.price / 100).toFixed(2)
          : '';
      });
    }
    ctx.json(0, '', goodsList);
  }
}

module.exports = GoodsRecommendController;
