import { Context } from 'astroboy';

import DeliveryService from '../../services/trade/DeliveryService';
import TradeService from '../../services/trade/TradeService';

import pick from 'lodash/pick';
import camelCase from 'lodash/camelCase';
import { mapValues, toString } from 'lodash';
import { formatDistanceToStr } from './utils/format';
import OrderBuyController = require('./OrderBuyController');

const BIRTHDAY_KEYS = [
  'BIRTHDAY_RELATION_NET_ORDER_MARK',
  'RELATION_BLESSING',
  'RELATION_TYPE_ID',
  'RELATION_TYPE_NAME',
];

// 取货方式
const EXPRESS_TYPE = {
  // 自提
  'SELF_TAKE': 1
};

type ObjectAny = Record<string, any>

class OrderBuySubController extends OrderBuyController {
  async prefetchPrepare(ctx: Context) {
    const { scene, prepare: prepareParams, version } = ctx.getPostData();
    const newPrepareParams = mapValues(prepareParams, toString)
    // 零售-点单宝场景
    if (scene === 'retail-shelf') {
      const { bookKey, defaultSelfFetchPoint, expressType } = await this.retailShelfCacheOrder(ctx);
      try {
        const prepare = await this.getPrepareByBookKeyEvent(ctx, {
          ...newPrepareParams,
          addressId: newPrepareParams.addressId || '',
          bookKey
        })
        const formatPrepare = this.retailShelfFormatPrepareData(ctx, {
          prepare,
          defaultSelfFetchPoint,
          expressType,
          bookKey
        })
        
        ctx.json(0, '', formatPrepare);
      } catch (error) {
        ctx.logger.warn(`prefetchPrepare 异常 ${(ctx.traceCtx as any)?.rootId} kdtId: ${ctx.kdtId},version: ${version}`, error as Error);
        if (version && +version > 1) {
          ctx.json(0, '', {
            bookKey,
            scene,
            hasError: true,
          });
        } else {
          ctx.fail(9999, 'prefetchPrepare 异常');
        }
      }
      return
    }
    // 无匹配场景
    ctx.fail(9999, '未查找到scene匹配');
  }

  // 零售-处理返回数据
  retailShelfFormatPrepareData(ctx: Context, options: ObjectAny) {
    const { prepare, defaultSelfFetchPoint, expressType } = options
    // 自提场景
    if (+expressType === EXPRESS_TYPE.SELF_TAKE) {
      let selfFetch = prepare.orderCreation?.delivery?.selfFetch;
      // 如果默认自提点一致,前端就使用defaultSelfFetch
      if (selfFetch?.id === defaultSelfFetchPoint?.id) {
        defaultSelfFetchPoint.distanceStr = formatDistanceToStr(defaultSelfFetchPoint.distance);
        selfFetch = { ...selfFetch, ...defaultSelfFetchPoint }
        prepare.orderCreation.delivery.defaultSelfFetch = selfFetch
      }
    }
    return prepare
  }

  // 零售-点单宝缓存下单信息
  async retailShelfCacheOrder(ctx: Context) {
    const { cacheOrder } = ctx.getPostData();
    const {
      goodsList,
      activities,
      selfFetch,
      deliveryAddress,
      expressType,
      dispatcherWarehouseId = '',
      extensions,
      source = {
        orderFrom: 'cart',
        orderMark: 'retail_minapp_shelf',
      },
      config = {},
      ttConfig,
      ttUmp,
      coupons = [],
    } = cacheOrder;

    try {
      goodsList.forEach((good: { bizExtension: ObjectAny; extensions: ObjectAny }) => {
        const { cartBizMark = {} } = good.bizExtension ?? {};
        BIRTHDAY_KEYS.forEach((key) => {
          const value = cartBizMark[key] ?? cartBizMark[camelCase(key)];
          if (value) {
            good.extensions[key] = value;
          }
        });
        if(cartBizMark.ATTR_REC_GOODS_TYPES) {
          good.extensions.ATTR_REC_GOODS_TYPES = cartBizMark.ATTR_REC_GOODS_TYPES;
        }
      });
    } catch { }

    const groupCoupons = goodsList.reduce((coupons: any, goods: any) => {
      if (goods.externalPlatformCoupon) {
        // @ts-ignore
        goods.externalPlatformCoupon.forEach(({ couponId, ...args }) => {
          coupons.push({
            ...args,
            id: couponId,
          });
        });
      }
      return coupons;
    }, []);

    const mergedCoupons = [];
    if (coupons?.length) mergedCoupons.push(...coupons);
    if (groupCoupons.length) mergedCoupons.push(...groupCoupons);

    let multiCoupon = null;
    if (mergedCoupons.length) {
      multiCoupon = {
        kdtId: ctx.kdtId,
        coupons: mergedCoupons,
      };
    }

    const params: ObjectAny = {
      buyer: this.buyer,
      items: goodsList,
      delivery: {
        dispatcherWarehouseId: '' + dispatcherWarehouseId,
        address: deliveryAddress
          ? {
            ...deliveryAddress,
            lat: String(deliveryAddress.lat),
            lon: String(deliveryAddress.lon),
            type: deliveryAddress.type === 'userLocation' ? 1 : deliveryAddress.type,
          }
          : null,
        selfFetch,
        expressTypeChoice: +expressType,
      },
      sellers: [
        {
          kdtId: ctx.kdtId,
        },
      ],
      source,
      ump: {
        activities,
        multiCoupon,
      },
      extensions,
      config
    };
    if (ttConfig) {
      // 抖音小程序侧的一些配置：禁掉支付页营销
      params.config = ttConfig;
    }
    // 指定抖音优惠券
    if (ttUmp) {
      params.ump = ttUmp;
    }

    let defaultSelfFetchPoint = null
    // 自提
    if (+expressType === EXPRESS_TYPE.SELF_TAKE) {
      const { location = {} } = selfFetch || {};

      defaultSelfFetchPoint = await new DeliveryService(ctx).getDefaultSelfFetch({
        kdtId: +ctx.kdtId,
        buyerId: this.buyer.buyerId,
        firstOneFill: true,
        lng: location.lon,
        lat: location.lat,
        items: goodsList.map((item: any) => pick(item, ['goodsId', 'skuId', 'num', 'combo'])),
        cityCode: 0, // 默认参数
        storeId: 0, // 默认参数
      });
      // 有默认的自提点
      // 这里需要将经纬度转成字符串（后面 create 接口里接受的是字符串类型）
      if (defaultSelfFetchPoint) {
        if (+defaultSelfFetchPoint.id === 0) {
          delete params.delivery.selfFetch;
        } else {
          params.delivery.selfFetch = {
            ...defaultSelfFetchPoint,
            lat: `${defaultSelfFetchPoint.lat}`,
            lng: `${defaultSelfFetchPoint.lng}`,
          };
        }
      }
    }

    const cacheOrderResult = await new TradeService(ctx).cacheOrderCreation(params);

    return {
      defaultSelfFetchPoint,
      bookKey: cacheOrderResult.bookKey,
      expressType
    }
  }
}

module.exports = OrderBuySubController;
