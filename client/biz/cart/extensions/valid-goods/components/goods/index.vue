<template>
  <div
    v-if="goods.num > 0"
    class="cart-goods"
    :cart-id="goods.cartId"
    :class="{
      'cart-goods--sub': isSub,
    }"
    @click="goToGoodsPage"
  >
    <van-swipe-cell :right-width="!isCanDelete ? 0 : 64">
      <van-checkbox
        v-if="isCanChoose"
        class="cart-goods__checkbox"
        :value="isChoose && !checkboxDisable"
        :disabled="checkboxDisable"
        @click.native.stop="chooseGoods"
      />

      <!-- 商品卡片 -->
      <Goods
        :goods="goods"
        :is-sub="isSub"
        :activity-type="activityType"
        :is-can-stepper="isCanStepper"
        :show-estimated-price="!isEditing && showEstimatedPrice"
        @num-change="numChangeAction"
        @change-goods-sku="changeSku"
        @hide-sale-countdown="$emit('hide-sale-countdown')"
        @open-combo-popup="$emit('open-combo-popup')"
      />

      <template slot="right">
        <div
          v-if="isCanDelete"
          class="cart-goods__swipe-delete"
          @click="deleteGoods"
        >
          <span>删除</span>
        </div>
      </template>
    </van-swipe-cell>
  </div>
</template>

<script>
import get from 'utils/get';
import ZNB from '@youzan/znb';
import Goods from './Goods';
import { action } from '@youzan/zan-jsbridge';
import { SwipeCell, Checkbox, Toast, Tag } from 'vant';
import { jumpLink, isTTApp } from '@shared/common/helper';
import { GOODS_LINK, IS_APP_CART, IS_OPEN_NEW } from '@biz/cart/constant';
export default {
  name: 'goods',

  components: {
    Goods,
    [Tag.name]: Tag,
    [Checkbox.name]: Checkbox,
    [SwipeCell.name]: SwipeCell,
  },

  data() {
    const isAlipayApp = get(window, '_global.miniprogram.isAlipayApp', false);
    const isQQApp = get(window, '_global.miniprogram.isQQApp', false);
    const isXhsApp = get(window, '_global.miniprogram.isXhsApp', false);
    return {
      isAlipayApp,
      isXhsApp,
      isQQApp
    };
  },

  props: {
    goods: Object,
    kdtId: Number,
    // 是否是无效商品
    isInvalid: {
      type: Boolean,
      default: false,
    },
    // 是否是附属商品
    isSub: {
      type: Boolean,
      default: false,
    },
    // 是否可以选中
    isCanChoose: {
      type: Boolean,
      default: true,
    },
    // 当前商品是否选中
    isChoose: {
      type: Boolean,
      default: false,
    },
    // 是否可以删除
    isCanDelete: {
      type: Boolean,
      default: true,
    },
    // 是否可以调整数量
    isCanStepper: {
      type: Boolean,
      default: true,
    },
    // 是否是活动商品
    isActivity: {
      type: Boolean,
      default: false,
    },
    // 活动类型
    activityType: Number,
    isEditing: {
      type: Boolean,
      default: false,
    },
    showEstimatedPrice: {
      type: Boolean,
      default: false,
    },
  },

  computed: {
    // 不满足起售数量
    isNotMeetStartSaleNum() {
      return this.startSaleNum && this.goods.num < this.startSaleNum;
    },

    // 不满足起售数量 || 待复活 || 未开售
    checkboxDisable() {
      return (
        (!!this.isNotMeetStartSaleNum ||
          this.revive ||
          !!this.isNotStartSold) &&
        !this.isEditing
      );
    },

    // 商品待开售
    isNotStartSold() {
      const nowTime = new Date().getTime() / 1000;
      const sellTime = this.goods.startSoldTime;
      return sellTime && sellTime > nowTime;
    },

    startSaleNum() {
      // 如果是换购商品，不限制起售
      if (this.activityType === 24) {
        return 0;
      }

      return this.goods.startSaleNum || 0;
    },

    // 待复活商品
    revive() {
      return get(this.goods, 'revive', false);
    },
  },

  methods: {
    changeSku() {
      this.$emit('change-goods-sku');
    },

    chooseGoods() {
      if (this.checkboxDisable) {
        // 不能低于起售
        if (this.isNotMeetStartSaleNum) {
          return Toast(`该商品${this.startSaleNum}件起售哦`);
        }
        if (this.isNotStartSold) {
          return Toast(this.goods.disableSelectMsg);
        }
        return;
      }

      const type = this.isChoose ? 'remove' : 'add';
      this.changeGoodsSelectStatus(type);
    },

    changeGoodsSelectStatus(type) {
      const payload = {
        rangeType: 'single',
        kdtId: this.kdtId,
        goods: this.goods,
        type,
        isActivity: this.isActivity,
      };

      this.$emit('change-item-checked', payload);
    },

    numChangeAction(val) {
      val = +val;

      this.$emit('num-change', val);
    },

    deleteGoods() {
      this.$emit('delete-goods');
    },

    goToGoodsPage() {
      // 点击商品图片时所做的操作
      const goodsLink = `${GOODS_LINK}?alias=${this.goods.alias}`;
      if (+this.isEditing > 0) {
        // 处于编辑状态则不跳
        return;
      }
      // 头条小程序内使用jumpLink，内有特殊逻辑
      if (isTTApp) {
        jumpLink(goodsLink);
      }

      if (this.isAlipayApp || this.isQQApp || this.isXhsApp) {
        ZNB.navigate({
          aliappUrl: `/packages/goods/detail/index?alias=${this.goods.alias}`,
          qqUrl: `/packages/goods/detail/index?alias=${this.goods.alias}`,
          xhsUrl: `/packages/goods/detail/index?alias=${this.goods.alias}`,
        });
        return;
      }

      if (!IS_APP_CART) {
        const options = {
          url: goodsLink,
        };
        // 如果页面 url 上有 __openNew__ 参数，说明需要用新页面打开链接
        options.fallback = IS_OPEN_NEW ? 'open' : 'redirect';
        ZNB.navigate(options);
        return;
      }

      action.gotoWebview({
        url: goodsLink,
        page: 'web',
      });
    },

    getContainer() {
      return document.querySelector('body');
    },
  },
};
</script>

<style lang="scss" scoped>
.cart-goods {
  position: relative;
  background: #fff;

  &:last-child {
    margin-bottom: 0;
  }

  &--sub {
    .cart-goods__title {
      -webkit-line-clamp: 1;
    }
  }

  &__checkbox {
    float: left;
    height: 120px;
    width: 44px;
    text-align: center;
    line-height: 100px;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__swipe-delete {
    height: 100%;
    width: 64px;
    text-align: center;
    background: #ff4343;
    color: #fff;
    line-height: 1;

    span {
      font-size: 14px;
      left: 20px;
      position: absolute;
      top: 50%;
      margin-top: -6px;
    }
  }
}
</style>
