import { api } from 'common/api';
import { Toast } from 'vant';
import get from 'utils/get';
import each from 'utils/each';
import args from 'utils/browser/args';
import mapKeysToCamelCase from '@youzan/utils/string/mapKeysToCamelCase';
import {submitLogger} from './submitLogger';
import { flatten } from 'lodash';

// 根据kdtId goodsId skuId 在商品列表中找出对应勾选的商品列表
export function findGoodsBuyIds(goodsList = [], allShopInfo) {
  const currentChosenGoods = {};
  each(allShopInfo, (shop) => {
    const kdtId = shop.kdt_id;

    each(shop.goods_group_list, (goodsGroup = {}) => {
      each(goodsGroup.goods_list, (goods = {}) => {
        const id = [
          kdtId,
          goods.goods_id,
          goods.sku_id,
          goods.activity_id,
        ].join('-');

        if (goodsList.indexOf(id) !== -1) {
          currentChosenGoods[kdtId] = currentChosenGoods[kdtId] || [];
          currentChosenGoods[kdtId].push(goods);
        }
      });
    });
  });
  return currentChosenGoods;
}

export function separateGoods(kvGoods, titleMap) {
  const result = [];
  each(kvGoods, (list, key) => {
    if (!list || !list.length) {
      return;
    }
    const item = {
      title: titleMap[key],
      list,
    };
    let totalPrice = 0;
    let totalNum = 0;
    each(list, (goods) => {
      totalPrice += goods.payPrice * goods.num;
      totalNum += goods.num;
    });
    item.totalPrice = totalPrice;
    item.totalNum = totalNum;
    result.push(item);
  });
  return result;
}

export function separateGoodsForMixType(goodsList) {
  // 分类普通，周期购，海淘商品
  const goodsMap = {
    HAITAO: {
      title: '海淘商品',
      list: [],
      totalPrice: 0,
    },
    PERIOD_BUY: {
      title: '周期购商品',
      list: [],
      totalPrice: 0,
    },
    COMMON: {
      title: '普通商品',
      list: [],
      totalPrice: 0,
    },
    COURSE: {
      title: '课程',
      list: [],
      totalPrice: 0,
    },
  };
  const data = [];
  // 分类
  each(goodsList, (goods) => {
    let mark = get(goods, 'settlementRule.settlementMark');
    if (!goodsMap[mark]) {
      mark = 'COMMON';
    }
    goodsMap[mark].totalPrice += goods.payPrice * goods.num;
    goodsMap[mark].list.push(goods);
  });
  // 过滤
  each(goodsMap, (item) => {
    if (item.list.length) {
      data.push(item);
    }
  });

  if (data.length > 1) {
    return {
      needSeparate: true,
      hasCourse: data.some(item => item.title === '课程'),
      data,
    };
  }
  return {
    needSeparate: false,
  };
}

export function separateGoodsForLogistics(goods) {
  const map = {
    NORMAL_EXPRESS: {
      title: '快递发货',
      expressType: 0,
      list: [],
    },
    LOCAL_DELIVERY: {
      title: '同城配送',
      expressType: 2,
      list: [],
    },
    SELF_TAKE: {
      title: '到店自提',
      expressType: 1,
      list: [],
    },
  };
  const data = [];
  const itemsLength = goods.length;
  // 是否存在共同的物流方式
  let needSeparate = true;

  // 无物流商品 -- 非实物商品
  let noLogisticsGoodsNum = 0;

  each(goods, (item) => {
    const { logisticsTypeList } = item;

    // 虚拟商品没有物流方式，需要推到所有物流方式中
    if (Array.isArray(logisticsTypeList) && logisticsTypeList.length === 0) {
      noLogisticsGoodsNum++;
      each(map, (logistics) => {
        logistics.list.push(item);
      });
    }

    each(logisticsTypeList, (type) => {
      const logistics = map[type];
      if (!logistics) {
        return;
      }

      logistics.list.push(item);
    });
  });

  each(map, (val) => {
    const listLength = val.list.length;

    let totalPrice = 0;

    if (listLength > noLogisticsGoodsNum || listLength === itemsLength) {
      each(val.list, (item) => {
        totalPrice += item.payPrice * item.num;
      });

      val.totalPrice = totalPrice;
      data.push(val);
    }

    if (listLength === itemsLength) {
      needSeparate = false;
    }
  });

  return {
    needSeparate,
    data,
  };
}

/**
 * 老版本处理逻辑
 */
// 生成商品下单信息
let generateBuyOrderData = function(goodsList, commonOptions) {
  const postGoodsList = [];
  const common = {
    activity_alias: [],
    activity_id: '',
    activity_type: '',
    order_from: 'cart',
    ...commonOptions,
  };
  const URL_FROM = args.get('from');
  // 餐饮商品需要查询用户选择商品
  const canyinIds = [];
  each(goodsList, (goods) => {
    let bizData;
    try {
      /* eslint-disable */
      bizData = JSON.parse(goods.extraAttribute || '{}').bizData;
    } catch (e) {
      console.log(e);
    }
    /* eslint-enable */

    if (goods.canyinId) {
      canyinIds.push(goods.canyinId);
    }
    const goodsInfoCollection = {
      goods_id: goods.goodsId,
      sku_id: goods.skuId,
      price: goods.payPrice || 0,
      num: goods.num,
      id: goods.skuId,
      sku_code: goods.code || '',
      activity_alias: goods.activityAlias,
      item_message: goods.messages,
      dc_ps: goods.dcPs || '',
      qr: goods.qr || '',
      update_time: goods.updatedTime || '',
      create_time: goods.createdTime || '',
      biz_trace_point_ext: bizData || '',
    };
    if (goods.goodsType === 24) {
      Object.assign(goodsInfoCollection, { deliver_time: goods.deliverTime });
    }
    if (+goods.activityType === 24) {
      Object.assign(goodsInfoCollection, {
        activity_id: goods.activityId,
        activity_type: 24,
      });
    }
    postGoodsList.push(goodsInfoCollection);
    common.activity_alias.push({
      goods_id: goods.goodsId,
      sku_id: goods.skuId,
      activity_alias: goods.activityAlias,
    });
    // 餐饮加料按单个商品计算
    each(goods.ingredientInfoList || [], (feedGoods) => {
      postGoodsList.push({
        goods_id: feedGoods.goodsId,
        num: goods.num,
        price: feedGoods.price,
        sku_id: feedGoods.skuId,
      });
      common.activity_alias.push({
        goods_id: feedGoods.goodsId,
      });
    });
  });
  common.kdt_id = get(goodsList, '[0].kdtId');
  common.store_id = ((goodsList || [])[0] || {}).storeId || 0;
  if (URL_FROM && URL_FROM.length > 0) {
    common.from = URL_FROM;
  }
  // canyinChannel告诉后端是从购物车下单，cartInfo中台保留商品关系
  return {
    canyinChannel: 1,
    cartInfo: {
      buyerId: window._global.buyer_id,
      nobody: window._global.nobody,
      kdtId: common.kdt_id,
    },
    canyinIds,
    common: JSON.stringify(common),
    isMultiShopChecked: 0,
    goodsList: JSON.stringify(postGoodsList),
  };
};

let goBuyTmp = function(goodsList = [], options = {}) {
  const isPeriodBuy = get(goodsList, '[0].goodsType') === 24;
  const kdtId = get(goodsList, '[0].kdtId', options.kdtId);
  const commonOptions = {};
  if (!isNaN(options.expressType)) {
    commonOptions.express_type_choice = +options.expressType;
  }
  const goToPayData = generateBuyOrderData(goodsList, commonOptions);

  return api
    .ajax({
      url: '/v2/trade/common/cache.json',
      method: 'POST',
      data: goToPayData,
      rawResponse: true,
    })
    .then((resp) => {
      // 新希望店铺特殊处理
      let payUrl = resp.data.buyUrl || window._global.url.trade + resp.data.url;
      if (options.isNewHope && isPeriodBuy) {
        payUrl = payUrl.replace('new_order', 'new_hope_order');
      }

      /* eslint-disable */
      payUrl = args.add(payUrl, {
        showwxpaytitle: 1,
        kdt_id: kdtId,
        book_key: resp.data.key,
      });

      return Promise.resolve({
        bookKey: resp.data.key,
        payUrl,
      });
    })
    .catch((e) => {
      Toast((e && e.msg) || '提交订单出错，请重试');
      return Promise.reject(e);
    });
};

if (window._global.isUseNewBoookJson) {
  // 生成商品下单信息
  generateBuyOrderData = function (goodsList, presentData = {}) {
    goodsList = mapKeysToCamelCase(goodsList);
    const config = {
      canyinChannel: 1,
      canyinIds: [],
    };
    const items = [];
    const itemSources = [];
    const activities = [];
    const sellers = [
      {
        kdtId: get(goodsList, '[0].kdtId'),
        storeId: get(goodsList, '[0].storeId'),
      },
    ];

    each(goodsList, (goods) => {
      let bizData;
      try {
        /* eslint-disable-next-line */
        bizData = JSON.parse(goods.extraAttribute || '{}').bizData;
      } catch (e) {
        /* eslint-disable-next-line */
        console.log(e);
      }
      const baseId = {
        kdtId: goods.kdtId,
        goodsId: goods.goodsId,
        skuId: goods.skuId,
        propertyIds: goods.propertyIds || [],
        activityId: goods.activityId,
        activityType: +goods.activityType || 0,
      };

      // 外部订单来源
      const tpps = get(goods, 'bizExtension.cartBizMark.tpps');
      const item = {
        ...baseId,
        storeId: goods.storeId,
        price: goods.payPrice || 0,
        num: goods.num,
        itemMessage: goods.messages,
        deliverTime: goods.deliverTime,
        cartId: goods.cartId || 0,
        extensions: { tpps, CART_ID: goods.cartId },
        isSevenDayUnconditionalReturn:
          goods.isSevenDayUnconditionalReturn || false,
      };

      // 支持商品套餐
      if(goods?.comboDetail?.groupList?.length){
        const subComboList = flatten(goods.comboDetail.groupList.map((group) => {
          return group.subComboList.map((subCombo) => {
            const { goodsId, groupId, num, skuId, properties = [] } = subCombo
            const propertyIds = properties.map(item => item.valId);
            return { goodsId, groupId, num, skuId, propertyIds }
          })
        }));
        item.combo= {
          comboType: goods.comboDetail.comboType,
          subComboList
        }
      }
      // 爱逛直播 id
      const liveStreamingId = get(
        goods,
        'bizExtension.cartBizMark.liveStreamingId'
      );
      if (liveStreamingId) item.liveStreamingId = liveStreamingId;

      // 0元抽奖活动标识
      const promotionMark = get(
        goods,
        'bizExtension.cartBizMark.promotionMark',
        ''
      );
      if (promotionMark) item.promotionMark = promotionMark;

      const itemSource = {
        ...baseId,
        bizTracePointExt: bizData,
        cartCreateTime: goods.createdTime,
        cartUpdateTime: goods.updatedTime,
      };

      const activity = {
        ...baseId,
        activityAlias: goods.activityAlias,
      };

      items.push(item);
      itemSources.push(itemSource);
      activities.push(activity);

      if (goods.canyinId) {
        config.canyinIds.push(goods.canyinId);
      }

      // 餐饮加料按单个商品计算
      each(goods.ingredientInfoList, (feedGoods) => {
        items.push({
          kdtId: feedGoods.kdtId,
          goodsId: feedGoods.goodsId,
          skuId: feedGoods.skuId,
          num: goods.num,
          price: feedGoods.price,
          extensions: { tpps },
        });

        activities.push({
          kdtId: feedGoods.kdtId,
          goodsId: feedGoods.goodsId,
          skuId: feedGoods.skuId,
        });
      });
    });

    return {
      config,
      items,
      sellers,
      source: {
        itemSources,
        orderFrom: 'cart',
      },
      ump: {
        activities,
      },
      extensions: {
        ...presentData,
      },
    };
  };

  goBuyTmp = function (goodsList = [], options = {}) {
    const isPeriodBuy = get(goodsList, '[0].goodsType') === 24;
    const isEduBuy = get(goodsList, '[0].goodsType') === 31;
    const kdtId = get(goodsList, '[0].kdtId', options.kdtId);

    const goToPayData = generateBuyOrderData(goodsList, options.presentData);
    if (!isNaN(options.expressType)) {
      goToPayData.delivery = {
        expressTypeChoice: +options.expressType,
      };
    }
    if(options.preToastDesc){
      goToPayData.config.preToastDesc=options.preToastDesc
    }
    return api
      .ajax({
        url: '/wsctrade/order/book.json',
        method: 'POST',
        data: goToPayData,
        rawResponse: true,
      })
      .then(({ data }) => {
        submitLogger(goodsList);
        // 新希望店铺特殊处理
        let payUrl = data.buyUrl || window._global.url.trade + data.url;
        if (options.isNewHope && isPeriodBuy) {
          payUrl = payUrl.replace('new_order', 'new_hope_order');
        }

        if(isEduBuy){
          payUrl = 'https://cashier.youzan.com/pay/wscvis_buy?orderFrom=cart';
        }

        payUrl = args.add(payUrl, {
          showwxpaytitle: 1,
          kdt_id: kdtId,
          book_key: data.bookKey,
        });

        return Promise.resolve({
          bookKey: data.bookKey,
          payUrl,
        });
      })
      .catch((e) => {
        Toast((e && e.msg) || '提交订单出错，请重试');
        return Promise.reject(e);
      });
  };
}

export const goBuy = goBuyTmp;
