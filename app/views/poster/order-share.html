

<div>
   <div class="poster" id="container" > 
    <div class="poster-image">
      <img class="poster-image__img" src="{{ imgUrl }}"  alt="商品名称"/>
    </div> 
    {% if nickName %}
    <div class="poster-user">
      <img class="user__avatar" src="{{ avatar }}" alt="用户名">
      <span class="user__nickname">{{ nickName }}</span>
      <span class="user__tip">买了这些，推荐给你</span>
    </div>
    {% endif %} 
    <div class="poster-goods">
      <div class="poster-goods_info">
        <p class="poster-goods_info__title">{{ title }}</p>
        <div class="poster-goods_info__price">
          {% if priceTag %}
            <span class="price__title">{{ priceTag }}：</span>
          {% endif %}
      
          <span class="price__current theme-color">
            <em class="price__yuan">￥</em>
            <em>
              {% if showOriginPrice %}
                <span>{{ originUnitPrice[0] }}</span>
                <span>.{{ originUnitPrice[1]}}</span>
              {% else %}
                <span>{{ unitPrice[0] }}</span>
                <span>.{{ unitPrice[1] }}</span>
              {% endif %}
            </em>
      
            <!-- {% if price2Arr[0] %}
              <sub>起</sub>
            {% endif %} -->
          </span>
      
          {% if showCrossLinePrice %}
           <del class="price__origin">{{ crossLinePrice }}</del>
          {% else %}
            <del class="price__origin">¥ {{ originUnitPrice[0] + '.' + originUnitPrice[1] }}</del>
          {% endif %}
        </div>
      </div>
      <div class="poster-goods_qrcode">
        <img class="poster-goods_qrcode__img" src="{{ qrcode }}" alt="店铺名">
      </div>
    </div>
 
    {% if isAssertSecured %}
    <div class="poster-guarantee">
      <span>本商品由</span>
      <img class="poster-guarantee__img" src="https://img01.yzcdn.cn/upload_files/2021/06/16/FtmPdZSEqublgXqTEY-Gkq0eESMk.png" alt="有赞担保">
      <span>全程保障</span>
    </div>
    {% endif %}
  </div>
</div>


{% block poster_style %}
  <style>
    .theme-color {
      color: {{ themeColor }} !important;
    }
  </style>
{% endblock %}

<style>
.poster {
  width: 375px;
  min-width: 260px;
  max-width: 100%;
  overflow: hidden;
  font-family: "PingFang SC", Arial, Helvetica, "STHeiti STXihei", "Microsoft YaHei", Tohoma, sans-serif;
}

.poster-image {
  margin-bottom: 16px;
}
.poster-image__img {
  display: block;
  width: 100%;
  max-height: 100%;
  overflow: hidden;
}
.poster-user {
  display: flex;
  align-items: center;
  padding: 0 16px 8px;
  font-size: 14px;
  color: #323233;
}
.poster-user .user__avatar {
  flex: 0 0 auto;
  width: 32px;
  height: 32px;
  margin-right: 4px;
  border-radius: 50%;
  overflow: hidden;
}
.poster-user .user__nickname {
  flex: 0 0 auto;
  margin-right: 8px;
}
.poster-user .user__tip {
  flex: 1;
  color: #969799;
}
.poster-goods {
  display: flex;
  align-items: flex-start;
  padding: 0 16px 16px;
}

.poster-goods_info {
  flex: 1;
  min-height: 88px;
  display: flex;
  flex-direction: column;
}

.poster-goods_info__title {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #323233;
  line-height: 18px;
  font-weight: bold;
  word-break: break-all;
  white-space: normal;
  font-family: PingFangSC-Medium, "PingFang SC", Arial, Helvetica, "STHeiti STXihei", "Microsoft YaHei", Tohoma, sans-serif;
}
.poster-goods_info__price {
  display: flex;
  align-items: baseline;
  flex-wrap: wrap;
  line-height: 22px;
}
.poster-goods_info__price .price__title {
  font-size: 14px;
  color: #969799;
  margin-right: -0.5em;
  font-weight: bold;
  vertical-align: baseline;
}
.poster-goods_info__price .price__yuan {
  margin-right: -4px;
  margin-left: -2px;
}

.poster-goods_info__price .price__current {
  display: inline-flex;
  align-items: baseline;
  margin-right: 4px;
  font-size: 24px;
  color: #ee0a24;
}
.poster-goods_info__price .price__current em {
  position: relative;
  /* 底对齐 */
  bottom: -1px;
  font-style: normal;
  display: inline-flex;
  align-items: center;
  font-family: Avenir, "PingFang SC", Arial, Helvetica, "STHeiti STXihei", "Microsoft YaHei", Tohoma, sans-serif;
  font-weight: 500;
}
.poster-goods_info__price .price__current sub {
  font-size: 14px;
  vertical-align: baseline;
  margin-left: 2px;
}
.poster-goods_info__price .price__origin {
  display: inline-block;
  font-size: 12px;
  font-family: Avenir, "PingFang SC", Arial, Helvetica, "STHeiti STXihei", "Microsoft YaHei", Tohoma, sans-serif;
  text-decoration: line-through;
  color: #969799;
}
.poster-goods_qrcode {
  flex: 0 0 auto;
  width: 88px;
  height: 88px;
  margin-left: 12px;
}
.poster-goods_qrcode__img {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.goods-img {
  width: 250px;
  height: 250px;
  object-fit: contain;
  margin-top: 14px;
  margin-left: 25px;
  border-radius: 6px;
} 

/* .content {
  position: relative;
  height: 130px;
}
.title {
  position: absolute;
  top: 14px;
  left: 24px;
  width: 153px;
  height: 36px;
  font-size: 14px;
  overflow: hidden;
  color: #111111;
  line-height: 18px;
  display: -webkit-box;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.price {
  position: absolute;
  top: 70px;
  left: 25px;
  bottom: 36px;
  display: flex;
}
.price-unit_fu {
  position: relative;
  top: 7px;
  font-size: 14px;
  font-weight: 500;
  color: #ff4444;
  margin-right: 3px;
}
.price-unit_a {
  font-size: 24px;
  font-weight: 500;
  color: #ff4444;
}
.price-unit_b {
  position: relative;
  top: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #ff4444;
  margin-right: 12px;
}
.price-origin {
  position: relative;
  top: 7px;
  font-size: 12px;
  color: #999999;
  text-decoration: line-through;
} */



.poster-guarantee {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 7px;
  text-align: center;
  line-height: 16px;
  letter-spacing: 0;
  background: #f7f8fa;
  color: #c8c9cc;
  font-size: 12px;
}

.poster-guarantee span {
  position: relative;
  top: 1px;
}

.poster-guarantee__img {
  position: relative;
  top: -2px;
  /* 垂直居中微调 */
  height: 13px;
  display: inline-block;
  margin: 0 4px;
  vertical-align: middle;
  overflow: hidden;
}
</style>
