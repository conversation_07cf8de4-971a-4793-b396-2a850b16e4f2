/** ************************************************************************************************
 * 商品类型枚举
 * 参考自: https://gitlab.qima-inc.com/trade-platform/trade-core/blob/4fe92ecc9320a845ef3f1130c10786989feec046/trade-core-api/src/main/java/com/youzan/trade/core/service/constant/GoodsTypeEnum.java
 ************************************************************************************************ */
enum GoodsTypeEnum {
  // 与商品平台匹配的枚举值
  COMMON = 0,
  AUCTION = 1,
  FOOD = 5,
  FENXIAO = 10,
  MEMBER_CARD = 20,
  GIFT_CARD = 21,
  TIME_CARD = 22,
  MEETINGS = 23,
  PERIOD_BUY = 24,
  CAHSIER_VIRTUAL = 30,
  KNOWLEDGE = 31,
  HOTEL = 35,
  HOTEL_PACKAGE = 185,
  SERVICE = 40,
  CARD_ITEM = 71,
  NORMAL_VIRTUAL = 182,
  VIRTUAL_TICKET = 183,
  MIXED = 184,

  // 自定义的枚举值
  OUT_MEMBER_CARD = 201,
  OUT_CASH = 202,
  OUT_COMMON = 203,
  OUT_SERVICE = 204,
  MOCK = 205,
  WX_QRCODE = 206,
  POINT_PURCHASE = 207,
  PAY_COUPONS = 208,
  COMMERCIAL_SALE_ITEM = 80,
}

class GoodsType {
  private static descriptions: { [key in GoodsTypeEnum]: string } = {
    [GoodsTypeEnum.COMMON]: "普通类型商品",
    [GoodsTypeEnum.AUCTION]: "拍卖商品",
    [GoodsTypeEnum.FOOD]: "餐饮商品",
    [GoodsTypeEnum.FENXIAO]: "分销商品",
    [GoodsTypeEnum.MEMBER_CARD]: "会员卡商品",
    [GoodsTypeEnum.GIFT_CARD]: "礼品卡商品",
    [GoodsTypeEnum.TIME_CARD]: "次卡商品",
    [GoodsTypeEnum.MEETINGS]: "有赞会议商品",
    [GoodsTypeEnum.PERIOD_BUY]: "周期购",
    [GoodsTypeEnum.CAHSIER_VIRTUAL]: "收银台商品",
    [GoodsTypeEnum.KNOWLEDGE]: "知识付费商品",
    [GoodsTypeEnum.HOTEL]: "酒店商品",
    [GoodsTypeEnum.HOTEL_PACKAGE]: "酒店套餐商品",
    [GoodsTypeEnum.SERVICE]: "普通服务类商品",
    [GoodsTypeEnum.CARD_ITEM]: "卡项商品",
    [GoodsTypeEnum.NORMAL_VIRTUAL]: "普通虚拟商品",
    [GoodsTypeEnum.VIRTUAL_TICKET]: "电子卡券商品",
    [GoodsTypeEnum.MIXED]: "混合类型",
    [GoodsTypeEnum.OUT_MEMBER_CARD]: "外部会员卡商品",
    [GoodsTypeEnum.OUT_CASH]: "外部直接收款商品",
    [GoodsTypeEnum.OUT_COMMON]: "外部普通商品",
    [GoodsTypeEnum.OUT_SERVICE]: "外部服务商品",
    [GoodsTypeEnum.MOCK]: "mock不存在商品",
    [GoodsTypeEnum.WX_QRCODE]: "小程序二维码",
    [GoodsTypeEnum.POINT_PURCHASE]: "积分充值商品",
    [GoodsTypeEnum.PAY_COUPONS]: "付费优惠券商品",
    [GoodsTypeEnum.COMMERCIAL_SALE_ITEM]: "商品化商品类型",
  };

  /**
   * 根据枚举值获取描述
   * @param value 商品类型的枚举值
   * @return string 描述
   */
  static getDesc(value: GoodsTypeEnum): string {
    return this.descriptions[value];
  }

  /**
   * 根据枚举值获取枚举名称
   * @param value 商品类型的代码值
   * @return string | null 枚举名称 (如果找不到返回null)
   */
  static getEnumName(value: number): string | null {
    const entry = Object.entries(GoodsTypeEnum).find(([_, val]) => val === value);
    return entry ? entry[0] : null;
  }
}

export {
  GoodsTypeEnum,
  GoodsType,
}