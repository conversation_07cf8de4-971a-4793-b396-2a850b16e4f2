import BaseService from '../base/BaseService';
import {
  IRechargeOrderPrepayRequest,
  IRechargeOrderPrePayResultDTO,
} from 'definitions/pay/IRechargeOrderPrepay';

class ValueCardRechargeOrderService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.pay.cardvoucher.biz.api.valuecard.ValueCardRechargeOrderService';
  }

  rechargeOrderPrepay(
    params: IRechargeOrderPrepayRequest
  ): Promise<IRechargeOrderPrePayResultDTO> {
    return this.invoke('rechargeOrderPrepay', [params]);
  }
}

export default ValueCardRechargeOrderService;
