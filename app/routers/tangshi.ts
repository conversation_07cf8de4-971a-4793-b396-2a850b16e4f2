export = [
  [
    'GET',
    ['/pay/wsctrade_tangshi_pay'],
    'tangshi.TradeTangshiController',
    [
      'validKdtId',
      'initPlatform',
      'initMpAccount',
      'initMpData',
      'initShopSettings',
      'initAcl',
      'getPayHtml'
    ]
  ],
  [
    'GET',
    ['/pay/wsctrade_tangshi_buy'],
    'tangshi.TradeTangshiController',
    [
      'validKdtId',
      'initPlatform',
      'initMpAccount',
      'initMpData',
      'initShopSettings',
      'initAcl',
      'getBuyHtml'
    ]
  ],
  [
    'GET',
    ['/pay/wsctrade_tangshi/payWait', '/pay/wsctrade_tangshi/payWait.html'],
    'tangshi.TradeTangshiController',
    [
      'validKdtId',
      'initPlatform',
      'initMpAccount',
      'initMpData',
      'initShopSettings',
      'initAcl',
      'getWaitHtml'
    ]
  ],
  [
    'GET',
    ['/pay/wsctrade_tangshi/detail', '/pay/wsctrade_tangshi/detail.html'],
    'tangshi.TradeTangshiController',
    [
      'validKdtId',
      'initPlatform',
      'initMpAccount',
      'initMpData',
      'initShopSettings',
      'initAcl',
      'getDetailHtml'
    ]
  ],
  ['GET', '/pay/wsctrade_tangshi/getOrderVersion.json', 'tangshi.TradeTangshiController', 'getOrderVersionJson'],
  ['POST', '/pay/wsctrade_tangshi/prepare.json', 'tangshi.TradeTangshiController', 'postPrepareJson'],
  ['POST', '/pay/wsctrade_tangshi/preparePayment.json', 'tangshi.TradeTangshiController', 'postPreparePaymentJson'],
  ['POST', '/pay/wsctrade_tangshi/create.json', 'tangshi.TradeTangshiController', 'postCreateJson'],
  ['GET', '/pay/wsctrade_tangshi/paySuccess.json', 'tangshi.TradeTangshiController', 'getPaySuccessJson'],
  ['GET', '/pay/wsctrade_tangshi/orderDetail.json', 'tangshi.TradeTangshiController', 'getOrderDetailJson'],
  ['POST', '/pay/wsctrade_tangshi/reOrder.json', 'tangshi.TradeTangshiController', 'postReOrder'],
];
