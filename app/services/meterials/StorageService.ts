import BaseService from '../base/BaseService';
import { IGenerateQiniuUploadTokenRequest } from 'definitions/materials/IGenerateQiniuUploadTokenRequest';

class StorageService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.material.materialcenter.api.service.aggregation.QiniuAggregationWriteService';
  }

  public async getCdnToken(options: IGenerateQiniuUploadTokenRequest) {
    const params: IGenerateQiniuUploadTokenRequest = {
      channel: 'mall_cloud',
      storeType: 2,
      mediaType: 6,
      mediaAccessType: 1,
      operatorType: 1,
      fromApp: 'wsc-h5-trade',
      ...options,
    };

    return this.invoke('getUploadToken', [params]);
  }
}

export = StorageService;
