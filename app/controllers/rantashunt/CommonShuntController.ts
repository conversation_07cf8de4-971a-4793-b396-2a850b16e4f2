import { Context } from 'astroboy';
import _ from 'lodash';
import BaseController from '../base/BaseController';
import { isInGrayReleaseByKdtId } from '../../lib/WhiteListUtils';


class CommonShuntController extends BaseController {
  // 获取小程序中台化配置切流结果
//  config: {
//     paid: 'useWeappRantaTeePaid',
//     cart: 'useWeappRantaTeeCart'
//   }
  async getWeappShuntConfig(ctx: Context): Promise<void> {
    const { config = {} } = ctx.getPostData();
    const result: {[key: string]: boolean} = {};
    const pageTypeKey: string[] = Object.keys(config);
    const pageTypeValue: string[]= Object.values(config);
    const promiseArr: Array<Promise<boolean>> = [];
    _.each(pageTypeValue, (item,index) => {
      result[pageTypeKey[index]] = false;
      promiseArr.push(
        isInGrayReleaseByKdtId(
          ctx,
          { namespace: 'wsc-h5-trade.gray-release', key: item },
          ctx.kdtId
        )
      );
    });
    try {
      const configGroup = await Promise.all(promiseArr);
      _.each(configGroup,(value,index)=> {
        result[pageTypeKey[index]] = value;
      });
    } catch (err) {}

    ctx.r(0, 'success', result);
   }
}
export = CommonShuntController;