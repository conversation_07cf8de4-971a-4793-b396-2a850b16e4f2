const PageBlock = require('./extensions/page-block/extension.json');
const ShopHeader = require('./extensions/shop-header/extension.json');
const CouponBar = require('./extensions/coupon-bar/extension.json');
const CouponAddonBar = require('./extensions/coupon-addon-bar/extension.json');
const ValidGoods = require('./extensions/valid-goods/extension.json');
const InvalidGoods = require('./extensions/invalid-goods/extension.json');
const GoodsSku = require('./extensions/goods-sku/extension.json');
const PresentSku = require('./extensions/present-sku/extension.json');
const CartUmp = require('./extensions/cart-ump/extension.json');
const EmptyCart = require('./extensions/empty-cart/extension.json');
const CpsGoodsRecommend = require('./extensions/cps-goods-recommend/extension.json');
const Recommend = require('./extensions/recommend/extension.json');
const Submit = require('./extensions/submit/extension.json');
const LoginTips = require('./extensions/level2-login-tips/extension.json');
const SpecialCustomization = require('./extensions/special-customization/extension.json');
const FlowEntranceBanner = require('./extensions/flow-entrance-banner/extension.json');

module.exports = {
  biz: '@wsc-h5-trade/cart',
  extensions: [
    PageBlock,
    ShopHeader,
    CouponBar,
    CouponAddonBar,
    ValidGoods,
    InvalidGoods,
    GoodsSku,
    PresentSku,
    CartUmp,
    EmptyCart,
    CpsGoodsRecommend,
    Recommend,
    Submit,
    LoginTips,
    SpecialCustomization,
    FlowEntranceBanner,
  ],

  modules: [
    {
      moduleId: '@wsc-h5-trade/page-block@random-string',
      extensionId: '@wsc-h5-trade/page-block',
      bindings: {
        event: {
          updateCartGoodsList: [
            {
              moduleId: '@wsc-h5-trade/valid-goods@random-string',
              name: 'updateCartGoodsList',
            },
            {
              moduleId: '@wsc-h5-trade/invalid-goods@random-string',
              name: 'updateCartGoodsList',
            },
            {
              moduleId: '@wsc-h5-trade/coupon-addon-bar@random-string',
              name: 'updateCartGoodsList',
            },
          ],
        },
      },
    },
    {
      moduleId: '@wsc-h5-trade/login-tips@random-string',
      extensionId: '@wsc-h5-trade/login-tips',
      bindings: {
        data: {
          shopCart: {
            moduleId: '@wsc-h5-trade/page-block@random-string',
            name: 'shopCart',
          },
        },
      },
    },
    {
      moduleId: '@wsc-h5-trade/shop-header@random-string',
      extensionId: '@wsc-h5-trade/shop-header',
      bindings: {
        data: {
          shopTitle: {
            moduleId: '@wsc-h5-trade/page-block@random-string',
            name: 'shopTitle',
          },
          shopCart: {
            moduleId: '@wsc-h5-trade/page-block@random-string',
            name: 'shopCart',
          },
        },
      },
    },
    {
      moduleId: '@wsc-h5-trade/coupon-bar@random-string',
      extensionId: '@wsc-h5-trade/coupon-bar',
      bindings: {
        data: {
          kdtId: {
            moduleId: '@wsc-h5-trade/page-block@random-string',
            name: 'kdtId',
          },
          shopCart: {
            moduleId: '@wsc-h5-trade/page-block@random-string',
            name: 'shopCart',
          },
          canUseCouponAddOn: {
            moduleId: '@wsc-h5-trade/coupon-addon-bar@random-string',
            name: 'canUseCouponAddOn',
          },
        },
      },
    },
    {
      moduleId: '@wsc-h5-trade/coupon-addon-bar@random-string',
      extensionId: '@wsc-h5-trade/coupon-addon-bar',
      bindings: {
        data: {
          kdtId: {
            moduleId: '@wsc-h5-trade/page-block@random-string',
            name: 'kdtId',
          },
          shopCart: {
            moduleId: '@wsc-h5-trade/page-block@random-string',
            name: 'shopCart',
          },
        },
        event: {
          updatingCart: [
            {
              moduleId: '@wsc-h5-trade/page-block@random-string',
              name: 'updatingCart',
            },
          ],
        },
      },
    },
    {
      moduleId: '@wsc-h5-trade/valid-goods@random-string',
      extensionId: '@wsc-h5-trade/valid-goods',
      bindings: {
        data: {
          shopCart: {
            moduleId: '@wsc-h5-trade/page-block@random-string',
            name: 'shopCart',
          },
          editMode: {
            moduleId: '@wsc-h5-trade/shop-header@random-string',
            name: 'editMode',
          },
        },
        event: {
          skuChanged: [
            {
              moduleId: '@wsc-h5-trade/goods-sku@random-string',
              name: 'skuChanged',
            },
          ],
          presentSkuChanged: [
            {
              moduleId: '@wsc-h5-trade/present-sku@random-string',
              name: 'presentSkuChanged',
            },
          ],
          toggleCheckedAll: [
            {
              moduleId: '@wsc-h5-trade/submit@random-string',
              name: 'toggleCheckedAll',
            },
          ],
          deleteCartItems: [
            {
              moduleId: '@wsc-h5-trade/submit@random-string',
              name: 'deleteCartItems',
            },
          ],
          cartGoodsListDidUpdate: [
            {
              moduleId: '@wsc-h5-trade/page-block@random-string',
              name: 'cartGoodsListDidUpdate',
            },
          ],
        },
        component: {
          'goods-group-ump': {
            moduleId: '@wsc-h5-trade/cart-ump@random-string',
            name: 'GoodsGroupUmp',
          },
          'goods-item-ump': {
            moduleId: '@wsc-h5-trade/cart-ump@random-string',
            name: 'GoodsItemUmp',
          },
        },
      },
    },
    {
      moduleId: '@wsc-h5-trade/invalid-goods@random-string',
      extensionId: '@wsc-h5-trade/invalid-goods',
      bindings: {
        data: {
          unavailableItems: {
            moduleId: '@wsc-h5-trade/page-block@random-string',
            name: 'unavailableItems',
          },
        },
      },
    },
    {
      moduleId: '@wsc-h5-trade/goods-sku@random-string',
      extensionId: '@wsc-h5-trade/goods-sku',
      bindings: {
        data: {
          shopCart: {
            moduleId: '@wsc-h5-trade/page-block@random-string',
            name: 'shopCart',
          },
        },
        event: {
          changeSku: [
            {
              moduleId: '@wsc-h5-trade/valid-goods@random-string',
              name: 'changeSku',
            },
          ],
        },
      },
    },
    {
      moduleId: '@wsc-h5-trade/present-sku@random-string',
      extensionId: '@wsc-h5-trade/present-sku',
      bindings: {
        event: {
          changePresentSku: [
            {
              moduleId: '@wsc-h5-trade/valid-goods@random-string',
              name: 'changePresentSku',
            },
          ],
        },
      },
    },
    {
      moduleId: '@wsc-h5-trade/empty-cart@random-string',
      extensionId: '@wsc-h5-trade/empty-cart',
      bindings: {
        data: {
          shopCart: {
            moduleId: '@wsc-h5-trade/page-block@random-string',
            name: 'shopCart',
          },
        },
      },
    },
    {
      moduleId: '@wsc-h5-trade/cart-ump@random-string',
      extensionId: '@wsc-h5-trade/cart-ump',
    },
    {
      moduleId: '@wsc-h5-trade/flow-extrance-banner@random-string',
      extensionId: FlowEntranceBanner.extensionId,
      bindings: {
        data: {
          bizName: {
            moduleId: '@wsc-h5-trade/page-block@random-string',
            name: 'bizName',
          },
        },
      },
    },
    {
      moduleId: '@wsc-h5-trade/cps-goods-recommend@random-string',
      extensionId: '@wsc-h5-trade/cps-goods-recommend',
      properties: {
        configKey: 'cps_goods_recommend_shopping_cart',
      },
    },
    {
      moduleId: '@wsc-h5-trade/recommend@random-string',
      extensionId: '@wsc-h5-trade/recommend',
    },
    {
      moduleId: '@wsc-h5-trade/submit@random-string',
      extensionId: '@wsc-h5-trade/submit',
      bindings: {
        widget: {
          UpperBar: {
            moduleId: '@wsc-h5-trade/coupon-addon-bar@random-string',
            name: 'CouponAddonBar',
          },
        },
        data: {
          test: {
            moduleId: '@wsc-h5-trade/coupon-addon-bar@random-string',
            name: 'test',
          },
          shopCart: {
            moduleId: '@wsc-h5-trade/page-block@random-string',
            name: 'shopCart',
          },
          editMode: {
            moduleId: '@wsc-h5-trade/shop-header@random-string',
            name: 'editMode',
          },
          isCheckedAll: {
            moduleId: '@wsc-h5-trade/valid-goods@random-string',
            name: 'isCheckedAll',
          },
          checkedGoodsList: {
            moduleId: '@wsc-h5-trade/valid-goods@random-string',
            name: 'checkedGoodsList',
          },
          presentData: {
            moduleId: '@wsc-h5-trade/valid-goods@random-string',
            name: 'presentData',
          },
        },
      },
    },
    {
      moduleId: '@wsc-h5-trade/special-customization@random-string',
      extensionId: '@wsc-h5-trade/special-customization',
    },
  ],

  // 页面整体结构
  app: {
    pages: [
      {
        routes: ['/cart'],
        modules: ['@wsc-h5-trade/page-block@random-string', '@wsc-h5-trade/coupon-addon-bar@random-string'],
        containers: [
          {
            contentType: 'module',
            layout: 'column',
            contents: [
              '@wsc-h5-trade/login-tips@random-string',
              '@wsc-h5-trade/shop-header@random-string',
              '@wsc-h5-trade/coupon-bar@random-string',
              '@wsc-h5-trade/valid-goods@random-string',
              '@wsc-h5-trade/cart-ump@random-string',
              '@wsc-h5-trade/invalid-goods@random-string',
              '@wsc-h5-trade/empty-cart@random-string',
              '@wsc-h5-trade/flow-extrance-banner@random-string',
              '@wsc-h5-trade/cps-goods-recommend@random-string',
              '@wsc-h5-trade/recommend@random-string',
              '@wsc-h5-trade/goods-sku@random-string',
              '@wsc-h5-trade/present-sku@random-string',
              '@wsc-h5-trade/submit@random-string',
              '@wsc-h5-trade/special-customization@random-string',
            ],
          },
        ],
      },
    ],
  },
};
