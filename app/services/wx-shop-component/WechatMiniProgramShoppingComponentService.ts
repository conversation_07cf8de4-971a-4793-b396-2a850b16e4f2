import BaseService from '../base/BaseService';

interface INeedSyncWechatRequest {
  /** 店铺ID */
  kdtId?: number;
  /** 下单时小程序的场景值 */
  scene?: number;
}

export class WechatMiniProgramShoppingComponentService extends BaseService {
  SERVICE_NAME = 'com.youzan.trade.business.wechat.api.WechatMiniProgramShoppingComponentService';

  WEAPP_UNIAON_SERVICE = 'com.youzan.ebiz.video.channels.trade.api.service.weapp.WeappUnionApiService'
  
  /**
   *  3.0校验使用该接口
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1309466
   *
   *  @param {Object} request -
   *  @param {number} request.kdtId - 店铺ID
   *  @param {number} request.scene - 下单时小程序的场景值
   *  @return {Promise}
   */
   async needSyncWechatNew(request: INeedSyncWechatRequest) {
    return this.invoke(this.WEAPP_UNIAON_SERVICE, "needSyncWechatNew", [request]);
  }

  /**
   *  是否需要将订单同步给微信（C端接口）
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1263147
   *
   *  @param {Object} request - 请求参数
   *  @param {number} request.kdtId - 店铺ID
   *  @param {number} request.scene - 下单时小程序的场景值
   *  @return {Promise}
   */
   async needSyncWechat(request: INeedSyncWechatRequest): Promise<boolean> {
    return this.invoke(this.WEAPP_UNIAON_SERVICE, 'needSyncWechat', [request]);
  }

  sceneCheck(params: IWechatMiniSceneCheckRequestDTO) {
    return this.invoke('sceneCheck', [params]);
  }

  getOrderDetail(params: IWechatMiniOrderQueryRequestDTO) {
    return this.invoke('getOrderDetail', [params]);
  }
}

/**
 * com.youzan.trade.business.wechat.model.req.WechatMiniSceneCheckRequestDTO
 */
export interface IWechatMiniSceneCheckRequestDTO {
  /** 店铺id */
  kdtId: number;
  /** 下单时小程序的场景值 */
  scene: number;
}

/**
 * com.youzan.trade.business.wechat.model.req.WechatMiniOrderQueryRequestDTO
 */
export interface IWechatMiniOrderQueryRequestDTO {
  /** 订单号 */
  orderNo: string;
  buyerId: number;
}
