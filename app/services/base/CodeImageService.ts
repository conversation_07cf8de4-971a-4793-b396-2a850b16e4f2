import BaseService from './BaseService';
import WeappQRCodeService from './WeappQRCodeService';

const defaultOptions = {
  margin: 0,
  case: 1,
};

class CodeImageService extends BaseService {
  /**
   *  url转短链接二维码base64`
   *
   *  @param {number} url - 需要转换的url
   *  @param {number} options - 二维码配置（参考二维码服务options，默认无边框，区分大小写，参数可选）
   *  @return {Promise}
   */
  async getShortenQrCode(url: string, options = {}) {
    let finalUrl;
    try {
      finalUrl = await this.ctx.shortUrl.toShort(url);
    } catch (e) {
      finalUrl = url;
    }
    const opts = {
      ...defaultOptions,
      ...options,
    };
    const code = await this.ctx.qrcode.create(finalUrl, opts as any, 'base64');
    return code;
  }

  /**
   *  小程序链接转小程序码base64
   *
   *  @param {number} page - 页面路径
   *  @param {number} query - 页面参数
   *  @return {Promise}
   */
  async getWeappCode(page: string, query: any) {
    let params = {
      ...query,
      kdtId: +this.ctx.kdtId,
      guestKdtId: +this.ctx.kdtId,
      page,
    };
    params = JSON.stringify(params);
    const weappQRCodeService = new WeappQRCodeService(this.ctx);
    const data = await weappQRCodeService.getCodeUltra(
      this.ctx.kdtId,
      'pages/common/blank-page/index',
      params
    );
    return 'data:image/png;base64,' + data.imageBase64;
  }

  /**
   *  小程序链接转小程序码base64
   *
   *  @param {number} page - 页面路径
   *  @param {number} query - 页面参数
   *  @return {Promise}
   */
  async getChainWeappCode(page: string, query: any, rootKdtId: number) {
    let params = {
      ...query,
      kdtId: rootKdtId,
      page,
      guestKdtId: this.ctx.kdtId,
    };
    params = JSON.stringify(params);
    const weappQRCodeService = new WeappQRCodeService(this.ctx);
    const data = await weappQRCodeService.getCodeUltra(
      rootKdtId,
      'pages/common/blank-page/index',
      params
    );
    return 'data:image/png;base64,' + data.imageBase64;
  }
}

export = CodeImageService;
