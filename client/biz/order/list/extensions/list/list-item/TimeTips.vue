<template>
  <div v-if="desc" class="list-item-time-tips">
    {{ pickUpCode }}
    {{ desc }}
  </div>
</template>
<script>
import get from 'utils/get';

export default {
  name: 'list-item-time-tips',

  props: {
    listIndex: {
      type: Number,
      default: 0,
    },
    listItem: Object,
  },

  data() {
    return {};
  },

  computed: {
    orderItem() {
      return this.listItem.orderItems[0] || {};
    },

    desc() {
      // 同城/周期购下次送达时间
      return get(this.orderItem, 'orderDesc.parcelArriveTimeDesc', '');
    },

    pickUpCode() {
      if (!this.isTTApp || get(this.orderItem, 'orderMark', '') !== 'retail_minapp_shelf') {
        return '';
      }

      const code = get(this.orderItem, 'orderDesc.pickUpCode', '');
      return code && `取货码：${code} `;
    },

    isTTApp() {
      return window._global.miniprogram.isTTApp;
    },
  },
};
</script>

<style scoped lang="scss">
.list-item-time-tips {
  background: var(--tag-bg, #f2f2ff);
  border-radius: 4px;
  padding: 8px;
  margin: 12px 12px 0;
  font-size: 12px;
  color: var(--tag-text, #323233);
  line-height: 16px;
}
</style>
