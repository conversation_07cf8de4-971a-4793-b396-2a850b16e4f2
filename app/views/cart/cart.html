{% if isRantaPage != true %}
  {% extends '../common/base.html' %}
{% else %}
  {% extends '../common/ranta_base.html' %}
{% endif %}

{% block viewport %}
  <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover">
{% endblock %}

{% block page_title %}购物车{% endblock %}

{% set pageName = pageName or "cart/v1" %}

{% block page_style %}
  {% if isRantaPage != true %}
    {{ loadCss(pageName) | safe }}
  {% endif %}
{% endblock %}

{% block page_content %}
  <style>
    body{margin:0}.app-wrapper{position:relative}.buy-skeleton{position:absolute;left:0;top:0;width:100%;z-index:10}
  </style>
  <div class="app-wrapper">
    {% if hideSkeleton != true %}
    <img class="js-skeleton buy-skeleton" src="https://img01.yzcdn.cn/public_files/2020/12/24/4b0b7a5046e76f5b024531324e02fd92.png" />
    {% endif %}
    <div id="app"></div>
  </div>
  <div id="shop-nav"></div>
{% endblock %}

{% block page_script %}
  {% include 'ecloud-view/extension.html' %}
  {% set pageJsName = ("version_ext_entry_js." if isRantaPage else "") + pageName %}
  {{ loadJs(pageJsName) | safe }}
{% endblock %}
