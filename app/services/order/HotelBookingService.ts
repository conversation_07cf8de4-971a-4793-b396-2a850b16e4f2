import BaseService from '../base/BaseService';

interface IRoomVoucherBooking {
  itemId: number;
  kdtId: number;
  checkInTime: number;
  checkOutTime: number;
}

class HotelBookingService extends BaseService {
  get SERVER_NAME(): string {
    return 'com.youzan.ebiz.mall.rigel.api.hotel.service.HotelBookingService';
  }

  /**
   *  查询房券套餐预定信息
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1176679
   *
   *  @param {Object} queryDTO -
   *  @param {number} queryDTO.itemId - 商品Id
   *  @param {string} queryDTO.checkOutTime - 离店时间
   *  @param {number} queryDTO.kdtId - 店铺Id
   *  @param {string} queryDTO.checkInTime - 入住时间
   *  @return {Promise}
   */
  async getRoomVoucherBookingBO(queryDTO: IRoomVoucherBooking) {
    return await this.invoke(this.SERVER_NAME, 'getRoomVoucherBookingBO', [queryDTO]);
  }
}

export = HotelBookingService;
