import BaseController from '../base/BaseController';
import VoucherSendService, {
  IVoucherCheckRequest,
  IVoucherSendRequest,
} from '../../services/order/VoucherSendService';
import { Context } from 'astroboy';

class VoucherSendController extends BaseController {
  async getVoucher(ctx: Context) {
    const { kdtId, userId } = ctx;
    const postData = ctx.getPostData();
    const param: IVoucherSendRequest = {
      ...postData,
      /** 用户id */
      userId,
      /** 店铺kdtId */
      kdtId,
      requestId: `${Date.now()}_${userId}`,
    };
    const { alias } = postData;
    const data = await this.sendCoupon(param, alias, '/wscump/coupon/cart/send-coupon.json')
    ctx.json(0, 'ok', data);
  }

  async checkVoucher(ctx: Context) {
    
    const { kdtId, userId } = ctx;
    const postData = ctx.getPostData();
    const params: IVoucherCheckRequest = {
      ...postData,
      kdtId,
      userId,
    };
    const data = await new VoucherSendService(ctx).check(params);
    ctx.json(0, 'ok', data);
  }
}

module.exports = VoucherSendController;
