import BaseService from '../../base/BaseService';

/**
 * 动态入口请求DTO参数类
 */
interface IMomentEntranceRequestDTO {
  /** 店铺ID */
  kdtId?: number;
  /** 请求UUID */
  requestId?: string;
  /** 请求来源 */
  from?: string;
  /** 操作人ID */
  operatorId?: number;
}

/**
 * 动态入口请求响应DTO类
 */
interface IMomentEntranceResponseDTO {
  /** 动态包含的用户数 */
  userCount?: number;
  /** 头像列表 */
  avatars?: string[];
}

class WelikeMomentService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.social.ebiz.content.api.welike.service.WelikeMomentService';
  }

  /**
   *  动态入口
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1139804
   *
   *  @param {Object} request - 动态入口请求参数对象
   *  @param {number} request.kdtId - 店铺ID
   *  @param {string} request.requestId - 请求UUID
   *  @param {string} request.from - 请求来源
   *  @param {number} request.operatorId - 操作人ID
   *  @return {Promise}
   */
  async momentEntrance(request: IMomentEntranceRequestDTO): Promise<IMomentEntranceResponseDTO> {
    return this.invoke('momentEntrance', [request]);
  }
}

export default WelikeMomentService;
