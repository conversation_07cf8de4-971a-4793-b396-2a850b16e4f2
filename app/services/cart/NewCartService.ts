import BaseService from '../base/BaseService';
import { ICartMakeUpPromotionCalculate } from 'definitions/cart/NewCart';

class NewCartService extends BaseService {
    get SERVICE_NAME() {
        return "com.youzan.trade.shopping.cart.api.query.TradeCartQueryService";
    }

  /**
    * 购物车内的凑单优惠
    * zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1356457 
    * 0927社电接口下线第二批
    *
    * @param {Object} request - 
    * @param {Object} request.extension - 扩展信息
    * @param {Object} request.address - 地址信息
    * @param {number} request.kdtId - 店铺id(可选)
    * @param {boolean} request.isYouzanApp - 是否买家版(可选)
    * @param {number} request.selectLogisticsType - 当前选择的物流方式
    9, "无需物流快递"
    0, "普通快递"
    2, "同城送"
    1, "自提"
    * @param {Object} request.source - 来源(可选)
    * @param {number} request.storeId - 门店id(可选)
    * @param {boolean} request.supportReviveGroup - 是否支持商品复活：目前只有商品规格售罄
    * @param {Object} request.buyer - 账号信息
    * @param {Object} request.searchParams - 搜索参数
    * @param {number} request.deliveryWareHouseId - 派单仓库
    * @param {number} request.areaId - 选中配送区域id
    * @param {boolean} request.supportCombo - 是否支持套餐商品
    * @param {number} request.channelId - 购物车商品渠道来源(可选)
    * @return {Promise}
 */
  async cartMakeUpPromotionCalculate(request: ICartMakeUpPromotionCalculate) {
    return this.invoke('cartMakeUpPromotionCalculate', [request]);
  }


  /**
   *  根据地址获取动态运费
   *
   *  @param {Object} params -
   *  @param {number} params.logisticType - 配送类型
   *  @param {number} params.itemInfoParamList - 商品信息
   *  @param {number} params.itemInfoParamList.itemId - 商品id
   *  @param {number} params.itemInfoParamList.skuId - 商品skuId
   *  @param {number} params.itemInfoParamList.uniqueKey - 购物车id - 唯一
   *  @param {number} params.itemInfoParamList.num - 数量
   *  @param {number} params.shippingAddress - 配送地址
   *  @param {string} params.shippingAddress.regionId - areaCode
   *  @param {number} params.shippingAddress.lon - 经纬度
   *  @param {number} params.shippingAddress.lat - 经纬度
   *  @param {number} params.shippingAddress.address - 地址
   *  @param {number} params.shippingAddress.countyName - 县
   *  @param {number} params.shippingAddress.cityName - 市
   *  @param {number} params.shippingAddress.provinceName - 省
   *  @param {number} params.shippingAddress.countryName - 国家
   *  @param {number} params.kdtId - kdtId
   *  @param {number} params.buyerId - 用户id
   *  @return {Promise}
   */
  async queryCartItemDeliveryTime(params: any) {
    return this.invoke('queryCartItemDeliveryTime', [params]);
  }
}

export default NewCartService;