const BaseService = require('../base/BaseService');

class NewHopeOrderDetailService extends BaseService {
  get NEWHOPE_ORDER_SERVICE() {
    return 'com.youzan.ebiz.newhope.api.NewhopeOrderService';
  }

  async orderDetail(orderNo) {
    const dubboData = await this.invoke(this.NEWHOPE_ORDER_SERVICE, 'getNewhopeOrder', [orderNo]);
    const postMsg = dubboData.newhopeOrderListDTO;
    const goodsMsg = dubboData.newhopeItemDTOS;
    // 对后端数据筛选处理-..-
    let preference = 0; // 商品优惠金额
    goodsMsg.forEach(item => {
      preference += +item.systemPreference || 0;
    });
    const orderDetail = {
      orderState: postMsg.state,
      address: {
        userName: postMsg.userName || '',
        tel: postMsg.buyerPhone || '',
        province: postMsg.province || '',
        city: postMsg.city || '',
        county: postMsg.county || '',
        community: postMsg.community || '',
        addressDetail: postMsg.address || ''
      },
      senderMsg: {
        name: postMsg.sender || '',
        phone: postMsg.senderPhone || ''
      },
      deliveryMode: goodsMsg[0].deliveryMode,
      buyerMsg: postMsg.buyerMsg || '无',
      expressTime: postMsg.expressTime || '',
      successTime: postMsg.successTime || '',
      price: postMsg.realPay || postMsg.pay,
      preference,
      postFree: postMsg.postFee || '',
      ecOrderNo: postMsg.ecOrderNo || '',
      orderSource: postMsg.orderSource || '',
      payTime: postMsg.payTime || '',
      goods: {
        allPrice: postMsg.pay * 100,
        itemList: goodsMsg.map(item => ({
          payPrice: item.price * 100 || '',
          title: item.goodsName || '',
          num: item.qty || '',
          imgUrl: item.goodsUrl || 'https://b.yzcdn.cn/v2/image/wap/newhope.png'
        }))
      }
    };
    return orderDetail;
  }

  async orderList(data) {
    const params = {
      userId: data.user_id,
      kdtId: data.kdt_id,
      pageSize: data.pageSize || 10,
      pageNum: data.currentPage || 1
    };
    const orderList = await this.invoke(this.NEWHOPE_ORDER_SERVICE, 'getNewhopeOrderList', [params]);
    orderList.forEach(item => {
      // 跳转详情页对应链接
      item.url = `/wsctrade/order/newhopedetail?order_no=${item.ecOrderNo}`;
      // 默认商品图
      item.goodsUrl = 'https://b.yzcdn.cn/v2/image/wap/newhope.png';
      // 默认都是周期购
      item.weekBuy = 1;
      // 实际费用容错处理
      item.realPay = item.realPay || item.pay;
    });
    return orderList;
  }
}

module.exports = NewHopeOrderDetailService;
