import BaseService from '../base/BaseService';

interface IPurchaseListQueryReqDTO {
  /** 店铺id 必传 */
  kdtId?: number;
  /** 用户id 必传 */
  buyerId?: number;
}

interface IPurchaseItemDTO {
  /** 进货单条目ID */
  purchaseItemId?: number;
  /** 商品id */
  goodsId?: number;
  /** 数量 */
  num?: number;
  /** skuId */
  skuId?: number;
}

interface IPurchaseItemSelectRequest extends IPurchaseListQueryReqDTO {
  /** 勾选的进货单条目ID 必填 */
  purchaseItems?: IPurchaseItemDTO[];
}

 interface IPurchaseItemUpdatetRequest extends IPurchaseListQueryReqDTO {
  /** 勾选的进货单条目ID 必填 */
  purchaseItem?: IPurchaseItemDTO;
}

interface IPurchaseItemDeleteRequest extends IPurchaseItemSelectRequest {
  /** <p>  0:用户手动删除 1：下单后删除  </p> */
  deleteMode?: number;
}

class PurchaseListService extends BaseService {
  get PURCHASE_SERVICE_NAME() {
    return 'com.youzan.ebiz.social.trade.api.purchase.PurchaseListService';
  }

  /**
   * 获取进货单详情
   * @link http://zanapi.qima-inc.com/site/service/view/1276444
   * @param {Object} purchaseListQueryReqDTO - 进货单列表查询请求
   * @param {number} purchaseListQueryReqDTO.kdtId - 店铺id 必传
   * @param {number} purchaseListQueryReqDTO.buyerId - 用户id 必传
   * @return {Promise}
   */
  async listPurchaseItem(purchaseListQueryReqDTO: IPurchaseListQueryReqDTO) {
    return this.invoke(this.PURCHASE_SERVICE_NAME, 'listPurchaseItem', [purchaseListQueryReqDTO]);
  }

  /**
   * 勾选进货单商品
   * @link http://zanapi.qima-inc.com/site/service/view/1280108
   */
  async selectPurchaseItem(purchaseItemSelectRequest: IPurchaseItemSelectRequest) {
    return this.invoke(this.PURCHASE_SERVICE_NAME, 'selectPurchaseItem', [purchaseItemSelectRequest]);
  }

  /**
   * 取消勾选进货单商品
   * @link http://zanapi.qima-inc.com/site/service/view/1280113
   */
  async unSelectPurchaseItem(purchaseItemSelectRequest: IPurchaseItemSelectRequest) {
    return this.invoke(this.PURCHASE_SERVICE_NAME, 'unSelectPurchaseItem', [purchaseItemSelectRequest]);
  }

  /**
   * 全选所有商品
   * @link http://zanapi.qima-inc.com/site/service/view/1280125
   */
  async selectAllPurchaseItem(purchaseListQueryReqDTO: IPurchaseListQueryReqDTO) {
    return this.invoke(this.PURCHASE_SERVICE_NAME, 'selectAllPurchaseItem', [purchaseListQueryReqDTO]);
  }

  /**
   * 取消全选所有商品
   * @link http://zanapi.qima-inc.com/site/service/view/1280126
   */
  async unSelectAllPurchaseItem(purchaseListQueryReqDTO: IPurchaseListQueryReqDTO) {
    return this.invoke(this.PURCHASE_SERVICE_NAME, 'unSelectAllPurchaseItem', [purchaseListQueryReqDTO]);
  }

  /**
   * 更新进货单商品数量
   * @link http://zanapi.qima-inc.com/site/service/view/1280155
   */
  async updatePurchaseItemNum(purchaseItemSelectRequest: IPurchaseItemUpdatetRequest) {
    return this.invoke(this.PURCHASE_SERVICE_NAME, 'updatePurchaseItemNum', [purchaseItemSelectRequest]);
  }

  /**
   * 删除进货单中商品
   * @link http://zanapi.qima-inc.com/site/service/view/1280188
   */
  async deletePurchaseItem(purchaseItemDeleteRequest: IPurchaseItemDeleteRequest) {
    return this.invoke(this.PURCHASE_SERVICE_NAME, 'deletePurchaseItem', [purchaseItemDeleteRequest]);
  }

  /**
   * 统计用户当前店铺下的商品数量
   * @link http://zanapi.qima-inc.com/site/service/view/1280198
   */
  async countPurchaseItemNum(purchaseListQueryReqDTO: IPurchaseListQueryReqDTO) {
    return this.invoke(this.PURCHASE_SERVICE_NAME, 'countPurchaseItemNum', [purchaseListQueryReqDTO]);
  }

  /**
   * 重选进货单中的商品
   * @link http://zanapi.qima-inc.com/site/service/view/1280209
   */
  async reselectPurchaseItem(purchaseItemReselectRequest: IPurchaseItemSelectRequest) {
    return this.invoke(this.PURCHASE_SERVICE_NAME, 'reselectPurchaseItem', [purchaseItemReselectRequest]);
  }
}

export = PurchaseListService;
