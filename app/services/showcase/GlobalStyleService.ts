import BaseService from '../base/BaseService';

interface GlobalStyleQueryParams {
  kdtId: number;
  version: number;
}

class GlobalStyleService extends BaseService {
  get GLOBAL_STYLE_SERVICE_NAME() {
    return 'com.youzan.showcase.center.api.service.template.GlobalStyleService';
  }

  /**
   *  获取店铺全店风格
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1276656
   *
   *  @param {Object} request
   *  @param {number} request.kdtId - 店铺id
   *  @param {number} request.version - 版本号，全店风格收拢这版固定传1，之前没有
   *  @return {Promise}
   */
  async queryGlobalStyleWithDefault(request: GlobalStyleQueryParams) {
    return this.invoke(this.GLOBAL_STYLE_SERVICE_NAME, 'queryGlobalStyleWithDefault', [request]);
  }
}

export = GlobalStyleService;
