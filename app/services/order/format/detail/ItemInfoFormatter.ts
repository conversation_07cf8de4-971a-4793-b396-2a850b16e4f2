import _ from 'lodash';
import mapKeysToCamelCase from '@youzan/utils/string/mapKeysToCamelCase';
import { IItemInfoDto } from 'definitions/order/detail/ItemInfo';
import flatten from '@youzan/utils/array/flatten';

export const TAG = {
  presale: { text: '预售', type: 'danger' },
  selfFetch: { text: '自提', type: 'primary' },
  periodBuy: { text: '周期购', type: 'primary' },
  enjoyBuy: { text: '随心订', type: 'danger' },
  fCode: { text: 'F码专享', type: 'danger' },
  bargain: { text: '砍价', type: 'danger' },
  inSourcing: { text: '内购价', type: 'danger' },
  seckill: { text: '秒杀', type: 'danger' },
  present: { text: '赠品', type: 'danger' },
  timelimitedDiscount: { text: '限时折扣', type: 'danger' },
  auction: { text: '降价拍', type: 'danger' },
  customerDiscount: { text: '会员折扣', type: 'primary' },
  plusBuy: { text: '加价购', type: 'danger' },
  crossBorder: { text: '海淘', type: 'primary' },
  exchangeCoupon: { text: '兑换券', type: 'danger' },
  drug: { text: '处方药', type: 'primary' },
};

class ItemInfoFormatter {
  /**
   * 格式化商品列表
   */
  public static format(items: any, refundDTO: any = {}, itemProcessInfo: any = {}, orderBizExtra?: any, activityType?: number): IItemInfoDto[] {
    // 格式化处理商品信息
    this.parseItems(items);

    // 简化对象
    const itemInfosDto: IItemInfoDto[] = this.simplifyItems(items);

    let refundOrderItemToItemidObj: IPureObject = {};
    (refundDTO.refundOrderItem || []).forEach(function (item: any) {
      refundOrderItemToItemidObj[item.itemId] = item;
    });

    for (const item of itemInfosDto) {
      // 1. 申请退款字段
      const controlButton = item.controlButton || {};
      const safeRefund = controlButton.safeRefundButton;
      if (safeRefund && Object.keys(safeRefund).length > 0) {
        item.safeRefund = safeRefund;
        delete item.controlButton.safeRefundButton;
      }

      const tags = [];

      // 商品标签
      controlButton.isPreSale && tags.push(TAG.presale);
      controlButton.isPeriodBuy && tags.push(TAG.periodBuy);
      controlButton.isSeckill && tags.push(TAG.seckill);
      controlButton.isPresent && tags.push(TAG.present);
      controlButton.isTimelimitedDiscount && tags.push(TAG.timelimitedDiscount);
      controlButton.isAuction && tags.push(TAG.auction);
      controlButton.isCustomerDiscount && tags.push(TAG.customerDiscount);
      controlButton.isCrossBorder && tags.push(TAG.crossBorder);
      controlButton.isUseGoodsExchangeCoupon && tags.push(TAG.exchangeCoupon);

      const extra = item.extra || {};
      const usedPro = extra.USED_PRO || {};

      orderBizExtra.isSelfFetch && tags.push(TAG.selfFetch); // 自提
      orderBizExtra.isEnjoyBuy && tags.push(TAG.enjoyBuy); // 随心订
      activityType === 20 && tags.push(TAG.fCode);
      activityType === 21 && tags.push(TAG.bargain);
      item.isUseFissionUmp && tags.push(TAG.inSourcing);
      +usedPro.activityType === 24 && tags.push(TAG.plusBuy); // 加价购

      item.formattedTags = tags;

      // 2. 商品名省略号
      const goodsInfo = item.goodsInfo || {};
      if (goodsInfo.title) {
        const title = goodsInfo.title;
        let suffix = '';
        if (title.length > 18) {
          suffix = '...';
        }
        goodsInfo.shortTitle = `${title.slice(0, 18)}${suffix}`;
      }

      // 3. 赋值差价
      item.refundOrderItem = refundOrderItemToItemidObj[item.itemId];

      // 4. 商品制作进度
      // itemProcessInfo[item.orderItemId] 为老接口返回格式，老接口下掉后可以删掉 itemProcessInfo 相关代码
      item.processInfo = itemProcessInfo[item.orderItemId] || item.processInfo || {};

      // 跨境商品税费计算
      // tariffTag， 1表示不是海淘订单，2表示含税，3表示不含税
      const { tariffTag, tariffPay = 0 } = item;
      if (tariffTag === 2) {
        item.formattedTaxTips = '进口税(含运费税款)：商品已含税';
      } else if (tariffTag === 3) {
        item.formattedTaxTips = `进口税(含运费税款)：¥ ${(tariffPay / 100).toFixed(2)}`;
      } else {
        item.formattedTaxTips = '';
      }

      /** 格式化金额（支持负数） */
      const formatPricePlus = (price: number): string => {
        if (!price) {
          return '';
        }
        const format = (price: number): string => {
          return `¥${(Math.abs(price) / 100).toFixed(2)}`;
        };
        // 支持负数
        return price > 0 ? `${format(price)}` : `-${format(price)}`;
      };

      let formatComboDetail = item?.comboDetail?.comboGroups?.map((comboGroup: any) => {
        let childrenGoods;
        if (comboGroup?.combos?.length) {
          childrenGoods = comboGroup?.combos?.map(({ gName, n, sName, properties, ap }: any) => {
            if (properties?.length || sName) {
              const propsDesc = properties
                ?.map((prop: any) => `${prop.valName}${formatPricePlus(prop.price)}`)
                ?.filter((x: any) => x)
                .join(';');
              if (properties?.length && sName) {
                // 既有属性又有规格
                return `${gName}（${sName}${formatPricePlus(ap)};${propsDesc}）x${n}`;
              }
              if (properties?.length && !sName) {
                // 有属性无规格
                return `${gName}（${propsDesc}）x${n}`;
              }
              if (!properties?.length && sName) {
                // 无属性有规格
                return `${gName}（${sName}${formatPricePlus(ap)}）x${n}`;
              }
            }
            return `${gName} x${n}`;
          });
        }
        return childrenGoods;
      });
      formatComboDetail = flatten(formatComboDetail).filter((x) => x);
      item.formattedComboDetail = formatComboDetail;

      // 格式化金额
      const formatPrice = (price = 0, operator?: string): string => {
        const symbol = operator ? `${operator} ¥` : '¥';
        return `${symbol} ${(Math.abs(price) / 100).toFixed(2)}`;
      };

      const isPaidContentOrder = item.goodsType === 31;
      const isSamePrice = item.unitPrice === item.originUnitPrice;
      // 划线价
      if (
        controlButton.isSeckill ||
        controlButton.isPresent ||
        controlButton.isTimelimitedDiscount ||
        controlButton.isAuction ||
        controlButton.isCustomerDiscount ||
        (isPaidContentOrder && !isSamePrice)
      ) {
        item.formattedOriginPrice = item.originUnitPrice ? formatPrice(item.originUnitPrice) : '';
      }

      // 动态计算提示信息
      const goodsTips = (good: any): string[] => {
        const tips = [];
        if (good.preSaleDate) {
          tips.push(good.preSaleDate);
        }
        if (good.expressPayMode) {
          tips.push('运费到付');
        }
        return tips;
      }

      item.formattedGoodsTips = goodsTips(item.unitPrice);

      // 如果为失效赠品，则展示失效状态
      item.isInvalidPresent = controlButton.isInvalidPresent;

      // 商品售后状态
      if (item?.safeRefund?.buttonType === 5) {
        item.formattedFeedbackTips = item?.safeRefund?.buttonText;
      }
    }

    return itemInfosDto;
  }

  /**
   * 格式化商品信息
   * 格式化部分字段的内容为json字符串
   */
  private static parseItems(items: any) {
    if (items && items.length > 0) {
      try {
        items.forEach(function (item: any) {
          if (item.goodsInfo && typeof item.goodsInfo === 'string') {
            item.goodsInfo = mapKeysToCamelCase(JSON.parse(item.goodsInfo));
          }
          if (item.sku && typeof item.sku === 'string') {
            item.sku = JSON.parse(item.sku);
          }
          if (item.buyerMemo && typeof item.buyerMemo === 'string') {
            try {
              // POS 订单单商品备注 item.buyerMemo = "备注信息"
              item.buyerMemo = JSON.parse(item.buyerMemo);
            } catch (error) {
              item.buyerMemo = ''
            }
          }
          if (item.extra && typeof item.extra === 'string') {
            item.extra = JSON.parse(item.extra);
          }
          if (_.isPlainObject(item.extra)) {
            Object.keys(item.extra).forEach(key => {
              if (item.extra[key] && typeof item.extra[key] === 'string') {
                try {
                  item.extra[key] = JSON.parse(item.extra[key]);
                } catch (error) {
                  // do nothing
                }
              }
            });
          }
        });
      } catch (error) {
        // do nothing
      }
    }
  }

  // 简化商品列表字段
  private static simplifyItems(items: any): IItemInfoDto[] {
    return items.map((item: any) => {
      const goodsInfo = item.goodsInfo || {};
      const newItem: any = _.pick(item, [
        'itemId',
        'skuId',
        'goodsId',
        'goodsType',
        'extra',
        'num',
        'unitPrice',
        'originUnitPrice',
        'snapShot',
        'sku',
        'controlButton',
        'controlExtra',
        'buyerMemo',
        'tariffTag',
        'tariffPay',
        'orderItemId',
        'processInfo',
        'isUseFissionUmp',
        'isShipped',
        'isCombo',
        'comboType',
        'comboDetail',
        'comboDetailStr',
        'expressPayMode',
        'bizExtra',
        'refundAmt',
        'refundRecord',
        'origin', // 划线价
        'payPrice', // 商品应付金额
        'realPay', // 商品实付金额
        'canReserveNum', // 先囤后约-后约路径-可预约商品数量
        'couponGoodsRelatedInfo', // 先囤后约-后约路径-可预约商品信息
        'hasReservedOrders',
        'outItemSnapKey', // 先囤后约下单快照
        'tags'
      ]);

      newItem.goodsInfo = {
        alias: goodsInfo.alias,
        title: goodsInfo.title,
        shortTitle: goodsInfo.shortTitle,
        imgUrl: goodsInfo.imgUrl,
        pointsPrice: goodsInfo.pointsPrice || 0,
        goodsDate: goodsInfo.goodsDate,
        mark: goodsInfo.mark,
        issue: goodsInfo.issue,
        planExpressTime: goodsInfo.planExpressTime,
        serviceTel: goodsInfo.serviceTel,
        courseGoodsMark: goodsInfo.courseGoodsMark || '',
        payCouponId: +goodsInfo?.extraMap?.PAY_COUPON_ID || 0,
        supportExchange: goodsInfo.supportExchange || false,
      };

      newItem.unitPrice = newItem.unitPrice || 0;
      newItem.num = +newItem.num;
      // 1表示不是海淘订单，2表示含税，3表示不含税
      newItem.tariffTag = +newItem.tariffTag;
      return newItem;
    });
  }
}

export default ItemInfoFormatter;
