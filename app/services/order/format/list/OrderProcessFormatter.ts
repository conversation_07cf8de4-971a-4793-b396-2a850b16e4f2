import { formatDistance } from '@youzan/utils/number/distance';
import {
  IItem<PERSON>rder<PERSON>ddress,
  IItemSeller,
  IOrderProcessActionLocationOpt,
  IOrderProcessActionTypeEnum,
  IOrderProcessItem,
  IOrderProcessTextNodeTypeEnum,
  IExtOpt,
  IVirtual,
  IMaterial,
  ILocalDeliveryInfo,
  IExpressInfo,
  IItemPack,
} from '../../../../../definitions/order/list/OrderListProcess';

// 流程中的action图标配置
export const ACTION_CONFIGS: {
  [s in IOrderProcessActionTypeEnum]: {
    icon: string;
    text: string;
  };
} = {
  [IOrderProcessActionTypeEnum.Tel]: {
    icon: 'https://img01.yzcdn.cn/upload_files/2024/12/17/Fk3zZbLJmwXk6-yb70G1oEPywvuy.png',
    text: '电话',
  },
  [IOrderProcessActionTypeEnum.Location]: {
    icon: 'https://img01.yzcdn.cn/upload_files/2024/12/17/Fm5ErJSAqRCwSafDIZ-1XVYSnOHc.png',
    text: '导航',
  },
  [IOrderProcessActionTypeEnum.VirtualCode]: {
    icon: 'https://img01.yzcdn.cn/upload_files/2024/12/17/FnDvErWaE3cg5oh2DCsw1zYz1Ak_.png',
    text: '核销码',
  },
  [IOrderProcessActionTypeEnum.SelfFetchCode]: {
    icon: 'https://img01.yzcdn.cn/upload_files/2024/12/17/FnDvErWaE3cg5oh2DCsw1zYz1Ak_.png',
    text: '核销码',
  },
  [IOrderProcessActionTypeEnum.Delivery]: {
    icon: 'https://img01.yzcdn.cn/upload_files/2024/12/17/FqQkgeKhZSJWm0_EOBu13ZnsEwbH.png',
    text: '配送',
  },
};

// seller格式化处理
function formatSeller(seller: IItemSeller): IOrderProcessActionLocationOpt {
  return {
    lat: seller.address.lat,
    lng: seller.address.lon,
    name: seller.shopName || '',
    formatAddress: [
      seller.address.province,
      seller.address.city,
      seller.address.county,
      seller.address.address,
    ].join(''),
  };
}

/**
 * 自提订单地址格式化处理
 * @param seller 门店信息
 * @returns IOrderProcessItem
 */
function selfFetchAddressFormat(seller: IItemSeller) {
  const sellerLocation = formatSeller(seller);
  const orderProcessAddress: IOrderProcessItem = {
    isPrimary: false,
    titleOpt: {
      type: IOrderProcessTextNodeTypeEnum.Text,
      text: seller.shopName,
    },
    descOpt: sellerLocation.lat
      ? {
          type: IOrderProcessTextNodeTypeEnum.Distance,
          distanceOpt: {
            defaultText: sellerLocation.formatAddress,
            preDesc: '距你 ',
            afterDesc: ` ${sellerLocation.formatAddress}`,
            lat: sellerLocation.lat,
            lng: sellerLocation.lng,
          },
        }
      : null,
    actions: [
      {
        type: IOrderProcessActionTypeEnum.Tel,
        ...ACTION_CONFIGS[IOrderProcessActionTypeEnum.Tel],
        opt: {
          tel: seller.tel,
        },
      },
      {
        type: IOrderProcessActionTypeEnum.Location,
        ...ACTION_CONFIGS[IOrderProcessActionTypeEnum.Location],
        opt: sellerLocation,
      },
    ],
  };
  return orderProcessAddress;
}

// 自提订单主要信息的格式化处理
function selfFetchCodePrimaryFormat({
  isVirtual,
  verifyCode,
  extOpt,
}: {
  isVirtual: boolean;
  verifyCode: string;
  extOpt: IExtOpt;
}): IOrderProcessItem {
  const { orderNo, kdtId } = extOpt;

  const codeType: IOrderProcessActionTypeEnum =
    IOrderProcessActionTypeEnum[isVirtual ? 'VirtualCode' : 'SelfFetchCode'];
  return {
    isPrimary: true,
    titleOpt: {
      type: IOrderProcessTextNodeTypeEnum.Text,
      text: `${isVirtual ? '核销码' : '提货码'} ${verifyCode}`,
    },
    descOpt: {
      type: IOrderProcessTextNodeTypeEnum.Text,
      text: '到店后请出示核销码或二维码核销',
    },
    actions: [
      {
        type: codeType,
        ...ACTION_CONFIGS[codeType],
        opt: {
          orderNo,
          kdtId,
        },
      },
    ],
  };
}

function orderAddressFormat(orderAddress: IItemOrderAddress): IOrderProcessItem {
  return {
    isPrimary: false,
    titleOpt: {
      type: IOrderProcessTextNodeTypeEnum.Text,
      text: `送至${orderAddress.detail}`,
    },
    descOpt: {
      type: IOrderProcessTextNodeTypeEnum.Text,
      text: `${orderAddress.receiverName} ${
        orderAddress.receiverTel.slice(0, 3) + '****' + orderAddress.receiverTel.slice(-4)
      }`,
    },
  };
}

// 包裹信息格式化
function packInfoFormat(pack: IItemPack) {
  const ret = [];
  if (pack.deliveryStatusDesc) {
    ret.push(pack.deliveryStatusDesc);
  }
  if (pack.estimateArriveTimeTip) {
    ret.push(pack.estimateArriveTimeTip);
  }
  return ret.join('，');
}

class OrderProcessFormatter {
  // 订单流程的主要信息数据获取
  public static formatPrimary(itemDetail: any, extOpt: IExtOpt): IOrderProcessItem | null {
    try {
      if (extOpt.orderStatus === 10) {
        return null;
      }

      const { orderNo, kdtId } = extOpt;
      // 堂食
      if (itemDetail.noDeliveryInfo) {
        const { siteNo = '', dinerNum = '', seller } = itemDetail.noDeliveryInfo;
        return {
          isPrimary: true,
          titleOpt: {
            type: IOrderProcessTextNodeTypeEnum.Text,
            text: `${siteNo} ${dinerNum}位`,
          },
          descOpt: {
            type: IOrderProcessTextNodeTypeEnum.Text,
            text: seller?.shopName || '',
          },
        };
      }

      // 自提订单 - 点单宝自提
      if (itemDetail.selfFetchInfo?.material) {
        const {
          autoVerify,
          pickUpCode,
          queue,
          verifyCode,
          progressStatus,
        } = itemDetail.selfFetchInfo.material;
        const orderProcessPrimaryConfig = selfFetchCodePrimaryFormat({
          isVirtual: false,
          verifyCode: +autoVerify === 1 ? pickUpCode : verifyCode,
          extOpt,
        });
        // 自提码自动核销 - 点单宝进度类型单独处理：如果当前没有进度则显示提示文案，自动核销不展示核销码action
        if (+autoVerify === 1) {
          orderProcessPrimaryConfig.actions = [];
          if (!queue) {
            orderProcessPrimaryConfig.descOpt = {
              type: IOrderProcessTextNodeTypeEnum.Text,
              // progressStatus == 2已取餐则不展示文案
              text: progressStatus === 2 ? '' : '你的餐品已准备好，请尽快前往取餐',
            };
          }
        }
        // 如果有进度信息，则为queue类型描述
        if (queue) {
          const { orderNum, itemNum, waitTime } = queue;
          orderProcessPrimaryConfig.descOpt = {
            type: IOrderProcessTextNodeTypeEnum.Queue,
            queueOpt: {
              queueStr: itemNum ? `${itemNum}/${orderNum}` : '',
              waitTime: Math.ceil(waitTime / 60),
            },
          };
        }
        return orderProcessPrimaryConfig;
      }

      // 自提订单 - 电子卡券
      if (itemDetail.selfFetchInfo?.virtual) {
        const { verifyCode, type } = itemDetail.selfFetchInfo.virtual as IVirtual;
        // 虚拟商品不展示primary信息
        if (+type === 182) {
          return null;
        }
        return selfFetchCodePrimaryFormat({
          isVirtual: true,
          verifyCode,
          extOpt,
        });
      }

      // 配送
      if (itemDetail.localDeliveryInfo) {
        const {
          deliveryShop,
          pack,
          deliveryTimeDesc = '',
        } = itemDetail.localDeliveryInfo as ILocalDeliveryInfo;

        if (pack) {
          return {
            isPrimary: true,
            titleOpt: {
              type: IOrderProcessTextNodeTypeEnum.Text,
              text: packInfoFormat(pack),
            },
            descOpt: {
              type: IOrderProcessTextNodeTypeEnum.Text,
              text: pack.distance ? `距离你 ${formatDistance(+pack.distance)}` : '',
            },
            actions: [
              ...(pack.transporterPhone
                ? [
                    {
                      type: IOrderProcessActionTypeEnum.Tel,
                      ...ACTION_CONFIGS[IOrderProcessActionTypeEnum.Tel],
                      opt: {
                        tel: pack.transporterPhone,
                      },
                    },
                  ]
                : []),
              {
                type: IOrderProcessActionTypeEnum.Delivery,
                ...ACTION_CONFIGS[IOrderProcessActionTypeEnum.Delivery],
                opt: {
                  orderNo,
                  kdtId,
                },
              },
            ],
          };
        }
        if (deliveryShop) {
          return {
            isPrimary: true,
            titleOpt: {
              type: IOrderProcessTextNodeTypeEnum.Text,
              text: `${deliveryTimeDesc}`,
            },
            descOpt: {
              type: IOrderProcessTextNodeTypeEnum.Text,
              text: `由${deliveryShop.shopName}发出`,
            },
            actions: deliveryShop.tel
              ? [
                  {
                    type: IOrderProcessActionTypeEnum.Tel,
                    ...ACTION_CONFIGS[IOrderProcessActionTypeEnum.Tel],
                    opt: {
                      tel: deliveryShop.tel,
                    },
                  },
                ]
              : [],
          };
        }
      }
      // 快递
      if (itemDetail.expressInfo) {
        const { pack } = itemDetail.expressInfo as IExpressInfo;
        if (!pack) {
          return null;
        }
        return {
          isPrimary: true,
          titleOpt: {
            type: IOrderProcessTextNodeTypeEnum.Text,
            text: packInfoFormat(pack),
          },
          descOpt: {
            type: IOrderProcessTextNodeTypeEnum.Text,
            text: pack.companyName ? `由${pack.companyName}为你配送` : '',
          },
          actions: [
            {
              type: IOrderProcessActionTypeEnum.Delivery,
              ...ACTION_CONFIGS[IOrderProcessActionTypeEnum.Delivery],
              opt: {
                orderNo,
                kdtId,
              },
            },
          ],
        };
      }
      return null;
    } catch (err) {
      // 报错后降级，返回null
    }
    return null;
  }

  public static formatAddress(itemDetail: any): IOrderProcessItem | null {
    try {
      // 自提订单 - 点单宝自提
      if (itemDetail.selfFetchInfo?.material) {
        const { selfFetchPoint } = itemDetail.selfFetchInfo.material as IMaterial;
        return selfFetchAddressFormat(selfFetchPoint);
      }
      // 自提订单 - 电子卡券
      if (itemDetail.selfFetchInfo?.virtual) {
        const { seller } = itemDetail.selfFetchInfo.virtual as IVirtual;
        return selfFetchAddressFormat(seller);
      }

      // 配送
      if (itemDetail.localDeliveryInfo) {
        const { orderAddress } = itemDetail.localDeliveryInfo as ILocalDeliveryInfo;
        return orderAddressFormat(orderAddress);
      }

      // 快递
      if (itemDetail.expressInfo) {
        const { orderAddress } = itemDetail.expressInfo as IExpressInfo;
        return orderAddressFormat(orderAddress);
      }

      // 选囤后约
      if (itemDetail.firseStoreOrderInfo) {
        return selfFetchAddressFormat(itemDetail.firseStoreOrderInfo.seller);
      }
    } catch (err) {
      // 报错后降级返回null
    }
    return null;
  }
}

export default OrderProcessFormatter;
