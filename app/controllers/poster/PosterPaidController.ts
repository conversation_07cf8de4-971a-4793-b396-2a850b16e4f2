import { PageException } from '@youzan/iron-base';
import { Context } from 'astroboy';
import PosterBaseController from './PosterBaseController';
import WeappQRCodeService from '../../services/base/WeappQRCodeService';
import MarketRemoteService from '../../services/yop/MarketRemoteService';
import {
  SelfFetchPosterRequestDTO,
  CardVoucherPosterRequestDTO,
} from '../../../definitions/poster/Poster';

/**
 * 订单分享海报
 */
class PosterPaidController extends PosterBaseController {
  // 生成小程序二维码
  async generateWxCode(ctx: Context) {
    let {
      kdtId = 40419900,
      // eslint-disable-next-line prefer-const
      page = 'pages/home/<USER>/index',
    } = ctx.getPostData();
    const wechatQrService = new WeappQRCodeService(ctx);
    const marketRemoteService = new MarketRemoteService(ctx);

    // 查询店铺使用的是共享版还是专享版
    const { isValid, useCommon } = await marketRemoteService.isWeappAuthTeam(
      +kdtId
    );
    kdtId = isValid ? +kdtId : 40419900;
    if (!isValid && !useCommon) {
      throw new PageException(-100, '商家没有小程序');
    }
    try {
      // 生成小程序码
      const query = {
        pages: 'pages/home/<USER>/index',
        guestKdtId: kdtId,
        kdtId,
        dcPs: '',
        offlineId: 0,
      };
      const { imageBase64 } = await wechatQrService.getCodeUltra(
        kdtId,
        page,
        JSON.stringify(query),
        { hyaLine: true }
      );
      ctx.json(0, 'ok', imageBase64);
    } catch (err) {
      throw new PageException(-101, '生成小程序二维码失败');
    }
  }

  // 绘制自提海报
  async initSelfFetchState(ctx: Context) {
    const params: SelfFetchPosterRequestDTO = ctx.getPostData();
    const isScanBuy = params.type === 'self-fetch-scan-buy';
    const height = !isScanBuy
      ? 473 + Math.ceil((params.fetchAddress?.length + 5) / 23) * 20
      : 402;
    ctx.posterOpt = {
      type: params.type || 'self-fetch',
      more: {
        width: 375,
        height,
      },
    };
    ctx.setState(params);
  }

  // 绘制电子海报
  async initCardVoucherState(ctx: Context) {
    const params: CardVoucherPosterRequestDTO = ctx.getPostData();
    const length = params.cardnums?.length;
    const height = length === undefined ? 478 : 504 + (length - 1) * 26;
    ctx.posterOpt = {
      type: 'card-voucher',
      more: {
        width: 375,
        height,
      },
    };
    ctx.setState({ ...params, height });
  }

  // 绘制订阅物流海报
  async initSubscription(ctx: Context) {
    ctx.posterOpt = {
      type: 'subscription',
      more: {
        width: 375,
        height: 400,
      },
    };
    const params = ctx.getPostData();
    ctx.setState(params);
  }
}

export = PosterPaidController;
