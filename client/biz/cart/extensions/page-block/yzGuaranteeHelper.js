/**
 * 购物车接口拆分有赞担保信息，此文件处理拆分接口后的逻辑
 */
import { Logger } from 'common/log/logger';
import {getGoodsGuarantee} from '../../api';

import {getGoodsAliasByCartList, formatYzGuaranteeValue} from './format';

const cacheMap = new Map();

const hasCache = (goodsAlias) => {
  const sortAliasStr = goodsAlias.sort().join(',');
  return cacheMap.has(sortAliasStr);
}
const setCache = (goodsAlias, securedItems) => {
  const sortAliasStr = goodsAlias.sort().join(',');
  cacheMap.set(sortAliasStr, securedItems);
}
const getCache = (goodsAlias) => {
  const sortAliasStr = goodsAlias.sort().join(',');
  return cacheMap.get(sortAliasStr);
}

const yzGuaranteeHelper = {
  // 使用缓存的有赞担保数据处理购物车商品
  useYzGuaranteeCache(shopCart) {
    const goodsAlias = getGoodsAliasByCartList(shopCart);
    if (!hasCache(goodsAlias)) {
      return shopCart;
    }
    return formatYzGuaranteeValue(shopCart, getCache(goodsAlias));
  },
  // 更新购物车商品有赞担保数据
  updateYzGuarantee(shopCart) {
    const goodsAlias = getGoodsAliasByCartList(shopCart);
    // 列表为空不处理
    if (!goodsAlias.length) {
      return Promise.reject();
    }
    // 已有缓存则会在接口请求完成后使用缓存，因此不处理
    if (hasCache(goodsAlias)) {
      return Promise.reject();
    }
    return getGoodsGuarantee(goodsAlias)
      .then((res) => {
        const securedItems = res.data?.securedItems;
        if (!securedItems) {
          return shopCart;
        }
        const newCart = formatYzGuaranteeValue(shopCart, securedItems);
        setCache(goodsAlias, securedItems);
        return newCart;
      })
      .then((newCart) => {
        if (!cacheMap.has('enterpage_cart_logger')) {
          try {
            const isGuarantee = newCart.items.some((goods) => goods.yzGuarantee);
            Logger &&
              Logger.log({
                et: 'view', // 事件类型
                ei: 'enterpage_cart', // 事件标识
                en: '购物车_有赞担保曝光', // 事件名称
                pt: 'cart', // 页面类型
                params: {
                  guarantee_on: !!isGuarantee,
                }, // 事件参数
              });
              cacheMap.set('enterpage_cart_logger', true);
          } catch {}
        }
        return newCart;
      });
  }
}

export default yzGuaranteeHelper;