import ajax, { rawAjax } from './ajax';

const { CancelToken } = rawAjax;
let fetchSkuSource = null;

/**
 * 获取换购商品
 * @param {*} data
 */
const findExchangeSkusJson = (data) =>
  ajax({
    data,
    url: '/wsctrade/cart/find-exchange-goods.json',
  });

/**
 * 获取商品skus信息
 * @param {*} data
 */
const getGoodSkusJson = (data) => {
  if (fetchSkuSource) {
    fetchSkuSource.cancel('Operation canceled by the user.');
    fetchSkuSource = null;
  }

  fetchSkuSource = CancelToken.source('Operation canceled by the user.');

  return ajax({
    data,
    url: '/wsctrade/fetch-sku.json',
    cancelToken: fetchSkuSource.token,
  });
};

// 批量添加购物车
const batchAddGoods = (data) =>
  ajax({
    url: '/wsctrade/cart/batchAddGoods.json',
    method: 'POST',
    data: {
      items: data,
    },
    errorMsg: '添加购物车失败，请重试',
  });

// 批量删除
const deleteBatch = (data) =>
  ajax({
    url: '/wsctrade/cart/deleteBatchList.json',
    method: 'POST',
    data: {
      ids: data,
    },
  });

export { deleteBatch, batchAddGoods, getGoodSkusJson, findExchangeSkusJson };
