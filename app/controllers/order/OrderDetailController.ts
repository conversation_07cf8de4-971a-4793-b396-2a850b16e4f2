/* eslint-disable @typescript-eslint/camelcase */
// /* eslint-disable @typescript-eslint/no-object-literal-type-assertion */
import get from 'lodash/get';
import escape from 'lodash/escape';
import { Context } from 'astroboy';
import { PageException } from '@youzan/iron-base';
import UA from '@youzan/iron-base/app/lib/UA';
import BaseController from '../base/BaseController';
import OrderDetailService from '../../services/order/OrderDetailService';
import OrderRelationQueryService from '../../services/order/OrderRelationQueryService';
import SecureService from '../../services/order/SecureService';
import ShopMultiStoreService from '../../services/shop/ShopMultiStoreService';
import ShopConfigReadService from '../../services/shop/ShopConfigReadService';
import OrderPayResultService from '../../services/order/OrderPayResultService';
import OrderPermission from '../../lib/OrderPermission';
import { IOrderInfoDto } from 'definitions/order/detail/OrderInfo';
import { IPaymentInfoDto } from 'definitions/order/detail/PaymentInfo';
import { IOrderDetailReq, IOrderDetailDto } from 'definitions/order/OrderDetail';
import CompareVersion from '../../lib/CompareVersion';
import OrderContext from '../../lib/OrderContext';
import { isInGrayReleaseByKdtId, isInGrayReleaseByKdtIdForQttApollo } from '../../lib/WhiteListUtils';
import { isNewhopeKdtShop } from '../../constants/NewHopeShopWhiteListUtil';
import VoucherSendService from '../../services/order/VoucherSendService';
import QttNoteQueryService from '../../services/order/QttNoteQueryService';
import QttOrderService from '../../services/order/QttOrderService';
import PaidPromotionDubboService from '../../services/api/ump/marketing/PaidPromotionDubboService';
import { initOrderData } from '../../lib/PriorUse';
import ExtensionPointService from '../../services/cloud/ExtensionPointService';
import ExpressDetailService from '../../services/express/ExpressDetailService';
import { checkPureWscSingleStore, checkRetailMinimalistShop } from '@youzan/utils-shop';
import { getDirectBuyAgainBtnConfig } from '../../lib/TradeConfig';
import { setQttWeappAppId } from '../../lib/Qtt';
import args from '@youzan/utils/url/args';
import EvaluateSearchService from '../../services/trade/EvaluateSearchService';
import { initRanta } from '@youzan/plugin-h5-ranta-config';
import ShelfProgressService from '../../services/api/retail/misc/ShelfProgressService';
import { IShelfOrderProgressRequest } from 'definitions/api/retail/misc/ShelfProgressService/queryOrderProgress';
import { formatOrderWaitingProgress, IFormattedOrderProgress } from './utils/order-detail';

// 盲盒订单类型
const ACTIVITY_TYPE = {
  blindBoxBuy: 401, // 盲盒购买订单
  blindBoxVerification: 402, // 盲盒核销订单
};

interface AddCartABTestConfig {
  abTraceId: string;
  abUid: string;
  bcm: string;
  testId: number;
  isValid: boolean;
  configurations: {
    hasAddToCartButton: boolean;
  };
}

function checkNeedDisplayPaidPromotion(orderBizExtra: any) {
  const { paidPromotion, hasRefund } = orderBizExtra || {};
  return paidPromotion && !hasRefund;
}

class OrderDetailController extends BaseController {
  public async init(): Promise<void> {
    const { ctx } = this;

    // 初始化订单上下文
    OrderContext.init(ctx);

    // 只有页面请求才需要初始化的数据
    if (!ctx.acceptJSON) {
      const { kdtId = 0 } = ctx;
      const detectStoreParams = {
        kdtId,
        storeId: null,
        sessionId: ctx.sessionId || '',
        userId: this.buyerId || 0, // 一般取youzan_user_id，不过有buyerId时两者值一致，只有极个别异常情况不一致
      };
      try {
        const offlineResult = await new ShopMultiStoreService(ctx).getMultiStore(detectStoreParams);
        if (!ctx.offlineId && offlineResult) {
          // ctx.offlineId没有注入，offlineResult请求放到了外面不会引起重复请求 20180622
          // 注入到ctx，在footer components里用到
          ctx.offlineId = Number(offlineResult && offlineResult.storeId);
        }
      } catch (e) {
        //
      }
    }
    await super.init({
      validKdtId: true,
      initGlobalTheme: true,
      initOpenAppConfig: true,
      initBaseFooter: !ctx.acceptJSON && !/from=ivr/.test(this.ctx.url),
    });
  }

  public async orderDetailAcl(allowNotLogin: boolean): Promise<void> {
    await this.acl({
      allowNotLogin,
      useAjaxLogin: false,
      forceOauthLogin: false,
      kdtId: this.ctx.kdtId,
    });
  }

  public async getGuideHtml(): Promise<void> {
    await this.ctx.render('order/guide.html');
  }

  private async getCurrentChannel(key: string) {
    const { ctx } = this;
    const isInChannel = key !== 'isWeb' ? (typeof ctx[key] === 'function' ? ctx[key]() : ctx[key]) : true;

    return (await isInChannel) ? key : '';
  }

  public async getIndexHtml(): Promise<void> {
    const { ctx } = this;
    // kostoken鉴权
    const kosTokenVerified = await new SecureService(ctx).validKosToken();

    // kosTokenVerified是可以不登录的一个条件
    await this.orderDetailAcl(!!kosTokenVerified);
    if (ctx.status === 302) {
      return;
    }

    // 有效请求参数
    const { query, kdtId = 0 } = ctx;
    const { from = '', isCrmOfflineOrder = false } = query;
    // 兼容开放跳转传orderNo的情况
    const orderNo = query.order_no || query.orderNo;
    const { buyer, buyerId } = this;

    // 如果没有定义或者定义的是v2则走中台化
    // 有赞云提供接口判断是否为大客定制
    let cloudDesign;
    try {
      // 该接口本地暂时无法访问，要部署到机器后才能正常访问
      cloudDesign = await new ExtensionPointService(ctx).checkCloudCustomized(kdtId, 'order-detail');
    } catch (error) {
      console.log('error', error);
      // 若判断定制接口挂了，则所有页面运行到旧的订单详情，保证定制的页面都正常
      // 若为本地开发，旧订单详情为 true, 新订单详情为 false, 勿提交
      cloudDesign = true;
    }

    const useWebRantaOrderDetailWithCloudDesign = await isInGrayReleaseByKdtId(
      ctx,
      { namespace: 'wsc-h5-trade.gray-release', key: 'useWebRantaOrderDetailWithCloudDesign' },
      ctx.kdtId
    );

    // const useNew = isUseNew && !isCustomized && !cloudDesign;
    let useNew = !cloudDesign || (cloudDesign && useWebRantaOrderDetailWithCloudDesign);

    const { force_shunt = '' } = ctx.query;
    if (force_shunt && force_shunt.length !== '') {
      switch (force_shunt) {
        case 'v1':
          useNew = false;
          break;
        case 'v2':
          useNew = true;
          break;
      }
    }

    ctx.setState('isCloudCustomized', cloudDesign);
    const teeDetailUrl = args.add('https://h5.youzan.com/wsctrade/order/tee-detail', {
      ...query,
      tee_page: true,
    });
    if (useNew && !ctx.query.tee_page) {
      // 跳转一次可以区分当前页面到底是不是定制，做排查用，稳定后可删
      return ctx.redirect(teeDetailUrl);
    }

    this.setPageCloudBizIds('orderDetail', 'orderNo', orderNo);

    const params: IOrderDetailReq = {
      orderNo,
      kdtId,
      buyerId: kosTokenVerified ? 0 : buyer.buyerId,
      customerId: kosTokenVerified ? 0 : buyer.fansId,
      customerType: kosTokenVerified ? 0 : buyer.fansType,
      sourceName: this.sourceName,
      marketingMark: {
        source: 'order_details_rcmd',
        bizName: 'order_details_rcmd',
      },
    };
    let data: IOrderDetailDto;

    // IVR订单
    if (from === 'ivr') {
      params.from = 'IVR';
      params.receiverTel = buyer.buyerPhone;
      // @ts-ignore
      delete params.kdtId;
      // @ts-ignore
      delete params.buyerId;
      // @ts-ignore
      delete params.customerId;
      // @ts-ignore
      delete params.customerType;
    }

    let goodsGift = null;
    let paidPromotion = {};
    try {
      data = await new OrderDetailService(ctx).detailByOrderNo(params, 'h5');
      if (get(data, 'sourceInfo.orderMark') === 'ph_mini_program') {
        throw new PageException(10500, '该订单需前往有赞旗下旺小店小程序查看');
      }
      // 判断是否是线下订单
      const isOfflineSign = get(data, 'sourceInfo.orderMark') === 'edt_offline_sign';
      if (isOfflineSign) {
        goodsGift = await new OrderDetailService(ctx).getGoodsGiftsByOrderNo(kdtId, orderNo);
      }
      // 判断是否需要展示支付有礼
      if (checkNeedDisplayPaidPromotion(get(data, 'orderBizExtra', {}))) {
        paidPromotion = await this.queryPaidPromotionFunc(orderNo, +ctx.kdtId);
      }
    } catch (e) {
      // console.error(e);
      const content = (e as any).errorContent || {};
      throw new PageException(content.code || 10500, content.msg || '订单数据异常');
    }
    if (!data || Object.keys(data).length === 0) {
      // 无此订单
      throw new PageException(10500, '无此订单');
    }

    // 群团购走订制订单详情
    if (data.sourceInfo && data.sourceInfo.originSource && data.sourceInfo.originSource.source === 'mall_group_buy') {
      ctx.redirect(`https://cashier.youzan.com/pay/wsctrade_tradeDetail?orderNo=${orderNo}&kdt_id=${kdtId}`);
      return;
    }

    // CRM线下门店订单不展示优惠券/复购券的信息。
    if (isCrmOfflineOrder) {
      if (data?.ump) {
        data.ump.goodsAvlActivity = {};
      }
      if (data?.marketingInfo) {
        data.marketingInfo = {};
      }
    }

    /**
     * 如果有优惠券，查一下优惠券是否可用
     * */
    if (data?.ump?.goodsAvlActivity?.id) {
      const activityId = data?.ump?.goodsAvlActivity?.id;
      const source = 'order_detail';
      try {
        await new VoucherSendService(ctx).check({
          activityId,
          source,
          kdtId,
          userId: buyerId,
        });
        ctx.setGlobal({
          buyAgainCouponAvailable: true,
        });
      } catch (e) {
        ctx.setGlobal({
          buyAgainCouponAvailable: false,
        });
      }
    } else {
      ctx.setGlobal({
        buyAgainCouponAvailable: false,
      });
    }
    const {
      mainOrderInfo: orderInfo = {} as IOrderInfoDto, // 订单主信息
      itemInfo = [], // 订单商品
      paymentInfo = {} as IPaymentInfoDto, // 支付数据
      orderAddressInfo = {}, // 收货地址
      shopInfo = {}, // 店铺/门店信息
      buyerInfo, // 买家信息
      ump, // 优惠数据
      invoiceInfo, // 发票信息
      orderExtra = {},
      hotelInfo = {},
      sourceInfo = {},
      privacyWaybill = {}, // 隐私面单-号码保护
      outerPromotion = {},
    } = data;

    const isFxZpp = ctx.query?.bizEnv === 'fx-zpp' || orderExtra.ATTR_IS_FX_ZPP_ORDER === '1'; // 赞拼拼小程序

    /**
     * 群团团刚刚下完单时存在取不到团购标题的情况
     * 在这里单独调接口取
     */
    // eslint-disable-next-line no-prototype-builtins
    if (isFxZpp && !orderExtra.hasOwnProperty('ATTR_QTT_ORDER_EXPORT')) {
      try {
        const note = await new QttNoteQueryService(ctx).queryNoteBaseById({ noteId: orderExtra.ATTR_FX_ZPP_NOTE_ID });
        orderExtra.ATTR_QTT_ORDER_EXPORT = JSON.stringify({ noteTitle: note.title });
      } catch (error) {
        console.log(error);
      }
    }

    // 支付宝交易组件订单不允许修改地址
    if (this.isAliPayTradeModule(orderExtra)) {
      orderInfo.allowShowModifyAddress = false;
    }

    // 群团团请求一些额外的信息
    let forQttOrderExtra = {};
    if (isFxZpp) {
      try {
        const [, note, response] = await Promise.all([
          setQttWeappAppId(ctx),
          new QttNoteQueryService(ctx).queryNoteBaseById({ noteId: orderExtra.ATTR_FX_ZPP_NOTE_ID }),
          new QttOrderService(ctx).listOrderExt({ orderNoList: [orderNo], withRemark: true }),
        ]);
        forQttOrderExtra = {
          noteConfig: note?.config || {},
          ...(response?.[0] || {}),
        };
      } catch (error) {
        console.log(error);
      }
    }

    ctx.setGlobal({
      forHotelInfo: hotelInfo,
      forOrderExtra: orderExtra,
      forQttOrderExtra,
      sourceInfo,
    });

    const rxNo = orderInfo.extra?.rxNo;
    // 当前登录用户是否是下单人，IVR订单可能不是 owner
    const isIVROwner = buyerInfo.buyerId === buyer.buyerId && from === 'ivr';

    ctx.setGlobal('giving_duration', goodsGift);

    // 权限校验
    if (
      !OrderPermission.isNotPremission(orderInfo.orderType) &&
      !OrderPermission.isUserValid(
        {
          orderType: orderInfo.orderType,
          buyerId: buyerInfo.buyerId,
          customerId: buyerInfo.customerId,
          customerType: buyerInfo.customerType,
        },
        buyer.buyerId,
        buyer.fansId,
        buyer.fansType
      ) &&
      !kosTokenVerified &&
      from !== 'ivr'
    ) {
      // 鉴权失败
      throw new PageException(40201, '你没有权限操作');
    }

    // 预售定金膨胀待支付尾款重定向到支付页
    // http://xiaolv.qima-inc.com/#/demand/search?show=true&ids=30439
    if (this._isDepositExpansionFinalToPay(data)) {
      return ctx.redirect(`https://cashier.youzan.com/pay/wsctrade_pay?order_no=${orderNo}&kdt_id=${kdtId}`);
    }

    // 会员卡订单直接跳转到商品购买页，原IRON 371189店铺（有赞大号）需要特殊处理，先忽略
    if (itemInfo.length > 0 && itemInfo[0].goodsType === 20) {
      const memberCard = itemInfo[0];
      const alias = (memberCard.goodsInfo && memberCard.goodsInfo.alias) || '';
      // 引导领券弹框-付费权益卡支付完成-查看详情需要带guideDialogType=success这个固定标识
      ctx.redirect(OrderContext.buildUrl(`/showcase/goods?alias=${alias}&guideDialogType=success`, 'wap'));
      return;
    }

    this.setExtensionParams(ctx, 'order-detail', { orderNo });

    // 集中处理异步请求
    let response;
    try {
      response = await Promise.all([
        ctx.isThirdApp(),
        new ShopConfigReadService(ctx).queryShopPointsName(kdtId),
        new ShopConfigReadService(ctx).getOrderEnterShopPolicy().catch(() => {}), // 获取历史订单的进店配置
        new ExpressDetailService(ctx).getExpressDetail({
          kdtId,
          orderNo,
          exchange: false,
          buyerId: buyer.buyerId,
          fromApp: 'wsc-h5-trade',
        }).catch(() => []), // 获取物流信息
      ]);
    } catch (error) {
      const content = (error as any).errorContent;
      if (content) {
        throw new PageException(content.code, content.msg);
      } else {
        throw error;
      }
    }

    const [isThirdApp, pointsName, orderEnterShopPolicy, expressData] = response;
    this._prepareShareSettings(orderNo, kdtId, itemInfo, !!orderInfo.isAllowShareOrder);
    orderInfo.expressData = expressData;
    // 积分自定义名称
    ctx.setGlobal({ pointsName });
    paymentInfo.pointsName = pointsName;

    const orderBizExtra = data.orderBizExtra || {};
    if (orderInfo && orderInfo.afterSaleContact && orderInfo.afterSaleContact.isImOrder) {
      orderInfo.afterSaleContact.imFromSource = this._computeImFromSource(kdtId, orderNo, itemInfo, shopInfo);
    }

    if (orderInfo && orderInfo.isAllowShareCoupon) {
      orderInfo.shareCouponStyleVersion = await this.shareCouponStyleVersion(ctx.kdtId);
    }

    const orderBizUrl = data.orderBizUrl || {};
    if (orderInfo.isAllowPeerpay) {
      // eslint-disable-next-line max-len
      orderBizUrl.peerpayUrl = await this.getPeerpayConfirmUrl(paymentInfo.peerpayResult || {}, kdtId); // 代付
    }

    // 从爱逛进入教育店铺，订单详情不展示商品推荐
    await this._filterEduRecommend();

    this.setYouzanSecured(orderInfo.yzGuarantee);

    // kostoken验证方式
    orderBizExtra.isKosTokenVerified = kosTokenVerified;

    // 是否是粉丝
    const { is_fans: isFans } = this.ctx.getLocalSession();

    // 再来一单按钮配置[直接进入下单页]
    let directBuyAgainBtnConfig = {};
    try {
      directBuyAgainBtnConfig = await getDirectBuyAgainBtnConfig(ctx);
    } catch (e) {
      console.log(e);
    }

    // xss防御
    this.escapeGoods(itemInfo);
    ctx.setGlobal({
      env: {
        isFxZpp,
        isThirdApp, // 检测第三方APP
        isWeixin: ctx.isWeixin,
        isSwanApp: ctx.isSwanApp, // 百度小程序
        isYouzanwsc: ctx.isYouzanwsc, // 检测微商城APP（卖家）
        isYouzanwxd: ctx.isYouzanwxd, // 检测微小店
        isYouzanmars: ctx.isYouzanmars, // 有赞精选APP
        isYouzanke: UA.isYouzanke(ctx.userAgent), // 有赞客
        isSupportMarsVip: this._isSupportMarsVip(),
      },
      isFans: isFans === 1,
      ump,
      rxNo,
      gift: data.gift,
      // 复购券信息
      marketingInfo: data.marketingInfo,
      isIVROwner,
      itemInfo,
      shopInfo,
      orderInfo,
      buyerInfo,
      paymentInfo,
      invoiceInfo,
      isNewhopeKdt: isNewhopeKdtShop(kdtId), // 新希望店铺标记
      orderBizUrl,
      paidPromotion,
      orderBizExtra,
      orderAddressInfo,
      microTransferInfo: data.microTransferInfo,
      orderMark: data.sourceInfo?.orderMark,
      directBuyAgainBtnConfig,
      isOfflineOrder: data.sourceInfo?.isOfflineOrder, // 是否是门店订单
      orderEnterShopPolicy, // 历史订单及其分享是否需要接入进店
      privacyWaybill,
      outerPromotion, // CRM 订单优惠信息
    });

    // 日志埋点模型
    this.setSpm('od', kdtId);

    /** @mock */
    await this.initPriorUseOrderDetailData(ctx, {
      isPriorUse: +orderInfo.buyWay === 49,
      isPriorUsePaid: orderInfo?.extra?.PRIOR_USE_COMPLETED === 1,
    });

    try {
      // @ts-ignore
      const { apolloClient } = global.getRuntime();
      const useNewReserveConfig = await apolloClient.getConfig({
        appId: 'wsc-h5-trade',
        namespace: 'wsc-h5-trade.gray-release',
        key: 'useOrderDetailNewReserve',
      });
      const config = JSON.parse(useNewReserveConfig);
      ctx.setGlobal('useNewReserve', config.value);
    } catch (error) {}

    // crm线下门店订单只有中台化页面
    if (isCrmOfflineOrder) {
      await initRanta(ctx as any, {
        framework: 'tee',
        bizName: '@wsc-tee-h5-trade/detail-crm',
        appName: 'wsc-tee-h5'
      });
    } else {
      // 群团团小程序订单详情展示插件内容
      const isQtt = get(ctx, 'query.bizEnv') === 'fx-zpp';
      const isInGray = await isInGrayReleaseByKdtIdForQttApollo(
        ctx,
        { namespace: 'wsc-h5-fenxiao.whitelist', key: 'order-detail-plugin' },
        ctx.kdtId
      );
      await initRanta(ctx as any, {
        framework: 'tee',
        appName: 'wsc-tee-h5',
        bizName: '@wsc-tee-h5-trade/order-detail',
        ecloud: {
          setExtensionParams: [
            {
              kdtId: +ctx.kdtId,
              pageName: 'order-detail',
              pageNameV2: 'order-detail',
              conditionContext: this.getConditionContext(ctx, { orderNo: ctx.query.order_no }, isQtt && isInGray),
            },
          ],
        },
      });
    }
    await ctx.render(useNew || isCrmOfflineOrder ? 'order/tee-detail.html' : 'order/detail.html', {
      // 是否是批发订单
      title: orderInfo?.isWholesaleOrder ? '批发订单详情' : '订单详情',
    }, {
        appName: 'wsc-tee-h5',
        skipLoadOpsWebConfig: true,
    });
  }

  // 是否支付宝交易组件订单
  isAliPayTradeModule(orderExtra: any) {
    try {
      const BIZ_ORDER_ATTRIBUTE = JSON.parse(orderExtra.BIZ_ORDER_ATTRIBUTE || '{}');
      return BIZ_ORDER_ATTRIBUTE.MULTI_PLAT_OUT_CHANNEL === 'ALIPAY_TRADE_MODULE';
    } catch (error) {
      return false;
    }
  }

  private async initPriorUseOrderDetailData(ctx: Context, options: Record<string, any>) {
    return initOrderData({
      type: 'detail',
      context: options,
      onSuccess: (data: any) => ctx.setGlobal('@cashier/prior-use', data),
    });
  }

  private async _filterEduRecommend() {
    const { ctx } = this;
    let filterEduRecommend = false;

    const isGuang = () => {
      const tpps = ctx.cookies.get('tpps') || '';
      const REG = /^pf\./;
      const platformSource = REG.test(tpps) ? tpps.replace(REG, '') : '';
      return platformSource === 'GUANG';
    };

    if (isGuang()) {
      const shopMetaInfo = await this.callService('iron-base/shop.ShopMetaReadService', 'getShopMetaInfo', ctx.kdtId);

      if (shopMetaInfo && shopMetaInfo.shopType === 0 && shopMetaInfo.shopTopic === 1) {
        filterEduRecommend = true;
      }
    }
    ctx.setGlobal({ filterEduRecommend });
  }

  private _isDepositExpansionFinalToPay(data: any): boolean {
    const orderActivities = get(data, 'ump.orderActivities', []);
    const phasePays = get(data, 'paymentInfo.phasePays', []);
    const finalPay = phasePays.length ? phasePays[phasePays.length - 1] : null;

    const isDepositExpansion = orderActivities.some((item: any) => item.type === 'depositExpansion');
    const isFinalToPay = finalPay ? finalPay.state && finalPay.state === 10 : false;

    return isDepositExpansion && isFinalToPay;
  }

  // 是否支持精选会员店
  private _isSupportMarsVip(): boolean {
    const { isYouzanmars, platformVersion, kdtId } = this.ctx;

    return (
      isYouzanmars &&
      /* eslint-disable-next-line */
      kdtId == '43100611' &&
      CompareVersion.isGte(platformVersion, '4.0.0')
    );
  }

  private _computeImFromSource(kdtId: JSNumber, orderNo: string, itemInfo: any, shopInfo: any): any {
    const imgs: string[] = [];
    let piece = 0;
    itemInfo.forEach((item: any) => {
      piece += +item.num || 0;
      imgs.length < 3 && item.goodsInfo && item.goodsInfo.imgUrl && imgs.push(item.goodsInfo.imgUrl);
    });

    return {
      kdt_id: kdtId, // kdt_id
      source: 'order', // 写死'order'
      endpoint: 'h5', // 渠道，wap端写死'h5'
      site_id: shopInfo.storeId || undefined, // 网点id
      site_name: shopInfo.offlineStoreName || undefined, // 网点name
      team_name: shopInfo.shopName || undefined, // 店铺名
      detail: {
        order_no: orderNo || '',
        piece, // 订单中商品数量，不同种类相加（如A*2，B*1，最后数量为3）
        imgs, // 取订单中商品图片（最多三张）
      },
    };
  }

  private async getPeerpayConfirmUrl(invite: any, kdtId: JSNumber): Promise<string> {
    const shopConfigs: any = await this.callService('iron-base/shop.ShopConfigService', 'getShopConfigs', kdtId, [
      'wxpay_test',
      'weixin_pay_big_sign',
      'weixin_pay_big_unbind',
      'weixin_pay',
    ]);

    let action;
    if (
      parseInt(shopConfigs.wxpay_test) &&
      !parseInt(shopConfigs.weixin_pay_big_sign) &&
      !parseInt(shopConfigs.weixin_pay_big_unbind)
    ) {
      if (invite.payStrategy) {
        action = '/test/peerpay';
      } else {
        action = '/test/peerpaySelect';
      }
      return `${action}?id=${invite.inviteNo}&sign=${invite.inviteSign}`;
    }

    if (parseInt(shopConfigs.weixin_pay) && parseInt(shopConfigs.weixin_pay_big_unbind)) {
      if (invite.payStrategy) {
        action = '/wxpay/peerpay';
      } else {
        action = '/wxpay/peerpaySelect';
      }
      return `${action}?id=${invite.inviteNo}&sign=${invite.inviteSign}&kdt_id=${kdtId}&showwxpaytitle=1`;
    }

    const giftUrlArr = [
      invite.payStrategy ? '/wxpay/peerpay' : '/wxpay/peerpaySelect',
      '?id=',
      invite.inviteNo,
      '&sign=',
      invite.inviteSign,
      kdtId ? `&kdt_id=${kdtId}` : '',
    ];
    return giftUrlArr.join('');
  }

  private _prepareShareSettings(orderNo: string, kdtId: JSNumber, items: any[], isAllowShareOrder: boolean): void {
    const itemName = (items.length && items[0].goodsName) || '';
    let itemNameMaxlen = 0;
    const itemImg = (items.length && items[0].goodsInfo.imgUrl) || '';
    let otherMessage = '';
    const orderSize = items.length;
    const sharedUrl = OrderContext.buildUrl(
      `/trade/order/result?is_share=1&shared=1&order_no=${orderNo}&kdt_id=${kdtId}`,
      'wap'
    );
    const notShare = !isAllowShareOrder;
    let shareText = '';

    // 订单包含多件商品,商品名最长11字
    if (orderSize > 1) {
      itemNameMaxlen = 11;
      otherMessage = '等已下单，必须炫耀一下';
    } else {
      // 订单仅含单件商品(包括size<=0的异常情况也做同样处理),商品名最长12字
      itemNameMaxlen = 12;
      otherMessage = '已下单，必须炫耀一下';
    }

    if (itemName.length > itemNameMaxlen) {
      // eslint-disable-next-line no-useless-escape
      shareText = `\"${itemName.substr(0, itemNameMaxlen)}...\"${otherMessage}`;
    } else {
      // eslint-disable-next-line no-useless-escape
      shareText = `\"${itemName}\"${otherMessage}`;
    }

    // 设置分享信息
    this.ctx.setShare({
      cover: itemImg,
      imgUrl: itemImg,
      title: shareText,
      desc: itemName,
      link: sharedUrl,
      notShare,
    });
  }

  public async getOrderInfo(): Promise<void> {
    const { ctx } = this;
    const reqBody = ctx.getPostData();
    let { orderNo = '' } = reqBody;
    const kdtId = reqBody.kdtId || ctx.kdtId || 0;
    const { buyer } = this;
    const supportBlindBox = reqBody.supportBlindBox || '';

    // V开头的虚拟订单 需要查询到E开头的订单号
    if (orderNo.startsWith('V')) {
      const orderRelationQueryService = new OrderRelationQueryService(ctx);
      try {
        // 虚拟单号 可能为订单号 或者 支付单号
        let { valueOrderNos = [] } =
          (await orderRelationQueryService.queryOrderRelationByOrderNoAndType(8, orderNo)) || {};
        if (valueOrderNos[0]) {
          orderNo = valueOrderNos[0];
        } else {
          ({ valueOrderNos = [] } =
            (await orderRelationQueryService.queryOrderRelationByOrderNoAndType(6, orderNo)) || {});
          if (valueOrderNos[0]) {
            orderNo = valueOrderNos[0];
          }
        }
      } catch (err) {
        // do nothing
      }
    }

    const params = {
      orderNo,
      kdtId,
      buyerId: buyer.buyerId,
      customerId: buyer.fansId,
      customerType: buyer.fansType,
      sourceName: this.sourceName,
      marketingMark: {
        source: 'order_details_rcmd',
        bizName: 'order_details_rcmd',
      },
    };

    const [data, pointsName, expressData] = await Promise.all([
      new OrderDetailService(ctx).detailByOrderNo(params, 'weapp'),
      new ShopConfigReadService(ctx).queryShopPointsName(kdtId).catch(() => {}),
      new ExpressDetailService(ctx).getExpressDetail({
        kdtId,
        orderNo,
        exchange: false,
        buyerId: buyer.buyerId,
        fromApp: 'wsc-h5-trade',
      }).catch(() => []),
    ]);

    try {
      data.mainOrderInfo.expressData = expressData;
    } catch (e) {
      // do noting
      console.log('expressData 获取失败: ', e);
    }

    if (checkNeedDisplayPaidPromotion(get(data, 'orderBizExtra', {}))) {
      data.ump.paidPromotion = await this.queryPaidPromotionFunc(orderNo, +ctx.kdtId);
    }

    // 积分订单返回自定义积分名称
    const { paymentInfo = {} as IPaymentInfoDto } = data;
    paymentInfo.pointsName = pointsName;

    // 低版本知识付费拼团不展示查看团详情按钮, 小程序里链接不对
    try {
      if (
        this.ctx.xExtraData.version &&
        CompareVersion.isLt(this.ctx.xExtraData.version, '2.24.3') &&
        data.itemInfo[0].goodsType === 31
      ) {
        data.mainOrderInfo.isAllowGroupon = false;
      }
    } catch (err) {
      // do noting
    }
    // 检查并设置不支持查看的信息
    // 教育订单已经支持在小程序上查看订单详情，现关闭弹窗
    // this._setWeappSupportedInfo(data, orderNo, kdtId);

    // 低版本小程序不支持查看盲盒订单详情
    if (!supportBlindBox) {
      this._setWeappSupportedInfo(data, orderNo, kdtId);
    }

    try {
      if (data.mainOrderInfo && data.mainOrderInfo.isAllowShareCoupon) {
        data.mainOrderInfo.shareCouponStyleVersion = await this.shareCouponStyleVersion(ctx.kdtId);
      }
    } catch (e) {
      console.log(e);
    }

    try {
      const directBuyAgainBtnConfig = await getDirectBuyAgainBtnConfig(ctx);
      // @ts-ignore
      data.directBuyAgainBtnConfig = directBuyAgainBtnConfig || {};
    } catch (e) {
      console.log(e);
    }

    // 支付宝交易组件订单不允许修改地址
    if (this.isAliPayTradeModule(data.orderExtra)) {
      data.mainOrderInfo.allowShowModifyAddress = false;
    }

    // 抖音券订单获取券二维码
    if (data?.sourceInfo?.orderMark === 'dy_coupon') {
      const params = {
        kdtId,
        pageSize: 10,
        pageNum: 1,
        sendOrderNo: orderNo,
        sendSource: 'pay_coupon',
      };
      const res: any = await new OrderDetailService(ctx).pageQueryVerifyCodeBySendNo(params);
      Object.assign(data, { dyCouponList: res?.data || [] });
    }

    ctx.json(0, '', data);
  }

  public async queryPaidPromotionFunc(orderNo: string, kdtId: number) {
    const { ctx, buyer } = this;
    const paidPromotionQuery = {
      orderNo,
      fansId: buyer.fansId,
      kdtId,
      buyerId: buyer.buyerId,
      fansType: buyer.fansType,
    };

    const result = (await new PaidPromotionDubboService(ctx).useActivityInOrder(paidPromotionQuery)) || {};

    new OrderPayResultService(ctx).patchPaidPromotion(result, orderNo);
    return result;
  }

  public async triggerActivity() {
    const { ctx } = this;
    const postData = ctx.getPostData();
    const paidPromotionQuery = {
      ...postData,
      kdtId: ctx.kdtId,
    };

    const result = (await new PaidPromotionDubboService(ctx).triggerActivity(paidPromotionQuery)) || {};
    ctx.json(0, '', result);
  }

  public async orderConfirmReceive(): Promise<void> {
    const { ctx, buyer } = this;
    const { isWeapp, isWeappNative, method } = ctx;

    let orderNo;
    let kdtId;
    let accessToken;

    if (method === 'POST') {
      // 小程序新版本&h5
      const { order_no } = ctx.getPostData();
      const { kdt_id } = ctx.query.kdt_id ? ctx.query : ctx.getPostData();
      const { access_token } = ctx.query;
      orderNo = order_no;
      kdtId = kdt_id;
      accessToken = access_token;
    } else if (method === 'GET') {
      // 小程序旧版本
      const { order_no, kdt_id, access_token } = ctx.query;
      orderNo = order_no;
      kdtId = kdt_id;
      accessToken = access_token;
    }

    if (isWeappNative && (!orderNo || !kdtId || !accessToken)) {
      ctx.r(1, '', '参数错误');
      return;
    }

    const params = {
      kdtId,
      orderNo,
      requestId: this.randomName(24, buyer.buyerId),
      operator: {
        operatorPhone: buyer.buyerPhone,
        operatorId: buyer.buyerId,
        operatorName: buyer.fansNickname,
        role: 'buyer',
      },
      source: {
        clientIp: this.getRemoteIp(),
        from: isWeapp ? 'XCX-001' : 'H5-001',
        open: false,
        remark: '',
      },
    };

    const res = await new OrderDetailService(ctx).orderConfirmRecvive(params);
    ctx.r(0, '', res);
  }

  public async orderDelayReceive(): Promise<void> {
    const { ctx, buyer } = this;
    const { isWeapp } = ctx;

    const { order_no: orderNo, kdt_id: kdtId } = ctx.getPostData();

    const params = {
      kdtId,
      orderNo,
      requestId: this.randomName(24, buyer.buyerId),
      operator: {
        operatorPhone: buyer.buyerPhone,
        operatorId: buyer.buyerId,
        operatorName: buyer.fansNickname,
        role: 'buyer',
      },
      source: {
        clientIp: this.getRemoteIp(),
        from: isWeapp ? 'XCX-001' : 'H5-001',
        open: false,
        remark: '',
      },
    };
    const res = await new OrderDetailService(ctx).orderDelayReceive(params);
    if (res) {
      ctx.r(0, '你的收货时间已延长3天，如需多次延长收货时间，请联系商家。', res);
    } else {
      ctx.r(9999, '网络错误，请稍后再试', res);
    }
  }

  public async checkOrderDelayReceive(): Promise<void> {
    const { ctx, buyer } = this;
    const { isWeapp } = ctx;

    const { order_no: orderNo, kdt_id: kdtId } = ctx.getPostData();

    const params = {
      kdtId,
      orderNo,
      requestId: this.randomName(24, buyer.buyerId),
      operator: {
        operatorPhone: buyer.buyerPhone,
        operatorId: buyer.buyerId,
        operatorName: buyer.fansNickname,
        role: 'buyer',
      },
      source: {
        clientIp: this.getRemoteIp(),
        from: isWeapp ? 'XCX-001' : 'H5-001',
        open: false,
        remark: '',
      },
    };
    const res = await new OrderDetailService(ctx).checkOrderDelayReceive(params);
    ctx.r(0, '', res);
  }

  /**
   * 生成随机字符串
   * @param len 随机字符串长度
   * @param id 在字符串前拼接的字符串
   */
  private randomName(len: number, id: number): string {
    id = id || 0;
    len = len || 24;
    const chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678';
    const maxPos = chars.length;
    let str = '';
    for (let i = 0; i < len; i++) {
      str += chars.charAt(Math.floor(Math.random() * maxPos));
    }
    return `${id}${new Date().getTime()}${str}`.substr(0, len);
  }

  // 设置小程序支持情况
  public _setWeappSupportedInfo(data: any, orderNo: string, kdtId: JSNumber): void {
    const { activityType = 1, state = 100 } = data.mainOrderInfo || {};
    if (
      (+activityType === ACTIVITY_TYPE.blindBoxBuy || +activityType === ACTIVITY_TYPE.blindBoxVerification) &&
      state === 100
    ) {
      const info = {
        content: '暂不支持在小程序中查看本订单详情，你可以复制链接在浏览器中查看。',
        url: OrderContext.buildUrl(`/wscump/blind-box/detail?orderNo=${orderNo}&kdtId=${kdtId}`, 'h5', kdtId),
      };
      data.unsupportedInfo = info;
    }
  }

  private escapeGoods(itemGoods: any = []): void {
    itemGoods.forEach((item: any) => {
      const { goodsInfo } = item;
      if (goodsInfo) {
        goodsInfo.title && (goodsInfo.title = escape(goodsInfo.title));
        goodsInfo.shortTitle && (goodsInfo.shortTitle = escape(goodsInfo.shortTitle));
      }
      // item.buyerMemo && (item.buyerMemo = escape(item.buyerMemo));
    });
  }

  private isGuang(): boolean {
    const tpps = this.ctx.cookies.get('tpps');
    const REG = /^pf\./;
    const platformSource = tpps && REG.test(tpps) ? tpps.replace(REG, '') : '';
    return platformSource === 'GUANG';
  }

  /**
   * 功能：根据店铺类型判断是否的确要显示【领优惠券】按钮
   * 本次改动，只支持微商城单店和连锁D - 20210607
   * 用途：如果不是微商城单店||连锁D，强制转成false
   * PRD链接：https://doc.qima-inc.com/pages/viewpage.action?pageId=319563848
   * 后续改进：如增加店铺类型，可直接在这里增加，或者删除
   * @param kdtId
   * @private
   */
  private async shareCouponStyleVersion(kdtId: string): Promise<'v1' | 'v2'> {
    try {
      const shopMetaInfo = await this.callService('iron-base/shop.ShopMetaReadService', 'getShopMetaInfo', kdtId);
      return checkPureWscSingleStore(shopMetaInfo) || checkRetailMinimalistShop(shopMetaInfo) ? 'v2' : 'v1';
    } catch (e) {
      return 'v1';
    }
  }

  // 判断是否能自动跳转发布评价页
  async getCanAutoGoEvaluate(orderNo: string, kdtId: number) {
    const { ctx } = this;
    // 仅支持微信小程序及非有赞精选 H5
    const { isWeapp, isYouzanmars, isMiniProgram } = ctx;
    if ((isMiniProgram && !isWeapp) || isYouzanmars) {
      return false;
    }
    const shopMetaInfo = await this.callService('iron-base/shop.ShopMetaReadService', 'getShopMetaInfo', kdtId);
    // 仅支持微商城单店
    if (!checkPureWscSingleStore(shopMetaInfo)) {
      return false;
    }

    // 店铺是否在白名单内
    const isInGray = await isInGrayReleaseByKdtId(
      ctx,
      { namespace: 'wsc-h5-trade.gray-release', key: 'afterConfirmAutoGoEvaluate' },
      ctx.kdtId
    );
    if (!isInGray) {
      return false;
    }

    try {
      const result = await new EvaluateSearchService(ctx).getEvaluateState({
        kdtId,
        orderNo,
      });
      return result;
    } catch (error) {
      ctx.logger.warn('确认收货后获取评价状态失败', error as any);
      return false;
    }
  }

  public async orderConfirmReceiveV2(): Promise<void> {
    const { ctx, buyer } = this;
    const { isWeapp, isWeappNative } = ctx;

    const { order_no: orderNo } = ctx.getPostData();
    const { kdt_id: kdtId } = ctx.query.kdt_id ? ctx.query : ctx.getPostData();
    const { access_token: accessToken } = ctx.query;

    if (isWeappNative && (!orderNo || !kdtId || !accessToken)) {
      ctx.r(1, '', '参数错误');
      return;
    }

    const params = {
      kdtId,
      orderNo,
      requestId: this.randomName(24, buyer.buyerId),
      operator: {
        operatorPhone: buyer.buyerPhone,
        operatorId: buyer.buyerId,
        operatorName: buyer.fansNickname,
        role: 'buyer',
      },
      source: {
        clientIp: this.getRemoteIp(),
        from: isWeapp ? 'XCX-001' : 'H5-001',
        open: false,
        remark: '',
      },
    };
    const result = {
      isAutoGoEvaluate: false,
      isConfirmSuccess: false,
    };
    const res = await new OrderDetailService(ctx).orderConfirmRecvive(params);
    result.isConfirmSuccess = true;

    if (res) {
      const isAutoGoEvaluate = await this.getCanAutoGoEvaluate(orderNo, kdtId);
      result.isAutoGoEvaluate = isAutoGoEvaluate;
    }

    ctx.success(result);
  }

  /** 给小程序提供的查询订单信息接口 */
  public async getOrderInfoByTrade() {
    const { ctx } = this;
    const { order_no } = ctx.query;
    const result = await new OrderDetailService(ctx).getTradeOrdersDetail({
      orderNos: [order_no],
      bizGroup: 'crm',
      app: 'wsc-h5-trade',
      options: { withMainOrderInfo: true, withSourceInfo: true },
    });
    ctx.r(0, 'ok', result);
  }

  /**
   * 获取零售订单进度数据（纯数据获取方法）
   *
   * @param {string} orderNo - 订单号
   * @returns {Promise<IFormattedOrderProgress>} 格式化后的订单进度数据
   */
  public async getOrderProgressData(orderNo: string): Promise<IFormattedOrderProgress> {
    const { ctx } = this;
    const { kdtId, adminId } = ctx;
    const shelfProgressService = new ShelfProgressService(ctx);

    // 构建请求参数
    const request: IShelfOrderProgressRequest = {
      orderNo,
      retailSource: 'wsc-h5-trade',
      kdtId: Number(kdtId),
      adminId: Number(adminId),
    };

    // 获取原始数据
    const rawData = await shelfProgressService.queryOrderProgress(request);

    // 格式化数据并返回
    return formatOrderWaitingProgress(rawData);
  }

  /**
   * 查询零售订单进度（接口方法）
   *
   * 请求参数：
   * - orderNo: 订单号（必填）
   *
   * 返回数据：格式化后的订单进度数据，包含步骤条、排队信息、等待时间等
   *
   * 注意：retailSource、kdtId、adminId 等参数会在内部自动获取
   *
   * 使用示例：
   * GET /order/query-progress?orderNo=E20241209123456789
   */
  public async queryOrderProgress(): Promise<void> {
    const { ctx } = this;
    const { orderNo } = ctx.query;

    // 参数验证
    if (!orderNo) {
      return ctx.json(400, '订单编号不能为空');
    }

    const result = await this.getOrderProgressData(orderNo);

    return ctx.json(0, '查询成功', result);
  }
}

export = OrderDetailController;
