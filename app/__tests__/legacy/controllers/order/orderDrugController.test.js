import { app, mock } from '@youzan/astroboy-mock/bootstrap';
import BaseController from '../../../controllers/base/BaseController';
import OrderDrugService from '../../../services/order/OrderDrugService';

jest.mock('../../../controllers/base/BaseController', () =>
  jest.requireActual('../../__mocks__/BaseController')
);

jest.mock('../../../services/order/OrderDrugService');

afterEach(() => {
  BaseController.mockClear();
  OrderDrugService.mockClear();
});

// 获取默认疾病
test('getDefaultDisease', async () => {
  app.mockContext({
    adminId: 8103531905,
    kdtId: 59415891,
  });

  OrderDrugService.mockImplementation(() => {
    return {
      getDefaultDisease: jest.fn().mockResolvedValue([]),
    };
  });

  const res = await app
    .httpRequest()
    .get('/wsctrade/order/drug/getDefaultDisease.json');

  expect(res.body).toEqual({
    code: 0,
    data: [],
    msg: 'ok',
  });
});

// 根据用药人和处方药 查询对应的确诊疾病列表
test('queryDiagnoseDisease', async () => {
  app.mockContext({
    adminId: 8103531905,
    kdtId: 59415891,
  });
  const data = {};
  OrderDrugService.mockImplementation(() => {
    return {
      queryDiagnoseDisease: jest.fn().mockResolvedValue(data),
    };
  });
  const res = await app
    .httpRequest()
    .post('/wsctrade/order/drug/queryDiagnoseDisease.json');
  expect(res.body).toEqual({
    code: 0,
    data: {},
    msg: 'ok'
  });
});

// 搜索疾病接口
test('searchDiseaseInfo', async () => {
  app.mockContext({
    adminId: 8103531905,
    kdtId: 59415891,
  });

  const data = {};

  OrderDrugService.mockImplementation(() => {
    return {
      searchDiseaseInfo: jest.fn().mockResolvedValue(data),
    };
  });
  const res = await app
    .httpRequest()
    .post('/wsctrade/order/drug/searchDiseaseInfo.json');
  expect(res.body).toEqual({
    code: 0,
    data: {},
    msg: 'ok'
  });
});

// 查询处方单
test('queryID', async () => {
  app.mockContext({
    adminId: 8103531905,
    kdtId: 59415891,
  });

  const data = {};

  OrderDrugService.mockImplementation(() => {
    return {
      get: jest.fn().mockResolvedValue(data),
    };
  });
  const res = await app.httpRequest().post('/wsctrade/order/drug/queryID.json');
  expect(res.body).toEqual({});
});

// 创建处方单
test('create', async () => {
  app.mockContext({
    adminId: 8103531905,
    kdtId: 59415891,
  });

  const data = {};

  OrderDrugService.mockImplementation(() => {
    return {
      get: jest.fn().mockResolvedValue(data),
    };
  });
  const res = await app.httpRequest().post('/wsctrade/order/drug/create.json');
  expect(res.body).toEqual({});
});

// 查询用药人
test('queryUser', async () => {
  app.mockContext({
    adminId: 8103531905,
    kdtId: 59415891,
  });

  const data = [];

  OrderDrugService.mockImplementation(() => {
    return {
      get: jest.fn().mockResolvedValue(data),
    };
  });
  const res = await app
    .httpRequest()
    .get('/wsctrade/order/drug/queryUser.json');
  expect(res.body).toEqual({});
});

// 创建用药人
test('createUser', async () => {
  app.mockContext({
    adminId: 8103531905,
    kdtId: 59415891,
  });

  const data = {};

  OrderDrugService.mockImplementation(() => {
    return {
      get: jest.fn().mockResolvedValue(data),
    };
  });
  const res = await app
    .httpRequest()
    .post('/wsctrade/order/drug/createUser.json');
  expect(res.body).toEqual({});
});

test('deleteUser', async () => {
  app.mockContext({
    adminId: 8103531905,
    kdtId: 59415891,
  });

  const data = {};

  OrderDrugService.mockImplementation(() => {
    return {
      deleteUser: jest.fn().mockResolvedValue(data),
    };
  });
  const res = await app
    .httpRequest()
    .post('/wsctrade/order/drug/deleteUser.json');
  expect(res.body).toEqual({
    code: 0,
    data: {},
    msg: 'ok'
  });
});
