/** ************************************************************************************************
 * 支付方式枚举值
 * 参考自: https://gitlab.qima-inc.com/trade-platform/trade-core/blob/596f07433e48459690f6ddcfc1a9924a039cc419/trade-core-api/src/main/java/com/youzan/trade/core/service/constant/PayWayEnum.java
 ************************************************************************************************ */
enum PayWayEnum {
  DEFAULT = 0,
  WXPAY = 1,
  ALIWAP = 2,
  ALIPAY = 3,
  UNIONPAY = 4,
  TENPAY = 5,
  UNIONWAP = 6,
  PEERPAY = 7,
  UMPAY = 8,
  CODPAY = 9,
  WXPAY_DAIXIAO = 10,
  WXPAY_SHOULI = 11,
  BAIDUWAP = 12,
  WX_APPPAY = 13,
  FX_MERGED = 14,
  UMP_PRESENT = 15,
  UMP_COUPON = 16,
  FX_SPLITTING = 17,
  AIXUEDAI = 18,
  WX_WAPPAY = 19,
  WX_HB = 20,
  UMP_REBATE = 21,
  UMP_HB = 22,
  PAYZA = 23,
  YZPAY = 24,
  PREPAID_CARD = 25,
  PAYPAL = 26,
  QQPAY = 27,
  ECARD = 28,
  BARCODE_WX = 29,
  BARCODE_ALIPAY = 30,
  GIFT_CARD = 33,
  UNIFIED_PREPAID_CARD = 35,
  CREDIT_CARD_UNIONPAY = 36,
  DEBIT_CARD_UNIONPAY = 37,
  INSTALMENT = 40,
  PRIOR_USE = 49,
  ALIPAY_HBFQ = 53,
  WX_NATIVE = 72,
  UN_SETTLED_AMOUNT_PAY = 80,
  ENCHASHMENT_GIFT_CARD = 90,
  OF_ONLINE_ACCOUNT = 100,
  OF_ONLINE_PREPAID_ACCOUNT = 300,
  OF_ONLINE_DEPOSIT_ACCOUNT = 400,
  OF_YOUZAN_QR = 101,
  OF_WEIXIN = 102,
  OF_ALIPAY = 103,
  OF_POS = 104,
  OF_TABLE_CARD = 105,
  OF_PREPAID_CARD = 106,
  OF_E_CARD = 107,
  MARK_PAY_WXPAY = 110,
  MARK_PAY_ALIPAY = 111,
  MARK_PAY_POS = 112,
  ALLIN_SWIPECARD = 113,
  MARK_PAY_DIY = 114,
  CHANGE_PAY = 115,
  UMP_PAY = 116,
  SUNMI_WX = 117,
  SUNMI_ALIPAY = 118,
  SUNMI_SWIPECARD = 119,
  OF_OFFLINE_ACCOUNT = 200,
  OF_CASH = 201,
  MIXED_PAYMENT = 202,
  OUTSIDE_PAYMENT = 203,
  TRANSFER_VOUCHER = 204,
  MARK_PAY_CREDIT_PAY = 205,
  TRANSFER_TO_PUBLIC = 206,
  WX_XIAO_DIAN = 207,
  ABC_EPAY = 4093,
  UPQUICKPASS = 56,
  CCB_PAY = 57,
  E_CNY_PAY = 58,
  ALIPAY_FLOWER = 4095,
  ALIPAY_AGREEMENT = 4096,
  ALIPAY_APPLET = 4097,
  ELECTRONIC_BANK_PAY = 4101,
  YZYUN_PAY = 55,
  PF_OFFLINE_PAY = 5123,
  MT_DIANPING_COUPON_PAY = 301,
  MT_DIANPING_COUPON_DISCOUNT_PAY = 302,
  DY_COUPON_PAY = 303,
  DY_COUPON_DISCOUNT_PAY = 304,
  CMCC_PAY = 401,
  DY_DYPAY = 990,
  DY_ALIPAY = 991,
  DY_WXPAY = 992,
  THIRD_WXPAY = 993,
  THIRD_ALIPAY = 994,
  MARK_PAY_EXCHANGE = 995,
  BANK_TRANSFER_LARGE = 62
}

class PayWay {
  private static descriptions: { [key in PayWayEnum]: string } = {
    [PayWayEnum.DEFAULT]: "默认值,未支付",
    [PayWayEnum.WXPAY]: "微信自有支付",
    [PayWayEnum.ALIWAP]: "支付宝wap",
    [PayWayEnum.ALIPAY]: "支付宝wap",
    [PayWayEnum.UNIONPAY]: "",
    [PayWayEnum.TENPAY]: "财付通",
    [PayWayEnum.UNIONWAP]: "",
    [PayWayEnum.PEERPAY]: "代付",
    [PayWayEnum.UMPAY]: "联动优势",
    [PayWayEnum.CODPAY]: "货到付款",
    [PayWayEnum.WXPAY_DAIXIAO]: "大账号代销",
    [PayWayEnum.WXPAY_SHOULI]: "受理模式",
    [PayWayEnum.BAIDUWAP]: "百付宝",
    [PayWayEnum.WX_APPPAY]: "sdk支付",
    [PayWayEnum.FX_MERGED]: "合并付货款",
    [PayWayEnum.UMP_PRESENT]: "领取赠品",
    [PayWayEnum.UMP_COUPON]: "优惠兑换",
    [PayWayEnum.FX_SPLITTING]: "自动付货款",
    [PayWayEnum.AIXUEDAI]: "爱学贷",
    [PayWayEnum.WX_WAPPAY]: "微信wap",
    [PayWayEnum.WX_HB]: "微信红包支付",
    [PayWayEnum.UMP_REBATE]: "返利",
    [PayWayEnum.UMP_HB]: "ump红包",
    [PayWayEnum.PAYZA]: "",
    [PayWayEnum.YZPAY]: "易宝支付",
    [PayWayEnum.PREPAID_CARD]: "储值卡",
    [PayWayEnum.PAYPAL]: "",
    [PayWayEnum.QQPAY]: "qq支付",
    [PayWayEnum.ECARD]: "有赞E卡支付",
    [PayWayEnum.BARCODE_WX]: "微信条码",
    [PayWayEnum.BARCODE_ALIPAY]: "支付宝条码",
    [PayWayEnum.GIFT_CARD]: "礼品卡支付",
    [PayWayEnum.UNIFIED_PREPAID_CARD]: "会员余额",
    [PayWayEnum.CREDIT_CARD_UNIONPAY]: "信用卡银联支付",
    [PayWayEnum.DEBIT_CARD_UNIONPAY]: "储蓄卡银联支付",
    [PayWayEnum.INSTALMENT]: "分期支付",
    [PayWayEnum.PRIOR_USE]: "先用后付",
    [PayWayEnum.ALIPAY_HBFQ]: "支付宝花呗分期支付",
    [PayWayEnum.WX_NATIVE]: "微信扫码二维码支付",
    [PayWayEnum.UN_SETTLED_AMOUNT_PAY]: "待结算&余额支付",
    [PayWayEnum.ENCHASHMENT_GIFT_CARD]: "礼品卡支付",
    [PayWayEnum.OF_ONLINE_ACCOUNT]: "代收账户",
    [PayWayEnum.OF_ONLINE_PREPAID_ACCOUNT]: "储值账户",
    [PayWayEnum.OF_ONLINE_DEPOSIT_ACCOUNT]: "保证金账户",
    [PayWayEnum.OF_YOUZAN_QR]: "收款码",
    [PayWayEnum.OF_WEIXIN]: "微信",
    [PayWayEnum.OF_ALIPAY]: "支付宝",
    [PayWayEnum.OF_POS]: "刷卡",
    [PayWayEnum.OF_TABLE_CARD]: "二维码台卡",
    [PayWayEnum.OF_PREPAID_CARD]: "储值卡",
    [PayWayEnum.OF_E_CARD]: "有赞E卡",
    [PayWayEnum.MARK_PAY_WXPAY]: "标记收款-自有微信支付",
    [PayWayEnum.MARK_PAY_ALIPAY]: "标记收款-自有支付宝",
    [PayWayEnum.MARK_PAY_POS]: "标记收款-自有POS刷卡",
    [PayWayEnum.ALLIN_SWIPECARD]: "通联刷卡支付",
    [PayWayEnum.MARK_PAY_DIY]: "标记收款-自定义",
    [PayWayEnum.CHANGE_PAY]: "有赞零钱支付",
    [PayWayEnum.UMP_PAY]: "优惠全额抵扣",
    [PayWayEnum.SUNMI_WX]: "商米支付",
    [PayWayEnum.SUNMI_ALIPAY]: "商米支付-支付宝",
    [PayWayEnum.SUNMI_SWIPECARD]: "商米pos",
    [PayWayEnum.OF_OFFLINE_ACCOUNT]: "记账账户",
    [PayWayEnum.OF_CASH]: "现金",
    [PayWayEnum.MIXED_PAYMENT]: "组合支付",
    [PayWayEnum.OUTSIDE_PAYMENT]: "外部支付",
    [PayWayEnum.TRANSFER_VOUCHER]: "汇款支付",
    [PayWayEnum.MARK_PAY_CREDIT_PAY]: "标记收款-挂账支付",
    [PayWayEnum.TRANSFER_TO_PUBLIC]: "对公转账",
    [PayWayEnum.WX_XIAO_DIAN]: "微信支付-视频号小店",
    [PayWayEnum.ABC_EPAY]: "农行商E付",
    [PayWayEnum.UPQUICKPASS]: "云闪付支付",
    [PayWayEnum.CCB_PAY]: "建行支付",
    [PayWayEnum.E_CNY_PAY]: "数币支付",
    [PayWayEnum.ALIPAY_FLOWER]: "花呗支付",
    [PayWayEnum.ALIPAY_AGREEMENT]: "支付宝免密支付",
    [PayWayEnum.ALIPAY_APPLET]: "支付宝-用户付",
    [PayWayEnum.ELECTRONIC_BANK_PAY]: "银联网银支付",
    [PayWayEnum.YZYUN_PAY]: "有赞云支付",
    [PayWayEnum.PF_OFFLINE_PAY]: "批发线下支付",
    [PayWayEnum.MT_DIANPING_COUPON_PAY]: "美团点评券支付",
    [PayWayEnum.MT_DIANPING_COUPON_DISCOUNT_PAY]: "美团点评券优惠",
    [PayWayEnum.DY_COUPON_PAY]: "抖音券支付",
    [PayWayEnum.DY_COUPON_DISCOUNT_PAY]: "抖音券优惠",
    [PayWayEnum.CMCC_PAY]: "和包支付",
    [PayWayEnum.DY_DYPAY]: "抖音-抖音支付",
    [PayWayEnum.DY_ALIPAY]: "抖音-支付宝支付",
    [PayWayEnum.DY_WXPAY]: "抖音-微信支付",
    [PayWayEnum.THIRD_WXPAY]: "微信支付-三方平台",
    [PayWayEnum.THIRD_ALIPAY]: "支付宝支付-三方平台",
    [PayWayEnum.MARK_PAY_EXCHANGE]: "标记支付-换货",
    [PayWayEnum.BANK_TRANSFER_LARGE]: "银行转帐（大额）支付"
  };

  static getDesc(value: PayWayEnum): string {
    return this.descriptions[value] || "未知支付方式";
  }

  static getEnumName(value: PayWayEnum): string {
    return PayWayEnum[value];
  }
}

export { PayWayEnum, PayWay };
