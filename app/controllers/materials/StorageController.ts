import BaseController from '../base/BaseController';
import StorageService = require('../../services/meterials/StorageService');

class StorageController extends BaseController {
  // 获取公开上传 Cdn 文件的 token
  public async getCdnToken() {
    const { ctx } = this;
    // const queryData = ctx.query;
    const data = await new StorageService(ctx).getCdnToken({
      operatorId: this.buyerId,
    });

    data.upload_token = data.token;

    ctx.r(0, '', data);
  }
}

export = StorageController;
