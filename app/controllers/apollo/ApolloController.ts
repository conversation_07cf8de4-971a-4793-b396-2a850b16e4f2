import BaseController from '../base/BaseController';
import { Context } from 'astroboy';
import { getTradeApolloConfig } from '../../lib/Apollo';
import { isInGrayReleaseByKdtId } from '../../lib/WhiteListUtils';
import { checkRetailShop } from '@youzan/utils-shop';

const setupCdn = require('@youzan/iron-base/app/lib/set-up-cdn');

class ApolloController extends BaseController {
  /**
   * 获取apollo配置，支持自定义namespace，支持单个key和key数组
   * 返回结构：
   * { key1: apolloValue1, key2: apolloValue2 }
   */
  async getConfigJson(ctx: Context) {
    const { namespace = 'wsc-h5-trade.application', key, keys } = ctx.query;

    let config: Record<string, string> = {};
    if (key) {
      config = { [key]: getTradeApolloConfig(namespace, key) || '' };
    } else if (keys) {
      let parsed: string[] = [];
      try {
        parsed = JSON.parse(keys);
      } catch (error) {}

      const entries = parsed.map((key) => [key, getTradeApolloConfig(namespace, key)] || '');
      config = Object.fromEntries(entries);
    }

    return ctx.json(0, 'ok', config);
  }

  /**
   * 获取灰度状态
   */
  async getApolloGray(ctx: Context) {
    const { namespace = 'wsc-h5-trade.application', key } = ctx.query;

    /**
     * 检查当前请求是否需要排除灰度切流
     * @description 比如某些场景只针对微商城店铺来做切流，所以通过这里直接排除零售店铺不参与灰度。
     */
    const isExclude = await this.shouldExcludeFromGray(ctx, namespace, key);
    if (isExclude) {
      return ctx.json(0, 'ok', false);
    }

    const config = await isInGrayReleaseByKdtId(ctx, { namespace, key }, ctx.kdtId);

    setupCdn(ctx, 15);
    return ctx.json(0, 'ok', config);
  }

  /**
   * 根据namespace和key检查是否需要排除灰度切流，默认不排除返回false
   * @description 某些特定场景（如零售店铺）需要被排除在灰度外，此方法用于检查这些特殊场景
   * @param ctx - 上下文对象
   * @param namespace - Apollo 配置命名空间
   * @param key - Apollo 配置键名
   * @returns {Promise<boolean>} true: 允许参与灰度, false: 被过滤，不允许参与灰度
   */
  async shouldExcludeFromGray(ctx: Context, namespace: string, key: string) {
    const checkConfigMaps = [
      {
        namespace: 'wsc-h5-trade.application',
        key: 'trade-buy-prerender-gray',
        check: async () => {
          const shopMetaInfo = await this.getShopMetaInfo(ctx);
          return checkRetailShop(shopMetaInfo);
        },
      },
    ];

    const checkItem = checkConfigMaps.find(item => item.namespace === namespace && item.key === key);
    return checkItem?.check ? checkItem.check() : false;
  }

  private async getShopMetaInfo(ctx: Context) {
    const { kdtId } = ctx;
    // 先从state中获取店铺信息 小程序场景下可能直接调接口 state还未初始化 在state中没有的情况下 调用服务获取店铺信息
    const shopMetaInfo =
      ctx.getState('shopMetaInfo' as never) ||
      this.callService('iron-base/shop.ShopMetaReadService', 'getShopMetaInfo', kdtId);
    return shopMetaInfo || {};
  }
}

export = ApolloController;
