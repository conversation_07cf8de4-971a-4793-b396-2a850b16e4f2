/**
 * 错误处理器
 *
 * 内置处理器：限流错误
 *
 *
 * @example 添加自定义错误处理器
 * const exceptionHandler = new ExceptionHandler();
 * exceptionHandler.addHandler(
 *   (e) => e.code === 500,
 *   (e, ctx) => {
 *     console.log('Handle Server Error');
 *     return true; // 返回 true 表示已完成错误处理
 *   }
 * )
 *
 * try {
 *   throw new Error({ code: 500 });
 * } catch(e) {
 *   if (exceptionHandler.handle(e, this.ctx)) return;
 *   // 其他错误
 *   throw e;
 * }
 */
import {Context} from "astroboy";
import buildUrl from "@youzan/utils/url/buildUrl";
import args from "@youzan/utils/url/args";

type ErrorPredicate = (error: unknown) => boolean;
type ErrorHandler = (error: unknown, ctx: Context) => boolean;

interface HandlerEntry {
  predicate: ErrorPredicate;
  handler: <PERSON>rrorHandler;
}

enum ErrorCode {
  LimitTraffic = 429,
}

class ExceptionHandler {
  private handlers: HandlerEntry[] = [];

  constructor() {
    this.addHandler(
      (e): e is { code: number } => (e as any).code === ErrorCode.LimitTraffic,
      (_, ctx) => {
        ctx.redirect(
          buildUrl(
            args.add('/v3/shop/mass-flow-waiting', { callbackURL: ctx.request.href }),
            '',
            1
          )
        );
        return true;
      }
    )
  }

  addHandler(predicate: ErrorPredicate, handler: ErrorHandler): void {
    this.handlers.push({ predicate, handler });
  }

  handle(error: unknown, ctx: Context): boolean {
    for (const { predicate, handler } of this.handlers) {
      if (predicate(error)) {
        return handler(error, ctx);
      }
    }
    return false;
  }
}

export default ExceptionHandler;